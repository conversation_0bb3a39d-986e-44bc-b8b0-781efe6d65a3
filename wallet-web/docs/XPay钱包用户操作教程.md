# XPay钱包用户操作教程

## 📖 系统概述

XPay钱包是一款数字资产管理应用程序
## 🚀 快速开始

### 1. 首次使用 - 钱包初始化

#### 1.1 启动应用
- 系统将自动检查钱包初始化状态

#### 1.2 选择钱包创建方式
系统提供两种方式：

**方式一：创建新钱包**
1. 点击"创建新钱包"按钮
2. 仔细阅读安全警告并逐一确认
3. 系统生成12位助记词，请务必安全备份
4. 验证助记词：按顺序选择正确的助记词
5. 设置钱包密码（建议使用强密码）
6. 配置谷歌验证器（扫描二维码或手动输入密钥）
7. 完成创建，系统自动跳转到登录页面

**方式二：导入现有钱包**
1. 点击"导入钱包"按钮
2. 输入12位助记词（用空格分隔）
3. 设置新的钱包密码
4. 配置谷歌验证器
5. 完成导入

### 2. 钱包解锁

#### 2.1 密码验证
1. 在登录页面输入钱包密码
2. 系统显示密码强度指示器
3. 点击"下一步"进入双重验证

#### 2.2 谷歌验证
1. 打开谷歌验证器应用
2. 输入6位动态验证码
3. 点击"解锁钱包"
4. 验证成功后自动进入主界面

## 🏠 主界面导航

### 侧边栏菜单说明
- **仪表盘**：查看资产概览和统计数据
- **地址管理**：管理钱包地址和标签
- **充值记录**：查看充值交易历史
- **归集计划**：设置和管理资产归集策略
- **费用管理**：监控和管理交易手续费
- **设置**：系统配置和安全设置

### 顶部操作栏
- 用户头像：显示当前用户信息
- 锁定钱包：安全退出当前会话

## 📊 核心功能详解

### 3. 仪表盘功能

#### 3.1 资产概览
- **总资产价值**：显示所有链上资产的总价值
- **资产分布图表**：可视化展示不同币种的资产占比
- **近期交易**：显示最新的交易记录

#### 3.2 统计数据
- 充值统计：总充值金额和笔数
- 归集统计：总归集金额和笔数
- 手续费统计：Gas费用消耗情况
- 待处理交易：需要关注的未完成交易

### 4. 地址管理

#### 4.1 查看地址列表
- 显示地址余额、交易次数等信息
- 支持地址搜索和排序

#### 4.2 地址操作
- **复制地址**：点击复制图标快速复制地址
- **标签管理**：为地址添加自定义标签便于识别
- **查看详情**：点击地址查看详细信息和交易历史

### 5. 充值记录管理

#### 5.1 查看充值记录
- 显示所有充值交易的详细信息
- 支持按时间、金额、状态筛选
- 实时更新交易状态

#### 5.2 充值操作
- 获取充值地址：系统自动生成专用充值地址
- 二维码分享：生成二维码便于移动端扫描
- 交易确认：监控区块链确认状态

### 6. 归集计划

#### 6.1 设置归集策略
- **自动归集**：设置触发条件（余额阈值、时间间隔）
- **归集地址**：配置目标归集地址

#### 6.2 归集记录
- 查看历史归集操作记录
- 监控归集状态和结果
- 分析归集效率和成本



## 📋 设置模块总览

XPay钱包设置系统采用标签页设计，包含5个主要设置模块：
- **安全设置** (SecuritySettings)
- **基本设置** (BasicSettings)
- **策略归集** (StrategyCollectionSettings)
- **费用管理** (FeeSettings)
- **充值设置** (DepositSettings)

每个设置模块都需要通过**密码+谷歌验证码**双重验证才能保存修改。

---

## 🔒 安全设置 (SecuritySettings)

### 功能概述
管理钱包的核心安全参数，包括密码管理和双重验证设置。

### 具体设置项

#### 1. 密码管理 (PasswordChangeSection)
- **当前密码验证**：输入当前钱包密码进行身份验证
- **新密码设置**：设置新的钱包密码
- **密码确认**：再次输入新密码确认
- **密码强度检测**：实时显示密码强度等级
- **安全要求**：
  - 最少8位字符
  - 包含大小写字母、数字、特殊字符
  - 不能与历史密码重复

#### 2. 谷歌验证器设置 (GoogleAuthSettingsSection)
- **验证器状态**：显示当前谷歌验证器启用状态
- **密钥管理**：查看和重新生成验证器密钥
- **二维码生成**：生成配置二维码供手机扫描
- **备份密钥**：提供手动输入的备份密钥
- **测试验证**：测试验证码是否正确配置

### 安全机制
- 所有密码修改需要当前密码验证
- 谷歌验证器变更需要旧验证码确认
- 操作日志记录所有安全设置变更

---

## ⚙️ 基本设置 (BasicSettings)

### 功能概述
配置钱包的基础运行参数，主要涉及归集地址和矿工费设置。

### 具体设置项

#### 1. TRX链设置
- **TRX归集地址** (`trx_collect_address`)
  - 用途：所有TRX资产的最终归集目标地址
  - 格式：TRX地址格式 (T开头的34位字符)
  - 验证：地址格式和有效性验证
  - 必填：是

- **TRX矿工费私钥** (`trx_fee_private_key`)
  - 用途：trc20 usdt 交易中会出现补币场景需要进行补充
  - 安全：私钥加密存储，界面不显示
  - 状态显示：显示是否已设置
  - 修改：输入新私钥覆盖旧设置

- **TRX矿工费地址** (`trx_fee_address`)
  - 用途：TRX矿工费支付地址，与私钥对应 此处是为了校验私钥和地址是否一致

#### 2. ETH链设置
- **ETH归集地址** (`eth_collect_address`)
  - 用途：所有ETH和ERC-20代币的归集目标
  - 格式：ETH地址格式 (0x开头的42位字符)
  - 验证：地址格式和校验和验证
  - 必填：是

- **ETH矿工费私钥** (`eth_fee_private_key`)
  - 用途：支付ETH erc20 usdt链上交易的Gas费
  - 安全：私钥加密存储
  - 状态显示：显示是否已设置
  - 修改：输入新私钥覆盖旧设置

- **ETH矿工费地址** (`eth_fee_address`)
  - 用途：Gas费支付地址，与私钥对应 此处是为了校验私钥和地址是否一致

### 数据验证
- 地址格式验证：确保地址符合对应链的格式要求
- 私钥验证：验证私钥格式和对应地址的正确性

---

## 📊 策略归集 (StrategyCollectionSettings)

### 功能概述
设置自动归集的触发条件和阈值，当地址余额达到设定阈值时自动执行归集。

### 具体设置项

#### 1. 归集阈值设置
- **ETH归集阈值** (`eth_collect_threshold`)
  - 单位：ETH
  - 精度：支持8位小数
  - 范围：≥0
  - 示例：0.1 ETH
  - 说明：当地址ETH余额≥此值时触发归集

- **TRX归集阈值** (`trx_collect_threshold`)
  - 单位：TRX
  - 精度：支持8位小数
  - 范围：≥0
  - 示例：1000 TRX
  - 说明：当地址TRX余额≥此值时触发归集

- **USDT归集阈值** (`usdt_collect_threshold`)
  - 单位：USDT
  - 精度：支持8位小数
  - 范围：≥0
  - 示例：100 USDT
  - 说明：当地址USDT余额≥此值时触发归集

### 归集逻辑
- 系统定期扫描所有地址余额
- 对比余额与设定阈值
- 满足条件时自动创建归集任务
- 考虑矿工费成本，确保归集有经济效益

---

## 💰 费用管理 (FeeSettings)

### 功能概述
精细化管理各类交易的手续费参数，优化成本控制。

### 具体设置项

#### 1. ETH链费用设置

- **ETH费用上限** (`eth_fee_max`)
  - 单位：ETH
  - 用途：单次ETH转账的最大Gas费限制
  - 保护：防止Gas费过高导致的损失

- **ERC-20费用上限** (`erc20_fee_max`)
  - 单位：ETH
  - 用途：ERC-20代币转账的最大Gas费限制
  - 特点：通常比ETH转账费用更高

#### 2. TRX链费用设置

- **TRX费用上限** (`trx_fee_max`)
  - 单位：TRX
  - 用途：单次TRX转账的最大费用限制
  - 特点：TRX网络费用通常较低
- **TRC20 最大能量费** (`trc20_max_energy_fee`)
  - 单位：TRX
  - 用途：TRC20购买能量的最大金额 防止能量很贵超额
- **TRC20 TRX保留金额** (`trc20_min_required_energy`)
  - 单位：TRX
  - 用途：发送trc20 usdt 交易的时候 保留的TRX金额 防止带宽不够交易失败


---

## 💳 充值设置 (DepositSettings)

### 功能概述
设置各币种的最小入账金额，过滤小额充值，减少系统负担。

### 具体设置项

#### 1. 最小入账金额设置
- **TRX最小入账金额** (`trx_min_take_amount`)
  - 单位：TRX
  - 精度：支持8位小数
  - 范围：≥0
  - 示例：0.0001 TRX
  - 说明：小于此金额的TRX充值将被忽略

- **ETH最小入账金额** (`eth_min_take_amount`)
  - 单位：ETH
  - 精度：支持8位小数
  - 范围：≥0
  - 示例：0.01 ETH
  - 说明：小于此金额的ETH充值将被忽略

- **USDT最小入账金额** (`usdt_min_take_amount`)
  - 单位：USDT
  - 精度：支持8位小数
  - 范围：≥0
  - 示例：10 USDT
  - 说明：小于此金额的USDT充值将被忽略

### 充值过滤逻辑
- 系统监控所有入账交易
- 对比交易金额与最小入账设置
- 小于阈值的交易不计入余额
- 减少粉尘攻击和无效小额交易

---

## 🔐 安全验证机制

### 双重验证要求
所有设置修改都需要通过以下验证：

1. **密码验证**
   - 输入当前钱包密码
   - 验证用户身份

2. **谷歌验证码**
   - 输入6位动态验证码
   - 确保操作安全性
