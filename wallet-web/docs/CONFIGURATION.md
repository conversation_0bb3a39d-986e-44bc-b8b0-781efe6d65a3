# Configuration Management

This document explains how to use the runtime configuration system in the XPay Wallet application.

## Overview

The application now supports loading configuration from a `.env` file at runtime, allowing you to change the API URL and other settings without rebuilding the application.

## How it Works

1. **Configuration Service** (`src/services/config.ts`): Loads configuration from `public/.env` file
2. **Configuration Hook** (`src/hooks/useConfig.ts`): React hook for accessing configuration
3. **Axios Integration**: Automatically updates API requests to use the configured URL
4. **Fallback Support**: Uses build-time environment variables if runtime config fails

## Usage

### Basic Usage

```typescript
import { useConfig } from '../hooks/useConfig';

const MyComponent = () => {
  const { config, loading, error, apiUrl } = useConfig();
  
  if (loading) return <div>Loading configuration...</div>;
  if (error) return <div>Configuration error: {error}</div>;
  
  return <div>API URL: {apiUrl}</div>;
};
```

### Get API URL Only

```typescript
import { useApiUrl } from '../hooks/useConfig';

const MyComponent = () => {
  const apiUrl = useApiUrl();
  return <div>API URL: {apiUrl}</div>;
};
```

### Utility Functions

```typescript
import { getApiUrl, isConfigLoaded, waitForConfig } from '../utils/config';

// Get current API URL
const url = getApiUrl();

// Check if config is loaded
if (isConfigLoaded()) {
  // Config is ready
}

// Wait for config to load
await waitForConfig();
```

## Configuration Files

### Development

- **File**: `public/.env`
- **Purpose**: Used during development when running `npm start`
- **Example**:
  ```
  # API URL for the backend service
  API_URL=http://127.0.0.1:9999
  ```

### Production (Docker)

- **Template**: `deployment/.env.template`
- **Generated**: `/usr/share/nginx/html/.env` (in container)
- **Source**: `deployment/config_variables.json`

The production deployment uses a Docker entrypoint script that:
1. Reads configuration from `config_variables.json`
2. Generates `.env` file from template
3. Places it in the nginx html directory for browser access

## Environment Variables

### Supported Variables

- `API_URL`: Base URL for API requests

### Variable Priority

1. **Runtime .env file** (highest priority)
2. **Build-time REACT_APP_API_URL**
3. **Default fallback** (`http://127.0.0.1:8001`)

## Axios Integration

The axios instance automatically:
- Uses the configured API URL as baseURL
- Updates baseURL when configuration changes
- Falls back to default URL if configuration fails

## Development

### Adding New Configuration

1. **Update the AppConfig interface** in `src/services/config.ts`:
   ```typescript
   interface AppConfig {
     API_URL: string;
     NEW_SETTING: string; // Add new setting
   }
   ```

2. **Update the parseEnvFile method** to handle the new variable:
   ```typescript
   if (key === 'NEW_SETTING') {
     config.NEW_SETTING = cleanValue;
   }
   ```

3. **Update the .env files** to include the new variable:
   ```
   NEW_SETTING=some_value
   ```

### Testing Configuration

Use the `ConfigDisplay` component to debug configuration:

```typescript
import ConfigDisplay from '../components/ConfigDisplay';

const DebugPage = () => {
  return (
    <div>
      <h1>Debug Page</h1>
      <ConfigDisplay />
    </div>
  );
};
```

## Troubleshooting

### Configuration Not Loading

1. **Check browser console** for error messages
2. **Verify .env file exists** in `public/` directory
3. **Check file format** - ensure proper `KEY=VALUE` format
4. **Check network tab** - verify `.env` file is accessible

### API Requests Failing

1. **Check API URL** in configuration
2. **Verify server is running** at the configured URL
3. **Check CORS settings** on the server
4. **Review axios interceptor logs** in browser console

### Docker Deployment Issues

1. **Check entrypoint script logs** in container
2. **Verify config_variables.json** exists and is valid
3. **Check generated .env file** in nginx html directory
4. **Verify nginx can serve .env file**

## Best Practices

1. **Always use the configuration service** instead of hardcoded URLs
2. **Handle loading states** in components that depend on configuration
3. **Provide fallbacks** for critical configuration values
4. **Test with different configurations** during development
5. **Monitor configuration loading** in production logs
