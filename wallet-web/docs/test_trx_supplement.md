# TRX补充字段测试文档

## 修改内容总结

### 1. 主表格页面 (FeeManagementPage.tsx)

#### 新增的表格列：

1. **TRX补充状态列**
   - 字段：`trx_supplement_status`
   - 显示条件：仅TRON链的USDT显示
   - 状态映射：
     - pending: 待处理 (橙色)
     - processing: 处理中 (蓝色)
     - success: 成功 (绿色)
     - failed: 失败 (红色)
   - 无需补充时显示："无需补充" (默认色)

2. **TRX补充金额列**
   - 字段：`trx_supplement_amount`
   - 显示条件：仅TRON链的USDT且需要补充时显示
   - 格式：`${amount} TRX`

3. **TRX余额变化列**
   - 字段：组合显示 `trx_balance_before` 和 `trx_balance_after`
   - 显示条件：仅TRON链的USDT且需要补充时显示
   - 成功状态时显示：
     - 余额变化：`before → after TRX`
     - 增量：`+difference` (绿色)

#### 新增的辅助函数：
- `getTrxSupplementStatusConfig`: TRX补充状态映射

### 2. 详情抽屉页面 (FeeDetailDrawer.tsx)

#### 新增组件：

1. **TrxSupplementStatusTag**
   - TRX补充状态标签组件
   - 根据状态显示对应颜色和文本

2. **TrxSupplementInfo**
   - TRX补充信息卡片组件
   - 显示条件：仅TRON链的USDT显示
   - 包含字段：
     - 是否需要补充
     - 补充状态
     - 补充金额
     - 补充前余额
     - 补充后余额
     - 补充交易哈希（可复制，可跳转到区块链浏览器）

## 数据模型字段

根据 `walletApiApiWalletV1TokenFeeSupplement.ts`，已包含以下TRX补充相关字段：

```typescript
interface WalletApiApiWalletV1TokenFeeSupplement {
  // ... 其他字段
  
  /** TRX补充是否需要 0-不需要 1-需要 */
  trx_supplement_needed?: number;
  
  /** TRX补充状态 pending-待处理 processing-处理中 success-成功 failed-失败 */
  trx_supplement_status?: string;
  
  /** TRX补充交易哈希 */
  trx_supplement_hash?: string;
  
  /** TRX补充数量 */
  trx_supplement_amount?: number;
  
  /** 补充前TRX余额 */
  trx_balance_before?: number;
  
  /** 补充后TRX余额 */
  trx_balance_after?: number;
}
```

## 功能特点

1. **条件显示**：只有TRON链的USDT交易才显示TRX补充相关信息
2. **状态管理**：完整的状态流转显示（待处理→处理中→成功/失败）
3. **用户体验**：
   - 交易哈希可复制
   - 可直接跳转到区块链浏览器查看交易
   - 余额变化直观显示
4. **数据完整性**：所有TRX补充相关字段都有对应的UI展示

## 测试建议

1. 测试TRON链USDT记录的TRX补充信息显示
2. 测试非TRON链或非USDT记录不显示TRX补充信息
3. 测试各种补充状态的正确显示
4. 测试交易哈希复制和跳转功能
5. 测试余额变化计算的准确性
