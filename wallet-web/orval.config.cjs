/** @type {import('orval').Config} */
module.exports = {
    xpayApi: { // 配置名称，可以自定义
        input: {
            target: 'http://127.0.0.1:8001/api.json', // **使用远程 URL**
        },
        output: {
            mode: 'tags-split',
            target: './src/services/api', // API 代码输出到 src/services/api 目录
            client: 'axios', // 使用 axios
            schemas: './src/services/api/model', // 数据模型输出到 src/services/api/model 目录
            mock: false,
            override: {
                mutator: {
                    path: './src/services/axiosInstance.ts', // 自定义 axios 实例路径
                    name: 'axiosInstance', // 自定义实例的导出名称
                },
            },
        },
    },
};
