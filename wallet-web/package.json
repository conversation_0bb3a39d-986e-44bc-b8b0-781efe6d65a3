{"name": "xpayweb", "version": "0.1.0", "description": "XPay Wallet Desktop Application", "author": "XPay Team", "private": true, "main": "electron/main.js", "dependencies": {"@ant-design/icons": "^5.6.1", "@reduxjs/toolkit": "^2.6.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "ahooks": "^3.8.4", "antd": "^5.24.3", "axios": "^1.8.3", "chart.js": "^4.4.9", "clipboard": "^2.0.11", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "moment": "^2.30.1", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-qr-code": "^2.0.15", "react-redux": "^9.2.0", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "redux": "^5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "gapi": "orval  --config  ./orval.config.cjs", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "lint:fix": "eslint --fix 'src/**/*.{js,jsx,ts,tsx}'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "pnpm@9.1.0", "devDependencies": {"@types/clipboard": "^2.0.10", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "concurrently": "^8.2.2", "eslint": "^8.0.0", "eslint-config-prettier": "^10.1.1", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "orval": "^7.7.0", "prettier": "^3.5.3", "wait-on": "^7.2.0"}, "build": {"appId": "com.xpay.wallet", "productName": "XPay Wallet", "files": ["build/**/*", "node_modules/**/*"], "directories": {"buildResources": "public"}, "mac": {"category": "public.app-category.finance", "target": ["dmg", "zip"], "icon": "public/logo512.png"}, "win": {"target": ["nsis"], "icon": "public/logo512.png"}, "linux": {"target": ["AppImage", "deb"], "category": "Finance", "icon": "public/logo512.png"}, "publish": {"provider": "github", "releaseType": "release"}}}