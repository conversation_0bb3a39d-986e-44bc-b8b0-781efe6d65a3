import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  login as loginApi,
  register as registerApi,
  getCurrentUser,
  UserInfo,
  LoginRequest,
  RegisterRequest,
} from '../../services/authService';

interface AuthState {
  user: UserInfo | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  countdown: number | null;
  countdownTimer: number | null;
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: !!localStorage.getItem('token'),
  loading: false,
  error: null,
  countdown: null,
  countdownTimer: null,
};

// 登录异步action
export const login = createAsyncThunk('auth/login', async (credentials: LoginRequest, { rejectWithValue }) => {
  try {
    const response = await loginApi(credentials);
    localStorage.setItem('token', response.token);
    return response;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || '登录失败');
  }
});

// 注册异步action
export const register = createAsyncThunk('auth/register', async (userData: RegisterRequest, { rejectWithValue }) => {
  try {
    const response = await registerApi(userData);
    localStorage.setItem('token', response.token);
    return response;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || '注册失败');
  }
});

// 获取当前用户信息异步action
export const fetchCurrentUser = createAsyncThunk('auth/fetchCurrentUser', async (_, { rejectWithValue }) => {
  try {
    return await getCurrentUser();
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || '获取用户信息失败');
  }
});

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      localStorage.removeItem('token');
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      if (state.countdownTimer) {
        window.clearInterval(state.countdownTimer);
        state.countdownTimer = null;
      }
      state.countdown = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    setCountdown: (state, action: PayloadAction<number>) => {
      console.log('setCountdown被调用，参数值:', action.payload);
      state.countdown = action.payload;
      console.log('countdown已设置:', state.countdown);

      // 清除之前的定时器
      if (state.countdownTimer) {
        console.log('清除旧定时器:', state.countdownTimer);
        window.clearInterval(state.countdownTimer);
      }
      // 设置新的定时器
      const timerId = window.setInterval(() => {
        console.log('定时器执行中，当前countdown:', state.countdown);
        if (state.countdown && state.countdown > 0) {
          state.countdown -= 1;
        } else {
          console.log('倒计时结束，执行登出流程');
          window.clearInterval(timerId);
          state.countdownTimer = null;
          // 倒计时结束，执行登出
          authSlice.caseReducers.logout(state);
          // 跳转到登录页面
          window.location.href = '/login';
        }
      }, 1000);
      console.log('新定时器已设置:', timerId);
      state.countdownTimer = timerId;
    },
  },
  extraReducers: (builder) => {
    // 登录
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        login.fulfilled,
        (state, action: PayloadAction<{ token: string; user: UserInfo; countdown?: number }>) => {
          state.loading = false;
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.isAuthenticated = true;
          console.log('登录成功，服务器返回数据:', action.payload);
          if (action.payload.countdown) {
            console.log('服务器返回的countdown值:', action.payload.countdown);
            authSlice.caseReducers.setCountdown(state, {
              type: 'auth/setCountdown',
              payload: action.payload.countdown,
            });
          } else {
            console.log('服务器未返回countdown值');
          }
        },
      )
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 注册
    builder
      .addCase(register.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action: PayloadAction<{ token: string; user: UserInfo }>) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 获取当前用户信息
    builder
      .addCase(fetchCurrentUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action: PayloadAction<UserInfo>) => {
        state.loading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
        state.token = null;
        localStorage.removeItem('token');
      });
  },
});

export const { logout, clearError, setCountdown } = authSlice.actions;
export default authSlice.reducer;
