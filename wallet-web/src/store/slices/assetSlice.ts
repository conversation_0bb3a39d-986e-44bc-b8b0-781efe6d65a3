import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { getAssetDetail, getAssetTransactions, sendAsset, receiveAsset } from '../../services/assetService';
import { AssetDetail } from '../../mock/assetData';
import { Transaction } from '../../services/walletService';

interface AssetState {
  assetDetail: AssetDetail | null;
  transactions: Transaction[];
  receiveAddress: string;
  loading: boolean;
  error: string | null;
  sendSuccess: boolean;
}

const initialState: AssetState = {
  assetDetail: null,
  transactions: [],
  receiveAddress: '',
  loading: false,
  error: null,
  sendSuccess: false,
};

// 获取资产详情
export const fetchAssetDetail = createAsyncThunk('asset/fetchDetail', async (chain: string, { rejectWithValue }) => {
  try {
    return await getAssetDetail(chain);
  } catch (error: any) {
    return rejectWithValue(error.message || '获取资产详情失败');
  }
});

// 获取资产交易历史
export const fetchAssetTransactions = createAsyncThunk(
  'asset/fetchTransactions',
  async (chain: string, { rejectWithValue }) => {
    try {
      return await getAssetTransactions(chain);
    } catch (error: any) {
      return rejectWithValue(error.message || '获取交易历史失败');
    }
  },
);

// 发送资产
export const sendAssetAction = createAsyncThunk(
  'asset/sendAsset',
  async (
    { chain, toAddress, amount, memo }: { chain: string; toAddress: string; amount: string; memo?: string },
    { rejectWithValue },
  ) => {
    try {
      return await sendAsset(chain, toAddress, amount, memo);
    } catch (error: any) {
      return rejectWithValue(error.message || '发送资产失败');
    }
  },
);

// 获取接收地址
export const fetchReceiveAddress = createAsyncThunk(
  'asset/fetchReceiveAddress',
  async (chain: string, { rejectWithValue }) => {
    try {
      const response = await receiveAsset(chain);
      return response.address;
    } catch (error: any) {
      return rejectWithValue(error.message || '获取接收地址失败');
    }
  },
);

const assetSlice = createSlice({
  name: 'asset',
  initialState,
  reducers: {
    resetSendStatus: (state) => {
      state.sendSuccess = false;
      state.error = null;
    },
    clearAssetState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // 获取资产详情
    builder
      .addCase(fetchAssetDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAssetDetail.fulfilled, (state, action: PayloadAction<AssetDetail>) => {
        state.loading = false;
        state.assetDetail = action.payload;
      })
      .addCase(fetchAssetDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 获取资产交易历史
    builder
      .addCase(fetchAssetTransactions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAssetTransactions.fulfilled, (state, action: PayloadAction<Transaction[]>) => {
        state.loading = false;
        state.transactions = action.payload;
      })
      .addCase(fetchAssetTransactions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // 发送资产
    builder
      .addCase(sendAssetAction.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.sendSuccess = false;
      })
      .addCase(sendAssetAction.fulfilled, (state) => {
        state.loading = false;
        state.sendSuccess = true;
      })
      .addCase(sendAssetAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.sendSuccess = false;
      });

    // 获取接收地址
    builder
      .addCase(fetchReceiveAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchReceiveAddress.fulfilled, (state, action: PayloadAction<string>) => {
        state.loading = false;
        state.receiveAddress = action.payload;
      })
      .addCase(fetchReceiveAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { resetSendStatus, clearAssetState } = assetSlice.actions;
export default assetSlice.reducer;
