import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  getWalletBalance,
  getTransactionHistory,
  getWalletAddress,
  transferFunds as transferFundsApi,
  depositFunds as depositFundsApi,
  withdrawFunds as withdrawFundsApi,
  getWalletAddressList,
  WalletBalance,
  Transaction,
  getWalletList as apiGetWalletList,
  generateAddresses as apiGenerateAddresses,
  Wallet,
  createWallet as apiCreateWallet,
  importWallet as apiImportWallet,
  // getWalletStatus as apiGetWalletStatus, // Removed
} from '../../services/walletService';
import { getPublic } from '../../services/api/public/public'; // Added
import type {
  WalletApiApiWalletV1GetPublicWalletInitStatusRes,
  WalletApiApiWalletV1LogoutReq, // Added for logout
} from '../../services/api/model'; // Added
import { getAuth } from '../../services/api/auth/auth'; // Corrected import for logout API
import { logout as logoutActionFromAuth } from '../slices/authSlice'; // Added for logout
import { TransferRequest, DepositRequest, WithdrawRequest } from '../../types/wallet';

// 从mock数据导入
import { WalletAddressItem } from '../../mock/walletData';

interface WalletState {
  balance: WalletBalance | null;
  address: string | null;
  transactions: Transaction[];
  totalTransactions: number;
  walletAddresses: WalletAddressItem[];
  totalWalletAddresses: number;
  statusMessage: string;
  loading: boolean;
  error: string | null;
  wallets: Wallet[];
  generatingAddresses: boolean;
}

const initialState: WalletState = {
  balance: null,
  address: localStorage.getItem('walletAddress') || null,
  transactions: [],
  totalTransactions: 0,
  walletAddresses: [],
  totalWalletAddresses: 0,
  statusMessage: '',
  loading: false,
  error: null,
  wallets: [],
  generatingAddresses: false,
};

// 检查钱包状态
export const checkWalletStatus = createAsyncThunk('wallet/checkStatus', async (_, { rejectWithValue }) => {
  try {
    // 调用新的公共API
    const response = await getPublic().getWalletInitStatus();
    return response; // 直接返回响应体，因为类型已是 WalletApiApiWalletV1GetPublicWalletInitStatusRes
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || error.message || '检查钱包初始化状态失败');
  }
});

// // 检查钱包是否初始化 (REMOVED as per plan)
// export const checkWalletInitialized = createAsyncThunk(
//   'wallet/checkInitialized',
//   async (userId: string, { rejectWithValue, dispatch }) => {
//     try {
//       // 获取完整的钱包状态
//       const status = await apiGetWalletStatus();
//       // 触发更新钱包状态动作
//       dispatch(updateWalletStatus(status));
//       return status.is_initialized === true;
//     } catch (error: any) {
//       return rejectWithValue(error.message || '检查钱包状态失败');
//     }
//   },
// );

// 创建新钱包
export const createWallet = createAsyncThunk(
  'wallet/create',
  async (
    {
      mnemonic,
      password,
      googleCode,
      secret,
    }: { mnemonic: string; password: string; googleCode?: string; secret?: string },
    { rejectWithValue },
  ) => {
    try {
      // 使用真实API，传递所有参数
      const response = await apiCreateWallet(mnemonic, password, googleCode, secret);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || '创建钱包失败');
    }
  },
);

// 导入钱包（通过助记词）
export const importWallet = createAsyncThunk(
  'wallet/import',
  async (
    {
      mnemonic,
      password,
      googleCode,
      secret,
    }: { mnemonic: string; password: string; googleCode: string; secret: string },
    { rejectWithValue },
  ) => {
    try {
      // 使用真实API，并传递所有参数
      const response = await apiImportWallet(mnemonic, password, googleCode, secret);
      return response;
    } catch (error: any) {
      console.log('error', error);
      return rejectWithValue(error.message || '导入钱包失败');
    }
  },
);

// 获取钱包余额
export const fetchWalletBalance = createAsyncThunk('wallet/fetchBalance', async (_, { rejectWithValue }) => {
  try {
    // 使用真实API
    return await getWalletBalance();
  } catch (error: any) {
    return rejectWithValue(error.message || '获取钱包余额失败');
  }
});

// 获取钱包地址
export const fetchWalletAddress = createAsyncThunk('wallet/fetchAddress', async (_, { rejectWithValue }) => {
  try {
    // 使用真实API
    return await getWalletAddress();
  } catch (error: any) {
    return rejectWithValue(error.message || '获取钱包地址失败');
  }
});

// 获取交易历史异步action
export const fetchTransactionHistory = createAsyncThunk(
  'wallet/fetchTransactions',
  async ({ page, limit }: { page: number; limit: number }, { rejectWithValue }) => {
    try {
      return await getTransactionHistory(page, limit);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取交易历史失败');
    }
  },
);

// 转账异步action
export const transferFunds = createAsyncThunk(
  'wallet/transferFunds',
  async (transferData: TransferRequest, { rejectWithValue }) => {
    try {
      return await transferFundsApi(transferData);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '转账失败');
    }
  },
);

// 充值异步action
export const depositFunds = createAsyncThunk(
  'wallet/depositFunds',
  async (depositData: DepositRequest, { rejectWithValue }) => {
    try {
      return await depositFundsApi(depositData);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '充值失败');
    }
  },
);

// 提款异步action
export const withdrawFunds = createAsyncThunk(
  'wallet/withdrawFunds',
  async (withdrawData: WithdrawRequest, { rejectWithValue }) => {
    try {
      return await withdrawFundsApi(withdrawData);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '提款失败');
    }
  },
);

// 获取钱包地址列表
export const fetchWalletAddresses = createAsyncThunk(
  'wallet/fetchAddresses',
  async (
    params: {
      page: number;
      pageSize: number;
      filters?: {
        type?: string;
        status?: string;
        search?: string;
        tags?: string[];
      };
    },
    { rejectWithValue },
  ) => {
    try {
      // 使用真实API
      return await getWalletAddressList(params.page, params.pageSize, params.filters);
    } catch (error: any) {
      return rejectWithValue(error.message || '获取钱包地址列表失败');
    }
  },
);

// 获取钱包列表
export const fetchWalletList = createAsyncThunk('wallet/fetchWalletList', async (_, { rejectWithValue }) => {
  try {
    // 使用真实API
    return await apiGetWalletList();
  } catch (error: any) {
    return rejectWithValue(error.message || '获取钱包列表失败');
  }
});

// 批量生成地址
export const generateWalletAddresses = createAsyncThunk(
  'wallet/generateAddresses',
  async (
    params: {
      walletId: string;
      network: string;
      count: number;
    },
    { rejectWithValue },
  ) => {
    try {
      // 使用真实API
      return await apiGenerateAddresses(params);
    } catch (error: any) {
      return rejectWithValue(error.message || '批量生成地址失败');
    }
  },
);

// 新的异步 Thunk 用于锁定钱包并调用退出登录接口
export const lockAndLogoutWallet = createAsyncThunk(
  'wallet/lockAndLogout',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      // 尝试调用后端登出接口
      const refreshToken = localStorage.getItem('refreshToken'); // Get refreshToken
      if (refreshToken) {
        await getAuth().postWalletLogout( // Use getAuth().postWalletLogout
          { refreshToken } as WalletApiApiWalletV1LogoutReq,
        );
      } else {
        // 如果没有 refreshToken，可以选择直接跳过 API 调用或抛出错误/警告
        // 为了与 MainLayout.tsx 的行为（即使没有 refreshToken 也继续本地登出）保持某种程度的一致性，这里可以选择 console.warn
        console.warn('lockAndLogoutWallet: refreshToken not found, skipping API logout.');
        // 或者，如果此 thunk 明确要求 API 调用成功，则可以：
        // throw new Error('RefreshToken is required for server logout.');
      }
      console.log('Successfully called logout API.');
    } catch (error: any) {
      console.error('Logout API call failed, proceeding with local logout:', error.response?.data?.message || error.message);
      // 即使API调用失败，我们仍然继续本地的登出和钱包重置流程
      // rejectWithValue 不会阻止后续的 dispatch
    }

    // Dispatch authSlice 的 logout action 来清理认证状态
    dispatch(logoutActionFromAuth());

    // !! 重要：此处的 dispatch(walletSlice.actions.resetWallet()) 将在 walletSlice 定义之后修复 !!
    // !! 当前这行会导致引用错误，因为 walletSlice 此时未定义 !!
    // !! 我们将在后续步骤中把此 Thunk 移到 walletSlice 定义之后，或修改此处的调用方式 !!
    // dispatch(walletSlice.actions.resetWallet()); // 暂时注释掉，等待修复引用

    // 确保 localStorage 中的 token 和 refreshToken (如果存在的话) 被清除
    // authSlice 的 logout 应该已经处理了 'token'
    localStorage.removeItem('token'); // 重复确保
    localStorage.removeItem('refreshToken'); // 尝试清除，即使不确定是否存在
    localStorage.removeItem('walletAddress'); // resetWallet 应该也做了这个

    return; // Thunk 成功完成
  },
);


const walletSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {
    clearWalletError: (state) => {
      state.error = null;
    },
    resetWallet: (state) => {
      // 这个 reducer 现在主要由 lockAndLogoutWallet thunk 触发
      state.balance = null;
      state.address = null;
      state.statusMessage = '';
      localStorage.removeItem('walletAddress');
      // 在 extraReducers 中处理 auth/logout 时，也已经覆盖了其他状态的重置
      // 此处保持简洁，主要重置核心钱包数据
    },
    // updateWalletStatus: ( // REMOVED as per plan
    //   state,
    //   action: PayloadAction<{ is_initialized?: boolean; message?: string }>,
    // ) => {
    //   state.statusMessage = action.payload.message || '';
    // },
  },
  extraReducers: (builder) => {
    // 检查钱包状态
    builder
      .addCase(checkWalletStatus.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        checkWalletStatus.fulfilled,
        (state: WalletState, action: PayloadAction<WalletApiApiWalletV1GetPublicWalletInitStatusRes>) => {
          state.loading = false;
          // state.statusMessage = action.payload.message || ''; // Removed as new API doesn't have message
        },
      )
      .addCase(checkWalletStatus.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 创建钱包
      .addCase(createWallet.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createWallet.fulfilled, (state: WalletState, action: PayloadAction<{ address: string }>) => {
        state.loading = false;
        state.address = action.payload.address;
        localStorage.setItem('walletAddress', action.payload.address);
      })
      .addCase(createWallet.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 导入钱包
      .addCase(importWallet.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(importWallet.fulfilled, (state: WalletState, action: PayloadAction<{ address: string }>) => {
        state.loading = false;
        state.address = action.payload.address;
        localStorage.setItem('walletAddress', action.payload.address);
      })
      .addCase(importWallet.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取钱包余额
      .addCase(fetchWalletBalance.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWalletBalance.fulfilled, (state: WalletState, action: PayloadAction<WalletBalance>) => {
        state.loading = false;
        state.balance = action.payload;
      })
      .addCase(fetchWalletBalance.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取钱包地址
      .addCase(fetchWalletAddress.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWalletAddress.fulfilled, (state: WalletState, action: PayloadAction<{ address: string }>) => {
        state.loading = false;
        state.address = action.payload.address;
        localStorage.setItem('walletAddress', action.payload.address);
      })
      .addCase(fetchWalletAddress.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取交易历史
      .addCase(fetchTransactionHistory.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTransactionHistory.fulfilled, (state: WalletState, action: PayloadAction<{transactions: Transaction[], total: number}>) => {
        state.loading = false;
        state.transactions = action.payload.transactions;
        state.totalTransactions = action.payload.total;
      })
      .addCase(fetchTransactionHistory.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 转账
      .addCase(transferFunds.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(transferFunds.fulfilled, (state: WalletState) => {
        state.loading = false;
        // 转账成功后不更新状态，需要重新获取余额和交易历史
      })
      .addCase(transferFunds.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 充值
      .addCase(depositFunds.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(depositFunds.fulfilled, (state: WalletState) => {
        state.loading = false;
        // 充值成功后不更新状态，需要重新获取余额和交易历史
      })
      .addCase(depositFunds.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 提款
      .addCase(withdrawFunds.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(withdrawFunds.fulfilled, (state: WalletState) => {
        state.loading = false;
        // 提款成功后不更新状态，需要重新获取余额和交易历史
      })
      .addCase(withdrawFunds.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取钱包地址列表
      .addCase(fetchWalletAddresses.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWalletAddresses.fulfilled, (state: WalletState, action: PayloadAction<{ addresses: WalletAddressItem[]; total: number }>) => {
        state.loading = false;
        state.walletAddresses = action.payload.addresses;
        state.totalWalletAddresses = action.payload.total;
      })
      .addCase(fetchWalletAddresses.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取钱包列表
      .addCase(fetchWalletList.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWalletList.fulfilled, (state: WalletState, action: PayloadAction<any>) => { // Consider more specific type for payload
        state.loading = false;
        if (Array.isArray(action.payload)) {
          state.wallets = action.payload;
        } else if (action.payload && typeof action.payload === 'object' && 'data' in action.payload && Array.isArray((action.payload as any).data)) {
          state.wallets = (action.payload as any).data;
        } else {
          console.warn('Unexpected wallet list format:', action.payload);
          state.wallets = [];
        }
      })
      .addCase(fetchWalletList.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 批量生成地址
      .addCase(generateWalletAddresses.pending, (state: WalletState) => {
        state.generatingAddresses = true;
        state.error = null;
      })
      .addCase(generateWalletAddresses.fulfilled, (state: WalletState) => {
        state.generatingAddresses = false;
      })
      .addCase(generateWalletAddresses.rejected, (state: WalletState, action: PayloadAction<any>) => {
        state.generatingAddresses = false;
        state.error = action.payload as string;
      })
      // 处理 lockAndLogoutWallet thunk 的状态
      .addCase(lockAndLogoutWallet.pending, (state: WalletState) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(lockAndLogoutWallet.fulfilled, (state: WalletState) => {
        state.loading = false;
        state.statusMessage = '钱包已锁定并成功退出登录。';
        // 核心状态重置由 auth/logout matcher 保证，该 matcher 会因 dispatch(logoutActionFromAuth()) 而触发
      })
      .addCase(lockAndLogoutWallet.rejected, (state: WalletState, action: PayloadAction<unknown>) => { // payload can be unknown for rejected thunks
        state.loading = false;
        state.error = (action.payload as string) || '锁定钱包并退出登录操作失败。';
        // 核心状态重置由 auth/logout matcher 保证
      })
      // 监听 auth/logout action 来重置钱包状态 (移到所有 addCase 之后)
      .addMatcher(
        (action): action is PayloadAction => action.type === 'auth/logout', // Type predicate for action
        (state: WalletState) => {
          // 调用已有的 resetWallet reducer 逻辑
          // 注意：这里直接修改 state，或者返回一个新的 initialState 副本
          // 为保持与现有 resetWallet reducer 一致，我们直接修改 state
          state.balance = null;
          state.address = null;
          state.statusMessage = '';
          localStorage.removeItem('walletAddress');
          // 如果有其他需要重置的状态，也在这里处理
          state.transactions = [];
          state.totalTransactions = 0;
          state.walletAddresses = [];
          state.totalWalletAddresses = 0;
          state.wallets = []; // 也重置钱包列表
          state.loading = false;
          state.error = null;
          // generatingAddresses 应该在 logout 时也重置
          state.generatingAddresses = false;
        }
      );
  },
});

export const { clearWalletError, resetWallet } = walletSlice.actions;

export default walletSlice.reducer;
