import React, { useEffect, useState } from 'react';
import { Button, Checkbox, Typography, Alert, Progress, Tooltip } from 'antd';
import { ArrowLeftOutlined, SafetyOutlined, InfoCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface SecurityWarningsProps {
  warnings: string[];
  checkedWarnings: string[];
  onWarningCheck: (warning: string) => void;
  onNext: () => void;
  onBack: () => void;
  onReset?: () => void;
}

const Step1SecurityWarnings: React.FC<SecurityWarningsProps> = ({
  warnings,
  checkedWarnings,
  onWarningCheck,
  onNext,
  onBack,
  onReset,
}) => {
  const allChecked = warnings.length === checkedWarnings.length;
  const [showTip, setShowTip] = useState(false);

  // Calculate progress percentage
  const progressPercent = Math.round((checkedWarnings.length / warnings.length) * 100);

  useEffect(() => {
    if (onReset) {
      onReset();
    }

    // Show tip after 3 seconds if no warnings checked
    const timer = setTimeout(() => {
      if (checkedWarnings.length === 0) {
        setShowTip(true);
      }
    }, 3000);

    return () => window.clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Hide tip when a warning is checked
  useEffect(() => {
    if (checkedWarnings.length > 0) {
      setShowTip(false);
    }
  }, [checkedWarnings]);

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
        <Button type="text" icon={<ArrowLeftOutlined />} onClick={onBack} style={{ marginRight: 16 }} />
        <Title level={2} style={{ margin: 0 }}>
          安全提示
        </Title>
      </div>

      <Alert
        message="在开始使用钱包前，请务必了解以下安全信息"
        description="这些安全提示对于保护您的数字资产至关重要，请仔细阅读并确认"
        type="warning"
        showIcon
        icon={<SafetyOutlined />}
        style={{ marginBottom: 24, borderRadius: 8 }}
      />

      {/* Progress indicator */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
          <Text strong>安全确认进度</Text>
          <Text type={allChecked ? "success" : "secondary"}>
            {checkedWarnings.length}/{warnings.length}
            {allChecked && <CheckCircleOutlined style={{ marginLeft: 8, color: '#52c41a' }} />}
          </Text>
        </div>
        <Progress
          percent={progressPercent}
          status={allChecked ? "success" : "active"}
          strokeColor={allChecked ? "#52c41a" : { from: '#108ee9', to: '#1890ff' }}
          showInfo={false}
          style={{ marginBottom: 8 }}
        />
      </div>

      <div className="stagger-fade-in" style={{ marginBottom: 36 }}>
        {warnings.map((warning, index) => (
          <div
            key={index}
            style={{
              marginBottom: 16,
              padding: 16,
              borderRadius: 8,
              background: checkedWarnings.includes(warning) ? '#f6ffed' : '#f5f5f5',
              borderLeft: `4px solid ${checkedWarnings.includes(warning) ? '#52c41a' : '#d9d9d9'}`,
              transition: 'all 0.3s',
              cursor: 'pointer',
              boxShadow: checkedWarnings.includes(warning) ? '0 2px 8px rgba(82, 196, 26, 0.15)' : 'none',
              transform: checkedWarnings.includes(warning) ? 'translateX(4px)' : 'none',
            }}
            onClick={() => onWarningCheck(warning)}
          >
            <Checkbox
              checked={checkedWarnings.includes(warning)}
              onChange={(e) => e.stopPropagation()}
              onClick={(e) => e.stopPropagation()}
              style={{ fontSize: 16 }}
            >
              <Text
                style={{
                  fontSize: 16,
                  marginLeft: 8,
                  fontWeight: checkedWarnings.includes(warning) ? 500 : 400,
                  color: checkedWarnings.includes(warning) ? '#262626' : '#595959',
                }}
              >
                {warning}
              </Text>
            </Checkbox>
          </div>
        ))}
      </div>

      {/* Animated tip */}
      {showTip && (
        <div
          className="tooltip-bounce"
          style={{
            marginBottom: 24,
            textAlign: 'center',
            color: '#1890ff',
            padding: '8px',
            borderRadius: '8px',
            background: '#e6f7ff',
          }}
        >
          <InfoCircleOutlined style={{ marginRight: 8 }} />
          <Text style={{ color: '#1890ff' }}>点击每个安全提示进行确认</Text>
        </div>
      )}

      <Tooltip
        title={!allChecked ? "请先确认所有安全提示" : "点击继续创建钱包"}
        placement="top"
        open={!allChecked && checkedWarnings.length > 0}
      >
        <Button
          type="primary"
          size="large"
          onClick={onNext}
          disabled={!allChecked}
          style={{
            width: '100%',
            height: 50,
            fontSize: 16,
            borderRadius: 8,
            opacity: allChecked ? 1 : 0.6,
            boxShadow: allChecked ? '0 4px 12px rgba(24, 144, 255, 0.25)' : 'none',
            transition: 'all 0.3s ease',
          }}
        >
          我已了解风险，继续
        </Button>
      </Tooltip>

      {!allChecked && (
        <Text type="secondary" style={{ display: 'block', textAlign: 'center', marginTop: 16 }}>
          请确认所有安全提示后继续
        </Text>
      )}
    </>
  );
};

export default Step1SecurityWarnings;
