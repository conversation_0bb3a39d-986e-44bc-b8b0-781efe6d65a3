import React, { useEffect, useState } from 'react';
import { Button, Typography, Result, Card, Row, Col } from 'antd';
import { CheckCircleFilled, SafetyOutlined, WalletOutlined, BankOutlined } from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

interface SuccessProps {
  onFinish: () => void;
}

const Step6Success: React.FC<SuccessProps> = ({ onFinish }) => {
  const [showFeatures, setShowFeatures] = useState(false);

  // Show features after initial animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowFeatures(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="wallet-init-step">
      <Result
        icon={<CheckCircleFilled style={{ color: '#52c41a', fontSize: 80 }} className="pulse" />}
        title={<Title level={2} className="fade-up">钱包创建成功！</Title>}
        subTitle={
          <Paragraph className="fade-up" style={{ fontSize: 16, color: '#666', marginBottom: 32 }}>
            您的数字钱包已成功创建，现在可以安全地管理您的资产
          </Paragraph>
        }
        extra={[
          <Button
            type="primary"
            onClick={onFinish}
            size="large"
            style={{
              height: 50,
              fontSize: 16,
              borderRadius: 8,
              width: '100%',
              maxWidth: 320,
              margin: '24px auto 0',
              boxShadow: '0 4px 12px rgba(24, 144, 255, 0.25)',
              animation: 'pulse 2s infinite',
            }}
            key="console"
          >
            进入钱包
          </Button>,
        ]}
        style={{
          padding: '40px 0 20px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
        }}
      />

      {/* Features section */}
      {showFeatures && (
        <Card
          className="fade-in"
          style={{
            borderRadius: 12,
            marginTop: 24,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
            border: 'none',
          }}
        >
          <Title level={4} style={{ textAlign: 'center', marginBottom: 24 }}>
            您的钱包功能
          </Title>

          <Row gutter={[24, 24]} className="stagger-fade-in">
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  width: 60,
                  height: 60,
                  borderRadius: '50%',
                  background: '#f6ffed',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  margin: '0 auto 16px',
                }}>
                  <SafetyOutlined style={{ fontSize: 28, color: '#52c41a' }} />
                </div>
                <Text strong style={{ display: 'block', marginBottom: 8 }}>安全存储</Text>
                <Text type="secondary" style={{ fontSize: 13 }}>多重加密保障资产安全</Text>
              </div>
            </Col>
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  width: 60,
                  height: 60,
                  borderRadius: '50%',
                  background: '#e6f7ff',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  margin: '0 auto 16px',
                }}>
                  <WalletOutlined style={{ fontSize: 28, color: '#1890ff' }} />
                </div>
                <Text strong style={{ display: 'block', marginBottom: 8 }}>资产管理</Text>
                <Text type="secondary" style={{ fontSize: 13 }}>轻松管理多种数字资产</Text>
              </div>
            </Col>
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  width: 60,
                  height: 60,
                  borderRadius: '50%',
                  background: '#f9f0ff',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  margin: '0 auto 16px',
                }}>
                  <BankOutlined style={{ fontSize: 28, color: '#722ed1' }} />
                </div>
                <Text strong style={{ display: 'block', marginBottom: 8 }}>交易转账</Text>
                <Text type="secondary" style={{ fontSize: 13 }}>便捷进行各类资产交易</Text>
              </div>
            </Col>
          </Row>
        </Card>
      )}
    </div>
  );
};

export default Step6Success;
