import React from 'react';
import { Button, Typography, Form, Input, Alert } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

const { Title } = Typography;
const { TextArea } = Input;

interface ImportStep1Props {
  onFinish: (values: { mnemonic: string }) => void;
  onBack: () => void;
}

const ImportStep1Mnemonic: React.FC<ImportStep1Props> = ({ onFinish, onBack }) => {
  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
        <Button type="text" icon={<ArrowLeftOutlined />} onClick={onBack} style={{ marginRight: 16 }} />
        <Title level={2} style={{ margin: 0 }}>
          导入钱包
        </Title>
      </div>

      <Alert
        message="请输入您的钱包助记词，用空格分隔每个单词"
        type="info"
        showIcon
        style={{ marginBottom: 24, borderRadius: 8 }}
      />

      <Form layout="vertical" onFinish={onFinish} requiredMark={false}>
        <Form.Item
          name="mnemonic"
          rules={[
            { required: true, message: '请输入助记词!' },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                const words = value.trim().split(/\s+/);
                if (words.length !== 12 && words.length !== 24) {
                  return Promise.reject('助记词应为12个或24个单词!');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <TextArea
            placeholder="请输入12个或24个助记词，用空格分隔"
            autoSize={{ minRows: 4, maxRows: 6 }}
            style={{ borderRadius: 8, padding: 12, fontSize: 16 }}
          />
        </Form.Item>

        <Alert
          message="安全提示"
          description={
            <ul style={{ paddingLeft: 16, margin: '8px 0' }}>
              <li>助记词是获取您钱包资产的重要凭证，请确保没有他人窥视</li>
              <li>导入后请妥善保管您的助记词，建议离线存储</li>
            </ul>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 24, borderRadius: 8 }}
        />

        <Form.Item style={{ marginTop: 24 }}>
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            style={{ width: '100%', height: 50, fontSize: 16, borderRadius: 8 }}
          >
            下一步
          </Button>
        </Form.Item>
      </Form>
    </>
  );
};

export default ImportStep1Mnemonic;
