import React from 'react';
import { Steps } from 'antd';
import {
  SafetyOutlined,
  KeyOutlined,
  CheckCircleOutlined,
  LockOutlined,
  QrcodeOutlined,
} from '@ant-design/icons';
import ImportStep1SecurityWarnings from './ImportStep1SecurityWarnings';
import ImportStep2Mnemonic from './ImportStep2Mnemonic';
import ImportStep3Password from './ImportStep3Password';
import ImportStep4GoogleAuth from './ImportStep4GoogleAuth';
import ImportStep5Success from './ImportStep5Success';
import { useImportWalletFlow, importWarningsList, ImportStep } from '../../hooks/useImportWalletFlow';

interface ImportWalletManagerProps {
  onFinishImport: (values: { mnemonic: string; password: string; googleCode: string; secret: string }) => void;
  onBack: () => void;
  loading: boolean;
  walletAddress?: string;
}

const ImportWalletManager: React.FC<ImportWalletManagerProps> = ({
  onFinishImport,
  onBack,
  loading,
  walletAddress,
}) => {
  const importWalletFlow = useImportWalletFlow({
    onSuccessNavigation: () => onBack(),
    onBack: onBack,
  });

  // 获取当前步骤索引
  const getStepIndex = (step: ImportStep): number => {
    const stepOrder: ImportStep[] = ['warning', 'mnemonic', 'password', 'google2fa', 'success'];
    return stepOrder.indexOf(step);
  };

  // 获取步骤图标
  const getStepIcon = (step: ImportStep) => {
    switch (step) {
      case 'warning':
        return <SafetyOutlined />;
      case 'mnemonic':
        return <KeyOutlined />;
      case 'password':
        return <LockOutlined />;
      case 'google2fa':
        return <QrcodeOutlined />;
      case 'success':
        return <CheckCircleOutlined />;
      default:
        return null;
    }
  };

  // 渲染进度条
  const renderProgressSteps = () => {
    const currentStep = getStepIndex(importWalletFlow.importStep);

    if (importWalletFlow.importStep === 'success') {
      return null;
    }

    return (
      <Steps
        className="wallet-init-progress fade-in"
        current={currentStep}
        items={[
          { title: '安全提示', icon: getStepIcon('warning') },
          { title: '输入助记词', icon: getStepIcon('mnemonic') },
          { title: '设置密码', icon: getStepIcon('password') },
          { title: '谷歌验证', icon: getStepIcon('google2fa') },
        ]}
        style={{
          marginBottom: 32,
          padding: '0 12px'
        }}
        progressDot
        size="small"
      />
    );
  };

  // 渲染导入钱包流程
  const renderImportWalletFlow = () => {
    switch (importWalletFlow.importStep) {
      case 'warning':
        return (
          <div className="wallet-init-step">
            <ImportStep1SecurityWarnings
              warnings={importWarningsList}
              checkedWarnings={importWalletFlow.checkedWarnings}
              onWarningCheck={importWalletFlow.handleWarningCheck}
              onNext={importWalletFlow.handleShowMnemonicInput}
              onBack={() => {
                onBack();
                importWalletFlow.resetImportFlow();
              }}
              onReset={importWalletFlow.handleResetWarnings}
            />
          </div>
        );
      case 'mnemonic':
        return (
          <div className="wallet-init-step">
            <ImportStep2Mnemonic
              onFinish={importWalletFlow.handleMnemonicFinish}
              onBack={() => importWalletFlow.setImportStep('warning')}
            />
          </div>
        );
      case 'password':
        return (
          <div className="wallet-init-step">
            <ImportStep3Password
              onFinish={importWalletFlow.handlePasswordFinish}
              onBack={() => importWalletFlow.setImportStep('mnemonic')}
            />
          </div>
        );
      case 'google2fa':
        return (
          <div className="wallet-init-step">
            <ImportStep4GoogleAuth
              onFinish={importWalletFlow.handleGoogleAuthFinish}
              onBack={() => importWalletFlow.setImportStep('password')}
              loading={importWalletFlow.loading}
              googleAuthData={importWalletFlow.googleAuthData}
              loadingGoogleAuth={importWalletFlow.loadingGoogleAuth}
              onCopySecretKey={importWalletFlow.handleCopySecretKey}
            />
          </div>
        );
      case 'success':
        return (
          <div className="wallet-init-step confetti-animation">
            {/* Confetti elements */}
            <div className="confetti"></div>
            <div className="confetti"></div>
            <div className="confetti"></div>
            <div className="confetti"></div>
            <div className="confetti"></div>
            <ImportStep5Success
              onFinish={importWalletFlow.onSuccessNavigation}
              walletAddress={importWalletFlow.walletAddress || walletAddress}
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div>
      {renderProgressSteps()}
      {renderImportWalletFlow()}
    </div>
  );
};

export default ImportWalletManager;
