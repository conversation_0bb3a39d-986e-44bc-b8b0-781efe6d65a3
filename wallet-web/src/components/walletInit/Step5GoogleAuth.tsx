import React from 'react';
import { Button, Typography, Form, Input, Alert, Spin } from 'antd';
import { ArrowLeftOutlined, QrcodeOutlined, CopyOutlined } from '@ant-design/icons';
import QRCode from 'react-qr-code';

const { Title, Text, Paragraph } = Typography;

interface GoogleAuthProps {
  googleAuthData: { secret: string; qrCode: string };
  onFinish: (values: { googleCode: string }) => void;
  onBack: () => void;
  loading: boolean;
  onCopySecretKey: () => void;
}

const Step5GoogleAuth: React.FC<GoogleAuthProps> = ({ googleAuthData, onFinish, onBack, loading, onCopySecretKey }) => {
  const [form] = Form.useForm();

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
        <Button type="text" icon={<ArrowLeftOutlined />} onClick={onBack} style={{ marginRight: 16 }} />
        <Title level={2} style={{ margin: 0 }}>
          设置谷歌验证器
        </Title>
      </div>

      <Alert
        message="安全提示：启用谷歌验证器可以大幅提高您的钱包安全性"
        type="info"
        showIcon
        style={{ marginBottom: 24, borderRadius: 8 }}
      />

      <div
        style={{
          background: 'white',
          padding: '24px',
          borderRadius: 12,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
          border: '1px solid #f0f0f0',
          marginBottom: 24,
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: 24,
            borderBottom: '1px solid #f5f5f5',
            paddingBottom: 16,
            width: '100%',
            justifyContent: 'center',
          }}
        >
          <div
            style={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              background: '#f0f7ff',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
            }}
          >
            <QrcodeOutlined style={{ fontSize: 20, color: '#1890ff' }} />
          </div>
          <Title level={4} style={{ margin: 0 }}>
            扫描二维码
          </Title>
        </div>

        <div
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <div
            style={{
              padding: '16px',
              borderRadius: 12,
              background: '#f8f8f8',
              marginBottom: 24,
              width: '90%',
              maxWidth: '220px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              boxShadow: 'inset 0 1px 4px rgba(0,0,0,0.05)',
            }}
          >
            {googleAuthData.qrCode ? (
              <QRCode
                value={googleAuthData.qrCode}
                size={180}
                style={{
                  maxWidth: '100%',
                  width: '180px',
                  height: '180px',
                  padding: 8,
                  background: 'white',
                  borderRadius: 8,
                }}
                level="H"
                bgColor="#FFFFFF"
                fgColor="#000000"
              />
            ) : (
              <Spin size="large" style={{ display: 'block', margin: '40px auto' }} />
            )}
          </div>

          <Paragraph
            style={{
              fontSize: 14,
              color: '#333',
              textAlign: 'center',
              margin: 0,
              fontWeight: 'medium',
            }}
          >
            使用Google Authenticator扫描二维码
          </Paragraph>
          <Paragraph
            style={{
              fontSize: 12,
              color: '#666',
              textAlign: 'center',
              margin: '8px 0 24px 0',
            }}
          >
            您可以从App Store或Google Play下载Google Authenticator应用
          </Paragraph>
        </div>

        <div style={{ marginBottom: 20, width: '100%' }}>
          <Text strong style={{ display: 'block', marginBottom: 8, fontSize: 14 }}>
            密钥
          </Text>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              background: '#f8f8f8',
              padding: '4px',
              borderRadius: 8,
              boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.05)',
            }}
          >
            <Input
              value={googleAuthData.secret}
              readOnly
              style={{
                background: 'transparent',
                border: 'none',
                boxShadow: 'none',
                fontFamily: 'monospace',
                fontSize: 15,
                fontWeight: 500,
                padding: '8px 12px',
                color: '#333',
              }}
            />
            <Button
              type="primary"
              icon={<CopyOutlined />}
              onClick={onCopySecretKey}
              style={{
                flexShrink: 0,
                borderRadius: 6,
                marginRight: 4,
                height: 36,
              }}
            />
          </div>
        </div>
      </div>

      <Form form={form} name="googleAuthForm" onFinish={onFinish} layout="vertical" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center', marginBottom: 20 }}>
          <Title level={4} style={{ margin: 0, marginBottom: 4 }}>
            输入谷歌验证码
          </Title>
          <Text type="secondary">打开验证器应用，输入显示的6位验证码</Text>
        </div>

        <Form.Item
          name="googleCode"
          rules={[
            { required: true, message: '请输入谷歌验证码!' },
            { pattern: /^\d{6}$/, message: '验证码为6位数字!' },
          ]}
        >
          <Input
            placeholder="6位数字验证码"
            style={{
              height: 50,
              borderRadius: 8,
              fontSize: 18,
              textAlign: 'center',
              letterSpacing: 4,
              fontWeight: 500,
            }}
            maxLength={6}
          />
        </Form.Item>

        <Form.Item style={{ marginTop: 24 }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            style={{
              height: 50,
              fontSize: 16,
              borderRadius: 8,
              width: '100%',
            }}
          >
            完成设置
          </Button>
        </Form.Item>
      </Form>
    </>
  );
};

export default Step5GoogleAuth;
