import React, { useState, useEffect } from 'react';
import { Button, Typography, Row, Col, Alert, Card, Tag, Space, Progress, Tooltip } from 'antd';
import { ArrowLeftOutlined, CloseCircleOutlined, CheckCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface MnemonicVerifyProps {
  shuffledWords: string[];
  selectedWords: string[];
  onSelectWord: (word: string) => void;
  onRemoveWord: (index: number) => void;
  onBack: () => void;
  onConfirm: () => void;
  expectedWordCount: number;
  errorMessage?: string;
}

const Step3MnemonicVerify: React.FC<MnemonicVerifyProps> = ({
  shuffledWords,
  selectedWords,
  onSelectWord,
  onRemoveWord,
  onBack,
  onConfirm,
  expectedWordCount,
  errorMessage,
}) => {
  const isComplete = selectedWords.length === expectedWordCount;
  const [showHint, setShowHint] = useState(false);

  // 筛选出未被选中的助记词
  const availableWords = shuffledWords.filter((word) => !selectedWords.includes(word));

  // Calculate progress percentage
  const progressPercent = Math.round((selectedWords.length / expectedWordCount) * 100);

  // Show hint after 5 seconds if no words selected
  useEffect(() => {
    const timer = setTimeout(() => {
      if (selectedWords.length === 0) {
        setShowHint(true);
      }
    }, 5000);

    return () => window.clearTimeout(timer);
  }, [selectedWords.length]);

  // Hide hint when words are selected
  useEffect(() => {
    if (selectedWords.length > 0) {
      setShowHint(false);
    }
  }, [selectedWords]);

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
        <Button type="text" icon={<ArrowLeftOutlined />} onClick={onBack} style={{ marginRight: 16 }} />
        <Title level={2} style={{ margin: 0 }}>
          验证助记词
        </Title>
      </div>

      <Alert
        message="请按照正确的顺序选择您刚才备份的助记词"
        description="点击下方单词，按照您记下的顺序重新排列助记词"
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: 24, borderRadius: 8 }}
      />

      {/* Progress indicator */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
          <Text strong>验证进度</Text>
          <Text type={isComplete ? "success" : "secondary"}>
            {selectedWords.length}/{expectedWordCount}
            {isComplete && <CheckCircleOutlined style={{ marginLeft: 8, color: '#52c41a' }} />}
          </Text>
        </div>
        <Progress
          percent={progressPercent}
          status={isComplete ? "success" : "active"}
          strokeColor={isComplete ? "#52c41a" : { from: '#108ee9', to: '#1890ff' }}
          showInfo={false}
          style={{ marginBottom: 16 }}
        />
      </div>

      <div style={{ marginBottom: 24 }}>
        <Card
          className={`verification-words-container ${selectedWords.length > 0 ? 'active' : ''} ${isComplete ? 'complete' : ''}`}
          style={{
            borderRadius: 12,
            background: '#f8f9fa',
            marginBottom: 16,
            minHeight: 120,
            border: errorMessage ? '1px solid #ff4d4f' : '1px dashed #d9d9d9',
            transition: 'all 0.3s ease',
          }}
          bodyStyle={{ padding: '24px 16px' }}
        >
          <div style={{ minHeight: 72, display: 'flex', flexWrap: 'wrap', gap: 8, alignItems: 'flex-start' }}>
            {selectedWords.length > 0 ? (
              selectedWords.map((word, index) => (
                <Tag
                  key={index}
                  closable
                  color="#1890ff"
                  onClose={() => onRemoveWord(index)}
                  style={{
                    padding: '6px 10px',
                    fontSize: 14,
                    borderRadius: 4,
                    margin: '4px',
                    userSelect: 'none',
                    boxShadow: '0 2px 5px rgba(24, 144, 255, 0.2)',
                    animation: 'fadeIn 0.3s ease-out',
                    transition: 'all 0.2s ease',
                  }}
                  className="mnemonic-word"
                >
                  <Space>
                    <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>{index + 1}.</Text>
                    <Text style={{ color: 'white', fontWeight: 500 }}>{word}</Text>
                  </Space>
                </Tag>
              ))
            ) : (
              <div className={showHint ? "tooltip-bounce" : ""} style={{ textAlign: 'center', width: '100%', marginTop: 24 }}>
                <InfoCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                <Text type="secondary">请从下方选择助记词，按照正确的顺序排列</Text>
              </div>
            )}
          </div>
        </Card>

        {errorMessage && (
          <Alert
            message={errorMessage}
            type="error"
            showIcon
            icon={<CloseCircleOutlined />}
            style={{ marginBottom: 16, borderRadius: 8, animation: 'fadeIn 0.3s ease-out' }}
          />
        )}

        <div style={{ marginBottom: 32 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={5} style={{ margin: 0 }}>
              请选择助记词
            </Title>
            <Text type="secondary" style={{ fontSize: 13 }}>
              剩余单词: {availableWords.length}
            </Text>
          </div>
          <Row gutter={[12, 12]} className="stagger-fade-in">
            {availableWords.map((word, index) => (
              <Col span={6} key={index}>
                <Button
                  className="mnemonic-word"
                  style={{
                    width: '100%',
                    borderRadius: 6,
                    transition: 'all 0.2s ease',
                  }}
                  onClick={() => onSelectWord(word)}
                  type={availableWords.length <= 6 ? "default" : "text"}
                >
                  {word}
                </Button>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      <Tooltip
        title={!isComplete ? `请选择所有${expectedWordCount}个助记词` : "点击确认验证助记词"}
        placement="top"
        open={!isComplete && selectedWords.length > 0}
      >
        <Button
          type="primary"
          size="large"
          onClick={onConfirm}
          disabled={!isComplete}
          style={{
            width: '100%',
            height: 50,
            fontSize: 16,
            borderRadius: 8,
            opacity: isComplete ? 1 : 0.6,
            boxShadow: isComplete ? '0 4px 12px rgba(24, 144, 255, 0.25)' : 'none',
            transition: 'all 0.3s ease',
          }}
          icon={isComplete ? <CheckCircleOutlined /> : null}
        >
          {isComplete ? "确认验证" : `继续选择 (${selectedWords.length}/${expectedWordCount})`}
        </Button>
      </Tooltip>

      {!isComplete && selectedWords.length === 0 && (
        <Text type="secondary" style={{ display: 'block', textAlign: 'center', marginTop: 16 }}>
          请按照正确顺序选择所有{expectedWordCount}个助记词
        </Text>
      )}
    </>
  );
};

export default Step3MnemonicVerify;
