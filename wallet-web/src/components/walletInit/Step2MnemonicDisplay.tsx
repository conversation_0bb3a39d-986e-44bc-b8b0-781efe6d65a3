import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Typography, <PERSON>, Col, Al<PERSON>, Card, Tooltip, Progress } from 'antd';
import {
  ArrowLeftOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  CopyOutlined,
  SafetyCertificateOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { copyToClipboard } from '../../utils/clipboardUtils';

const { Title, Text } = Typography;

interface MnemonicDisplayProps {
  mnemonic: string;
  onBack: () => void;
  onNext: () => void;
}

const Step2MnemonicDisplay: React.FC<MnemonicDisplayProps> = ({ mnemonic, onBack, onNext }) => {
  const [hideWords, setHideWords] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showCopyWarning, setShowCopyWarning] = useState(false);
  const [readyToContinue, setReadyToContinue] = useState(false);
  const mnemonicWords = mnemonic.split(' ');

  // After 10 seconds, assume user has had time to backup
  useEffect(() => {
    if (!hideWords) {
      const timer = setTimeout(() => {
        setReadyToContinue(true);
      }, 10000);

      return () => window.clearTimeout(timer);
    }
  }, [hideWords]);

  const handleCopyMnemonic = async () => {
    try {
      await copyToClipboard(mnemonic);
      setCopySuccess(true);
      setShowCopyWarning(true);

      // Reset copy success indicator after 3 seconds
      setTimeout(() => {
        setCopySuccess(false);
      }, 3000);
    } catch (error) {
      // Error handling is already done in copyToClipboard utility
      console.error('Failed to copy mnemonic:', error);
    }
  };

  const toggleVisibility = () => {
    setHideWords(!hideWords);
  };

  return (
    <div className="wallet-init-step">
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
        <Button type="text" icon={<ArrowLeftOutlined />} onClick={onBack} style={{ marginRight: 16 }} />
        <Title level={2} style={{ margin: 0 }}>
          备份助记词
        </Title>
      </div>

      <Alert
        message="请将这些单词按顺序抄写到安全的地方，切勿截图或保存到电子设备中"
        description="助记词是恢复钱包的唯一凭证，请妥善保管并确保没有他人能够获取"
        type="warning"
        showIcon
        icon={<SafetyCertificateOutlined />}
        style={{ marginBottom: 24, borderRadius: 8 }}
      />

      {/* Backup progress indicator */}
      {!hideWords && (
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
            <Text strong>备份进度</Text>
            <Text type={readyToContinue ? "success" : "secondary"}>
              {readyToContinue ? "已准备好继续" : "请记下您的助记词"}
              {readyToContinue && <CheckCircleOutlined style={{ marginLeft: 8, color: '#52c41a' }} />}
            </Text>
          </div>
          <Progress
            percent={readyToContinue ? 100 : 50}
            status={readyToContinue ? "success" : "active"}
            strokeColor={readyToContinue ? "#52c41a" : { from: '#108ee9', to: '#1890ff' }}
            showInfo={false}
            style={{ marginBottom: 16 }}
          />
        </div>
      )}

      <div style={{ position: 'relative', marginBottom: 32 }}>
        <Card
          className={hideWords ? "" : "fade-in"}
          style={{
            borderRadius: 12,
            background: '#f8f9fa',
            border: '1px dashed #d9d9d9',
            marginBottom: 16,
            boxShadow: hideWords ? 'none' : '0 4px 12px rgba(0, 0, 0, 0.05)',
            transition: 'all 0.3s ease',
          }}
          bodyStyle={{ padding: '24px 16px' }}
        >
          <div
            style={{
              filter: hideWords ? 'blur(5px)' : 'none',
              transition: 'filter 0.3s ease',
              userSelect: hideWords ? 'none' : 'auto',
            }}
          >
            <Row gutter={[16, 16]} className={hideWords ? "" : "stagger-fade-in"}>
              {mnemonicWords.map((word, index) => (
                <Col span={8} key={index}>
                  <Card
                    size="small"
                    className="mnemonic-word"
                    style={{
                      textAlign: 'center',
                      borderRadius: 8,
                      background: '#fff',
                      border: '1px solid #f0f0f0',
                      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.03)',
                      transition: 'all 0.2s ease',
                    }}
                  >
                    <Text style={{ fontSize: 14, color: '#444', fontWeight: 500 }}>
                      <Text type="secondary" style={{ marginRight: 8, fontSize: 12, fontWeight: 'bold' }}>
                        {index + 1}.
                      </Text>
                      {word}
                    </Text>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>

          {hideWords && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                backdropFilter: 'blur(0px)',
              }}
            >
              <Button
                type="primary"
                icon={<EyeOutlined />}
                onClick={toggleVisibility}
                size="large"
                style={{
                  boxShadow: '0 4px 12px rgba(24, 144, 255, 0.25)',
                  height: '50px',
                  borderRadius: '8px',
                  padding: '0 24px',
                }}
                className="tooltip-bounce"
              >
                点击查看助记词
              </Button>
            </div>
          )}
        </Card>

        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
          <Tooltip title={hideWords ? "显示助记词" : "隐藏助记词"}>
            <Button
              type={hideWords ? "primary" : "default"}
              icon={hideWords ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={toggleVisibility}
              ghost={hideWords}
            >
              {hideWords ? '显示' : '隐藏'}助记词
            </Button>
          </Tooltip>

          <Tooltip title={hideWords ? "请先显示助记词" : "复制到剪贴板"}>
            <Button
              type={copySuccess ? "default" : "primary"}
              icon={copySuccess ? <CheckCircleOutlined /> : <CopyOutlined />}
              onClick={handleCopyMnemonic}
              disabled={hideWords}
              ghost={!copySuccess}
              style={{
                color: copySuccess ? '#52c41a' : undefined,
                borderColor: copySuccess ? '#52c41a' : undefined
              }}
            >
              {copySuccess ? '已复制' : '复制助记词'}
            </Button>
          </Tooltip>
        </div>

        {showCopyWarning && (
          <Alert
            message="复制提示"
            description="您已复制助记词到剪贴板，请注意不要将其粘贴到不安全的地方，建议使用纸笔抄写"
            type="warning"
            showIcon
            icon={<WarningOutlined />}
            style={{ marginBottom: 24, borderRadius: 8, animation: 'fadeIn 0.3s ease-out' }}
            closable
            onClose={() => setShowCopyWarning(false)}
          />
        )}

        <Alert
          message="安全提示"
          description={
            <ul style={{ paddingLeft: 16, margin: '8px 0' }} className="stagger-fade-in">
              <li>任何人获取了您的助记词，就可以直接控制您的钱包资产</li>
              <li>不要截图或通过网络传输您的助记词</li>
              <li>建议使用纸笔抄写并保管在安全的地方</li>
              <li>请确保周围没有其他人或摄像头</li>
            </ul>
          }
          type="info"
          showIcon
          style={{ marginBottom: 24, borderRadius: 8 }}
        />
      </div>

      <Tooltip title={hideWords ? "请先查看并记下您的助记词" : ""}>
        <Button
          type="primary"
          size="large"
          onClick={onNext}
          disabled={hideWords}
          style={{
            width: '100%',
            height: 50,
            fontSize: 16,
            borderRadius: 8,
            opacity: hideWords ? 0.6 : 1,
            boxShadow: hideWords ? 'none' : '0 4px 12px rgba(24, 144, 255, 0.25)',
            transition: 'all 0.3s ease',
          }}
          icon={readyToContinue ? <CheckCircleOutlined /> : null}
        >
          {readyToContinue ? "我已备份，继续" : "请记下助记词后继续"}
        </Button>
      </Tooltip>
    </div>
  );
};

export default Step2MnemonicDisplay;
