import React, { useState } from 'react';
import { Button, Typography, Form, Input, Alert, Progress } from 'antd';
import { ArrowLeftOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';

const { Title, Text } = Typography;

interface ImportStep3Props {
  onFinish: (values: { password: string }) => void;
  onBack: () => void;
}

const ImportStep3Password: React.FC<ImportStep3Props> = ({ onFinish, onBack }) => {
  const [form] = Form.useForm();
  const [passwordStrength, setPasswordStrength] = useState<'weak' | 'medium' | 'strong' | 'very-strong' | ''>('');

  const validateConfirmPassword = (_: any, value: string) => {
    const password = form.getFieldValue('password');
    if (!value || password === value) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('两次输入的密码不一致!'));
  };

  const checkPasswordStrength = (password: string) => {
    if (!password) {
      setPasswordStrength('');
      return;
    }

    // 简单的密码强度检测
    let strength = 0;
    if (password.length >= 8) strength += 1;
    if (password.length >= 12) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    if (strength <= 2) setPasswordStrength('weak');
    else if (strength <= 4) setPasswordStrength('medium');
    else if (strength <= 5) setPasswordStrength('strong');
    else setPasswordStrength('very-strong');
  };

  const getPasswordStrengthText = () => {
    switch (passwordStrength) {
      case 'weak':
        return '弱';
      case 'medium':
        return '中';
      case 'strong':
        return '强';
      case 'very-strong':
        return '非常强';
      default:
        return '';
    }
  };

  const getPasswordStrengthPercent = () => {
    switch (passwordStrength) {
      case 'weak':
        return 25;
      case 'medium':
        return 50;
      case 'strong':
        return 75;
      case 'very-strong':
        return 100;
      default:
        return 0;
    }
  };

  const getPasswordStrengthColor = () => {
    switch (passwordStrength) {
      case 'weak':
        return '#ff4d4f';
      case 'medium':
        return '#faad14';
      case 'strong':
        return '#52c41a';
      case 'very-strong':
        return '#13c2c2';
      default:
        return '';
    }
  };

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
        <Button type="text" icon={<ArrowLeftOutlined />} onClick={onBack} style={{ marginRight: 16 }} />
        <Title level={2} style={{ margin: 0 }}>
          设置钱包密码
        </Title>
      </div>

      <Alert
        message="请设置一个安全的钱包密码，该密码将用于保护您的钱包和资产"
        type="info"
        showIcon
        style={{ marginBottom: 24, borderRadius: 8 }}
      />

      <Form
        form={form}
        layout="vertical"
        onFinish={(values) => onFinish(values)}
        autoComplete="off"
        requiredMark={false}
        className="fade-up"
      >
        <Form.Item
          name="password"
          label="钱包密码"
          rules={[
            { required: true, message: '请输入钱包密码!' },
            { min: 8, message: '密码长度至少为8个字符!' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入至少8位的密码"
            size="large"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            style={{ borderRadius: 8, height: 50 }}
            onChange={(e) => checkPasswordStrength(e.target.value)}
          />
        </Form.Item>

        {passwordStrength && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
              <Text type="secondary">密码强度</Text>
              <Text style={{ color: getPasswordStrengthColor() }}>{getPasswordStrengthText()}</Text>
            </div>
            <Progress
              percent={getPasswordStrengthPercent()}
              showInfo={false}
              strokeColor={getPasswordStrengthColor()}
              size="small"
              style={{ marginBottom: 0 }}
            />
          </div>
        )}

        <Form.Item
          name="confirmPassword"
          label="确认密码"
          dependencies={['password']}
          rules={[{ required: true, message: '请确认您的密码!' }, { validator: validateConfirmPassword }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请再次输入密码"
            size="large"
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            style={{ borderRadius: 8, height: 50 }}
          />
        </Form.Item>

        <div style={{ marginTop: 24 }}>
          <Alert
            message="密码安全提示"
            description={
              <ul style={{ paddingLeft: 16, margin: '8px 0' }}>
                <li>密码不会上传至服务器，请务必牢记</li>
                <li>如果忘记密码，您需要使用助记词重新导入钱包</li>
                <li>建议使用不同于其他账户的独特密码</li>
              </ul>
            }
            type="warning"
            showIcon
            style={{ marginBottom: 24, borderRadius: 8 }}
          />
        </div>

        <Form.Item style={{ marginTop: 24 }}>
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            style={{
              width: '100%',
              height: 50,
              fontSize: 16,
              borderRadius: 8,
              boxShadow: '0 4px 12px rgba(24, 144, 255, 0.25)',
              transition: 'all 0.3s ease',
            }}
          >
            下一步
          </Button>
        </Form.Item>
      </Form>
    </>
  );
};

export default ImportStep3Password;
