import React from 'react';
import { Button, Typography, Checkbox, Alert, Tooltip } from 'antd';
import { ArrowLeftOutlined, SafetyOutlined } from '@ant-design/icons';

const { Title, Text } = Typography; // Removed unused Paragraph

interface ImportStep1SecurityWarningsProps {
  warnings: string[];
  checkedWarnings: string[];
  onWarningCheck: (warning: string) => void;
  onNext: () => void;
  onBack: () => void;
  onReset: () => void;
}

const ImportStep1SecurityWarnings: React.FC<ImportStep1SecurityWarningsProps> = ({
  warnings,
  checkedWarnings,
  onWarningCheck,
  onNext,
  onBack,
  onReset,
}) => {
  const allChecked = warnings.length > 0 && checkedWarnings.length === warnings.length;

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
        <Button type="text" icon={<ArrowLeftOutlined />} onClick={onBack} style={{ marginRight: 16 }} />
        <Title level={2} style={{ margin: 0 }}>
          安全提示
        </Title>
      </div>

      <Alert
        message="导入钱包前，请确认以下安全事项"
        description="为了保障您的资产安全，请仔细阅读并确认以下内容"
        type="info"
        showIcon
        icon={<SafetyOutlined />}
        style={{ marginBottom: 24, borderRadius: 8 }}
      />

      <div className="stagger-fade-in" style={{ marginBottom: 32 }}>
        {warnings.map((warning, index) => (
          <div
            key={index}
            style={{
              marginBottom: 16,
              padding: 16,
              borderRadius: 8,
              background: checkedWarnings.includes(warning) ? '#f6ffed' : '#f9f9f9',
              border: `1px solid ${checkedWarnings.includes(warning) ? '#b7eb8f' : '#f0f0f0'}`,
              transition: 'all 0.3s ease',
            }}
          >
            <Checkbox
              checked={checkedWarnings.includes(warning)}
              onChange={() => onWarningCheck(warning)}
              style={{ fontSize: 16 }}
            >
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: checkedWarnings.includes(warning) ? 500 : 400,
                  color: checkedWarnings.includes(warning) ? '#52c41a' : 'inherit',
                  transition: 'all 0.3s ease',
                }}
              >
                {warning}
              </Text>
            </Checkbox>
          </div>
        ))}
      </div>

      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Button onClick={onReset} style={{ borderRadius: 8 }}>
          重置
        </Button>
      </div>

      <Tooltip
        title={!allChecked ? "请先确认所有安全提示" : "点击继续导入钱包"}
        placement="top"
        open={!allChecked && checkedWarnings.length > 0}
      >
        <Button
          type="primary"
          size="large"
          onClick={onNext}
          disabled={!allChecked}
          style={{
            width: '100%',
            height: 50,
            fontSize: 16,
            borderRadius: 8,
            opacity: allChecked ? 1 : 0.6,
            boxShadow: allChecked ? '0 4px 12px rgba(24, 144, 255, 0.25)' : 'none',
            transition: 'all 0.3s ease',
          }}
        >
          我已了解风险，继续
        </Button>
      </Tooltip>

      {!allChecked && (
        <Text type="secondary" style={{ display: 'block', textAlign: 'center', marginTop: 16 }}>
          请确认所有安全提示后继续
        </Text>
      )}
    </>
  );
};

export default ImportStep1SecurityWarnings;
