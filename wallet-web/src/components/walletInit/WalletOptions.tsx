import React, { useState, useEffect } from 'react';
import { Button, Typography, Tooltip } from 'antd';
import { PlusCircleOutlined, ImportOutlined, SafetyOutlined, WalletOutlined, BankOutlined } from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

interface WalletOptionsProps {
  onCreateWallet: () => void;
  onImportWallet: () => void;
  logo: string;
}

const WalletOptions: React.FC<WalletOptionsProps> = ({ onCreateWallet, onImportWallet, logo }) => {
  const [showFeatures, setShowFeatures] = useState(false);

  // Animate features after initial render
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowFeatures(true);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="wallet-init-step" style={{ textAlign: 'center', padding: '20px 10px' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: 36,
          position: 'relative',
        }}
      >
        <div
          className="pulse"
          style={{
            position: 'absolute',
            width: '180px',
            height: '180px',
            background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
            borderRadius: '50%',
            opacity: 0.7,
          }}
        />
        <img
          src={logo}
          alt="Logo"
          className="float"
          style={{
            height: 160,
            position: 'relative',
            zIndex: 1,
            filter: 'drop-shadow(0 4px 12px rgba(24, 144, 255, 0.2))',
          }}
        />
      </div>

      <Title level={1} className="fade-up" style={{ marginBottom: 16, color: '#262626', fontWeight: 600 }}>
        欢迎使用数字钱包
      </Title>
      <Paragraph
        className="fade-up"
        style={{
          fontSize: 18,
          color: '#595959',
          marginBottom: 24,
          maxWidth: '480px',
          margin: '0 auto 24px'
        }}
      >
        安全、便捷地管理您的数字资产
      </Paragraph>

      {/* Features section */}
      <div
        className={`stagger-fade-in ${showFeatures ? 'visible' : ''}`}
        style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '16px',
          marginBottom: '40px',
          maxWidth: '600px',
          margin: '0 auto 40px'
        }}
      >
        <div style={{ textAlign: 'center', flex: 1 }}>
          <SafetyOutlined style={{ fontSize: '28px', color: '#52c41a', marginBottom: '8px' }} />
          <Text strong style={{ display: 'block', marginBottom: '4px' }}>安全可靠</Text>
          <Text type="secondary" style={{ fontSize: '13px' }}>多重加密保障资产安全</Text>
        </div>
        <div style={{ textAlign: 'center', flex: 1 }}>
          <WalletOutlined style={{ fontSize: '28px', color: '#1890ff', marginBottom: '8px' }} />
          <Text strong style={{ display: 'block', marginBottom: '4px' }}>便捷管理</Text>
          <Text type="secondary" style={{ fontSize: '13px' }}>轻松管理多种数字资产</Text>
        </div>
        <div style={{ textAlign: 'center', flex: 1 }}>
          <BankOutlined style={{ fontSize: '28px', color: '#722ed1', marginBottom: '8px' }} />
          <Text strong style={{ display: 'block', marginBottom: '4px' }}>完全掌控</Text>
          <Text type="secondary" style={{ fontSize: '13px' }}>您是资产唯一的控制者</Text>
        </div>
      </div>

      <div
        className="fade-in"
        style={{
          border: 'none',
          borderRadius: 24,
          background: 'white',
          boxShadow: '0 10px 30px rgba(0, 82, 204, 0.08)',
          overflow: 'hidden',
          maxWidth: 520,
          margin: '0 auto',
          transition: 'all 0.3s ease',
        }}
      >
        {/* 创建钱包选项 */}
        <div
          className="interactive-card"
          style={{
            padding: '40px 36px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            background: 'white',
            borderBottom: '1px solid #f0f0f0',
            position: 'relative',
            overflow: 'hidden',
            borderRadius: '24px 24px 0 0',
          }}
          onClick={onCreateWallet}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#f9fafc';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'white';
          }}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '4px',
              height: '100%',
              background: 'linear-gradient(to bottom, #1890ff, #096dd9)',
              opacity: 0,
              transition: 'opacity 0.3s ease'
            }}
            className="indicator"
          />
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              className="float"
              style={{
                background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                borderRadius: '50%',
                width: 80,
                height: 80,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 28,
                flexShrink: 0,
                boxShadow: '0 6px 16px rgba(24, 144, 255, 0.15)',
                transition: 'all 0.3s ease',
              }}
            >
              <PlusCircleOutlined style={{ fontSize: 36, color: '#1890ff' }} />
            </div>
            <div style={{ textAlign: 'left', flex: 1 }}>
              <Title level={3} style={{ marginBottom: 10, marginTop: 0, color: '#262626', fontWeight: 600 }}>
                创建新钱包
              </Title>
              <Paragraph style={{ fontSize: 15, color: '#595959', margin: 0, lineHeight: '1.6' }}>
                生成新的钱包并获得助记词，安全管理您的数字资产
              </Paragraph>
            </div>
          </div>

          <Tooltip title="创建全新的钱包，您将获得一组助记词" placement="bottom">
            <Button
              type="primary"
              size="large"
              className="hover-shadow"
              style={{
                borderRadius: 12,
                height: 50,
                width: '100%',
                marginTop: 28,
                fontWeight: 500,
                fontSize: '16px',
                boxShadow: '0 4px 12px rgba(24, 144, 255, 0.25)',
                transition: 'all 0.3s ease',
              }}
            >
              创建钱包
            </Button>
          </Tooltip>

          <div className="tooltip-bounce" style={{ position: 'absolute', top: '15px', right: '15px', fontSize: '12px', color: '#1890ff' }}>
            推荐
          </div>
        </div>

        {/* 导入钱包选项 */}
        <div
          className="interactive-card"
          style={{
            padding: '40px 36px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            background: 'white',
            position: 'relative',
            overflow: 'hidden',
            borderRadius: '0 0 24px 24px',
          }}
          onClick={onImportWallet}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#f9fafc';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'white';
          }}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '4px',
              height: '100%',
              background: 'linear-gradient(to bottom, #fa8c16, #d46b08)',
              opacity: 0,
              transition: 'opacity 0.3s ease'
            }}
            className="indicator"
          />
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              className="float"
              style={{
                background: 'linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%)',
                borderRadius: '50%',
                width: 80,
                height: 80,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 28,
                flexShrink: 0,
                boxShadow: '0 6px 16px rgba(250, 140, 22, 0.15)',
                transition: 'all 0.3s ease',
              }}
            >
              <ImportOutlined style={{ fontSize: 36, color: '#fa8c16' }} />
            </div>
            <div style={{ textAlign: 'left', flex: 1 }}>
              <Title level={3} style={{ marginBottom: 10, marginTop: 0, color: '#262626', fontWeight: 600 }}>
                导入钱包
              </Title>
              <Paragraph style={{ fontSize: 15, color: '#595959', margin: 0, lineHeight: '1.6' }}>
                使用已有的助记词恢复钱包，找回您的数字资产
              </Paragraph>
            </div>
          </div>

          <Tooltip title="使用已有的助记词导入您的钱包" placement="bottom">
            <Button
              size="large"
              className="hover-shadow"
              style={{
                borderRadius: 12,
                height: 50,
                width: '100%',
                marginTop: 28,
                fontWeight: 500,
                fontSize: '16px',
                border: '1px solid #fa8c16',
                color: '#fa8c16',
                transition: 'all 0.3s ease',
              }}
              ghost
            >
              导入钱包
            </Button>
          </Tooltip>
        </div>
      </div>
      <br />
    </div>
  );
};

export default WalletOptions;
