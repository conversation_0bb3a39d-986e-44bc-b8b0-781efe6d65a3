import React, { useState, useEffect, useCallback } from 'react';
import { Table, Card, Space, Tag, Typography, message, Button } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { getWithdrawPlan } from '../../services/api/withdraw_plan/withdraw_plan';
import type { WalletApiApiWalletV1GetWithdrawPlanListReq } from '../../services/api/model'; // Import request type
import WithdrawPlanFilters, { WithdrawPlanFiltersState } from './components/WithdrawPlanFilters'; // Import Filters
import WithdrawPlanStatsCard from './components/WithdrawPlanStatsCard'; // Import Stats Card
import AddWithdrawPlanModal from './components/AddWithdrawPlanModal'; // Import AddWithdrawPlanModal
import styles from './WithdrawPlan.module.css';
import { CalendarOutlined, PlusOutlined } from '@ant-design/icons';

const { Text } = Typography;

const initialFiltersState: WithdrawPlanFiltersState = {
  chan: '',
  state: '',
};

interface WithdrawPlanItem {
  withdrawPlanId: number;
  chan: string;
  address: string;
  state: number;
  errorMessage: string;
  createdAt: string;
  updatedAt: string;
}

interface WithdrawPlanStatistics {
  totalCount: number;
  pendingCount: number;
  completedCount: number;
  failedCount: number;
  ethCount: number;
  tronCount: number;
}

const WithdrawPlanList: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [withdrawPlans, setWithdrawPlans] = useState<WithdrawPlanItem[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState<WithdrawPlanFiltersState>(initialFiltersState);
  const [addModalVisible, setAddModalVisible] = useState(false);

  // Statistics state
  const [statistics, setStatistics] = useState<WithdrawPlanStatistics>({
    totalCount: 0,
    pendingCount: 0,
    completedCount: 0,
    failedCount: 0,
    ethCount: 0,
    tronCount: 0
  });

  const loadWithdrawPlans = useCallback(async () => {
    setLoading(true);
    try {
      const { getWalletWithdrawPlanList } = getWithdrawPlan();
      const params: WalletApiApiWalletV1GetWithdrawPlanListReq = {
        page: currentPage,
        limit: pageSize,
      };

      if (filters.chan && filters.chan !== '') {
        params.chan = String(filters.chan);
      }
      if (filters.state !== '' && filters.state !== undefined) {
        params.state = Number(filters.state);
      }

      const response = await getWalletWithdrawPlanList(params);

      // Convert API response to WithdrawPlanItem type
      const plans = (response.list || []).map(item => ({
        withdrawPlanId: item.withdrawPlanId || 0,
        chan: item.chan || '',
        address: item.address || '',
        state: item.state || 0,
        errorMessage: item.errorMessage || '',
        createdAt: item.createdAt || '',
        updatedAt: item.updatedAt || ''
      }));

      setWithdrawPlans(plans);
      setTotal(response.total || 0);

      // Calculate statistics
      const pendingCount = plans.filter(item => item.state === 1).length;
      const completedCount = plans.filter(item => item.state === 2).length;
      const failedCount = plans.filter(item => item.state === 0).length;
      const ethCount = plans.filter(item => item.chan === 'ETH').length;
      const tronCount = plans.filter(item => item.chan === 'TRON').length;

      setStatistics({
        totalCount: plans.length,
        pendingCount,
        completedCount,
        failedCount,
        ethCount,
        tronCount
      });
    } catch (error: any) {
      console.error('获取归集计划列表失败:', error);
      message.error(error?.message || '获取归集计划列表失败');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, filters]);

  useEffect(() => {
    loadWithdrawPlans();
  }, [loadWithdrawPlans]);

  const handleFilterChange = (newFilters: Partial<WithdrawPlanFiltersState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page on new search
    loadWithdrawPlans();
  };

  const handleReset = () => {
    setFilters(initialFiltersState);
    setCurrentPage(1);
    // loadWithdrawPlans will be called by useEffect due to filters change if initialFiltersState is different
    // Or call it explicitly if needed, but let's rely on useEffect for now if filters change
    // To ensure it reloads even if filters reset to the same "initial" state that might have been current
    // it's better to call it directly or ensure `loadWithdrawPlans` dependency array correctly captures this reset.
    // For simplicity and directness:
    // setWithdrawPlans([]); // Optionally clear current data
    // setTotal(0);
    // loadWithdrawPlans(); // This might cause double load if useEffect also triggers.
    // Let's refine this: if we set filters and page, useEffect on loadWithdrawPlans will pick it up.
    // So, changing filters state should be enough.
    // If the reset values are the same as current, useEffect won't re-run.
    // Thus, explicit call after state reset can be more reliable for a "reset" button.
    // However, the common pattern is to change state and let useEffect handle it.
    // Let's ensure the callback itself is stable and dependencies are correct.
    // If initialFiltersState is truly initial, and filters are already initial, then handleSearch (load) might not be needed.
    // But user expects a "reset" to fetch initial data.
    // For robust reset:
    setFilters(initialFiltersState);
    setCurrentPage(1);
    // We need to ensure loadWithdrawPlans is called.
    // Since loadWithdrawPlans depends on 'filters', changing 'filters' to initialFiltersState
    // will trigger the useEffect. If initialFiltersState is *identical* to current filters,
    // no re-render. So, best to call loadWithdrawPlans *after* state update or in a separate useEffect.
    // The current useEffect on loadWithdrawPlans should handle it if `filters` object reference changes.
  };

  // Add a specific useEffect to reload data when filters are reset by handleReset
  // This ensures that if handleReset sets filters to their current values (e.g. already initial)
  // a reload is still triggered. However, `loadWithdrawPlans` already depends on `filters`.
  // If `initialFiltersState` is a new object each time, or different, it will trigger.
  // The existing `useEffect([loadWithdrawPlans])` is fine as `loadWithdrawPlans` itself
  // depends on `filters`. The key is that `setFilters(initialFiltersState)` must cause `filters` to change.


  const columns: ColumnsType<WithdrawPlanItem> = [
    {
      title: 'ID',
      dataIndex: 'withdrawPlanId',
      key: 'withdrawPlanId',
    },
    {
      title: '链',
      dataIndex: 'chan',
      key: 'chan',
      render: (text: string) => {
        let color = '';
        switch (text) {
          case 'ETH':
            color = 'blue';
            break;
          case 'TRON':
            color = 'red';
            break;
          default:
            color = 'default';
        }
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      render: (text: string) => (
        <Space>
          <Text>
            {text || ''}
          </Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      render: (state: number) => (
        <Tag color={state === 1 ? 'processing' : state === 2 ? 'success' : 'error'}>
          {state === 1 ? '待确认' : state === 2 ? '完成' : '失败'}
        </Tag>
      ),
    },
    {
      title: '错误信息',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      render: (text: string) => (
        <Text type="danger">{text || '-'}</Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => {
        try {
          return new Date(text).toLocaleString();
        } catch (e) {
          return '-';
        }
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (text: string) => {
        try {
          return new Date(text).toLocaleString();
        } catch (e) {
          return '-';
        }
      },
    },
  ];

  return (
    <div className={styles.withdrawPlanList}>
      <WithdrawPlanStatsCard statistics={statistics} />

      <Card
        className={`${styles.card} hover-shadow`}
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
          border: 'none',
          overflow: 'hidden',
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <CalendarOutlined style={{ color: '#fa8c16' }} />
              <span style={{ fontSize: '16px', fontWeight: 600 }}>归集计划列表</span>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setAddModalVisible(true)}
              style={{
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
              }}
            >
              添加归集计划
            </Button>
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #f0f0f0',
            padding: '16px 24px',
          },
          body: {
            padding: '24px',
          }
        }}
      >
        <WithdrawPlanFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onSearch={handleSearch}
          onReset={handleReset}
          onRefresh={loadWithdrawPlans} // Pass loadWithdrawPlans to onRefresh
        />
        <Table
          columns={columns}
          dataSource={withdrawPlans}
          rowKey="withdrawPlanId"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <AddWithdrawPlanModal
        visible={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onSuccess={() => {
          setAddModalVisible(false);
          loadWithdrawPlans();
        }}
      />
    </div>
  );
};

export default WithdrawPlanList;
