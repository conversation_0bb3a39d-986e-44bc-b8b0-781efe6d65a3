import React from 'react';
import { Card, Row, Col, Statistic, Typography } from 'antd';
import {
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  PieChartOutlined
} from '@ant-design/icons';
import styles from '../WithdrawPlan.module.css';

const { Text } = Typography;

interface WithdrawPlanStatistics {
  totalCount: number;
  pendingCount: number;
  completedCount: number;
  failedCount: number;
  ethCount: number;
  tronCount: number;
}

interface WithdrawPlanStatsCardProps {
  statistics: WithdrawPlanStatistics;
}

const WithdrawPlanStatsCard: React.FC<WithdrawPlanStatsCardProps> = ({ statistics }) => {
  return (
    <Card
      className={`${styles.statsCard} fade-in hover-shadow`}
      style={{
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
        border: 'none',
        overflow: 'hidden',
        marginBottom: '24px',
      }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <PieChartOutlined style={{ color: '#fa8c16' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>归集计划统计</span>
        </div>
      }
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '24px',
        }
      }}
    >
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>计划总数</Text>
              }
              value={statistics.totalCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#fa8c16' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <CalendarOutlined style={{ color: '#fa8c16', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>待确认</Text>
              }
              value={statistics.pendingCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#1890ff' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <ClockCircleOutlined style={{ color: '#1890ff', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>已完成</Text>
              }
              value={statistics.completedCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#52c41a' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>失败</Text>
              }
              value={statistics.failedCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#ff4d4f' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>ETH网络</Text>
              }
              value={statistics.ethCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#1890ff' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <CalendarOutlined style={{ color: '#1890ff', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>TRON网络</Text>
              }
              value={statistics.tronCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#ff4d4f' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <CalendarOutlined style={{ color: '#ff4d4f', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
      </Row>
    </Card>
  );
};

export default WithdrawPlanStatsCard;
