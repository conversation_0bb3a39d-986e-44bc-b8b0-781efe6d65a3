import React, { useState } from 'react';
import { Modal, Form, Input, Switch, InputNumber, message, Space } from 'antd';
import { getWithdrawPlan } from '../../../services/api/withdraw_plan/withdraw_plan';
import type { WalletApiApiWalletV1AddAddressToWithdrawPlanReq } from '../../../services/api/model';
import NativeFilterSelect from '../../common/NativeFilterSelect/NativeFilterSelect';

interface AddWithdrawPlanModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const AddWithdrawPlanModal: React.FC<AddWithdrawPlanModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [checkBalance, setCheckBalance] = useState(true);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const { postWalletAddAddressToWithdrawPlan } = getWithdrawPlan();
      
      const params: WalletApiApiWalletV1AddAddressToWithdrawPlanReq = {
        address: values.address,
        chan: values.chan,
        check_balance: values.check_balance,
      };

      if (values.check_balance && values.min_balance_amount) {
        params.min_balance_amount = String(values.min_balance_amount);
      }

      const response = await postWalletAddAddressToWithdrawPlan(params);

      if (response.success) {
        message.success('添加归集计划成功');
        form.resetFields();
        onSuccess();
      } else {
        message.error(response.balance_check_info || '添加归集计划失败');
      }
    } catch (error: any) {
      console.error('添加归集计划失败:', error);
      message.error(error?.message || '添加归集计划失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setCheckBalance(true);
    onCancel();
  };

  return (
    <Modal
      title="添加归集计划"
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      okText="确认"
      cancelText="取消"
      width={520}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          check_balance: true,
        }}
      >
        <Form.Item
          name="chan"
          label="链类型"
          rules={[{ required: true, message: '请选择链类型' }]}
        >
          <NativeFilterSelect
            options={[
              { value: 'ETH', text: 'ETH' },
              { value: 'TRON', text: 'TRON' },
            ]}
            value={form.getFieldValue('chan')}
            onChange={(e) => form.setFieldsValue({ chan: e.target.value })}
            placeholder="请选择链类型"
          />
        </Form.Item>

        <Form.Item
          name="address"
          label="地址"
          rules={[
            { required: true, message: '请输入地址' },
            {
              pattern: /^(0x[a-fA-F0-9]{40}|T[a-zA-Z0-9]{33})$/,
              message: '请输入有效的地址格式',
            },
          ]}
        >
          <Input placeholder="请输入钱包地址" />
        </Form.Item>

        <Form.Item
          name="check_balance"
          label="检查余额"
          valuePropName="checked"
        >
          <Switch
            onChange={(checked) => {
              setCheckBalance(checked);
              if (!checked) {
                form.setFieldsValue({ min_balance_amount: undefined });
              }
            }}
          />
        </Form.Item>

        {checkBalance && (
          <Form.Item
            name="min_balance_amount"
            label="最小余额要求"
            tooltip="仅在检查余额开启时生效"
            rules={[
              {
                type: 'number',
                min: 0,
                message: '最小余额必须大于等于0',
              },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入最小余额要求"
              precision={6}
              min={0}
              step={0.1}
            />
          </Form.Item>
        )}
      </Form>

      {checkBalance && (
        <Space direction="vertical" style={{ width: '100%', marginTop: 16 }}>
          <div style={{ color: '#666', fontSize: '12px' }}>
            提示：开启余额检查后，只有当地址余额大于最小余额要求时才会加入归集计划。
          </div>
        </Space>
      )}
    </Modal>
  );
};

export default AddWithdrawPlanModal;