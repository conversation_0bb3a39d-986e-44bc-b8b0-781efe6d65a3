import React from 'react';
import { Button } from 'antd'; // Removed unused Space
import NativeFilterSelect from '../../common/NativeFilterSelect/NativeFilterSelect';
import { FilterOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons'; // SearchOutlined removed, SyncOutlined added
import styles from '../WithdrawPlan.module.css'; // 将使用父组件的样式

// const { RangePicker } = DatePicker; // RangePicker is not used anymore

// 假设 WithdrawPlan 的状态定义
// 0: 失败, 1: 待确认, 2: 完成
// 后续需要从 API 或公共类型中获取准确的类型定义
enum WithdrawPlanState {
  Pending = 1,
  Completed = 2,
  Failed = 0,
}

const withdrawPlanStateOptions = [
  { text: '待确认', value: WithdrawPlanState.Pending },
  { text: '完成', value: WithdrawPlanState.Completed },
  { text: '失败', value: WithdrawPlanState.Failed },
];

// 假设 WithdrawPlan 的链定义
// 后续需要从 API 或公共类型中获取准确的类型定义
enum WithdrawPlanChain {
  ETH = 'ETH',
  TRON = 'TRON',
  // 根据实际情况添加更多链
}

const withdrawPlanChainOptions = Object.values(WithdrawPlanChain).map(value => ({
  text: value,
  value: value,
}));


export interface WithdrawPlanFiltersState {
  chan?: WithdrawPlanChain | string; // 允许string以兼容 ''
  state?: WithdrawPlanState | string; // 允许string以兼容 ''
}

interface WithdrawPlanFiltersProps {
  filters: WithdrawPlanFiltersState;
  onFilterChange: (newFilters: Partial<WithdrawPlanFiltersState>) => void;
  onSearch: () => void;
  onReset: () => void;
  onRefresh: () => void; // Added onRefresh prop
}

const WithdrawPlanFilters: React.FC<WithdrawPlanFiltersProps> = ({
  filters,
  onFilterChange,
  onSearch,
  onReset,
  onRefresh, // Destructure onRefresh
}) => {
  const handleChanChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange({ chan: event.target.value as WithdrawPlanChain | '' });
  };

  const handleStateChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange({ state: event.target.value === '' ? '' : Number(event.target.value) as WithdrawPlanState | '' });
  };

  return (
    <div className={styles.tableHeader}>
      <div className={styles.searchFilters}>
        <div className={styles.filterRow}>
          <div className={styles.filterLabel}>筛选条件：</div>
          <div className={styles.filterSelects}>
            <NativeFilterSelect
              placeholder="全部链"
              value={filters.chan || ''}
              onChange={handleChanChange}
              options={withdrawPlanChainOptions}
              style={{ width: 150 }}
              className={`${styles.filterSelect} hover-shadow transition-all`}
            />

            <NativeFilterSelect
              placeholder="全部状态"
              value={filters.state === '' ? '' : filters.state?.toString() || ''}
              onChange={handleStateChange}
              options={withdrawPlanStateOptions.map(opt => ({ text: opt.text, value: opt.value.toString() }))}
              style={{ width: 150 }}
              className={`${styles.filterSelect} hover-shadow transition-all`}
            />
          </div>
        </div>
      </div>

      <div className={styles.actions}>
        <Button
          type="primary"
          icon={<FilterOutlined />}
          onClick={onSearch}
          style={{
            marginRight: 12,
            borderRadius: '8px',
            height: '32px',
          }}
          className="transition-all"
        >
          筛选
        </Button>
        <Button
          icon={<ReloadOutlined />}
          onClick={onReset}
          style={{
            borderRadius: '8px',
            height: '32px',
          }}
          className="transition-all"
        >
          重置
        </Button>
        <Button
          icon={<SyncOutlined />}
          onClick={onRefresh} // Call onRefresh
          style={{
            marginLeft: 12, // Add some margin
            borderRadius: '8px',
            height: '32px',
          }}
          className="transition-all"
        >
          刷新
        </Button>
      </div>
    </div>
  );
};

export default WithdrawPlanFilters;
