.withdrawPlanList {
  width: 100%;
  animation: fadeIn 0.5s ease-out;
}

.card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 82, 204, 0.08);
  border: none;
  overflow: hidden;
}

.statsCard {
  margin-bottom: 24px;
}

.tableCard {
  margin-top: 24px;
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  flex-wrap: wrap;
  padding: 8px 0;
}

.searchFilters {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.filterRow {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filterLabel {
  font-weight: 500;
  color: #262626;
  min-width: 80px;
}

.filterSelects {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.searchInput {
  width: 220px;
}

.filterButton {
  margin-right: 8px;
}

.statusTag {
  min-width: 70px;
  text-align: center;
  border-radius: 4px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
