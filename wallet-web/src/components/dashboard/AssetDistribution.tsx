import React, { useState, useRef } from 'react';
import { Card, Typography, Progress, Toolt<PERSON>, Button, Spin } from 'antd';
import { PieChartOutlined, ReloadOutlined, BarChartOutlined, LineChartOutlined, BarsOutlined, DownOutlined } from '@ant-design/icons';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title as ChartTitle
} from 'chart.js';
import { Pie, Bar, Line } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ChartTitle
);

const { Text, Title, Paragraph } = Typography;

interface AssetDistributionItem {
  name: string;
  value: number;
  percent: string;
}

interface AssetDistributionProps {
  distribution: AssetDistributionItem[];
}

const AssetDistribution: React.FC<AssetDistributionProps> = ({ distribution }) => {
  const [chartType, setChartType] = useState<'pie' | 'bar' | 'line' | 'progress'>('progress');
  const [chartLoading, setChartLoading] = useState(false);
  const chartRef = useRef<ChartJS>(null);

  // 为每个资产分配一个固定的颜色
  const getColorByIndex = (index: number): string => {
    const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96'];
    return colors[index % colors.length];
  };

  // 获取图表类型图标
  const getChartTypeIcon = () => {
    switch (chartType) {
      case 'pie': return <PieChartOutlined />;
      case 'bar': return <BarChartOutlined />;
      case 'line': return <LineChartOutlined />;
      case 'progress': return <BarsOutlined />;
      default: return <PieChartOutlined />;
    }
  };

  // 准备图表数据
  const prepareChartData = () => {
    const labels = distribution.map(item => item.name);
    const values = distribution.map(item => item.value);
    const backgroundColors = distribution.map((_, index) => getColorByIndex(index));
    const borderColors = distribution.map((_, index) => getColorByIndex(index));

    return {
      labels,
      datasets: [
        {
          label: '资产价值',
          data: values,
          backgroundColor: backgroundColors,
          borderColor: borderColors,
          borderWidth: 1,
          hoverOffset: 4,
          tension: 0.1, // 用于线图的曲线平滑度
        },
      ],
    };
  };

  // 图表配置
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          font: {
            size: 12,
          },
          padding: 20,
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.raw || 0;
            const percentage = distribution[context.dataIndex]?.percent || '0';
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart' as const,
    },
  };

  // 处理图表类型切换
  const handleChartTypeChange = (type: 'pie' | 'bar' | 'line' | 'progress') => {
    setChartLoading(true);
    setChartType(type);
    // 模拟加载延迟，增强用户体验
    setTimeout(() => {
      setChartLoading(false);
    }, 300);
  };

  // 处理刷新图表
  const handleRefreshChart = () => {
    setChartLoading(true);
    // 模拟刷新延迟
    setTimeout(() => {
      if (chartRef.current) {
        chartRef.current.update();
      }
      setChartLoading(false);
    }, 500);
  };

  if (!distribution || distribution.length === 0) {
    return (
      <Card
        className="fade-in hover-shadow"
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <PieChartOutlined style={{ color: '#1890ff' }} />
            <Title level={4} style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>资产分布</Title>
          </div>
        }
        style={{
          borderRadius: '16px',
          boxShadow: '0 10px 20px rgba(0, 82, 204, 0.1)',
          border: 'none',
          overflow: 'hidden',
          height: '100%',
        }}
        styles={{
          header: {
            borderBottom: '1px solid #f0f0f0',
            padding: '16px 24px',
          },
          body: {
            padding: '24px',
          }
        }}
      >
        <div style={{ padding: '40px 0', textAlign: 'center' }}>
          <div
            className="pulse"
            style={{
              marginBottom: '24px',
              opacity: 0.7,
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              margin: '0 auto 24px'
            }}
          >
            <PieChartOutlined style={{ fontSize: '40px', color: '#1890ff' }} />
          </div>
          <Title level={4} style={{ marginBottom: '8px', color: '#595959' }}>暂无资产分布数据</Title>
          <Paragraph type="secondary" style={{ fontSize: '14px' }}>
            添加更多资产后，您将在这里看到资产分布情况
          </Paragraph>
        </div>
      </Card>
    );
  }



  return (
    <Card
      className="fade-in hover-shadow"
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {getChartTypeIcon()}
          <Title level={4} style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>资产分布</Title>
        </div>
      }
      style={{
        borderRadius: '16px',
        boxShadow: '0 10px 20px rgba(0, 82, 204, 0.1)',
        border: 'none',
        overflow: 'hidden',
        height: '100%',
      }}
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '24px',
        }
      }}
      extra={
        <div style={{ display: 'flex', gap: '8px' }}>
          <Tooltip title="进度条">
            <Button
              type={chartType === 'progress' ? 'primary' : 'text'}
              icon={<BarsOutlined />}
              size="small"
              onClick={() => handleChartTypeChange('progress')}
              shape="circle"
              className="transition-all"
            />
          </Tooltip>
          <Tooltip title="饼图">
            <Button
              type={chartType === 'pie' ? 'primary' : 'text'}
              icon={<PieChartOutlined />}
              size="small"
              onClick={() => handleChartTypeChange('pie')}
              shape="circle"
              className="transition-all"
            />
          </Tooltip>
          <Tooltip title="柱状图">
            <Button
              type={chartType === 'bar' ? 'primary' : 'text'}
              icon={<BarChartOutlined />}
              size="small"
              onClick={() => handleChartTypeChange('bar')}
              shape="circle"
              className="transition-all"
            />
          </Tooltip>
          <Tooltip title="折线图">
            <Button
              type={chartType === 'line' ? 'primary' : 'text'}
              icon={<LineChartOutlined />}
              size="small"
              onClick={() => handleChartTypeChange('line')}
              shape="circle"
              className="transition-all"
            />
          </Tooltip>
          <Tooltip title="刷新">
            <Button
              type="text"
              icon={<ReloadOutlined spin={chartLoading} />}
              size="small"
              shape="circle"
              onClick={handleRefreshChart}
              className="hover-shadow transition-all"
            />
          </Tooltip>
        </div>
      }
    >
      {/* 图表区域 - 只在非进度条模式下显示 */}
      {chartType !== 'progress' && (
        <div
          className="fade-in"
          style={{
            height: '200px',
            marginBottom: '20px',
            position: 'relative'
          }}
        >
          <Spin spinning={chartLoading} tip="加载中...">
            <div style={{ height: '100%', width: '100%' }}>
              {chartType === 'pie' && (
                <Pie
                  data={prepareChartData()}
                  options={chartOptions}
                  ref={chartRef as any}
                  className="chart-animation"
                />
              )}
              {chartType === 'bar' && (
                <Bar
                  data={prepareChartData()}
                  options={{
                    ...chartOptions,
                    scales: {
                      y: {
                        beginAtZero: true
                      }
                    }
                  }}
                  ref={chartRef as any}
                  className="chart-animation"
                />
              )}
              {chartType === 'line' && (
                <Line
                  data={prepareChartData()}
                  options={{
                    ...chartOptions,
                    scales: {
                      y: {
                        beginAtZero: true
                      }
                    }
                  }}
                  ref={chartRef as any}
                  className="chart-animation"
                />
              )}
            </div>
          </Spin>
        </div>
      )}

      {/* 资产列表 - 只在进度条模式下显示 */}
      {chartType === 'progress' && (
        <div className="stagger-fade-in chart-animation">
          {distribution.map((item, index) => (
            <div
              key={index}
              style={{
                marginBottom: 20,
                animation: `fadeIn 0.5s ease-out forwards ${index * 0.1}s`,
                opacity: 0
              }}
              className="interactive-card"
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div
                    style={{
                      width: 16,
                      height: 16,
                      borderRadius: '50%',
                      backgroundColor: getColorByIndex(index),
                      marginRight: 8,
                      boxShadow: `0 2px 6px ${getColorByIndex(index)}80`,
                    }}
                  />
                  <Text strong style={{ fontSize: '15px' }}>{item.name}</Text>
                </div>
                <Text strong style={{ color: getColorByIndex(index), fontSize: '15px' }}>{item.percent}%</Text>
              </div>
              <Progress
                percent={parseFloat(item.percent)}
                showInfo={false}
                strokeColor={{
                  '0%': getColorByIndex(index),
                  '100%': `${getColorByIndex(index)}90`,
                }}
                trailColor="#f5f5f5"
                strokeWidth={10}
                style={{
                  borderRadius: 5,
                  overflow: 'hidden',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                }}
              />
            </div>
          ))}
        </div>
      )}

      {/* 在非进度条模式下，显示"查看资产分布"按钮 */}
      {chartType !== 'progress' && (
        <div style={{ textAlign: 'center', marginTop: '10px', marginBottom: '15px' }}>
          <Button
            type="link"
            onClick={() => handleChartTypeChange('progress')}
            className="hover-shadow transition-all"
          >
            查看资产分布详情 <DownOutlined />
          </Button>
        </div>
      )}

      <div style={{ marginTop: '24px', textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: '13px' }}>
          总计 {distribution.length} 种资产 · 更新于 {new Date().toLocaleTimeString()}
        </Text>
      </div>
    </Card>
  );
};

export default AssetDistribution;
