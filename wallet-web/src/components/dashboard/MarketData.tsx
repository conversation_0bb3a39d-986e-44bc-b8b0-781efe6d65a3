import React from 'react';
import { <PERSON>, <PERSON>, Button, Typography, Badge } from 'antd';
import { SyncOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Text } = Typography;

interface MarketItem {
  symbol: string;
  price: string;
  change: string;
  volume: string;
}

interface MarketDataProps {
  marketData: MarketItem[];
}

const MarketData: React.FC<MarketDataProps> = ({ marketData }) => {
  const navigate = useNavigate();

  return (
    <Card title="市场行情" extra={<Badge count={<SyncOutlined style={{ color: '#1890ff' }} />} />}>
      <List
        dataSource={marketData}
        renderItem={(item) => (
          <List.Item
            key={item.symbol}
            actions={[
              <Button type="link" onClick={() => navigate(`/market/${item.symbol.replace('/', '-')}`)}>
                详情
              </Button>,
            ]}
          >
            <List.Item.Meta title={item.symbol} description={`成交量: ${item.volume}`} />
            <div>
              <div style={{ textAlign: 'right' }}>
                <Text strong>${item.price}</Text>
              </div>
              <div>
                <Text
                  type={item.change.startsWith('+') ? 'success' : item.change.startsWith('-') ? 'danger' : 'secondary'}
                >
                  {item.change}
                </Text>
              </div>
            </div>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default MarketData;
