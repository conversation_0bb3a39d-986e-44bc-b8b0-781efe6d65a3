import React from 'react';
import { Typography } from 'antd';
import { ArrowDownOutlined, ArrowUpOutlined, SwapOutlined } from '@ant-design/icons';
import { GetWalletTransactionRecordType } from '../../services/api/model'; // Updated import path
import { GetWalletTransactionRecordStatus } from '../../services/api/model/getWalletTransactionRecordStatus';

const { Text } = Typography;

interface TransactionTypeInfo {
  icon: React.ReactNode;
  color: string;
  text: string;
}

export const getTransactionTypeInfo = (type: string): TransactionTypeInfo => {
  const typeInfoMap: Record<string, TransactionTypeInfo> = {
    recharge: { // Added specific handling for 'recharge' type
      icon: <ArrowDownOutlined />,
      color: 'green',
      text: '充值',
    },
    [GetWalletTransactionRecordType.deposit]: {
      icon: <ArrowDownOutlined />,
      color: 'green',
      text: '充值', // Or '存款' if different from 'recharge'
    },
    [GetWalletTransactionRecordType.withdraw]: {
      icon: <ArrowUpOutlined />,
      color: 'orange',
      text: '归集',
    },
    [GetWalletTransactionRecordType.transfer]: {
      icon: <SwapOutlined />,
      color: 'blue',
      text: '转账',
    },
    [GetWalletTransactionRecordType.miner_fee]: {
      icon: <ArrowUpOutlined />,
      color: 'red',
      text: '矿工费',
    },
  };

  return (
    typeInfoMap[type.toLowerCase()] || { // Convert type to lowercase for case-insensitive matching
      icon: <SwapOutlined />,
      color: 'default',
      text: '未知',
    }
  );
};

export const getStatusTag = (status: string): React.ReactNode => {
  const statusMap: Record<string, React.ReactNode> = {
    [GetWalletTransactionRecordStatus.completed]: <Text type="success">已完成</Text>,
    [GetWalletTransactionRecordStatus.pending]: <Text type="warning">处理中</Text>,
    [GetWalletTransactionRecordStatus.processing]: <Text type="warning">处理中</Text>,
    [GetWalletTransactionRecordStatus.failed]: <Text type="danger">失败</Text>,
    [GetWalletTransactionRecordStatus.canceled]: <Text type="danger">失败</Text>,
  };

  return statusMap[status] || <Text type="secondary">未知</Text>;
};