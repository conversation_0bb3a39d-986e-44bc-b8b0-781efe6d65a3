import { useState, useEffect, useCallback } from 'react';
import { getRecharge } from '../../../services/api/recharge/recharge'; // Updated import
import { GetWalletRechargeRecordParams, WalletApiApiWalletV1RechargeRecord } from '../../../services/api/model'; // Updated import
import { Transaction } from '../../../types/transaction'; // 使用全局类型
import { GetWalletTransactionRecordStatus } from '../../../services/api/model/getWalletTransactionRecordStatus'; // Import for status mapping

const { getWalletRechargeRecord } = getRecharge(); // Updated function call

export interface UseRecentTransactionsDataReturn {
  transactions: Transaction[];
  loading: boolean;
  error: Error | null;
  loadTransactions: () => Promise<void>;
}

const useRecentTransactionsData = (): UseRecentTransactionsDataReturn => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const loadTransactions = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const params: GetWalletRechargeRecordParams = { // Updated params type
        page: 1,
        limit: 5, // 获取最近5条
        sort_field: 'created_at', // Updated sort field for recharge records
        sort_order: 'desc',
      };

      const response = await getWalletRechargeRecord(params); // Updated API call

      if (response?.list) {
        const mappedTransactions = response.list.map((rechargeRecord: WalletApiApiWalletV1RechargeRecord): Transaction => {
          let status: GetWalletTransactionRecordStatus | undefined;
          if (rechargeRecord.state === 1) {
            status = GetWalletTransactionRecordStatus.pending;
          } else if (rechargeRecord.state === 2) {
            status = GetWalletTransactionRecordStatus.completed;
          }
          // Add more sophisticated status mapping if needed

          // Helper function to safely get string values, defaulting to undefined if null/empty
          const getString = (value: string | null | undefined): string | undefined => {
            return value || undefined;
          };

          // Process transaction_time
          let transactionTime: string | undefined;
          if (rechargeRecord.created_at && rechargeRecord.created_at !== '0' && !isNaN(new Date(rechargeRecord.created_at).getTime())) {
            transactionTime = rechargeRecord.created_at;
          }


          return {
            // Map common fields
            transaction_id: rechargeRecord.recharges_id?.toString(),
            transaction_hash: getString(rechargeRecord.tx_hash),
            transaction_time: transactionTime,
            transaction_type: 'recharge', // Explicitly set type as recharge
            transaction_status: status,
            sender_address: getString(rechargeRecord.from_address),
            receiver_address: getString(rechargeRecord.to_address),
            amount: rechargeRecord.amount, // Assuming amount is always present and valid
            chain: getString(rechargeRecord.chain),
            token_name: getString(rechargeRecord.name),
            confirmations: rechargeRecord.confirmations,
            // Potentially other fields from Transaction type if they make sense for recharge
          };
        });
        setTransactions(mappedTransactions);
      } else {
        setTransactions([]);
      }
    } catch (err: any) {
      console.error('获取最近交易记录失败:', err);
      setError(err instanceof Error ? err : new Error('获取交易数据时发生未知错误'));
      setTransactions([]); // 出错时清空交易
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  return { transactions, loading, error, loadTransactions };
};

export default useRecentTransactionsData;