import React, { useState, useEffect } from 'react';
import { Button, Typography, Card, Row, Col, Tooltip } from 'antd';
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  DashboardOutlined,
  // UserOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
const { Title, Text } = Typography;

interface DashboardHeaderProps {
  username: string;
  hideBalances: boolean;
  toggleHideBalances: () => void;
  onRefresh?: () => void;
  loading?: boolean;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ username, hideBalances, toggleHideBalances, onRefresh, loading = false }) => {
  const [greeting, setGreeting] = useState('');
  const navigate = useNavigate();
  // 根据时间设置问候语
  useEffect(() => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) {
      setGreeting('早上好');
    } else if (hour >= 12 && hour < 18) {
      setGreeting('下午好');
    } else {
      setGreeting('晚上好');
    }
  }, []);

  return (
    <Card
      className="fade-in hover-shadow"
      style={{
        marginBottom: 24,
        borderRadius: '24px',
        boxShadow: '0 10px 20px rgba(0, 82, 204, 0.1)',
        border: 'none',
        overflow: 'hidden',
        background: 'linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%)',
      }}
    >
      <Row align="middle" justify="space-between" gutter={[16, 16]}>
        <Col xs={24} md={16}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div
              className="pulse"
              style={{
                width: 60,
                height: 60,
                borderRadius: '16px',
                background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                boxShadow: '0 8px 16px rgba(24, 144, 255, 0.2)',
              }}
            >
              <DashboardOutlined style={{ fontSize: '30px', color: '#1890ff' }} />
            </div>
            <div className="fade-up">
              <Title level={2} style={{ marginBottom: 4, fontSize: '28px', fontWeight: 600 }}>
                {greeting}，{username || '用户'}
              </Title>
              <Text type="secondary" style={{ fontSize: '16px' }}>
                欢迎回到您的数字资产管理中心
              </Text>
            </div>
          </div>
        </Col>
        <Col xs={24} md={8} style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px', alignItems: 'center' }}>
          <Tooltip title="刷新">
            <Button
              icon={<ReloadOutlined />}
              shape="circle"
              className="hover-shadow transition-all"
              style={{
                height: '40px',
                width: '40px',
                borderRadius: '50%',
                boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
              }}
              onClick={onRefresh}
              loading={loading}
            />
          </Tooltip>

          <Tooltip title="设置">
            <Button
              icon={<SettingOutlined />}
              shape="circle"
              className="hover-shadow transition-all"
              style={{
                height: '40px',
                width: '40px',
                borderRadius: '50%',
                boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
              }}
              onClick={() => (
                navigate('/settings')
              )}
            />
          </Tooltip>

          <Tooltip title={hideBalances ? '显示余额' : '隐藏余额'}>
            <Button
              icon={hideBalances ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={toggleHideBalances}
              className="hover-shadow transition-all"
              style={{
                height: '40px',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 82, 204, 0.1)',
                fontWeight: 500,
              }}
            >
              {hideBalances ? '显示余额' : '隐藏余额'}
            </Button>
          </Tooltip>
          {/*
          <Avatar
            icon={<UserOutlined />}
            style={{
              backgroundColor: '#1890ff',
              boxShadow: '0 4px 12px rgba(0, 82, 204, 0.2)',
            }}
            size={40}
            className="hover-shadow transition-all"
          />
          */}
        </Col>
      </Row>
    </Card>
  );
};

export default DashboardHeader;
