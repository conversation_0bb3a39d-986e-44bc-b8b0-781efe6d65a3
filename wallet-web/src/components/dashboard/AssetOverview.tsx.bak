// import React from 'react';
// import { Row, Col, Card, Statistic, Typography } from 'antd';
// import { DollarOutlined } from '@ant-design/icons';
// const { Text } = Typography;

// interface AssetOverviewProps {
//   totalAssetValue: string;
//   address: string;
//   loading: boolean;
//   hideBalances: boolean;
//   copyToClipboard: (text: string) => void;
// }

// const AssetOverview: React.FC<AssetOverviewProps> = ({ totalAssetValue, loading, hideBalances }) => {
//   return (
//     <Card style={{ marginBottom: 24 }}>
//       <Row gutter={[24, 24]} align="middle">
//         <Col xs={24} md={8}>
//           <Statistic
//             title={
//               <Text strong style={{ fontSize: 16 }}>
//                 总资产价值
//               </Text>
//             }
//             value={hideBalances ? '******' : totalAssetValue}
//             precision={2}
//             valueStyle={{ color: '#1890ff', fontSize: 28 }}
//             prefix={<DollarOutlined />}
//             suffix="USD"
//             loading={loading}
//           />
//           <Text type="secondary">更新于 {new Date().toLocaleTimeString()}</Text>
//         </Col>
//       </Row>
//     </Card>
//   );
// };

// export default AssetOverview;
