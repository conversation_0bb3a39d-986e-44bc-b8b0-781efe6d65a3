import React from 'react';
import { Modal, Button, Descriptions, Space, Typography } from 'antd';
import { CopyOutlined, LinkOutlined } from '@ant-design/icons';
import { Transaction } from '../../types/transaction';
import { formatAddress, formatUnixTimestamp } from '../../utils/format';
import { getTransactionTypeInfo, getStatusTag } from './transactionDisplayUtils';
import { copyToClipboard } from '../../utils/clipboardUtils';
import { openInExplorer } from '../../utils/explorerUtils';

const { Text } = Typography;

interface TransactionDetailModalProps {
  visible: boolean;
  transaction: Transaction | null;
  onClose: () => void;
  hideBalances: boolean;
}

const TransactionDetailModal: React.FC<TransactionDetailModalProps> = ({
  visible,
  transaction,
  onClose,
  hideBalances,
}) => {
  if (!transaction) {
    return null;
  }

  const typeInfo = getTransactionTypeInfo(transaction.transaction_type || '');
  const statusDisplayFromFunc = getStatusTag(transaction.transaction_status || '');
  const statusDisplay: React.ReactNode = statusDisplayFromFunc || '状态未知';
  const formattedTime = formatUnixTimestamp(transaction.transaction_time);

  return (
    <Modal
      title="交易详情"
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        transaction.transaction_hash && (
          <Button
            key="explorer"
            type="primary"
            icon={<LinkOutlined />}
            onClick={() => openInExplorer('tx', transaction.transaction_hash!, transaction.chain || '')}
          >
            在区块浏览器中查看
          </Button>
        ),
      ]}
      width={600}
    >
      <Descriptions column={1} bordered>
        <Descriptions.Item label="交易类型">{typeInfo.text}</Descriptions.Item>
        <Descriptions.Item label="交易状态">{statusDisplay}</Descriptions.Item>
        <Descriptions.Item label="交易时间">{formattedTime}</Descriptions.Item>
        <Descriptions.Item label="交易金额">
          {hideBalances
            ? '******'
            : `${transaction.amount || '0'} ${transaction.token_name || transaction.chain || ''}`}
        </Descriptions.Item>
        <Descriptions.Item label="交易手续费">
          {hideBalances
            ? '******'
            : `${transaction.transaction_fee || '0'} ${transaction.chain || ''}`}
        </Descriptions.Item>
        <Descriptions.Item label="发送地址">
          <Space>
            <Text copyable={{ text: transaction.sender_address || '' }}>
              {formatAddress(transaction.sender_address || '')}
            </Text>
            <Button
              type="link"
              icon={<CopyOutlined />}
              onClick={() => copyToClipboard(transaction.sender_address || '')}
              aria-label="复制发送地址"
            />
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="接收地址">
          <Space>
            <Text copyable={{ text: transaction.receiver_address || '' }}>
              {formatAddress(transaction.receiver_address || '')}
            </Text>
            <Button
              type="link"
              icon={<CopyOutlined />}
              onClick={() => copyToClipboard(transaction.receiver_address || '')}
              aria-label="复制接收地址"
            />
          </Space>
        </Descriptions.Item>
        {transaction.memo && <Descriptions.Item label="备注">{transaction.memo}</Descriptions.Item>}
      </Descriptions>
    </Modal>
  );
};

export default TransactionDetailModal;