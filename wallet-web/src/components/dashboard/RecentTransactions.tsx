import React, { useState, useCallback } from 'react';
import { Card, List, Button, Spin, Empty } from 'antd';
import { HistoryOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import styles from './RecentTransactions.module.css';
import { Transaction } from '../../types/transaction'; // 使用全局类型
import TransactionListItem from './TransactionListItem';
import TransactionDetailModal from './TransactionDetailModal';
import useRecentTransactionsData from './hooks/useRecentTransactionsData';
import { openInExplorer } from '../../utils/explorerUtils'; // 使用全局工具函数

interface RecentTransactionsProps {
  hideBalances: boolean;
}

const RecentTransactions: React.FC<RecentTransactionsProps> = ({ hideBalances }) => {
  const navigate = useNavigate();
  const { transactions, loading, error } = useRecentTransactionsData(); // 使用自定义 Hook
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);

  // 处理显示交易详情的逻辑
  const handleShowDetails = useCallback((transaction: Transaction) => {
    // 如果交易有哈希，则在区块浏览器中打开
    // 否则，显示详情模态框
    if (transaction.transaction_hash && transaction.chain) {
      openInExplorer('tx', transaction.transaction_hash, transaction.chain);
    } else {
      // 对于没有哈希或链信息的交易（例如，内部记录但未上链，或数据不完整）
      // 或者如果用户明确点击了列表项中的“详情”按钮（而非直接的区块浏览器链接）
      // 我们显示模态框
      setSelectedTransaction(transaction);
      setDetailsVisible(true);
    }
  }, []);

  // 关闭详情模态框
  const handleCloseDetailsModal = useCallback(() => {
    setDetailsVisible(false);
    setSelectedTransaction(null); // 清除选中的交易
  }, []);

  // 渲染列表项的函数，现在传递给 TransactionListItem
  // 但实际上 TransactionListItem 内部已经处理了渲染逻辑
  // 我们只需要确保 handleShowDetails 传递给 TransactionListItem 或者在其中正确调用
  // 在 TransactionListItem 的实现中，我们让它自己处理部分点击逻辑，
  // 但更纯粹的做法是 ListItem 触发一个事件，由这里处理。
  // 为了简化，我们让 ListItem 的点击直接调用 handleShowDetails。

  if (error) {
    // 可以根据错误类型显示更友好的错误信息
    return (
      <Card title="最近充值" className={styles.recentTransactionsCard}>
        <Empty description={`加载充值记录失败: ${error.message}`} />
      </Card>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <HistoryOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>最近充值</span>
        </div>
      }
      extra={
        <Button
          type="link"
          onClick={() => navigate('/recharge')} // 链接到充值记录页面，当前符合需求
          style={{
            fontSize: '14px',
            fontWeight: 500,
            color: '#1890ff',
          }}
        >
          查看全部
        </Button>
      }
      className={`${styles.recentTransactionsCard} fade-in hover-shadow`}
      style={{
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
        border: 'none',
        overflow: 'hidden',
      }}
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '0',
        }
      }}
    >
      {loading ? (
        <div className={styles.loadingContainer}> {/* 使用 CSS Module 样式 */}
          <Spin />
        </div>
      ) : transactions.length > 0 ? (
        <List
          dataSource={transactions}
          renderItem={(item) => (
            <TransactionListItem
              transaction={item}
              hideBalances={hideBalances}
              onViewDetailsClick={handleShowDetails}
            />
          )}
          style={{ padding: '0 16px' }}
        />
      ) : (
        <div style={{ padding: '40px 0', textAlign: 'center' }}>
          <div style={{ marginBottom: '16px', opacity: 0.5 }}>
            <HistoryOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          </div>
          <Empty
            description="暂无充值记录"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      )}

      <TransactionDetailModal
        visible={detailsVisible}
        transaction={selectedTransaction}
        onClose={handleCloseDetailsModal}
        hideBalances={hideBalances}
      />
    </Card>
  );
};

export default RecentTransactions;
