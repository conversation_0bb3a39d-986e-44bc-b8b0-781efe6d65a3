.recentTransactionsCard {
    margin-bottom: 24px;
}

.recentTransactionsCard :global(.ant-list-item) {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.transactionItem {
    transition: all 0.3s;
}

.transactionItem:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.typeIconWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    color: white;
    font-size: 16px;
    margin-right: 8px;
}

.amount {
    font-weight: 500;
    font-size: 14px;
    margin: 0 8px;
}

/* 优化列表项布局 */
.recentTransactionsCard :global(.ant-list-item-meta) {
    align-items: center;
}

.recentTransactionsCard :global(.ant-list-item-meta-title) {
    margin-bottom: 4px !important;
    display: flex;
    align-items: center;
}

.recentTransactionsCard :global(.ant-list-item-meta-description) {
    font-size: 12px;
    color: #999;
}

.recentTransactionsCard :global(.ant-list-item-action) {
    margin-left: 48px;
}

.recentTransactionsCard :global(.ant-tag) {
    margin-right: 0;
    font-size: 12px;
    line-height: 20px;
    height: 22px;
    padding: 0 6px;
}

/* 响应式调整 */
@media (max-width: 576px) {
    .typeIconWrapper {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }
}

.loadingContainer {
    text-align: center;
    padding: 20px 0;
}