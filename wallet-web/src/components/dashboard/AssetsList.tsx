import React, { useState } from 'react';
import { Card, List, Typography, Tag, Avatar, Button, Badge } from 'antd';
import {
  WalletOutlined,
  ArrowRightOutlined
  // Removed unused PlusOutlined, SwapOutlined, SendOutlined, Tooltip
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

// 导入SVG图标
import ethIcon from '../../assets/images/eth.svg';
import tronIcon from '../../assets/images/tron.svg';
import usdtIcon from '../../assets/images/usdt.svg';

const { Text, Title } = Typography;

interface Asset {
  chain: string;
  name: string;
  balance: string;
  value: string;
  currency: string;
  change?: string;
  icon?: string;
}

interface AssetsListProps {
  assets: Asset[];
  hideBalances: boolean;
}

const AssetsList: React.FC<AssetsListProps> = ({ assets, hideBalances }) => {
  // const navigate = useNavigate();
  const [hoveredAsset, setHoveredAsset] = useState<string | null>(null);
  const navigate = useNavigate();
  // 获取资产对应的图标
  const getAssetIcon = (chain: string): string => {
    switch (chain) {
      case 'ETH':
        return ethIcon;
      case 'TRON':
        return tronIcon;
      case 'ERC20':
      case 'TRC20':
        return usdtIcon;
      default:
        return '';
    }
  };

  // 获取资产对应的颜色
  const getAssetColor = (chain: string): string => {
    switch (chain) {
      case 'ETH':
        return '#1890ff';
      case 'TRON':
        return '#fa8c16';
      case 'ERC20':
        return '#52c41a';
      case 'TRC20':
        return '#722ed1';
      default:
        return '#1890ff';
    }
  };

  return (
    <Card
      className="fade-in hover-shadow"
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <WalletOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
            <Title level={4} style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>多链资产</Title>
            <Badge count={assets.length} style={{ backgroundColor: '#1890ff' }} />
          </div>
          {/* <div>
            <Button
              type="text"
              icon={<PlusOutlined />}
              style={{ color: '#1890ff', fontWeight: 500 }}
              className="hover-shadow"
            >
              添加资产
            </Button>
          </div> */}
        </div>
      }
      style={{
        borderRadius: '16px',
        boxShadow: '0 10px 20px rgba(0, 82, 204, 0.1)',
        border: 'none',
        overflow: 'hidden',
      }}
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '0',
        }
      }}
      extra={
        <Button
          type="link"
          icon={<ArrowRightOutlined />}
          style={{ fontWeight: 500 }}
          onClick={() => navigate('/addresses')}
        >
          查看全部
        </Button>
      }
    >
      <List
        dataSource={assets}
        renderItem={(item) => {
          const assetId = `${item.chain}-${item.name}`;
          const isHovered = hoveredAsset === assetId;
          return (
            <List.Item
              key={assetId}
              className="interactive-card"
              style={{
                padding: '20px 24px',
                borderBottom: '1px solid #f0f0f0',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                position: 'relative',
                overflow: 'hidden',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f9fafc';
                setHoveredAsset(assetId);
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                setHoveredAsset(null);
              }}
            >
              {/* 左侧指示条 */}
              <div
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  width: '4px',
                  height: '100%',
                  background: getAssetColor(item.chain),
                  opacity: isHovered ? 1 : 0,
                  transition: 'opacity 0.3s ease'
                }}
              />

              <List.Item.Meta
                avatar={
                  <Avatar
                    src={getAssetIcon(item.chain)}
                    size={50}
                    style={{
                      padding: '6px',
                      backgroundColor: '#f5f5f5',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                      transition: 'all 0.3s ease',
                      transform: isHovered ? 'scale(1.05)' : 'scale(1)',
                    }}
                    className={isHovered ? "pulse" : ""}
                  />
                }
                title={
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                    <span style={{ fontSize: '18px', fontWeight: 600 }}>{item.name}</span>
                    <Tag
                      color={getAssetColor(item.chain)}
                      style={{
                        marginLeft: 8,
                        borderRadius: '4px',
                        fontWeight: 500
                      }}
                    >
                      {item.chain}
                    </Tag>
                  </div>
                }
                description={
                  <Text type="secondary" style={{ fontSize: '14px' }}>
                    {item.chain === 'ETH'
                      ? 'ETH主网'
                      : item.chain === 'TRON'
                        ? 'TRON主网'
                        : item.chain === 'ERC20'
                          ? 'ETH代币'
                          : 'TRON代币'}
                  </Text>
                }
              />

              <div style={{ textAlign: 'right' }}>
                <div style={{ marginBottom: '8px' }}>
                  <Text
                    strong
                    style={{
                      fontSize: '18px',
                      color: getAssetColor(item.chain),
                      fontWeight: 600,
                    }}
                  >
                    {hideBalances ? '******' : `${item.balance} ${item.currency}`}
                  </Text>
                </div>

                {/* 操作按钮，仅在悬停时显示 */}
                {/* <div
                  style={{
                    display: 'flex',
                    gap: '8px',
                    justifyContent: 'flex-end',
                    opacity: isHovered ? 1 : 0,
                    transform: isHovered ? 'translateY(0)' : 'translateY(10px)',
                    transition: 'all 0.3s ease',
                  }}
                >
                  <Tooltip title="发送">
                    <Button
                      type="text"
                      size="small"
                      icon={<SendOutlined />}
                      style={{ color: getAssetColor(item.chain) }}
                    />
                  </Tooltip>
                  <Tooltip title="交易">
                    <Button
                      type="text"
                      size="small"
                      icon={<SwapOutlined />}
                      style={{ color: getAssetColor(item.chain) }}
                    />
                  </Tooltip>
                  <Tooltip title="详情">
                    <Button
                      type="text"
                      size="small"
                      icon={<ArrowRightOutlined />}
                      style={{ color: getAssetColor(item.chain) }}
                    />
                  </Tooltip>
                </div> */}
              </div>
            </List.Item>
          );
        }}
        locale={{
          emptyText: (
            <div style={{ padding: '60px 0', textAlign: 'center' }}>
              <div
                className="pulse"
                style={{
                  marginBottom: '24px',
                  opacity: 0.7,
                  width: '80px',
                  height: '80px',
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  margin: '0 auto 24px'
                }}
              >
                <WalletOutlined style={{ fontSize: '40px', color: '#1890ff' }} />
              </div>
              <Title level={4} style={{ marginBottom: '8px', color: '#595959' }}>暂无资产数据</Title>
              {/* <Text type="secondary" style={{ fontSize: '14px' }}>您可以点击"添加资产"开始管理您的数字资产</Text> */}
              {/* <div style={{ marginTop: '24px' }}>
                <Button type="primary"  icon={<PlusOutlined />}>添加资产</Button>
              </div> */}
            </div>
          )
        }}
      />
    </Card>
  );
};

export default AssetsList;
