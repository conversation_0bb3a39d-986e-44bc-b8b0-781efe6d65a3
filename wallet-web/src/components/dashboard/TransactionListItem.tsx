import React from 'react';
import { List, Button, Typography, Tag } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { Transaction } from '../../types/transaction';
import { formatUnixTimestamp } from '../../utils/format';
import { getTransactionTypeInfo, getStatusTag } from './transactionDisplayUtils';
// import { openInExplorer } from '../../utils/explorerUtils'; // No longer used here
import styles from './RecentTransactions.module.css'; // 假设样式可以复用

const { Text } = Typography;

interface TransactionListItemProps {
  transaction: Transaction;
  hideBalances: boolean;
  onViewDetailsClick: (transaction: Transaction) => void;
}

const TransactionListItem: React.FC<TransactionListItemProps> = ({
  transaction,
  hideBalances,
  onViewDetailsClick,
}) => {
  const typeInfo = getTransactionTypeInfo(transaction.transaction_type || '');

  // 移除内部的 handleShowDetails，直接使用父组件传递的 onViewDetailsClick
  // const handleShowDetails = () => {
  //   if (transaction.transaction_hash) {
  //     openInExplorer('tx', transaction.transaction_hash, transaction.chain || '');
  //   } else {
  //     console.warn('Transaction hash is missing, cannot open in explorer. Modal display should be handled by parent.');
  //   }
  // };

  return (
    <List.Item
      key={transaction.id || transaction.transaction_id || transaction.transaction_hash}
      actions={[
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => onViewDetailsClick(transaction)} // 调用父组件传递的回调
        >
          详情
        </Button>,
      ]}
      className={styles.transactionItem}
    >
      <List.Item.Meta
        avatar={
          <div
            className={styles.typeIconWrapper}
            style={{ backgroundColor: `var(--ant-${typeInfo.color}-6)` }}
          >
            {typeInfo.icon}
          </div>
        }
        title={
          <>
            <Tag color={typeInfo.color}>{typeInfo.text}</Tag>
            <span className={styles.amount}>
              {hideBalances
                ? '******'
                : `${transaction.amount || '0'} ${transaction.token_name || transaction.chain || ''}`}
            </span>
            {getStatusTag(transaction.transaction_status || '')}
          </>
        }
        description={<Text type="secondary">{formatUnixTimestamp(transaction.transaction_time)}</Text>}
      />
    </List.Item>
  );
};

export default TransactionListItem;