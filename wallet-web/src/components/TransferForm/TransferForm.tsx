import React from 'react';
import { Form, Input, Button, InputNumber, message } from 'antd';
import { SendOutlined } from '@ant-design/icons';
import { useAppDispatch } from '../../hooks/useAppDispatch';
import { transferFunds } from '../../store/slices/walletSlice';
import { TransferRequest } from '../../types/wallet';
import styles from './TransferForm.module.css';

interface TransferFormProps {
  onSuccess?: () => void;
  loading?: boolean;
}

const TransferForm: React.FC<TransferFormProps> = ({ onSuccess, loading = false }) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();

  const handleSubmit = async (values: TransferRequest) => {
    try {
      await dispatch(transferFunds(values)).unwrap();
      message.success('转账成功');
      form.resetFields();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      message.error(error.message || '转账失败，请稍后再试');
    }
  };

  return (
    <div className={styles.transferForm}>
      <h3 className={styles.formTitle}>转账</h3>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="toWalletId"
          label="归集钱包ID"
          rules={[
            { required: true, message: '请输入归集钱包ID' },
            { min: 6, message: '钱包ID至少6个字符' },
          ]}
        >
          <Input placeholder="请输入归集钱包ID" />
        </Form.Item>

        <Form.Item name="amount" label="转账金额" rules={[{ required: true, message: '请输入转账金额' }]}>
          <InputNumber
            className={styles.amountInput}
            min={0.01}
            step={0.01}
            precision={2}
            placeholder="请输入转账金额"
          />
        </Form.Item>

        <Form.Item name="description" label="转账说明">
          <Input.TextArea placeholder="请输入转账说明（选填）" rows={3} />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SendOutlined />} loading={loading} block>
            确认转账
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default TransferForm;
