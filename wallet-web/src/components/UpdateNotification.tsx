import React from 'react';
import { Button, notification } from 'antd';
import { CloudDownloadOutlined, SyncOutlined } from '@ant-design/icons';
import useElectron from '../hooks/useElectron';

const UpdateNotification: React.FC = () => {
  const { isElectron, isUpdateAvailable, isUpdateDownloaded, installUpdate } = useElectron();

  // Show notification when update is available
  React.useEffect(() => {
    if (isElectron && isUpdateAvailable && !isUpdateDownloaded) {
      notification.info({
        message: '有新版本可用',
        description: '新版本正在下载中，下载完成后将通知您安装。',
        duration: 5,
        icon: <CloudDownloadOutlined style={{ color: '#1890ff' }} />,
      });
    }
  }, [isElectron, isUpdateAvailable, isUpdateDownloaded]);

  // Show notification when update is downloaded
  React.useEffect(() => {
    if (isElectron && isUpdateDownloaded) {
      notification.success({
        message: '更新已准备就绪',
        description: '新版本已下载完成，点击安装按钮重启应用并安装更新。',
        duration: 0, // Don't auto-close
        btn: (
          <Button type="primary" onClick={installUpdate}>
            立即安装
          </Button>
        ),
        icon: <SyncOutlined style={{ color: '#52c41a' }} />,
      });
    }
  }, [isElectron, isUpdateDownloaded, installUpdate]);

  // This component doesn't render anything visible
  return null;
};

export default UpdateNotification;
