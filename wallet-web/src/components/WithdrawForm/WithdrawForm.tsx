import React from 'react';
import { Form, Input, Button, InputNumber, message } from 'antd';
import { BankOutlined } from '@ant-design/icons';
import { useAppDispatch } from '../../hooks/useAppDispatch';
import { withdrawFunds } from '../../store/slices/walletSlice';
import { WithdrawRequest } from '../../types/wallet';
import styles from './WithdrawForm.module.css';

interface WithdrawFormProps {
  onSuccess?: () => void;
  loading?: boolean;
}

const WithdrawForm: React.FC<WithdrawFormProps> = ({ onSuccess, loading = false }) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();

  const handleSubmit = async (values: WithdrawRequest) => {
    try {
      await dispatch(withdrawFunds(values)).unwrap();
      message.success('提款申请已提交，等待处理');
      form.resetFields();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      message.error(error.message || '提款失败，请稍后再试');
    }
  };

  return (
    <div className={styles.withdrawForm}>
      <h3 className={styles.formTitle}>提款</h3>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item name="amount" label="提款金额" rules={[{ required: true, message: '请输入提款金额' }]}>
          <InputNumber className={styles.amountInput} min={10} step={10} precision={2} placeholder="请输入提款金额" />
        </Form.Item>

        <Form.Item
          name="bankAccount"
          label="银行账户"
          rules={[
            { required: true, message: '请输入银行账户' },
            { min: 10, message: '银行账户至少10个字符' },
          ]}
        >
          <Input placeholder="请输入银行账户" />
        </Form.Item>

        <Form.Item name="description" label="提款说明">
          <Input.TextArea placeholder="请输入提款说明（选填）" rows={3} />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<BankOutlined />} loading={loading} block>
            确认提款
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default WithdrawForm;
