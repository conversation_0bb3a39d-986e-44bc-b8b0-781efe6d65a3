import React from 'react';
import { Card, Typography, Tag, Space } from 'antd';
import { useConfig } from '../hooks/useConfig';

const { Title, Text } = Typography;

/**
 * Component to display current configuration
 * Useful for debugging and development
 */
const ConfigDisplay: React.FC = () => {
  const { config, loading, error, apiUrl } = useConfig();

  return (
    <Card title="Current Configuration" style={{ margin: '16px 0' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>Status: </Text>
          {loading && <Tag color="blue">Loading</Tag>}
          {error && <Tag color="red">Error: {error}</Tag>}
          {config && !loading && !error && <Tag color="green">Loaded</Tag>}
        </div>
        
        <div>
          <Text strong>API URL: </Text>
          <Text code>{apiUrl}</Text>
        </div>

        {config && (
          <div>
            <Title level={5}>Full Configuration:</Title>
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '8px', 
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              {JSON.stringify(config, null, 2)}
            </pre>
          </div>
        )}
      </Space>
    </Card>
  );
};

export default ConfigDisplay;
