import { useState, useEffect } from 'react';
import { useAppDispatch } from '../../hooks/useAppDispatch';
import { useAppSelector } from '../../hooks/useAppSelector';
import { fetchWalletAddresses } from '../../store/slices/walletSlice';
import { useMemoizedFn } from 'ahooks';

export interface WalletAddressFilters {
  type?: string;
  status?: string;
  tags?: string[];
  search?: string;
}

export const useWalletAddressData = () => {
  const dispatch = useAppDispatch();
  const { walletAddresses, totalWalletAddresses, loading } = useAppSelector((state) => state.wallet);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState<Omit<WalletAddressFilters, 'search'>>({});

  const loadWalletAddresses = useMemoizedFn(() => {
    const currentFilters: WalletAddressFilters = {
      ...filters,
    };
    if (searchText) {
      currentFilters.search = searchText;
    }
    dispatch(
      fetchWalletAddresses({
        page: currentPage,
        pageSize,
        filters: currentFilters,
      }),
    );
  });

  const handleSearch = useMemoizedFn(() => {
    setCurrentPage(1); // 重置到第一页
    // loadWalletAddresses 会在 useEffect 中因为 currentPage 变化而被调用，或者可以显式调用
    // 为了确保立即反馈，这里可以显式调用，但要注意 useEffect 的依赖可能导致重复调用
    // 最佳实践是让 useEffect 处理依赖变化，但如果需要立即触发，则需小心
    // 此处选择在 useEffect 中处理，因为 searchText 变化会触发
  });

  const handleReset = useMemoizedFn(() => {
    setSearchText('');
    setFilters({});
    setCurrentPage(1);
    // dispatch(
    //   fetchWalletAddresses({
    //     page: 1,
    //     pageSize,
    //     filters: {}, // 确保传递空对象
    //   }),
    // );
    // loadWalletAddresses 会在 useEffect 中因为 filters 和 currentPage 变化而被调用
  });

  useEffect(() => {
    loadWalletAddresses();
  }, [currentPage, pageSize, filters, searchText, loadWalletAddresses]); // 添加 searchText 到依赖

  return {
    walletAddresses,
    totalWalletAddresses,
    loading,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    searchText,
    setSearchText,
    filters,
    setFilters,
    handleSearch, // searchText 变化时，useEffect 会自动触发 loadWalletAddresses
    handleReset,
    loadWalletAddresses, // 也导出 loadWalletAddresses 供外部手动刷新
  };
};