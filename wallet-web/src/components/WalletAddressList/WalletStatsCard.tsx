import React from 'react';
import { Card, Row, Col, Statistic, Typography } from 'antd';
import { WalletOutlined, PieChartOutlined } from '@ant-design/icons';
import styles from './WalletAddressList.module.css'; // Reuse styles or create specific ones

const { Text } = Typography;

interface StatisticsData {
  totalBalance: string;
  activeWallets: number;
  mainWallets: number;
  savingsWallets: number;
  investmentWallets: number;
}

interface WalletStatsCardProps {
  statistics: StatisticsData;
  totalWalletAddresses: number;
}

const WalletStatsCard: React.FC<WalletStatsCardProps> = ({ statistics, totalWalletAddresses }) => {
  return (
    <Card
      className={`${styles.statsCard} fade-in hover-shadow`}
      style={{
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
        border: 'none',
        overflow: 'hidden',
      }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <PieChartOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>钱包资产统计</span>
        </div>
      }
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '24px',
        }
      }}
    >
      <Row gutter={24}>
        <Col span={4}>
          <Statistic title="钱包总数" value={totalWalletAddresses} prefix={<WalletOutlined />} />
        </Col>
        <Col span={5}>
          <Statistic title="总余额" value={statistics.totalBalance} precision={2} suffix="CNY" />
        </Col>
        <Col span={5}>
          <Statistic title="活跃钱包" value={statistics.activeWallets} suffix={`/ ${totalWalletAddresses}`} />
        </Col>
        <Col span={10}>
          <Row gutter={16}>
            <Col span={8}>
              <Statistic title="主钱包" value={statistics.mainWallets} valueStyle={{ color: '#1890ff' }} />
            </Col>
            <Col span={8}>
              <Statistic title="储蓄钱包" value={statistics.savingsWallets} valueStyle={{ color: '#52c41a' }} />
            </Col>
            <Col span={8}>
              <Statistic title="投资钱包" value={statistics.investmentWallets} valueStyle={{ color: '#faad14' }} />
            </Col>
          </Row>
        </Col>
      </Row>
    </Card>
  );
};

export default WalletStatsCard;