import React from 'react';
import { Input, Button, Select } from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import styles from './WalletAddressList.module.css'; // Reuse styles or create specific ones
import { WalletAddressFilters } from './useWalletAddressData'; // Import for filters type

const { Option } = Select;

interface WalletTableToolbarProps {
  searchText: string;
  setSearchText: (text: string) => void;
  filters: Omit<WalletAddressFilters, 'search'>;
  setFilters: (filters: Omit<WalletAddressFilters, 'search'>) => void;
  handleSearch: () => void;
  handleReset: () => void;
  onAddNewWallet: () => void; // Callback for "新建钱包"
  onRefresh: () => void;
  onExport: () => void;
  loading?: boolean; // For refresh button
}

const WalletTableToolbar: React.FC<WalletTableToolbarProps> = ({
  searchText,
  setSearchText,
  filters,
  setFilters,
  handleSearch,
  handleReset,
  onAddNewWallet,
  onRefresh,
  onExport,
  loading,
}) => {
  return (
    <div className={styles.tableHeader}>
      <div className={styles.searchFilters}>
        <Input
          placeholder="搜索钱包地址或名称"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onPressEnter={handleSearch} // Allow Enter key to trigger search
          prefix={<SearchOutlined />}
          style={{ width: 250 }}
        />
        <Select
          placeholder="钱包类型"
          allowClear
          style={{ width: 120 }}
          value={filters.type}
          onChange={(value) => setFilters({ ...filters, type: value })}
        >
          <Option value="main">主钱包</Option>
          <Option value="savings">储蓄钱包</Option>
          <Option value="investment">投资钱包</Option>
        </Select>
        <Select
          placeholder="状态"
          allowClear
          style={{ width: 120 }}
          value={filters.status}
          onChange={(value) => setFilters({ ...filters, status: value })}
        >
          <Option value="active">活跃</Option>
          <Option value="inactive">非活跃</Option>
        </Select>
        <Button type="primary" onClick={handleSearch} icon={<FilterOutlined />}>
          筛选
        </Button>
        <Button onClick={handleReset}>重置</Button>
      </div>
      <div className={styles.actions}>
        <Button type="primary" icon={<PlusOutlined />} style={{ marginRight: 8 }} onClick={onAddNewWallet}>
          新建钱包
        </Button>
        <Button
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          loading={loading}
          style={{ marginRight: 8 }}
        >
          刷新
        </Button>
        <Button icon={<ExportOutlined />} onClick={onExport}>
          导出
        </Button>
      </div>
    </div>
  );
};

export default WalletTableToolbar;