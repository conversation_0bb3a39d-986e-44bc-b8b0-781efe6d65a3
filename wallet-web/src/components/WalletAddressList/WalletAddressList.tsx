import React, { useMemo } from 'react'; // Added useMemo
import {
  Table,
  Card,
  // Input, // Moved to Tool<PERSON>
  Button, // Still needed for Dropdown action button
  Space,
  Tag,
  // Select, // Moved to Toolbar
  Dropdown,
  Typography,
} from 'antd';
import {
  // SearchOutlined, // Moved to Toolbar
  // FilterOutlined, // Moved to Toolbar
  EllipsisOutlined, // Still needed for Dropdown action button
  // ExportOutlined, // Moved to Toolbar
  // ReloadOutlined, // Moved to Toolbar
  // PlusOutlined, // Moved to Toolbar
  WalletOutlined,
} from '@ant-design/icons';
// Removed useAppDispatch, useAppSelector, fetchWalletAddresses as they are in the hook
import { WalletAddressItem } from '../../mock/walletData';
import { copyToClipboard } from '../../utils/clipboardUtils';
import { exportDataToCsv } from '../../utils/exportUtils'; // Keep for handleExport
import styles from './WalletAddressList.module.css';
import { ColumnsType } from 'antd/es/table';
// Removed useMemoizedFn as it's used within the hook
import { useWalletAddressData } from './useWalletAddressData'; // Import the new hook
import WalletStatsCard from './WalletStatsCard'; // Import the new Stats Card component
import WalletTableToolbar from './WalletTableToolbar'; // Import the new Toolbar component
import NativeFilterSelect from '../common/NativeFilterSelect/NativeFilterSelect'; // 新增导入

// const { Option } = Select; // Option is now only used in WalletTableToolbar
const { Text } = Typography;

const WalletAddressList: React.FC = () => {
  const {
    walletAddresses,
    totalWalletAddresses,
    loading,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    searchText,
    setSearchText,
    filters,
    setFilters,
    handleSearch, // Use from hook
    handleReset,  // Use from hook
    loadWalletAddresses,
  } = useWalletAddressData();

  const handleExport = () => {
    const dataToExport = walletAddresses.map((addr) => ({
      address: addr.address,
      name: addr.name,
      balance: addr.balance,
      currency: addr.currency,
      type: addr.type, // 类型和状态的转换可以在 headersMap 中处理或保持原样
      status: addr.status,
      createdAt: new Date(addr.createdAt).toLocaleString(),
      lastUsed: new Date(addr.lastUsed).toLocaleString(),
      tags: addr.tags?.join(' | ') || '', // 将标签数组转换为字符串
    }));

    const headersMap = {
      address: '地址',
      name: '名称',
      balance: '余额',
      currency: '货币',
      type: '类型',
      status: '状态',
      createdAt: '创建时间',
      lastUsed: '最后使用',
      tags: '标签',
    };

    // 使用通用的导出函数
    exportDataToCsv(dataToExport, '钱包地址列表', headersMap);
  };

  // 计算统计数据
  // This calculation should ideally also be memoized or moved if complex and walletAddresses changes frequently
  // For now, keeping it here but using useMemo for optimization.
  const statistics = React.useMemo(() => ({
    totalBalance: walletAddresses.reduce((sum, addr) => sum + parseFloat(addr.balance), 0).toFixed(2),
    activeWallets: walletAddresses.filter((addr) => addr.status === 'active').length,
    mainWallets: walletAddresses.filter((addr) => addr.type === 'main').length,
    savingsWallets: walletAddresses.filter((addr) => addr.type === 'savings').length,
    investmentWallets: walletAddresses.filter((addr) => addr.type === 'investment').length,
  }), [walletAddresses]);

  // useEffect for loading data is now inside useWalletAddressData hook

  const columns: ColumnsType<WalletAddressItem> = useMemo(() => [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: WalletAddressItem) => (
        <Space>
          <Text strong>{text}</Text>
          {record.tags &&
            record.tags.map((tag: string) => (
              <Tag
                key={tag}
                color={tag === '默认' ? 'blue' : tag === '常用' ? 'green' : tag === '商业' ? 'orange' : 'purple'}
              >
                {tag}
              </Tag>
            ))}
        </Space>
      ),
    },

    // {
    //     title: '地址',
    //     dataIndex: 'address',
    //     key: 'address',
    //     render: (text: string) => (
    //         <Space>
    //             <Text ellipsis style={{ width: 180 }}>{text}</Text>
    //             <Button
    //                 type="text"
    //                 icon={<CopyOutlined />}
    //                 onClick={() => copyToClipboard(text)}
    //                 size="small"
    //             />
    //         </Space>
    //     )
    // },
    // {
    //     title: '余额',
    //     dataIndex: 'balance',
    //     key: 'balance',
    //     render: (text: string, record: WalletAddressItem) => (
    //         <Text strong>{`${text} ${record.currency}`}</Text>
    //     ),
    //     sorter: (a: WalletAddressItem, b: WalletAddressItem) =>
    //         parseFloat(a.balance) - parseFloat(b.balance)
    // },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (text: string) => {
        let color = '';
        let label = '';

        switch (text) {
          case 'main':
            color = 'blue';
            label = '主钱包';
            break;
          case 'savings':
            color = 'green';
            label = '储蓄钱包';
            break;
          case 'investment':
            color = 'gold';
            label = '投资钱包';
            break;
          default:
            color = 'default';
            label = text;
        }

        return <Tag color={color}>{label}</Tag>;
      },
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
        const typeOptions = [
          { text: '主钱包', value: 'main' },
          { text: '储蓄钱包', value: 'savings' },
          { text: '投资钱包', value: 'investment' },
        ];
        return (
          <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
            <NativeFilterSelect
              id="wallet-address-type-filter"
              placeholder="选择类型"
              options={typeOptions}
              value={selectedKeys[0] as string | number | undefined}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              style={{ marginBottom: 8, display: 'block', width: 188 }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  confirm();
                  close();
                }}
                size="small"
                style={{ width: 90 }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  if (clearFilters) {
                    clearFilters();
                  }
                  setSelectedKeys([]);
                  confirm();
                  close();
                }}
                size="small"
                style={{ width: 90 }}
              >
                重置
              </Button>
            </Space>
          </div>
        );
      },
      onFilter: (value: any, record: WalletAddressItem) => record.type === value,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => (
        <Tag color={text === 'active' ? 'success' : 'error'}>{text === 'active' ? '活跃' : '非活跃'}</Tag>
      ),
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
        const statusOptions = [
          { text: '活跃', value: 'active' },
          { text: '非活跃', value: 'inactive' },
        ];
        return (
          <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
            <NativeFilterSelect
              id="wallet-address-status-filter"
              placeholder="选择状态"
              options={statusOptions}
              value={selectedKeys[0] as string | number | undefined}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              style={{ marginBottom: 8, display: 'block', width: 188 }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  confirm();
                  close();
                }}
                size="small"
                style={{ width: 90 }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  if (clearFilters) {
                    clearFilters();
                  }
                  setSelectedKeys([]);
                  confirm();
                  close();
                }}
                size="small"
                style={{ width: 90 }}
              >
                重置
              </Button>
            </Space>
          </div>
        );
      },
      onFilter: (value: any, record: WalletAddressItem) => record.status === value,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleString(),
      sorter: (a: WalletAddressItem, b: WalletAddressItem) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: '最后使用',
      dataIndex: 'lastUsed',
      key: 'lastUsed',
      render: (text: string) => new Date(text).toLocaleString(),
      sorter: (a: WalletAddressItem, b: WalletAddressItem) =>
        new Date(a.lastUsed).getTime() - new Date(b.lastUsed).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: WalletAddressItem) => (
        <Dropdown
          menu={{
            items: [
              { key: 'details', label: '查看详情' },
              { key: 'edit', label: '编辑' },
              { key: 'transfer', label: '转账' },
              { key: 'copy', label: '复制地址', onClick: () => copyToClipboard(record.address) },
              record.status === 'active' ? { key: 'deactivate', label: '停用' } : { key: 'activate', label: '激活' },
            ],
          }}
        >
          <Button type="text" icon={<EllipsisOutlined />} />
        </Dropdown>
      ),
    },
  ], [copyToClipboard]); // copyToClipboard is stable, but good practice if it were not

  return (
    <div className={styles.walletAddressList}>
      <WalletStatsCard statistics={statistics} totalWalletAddresses={totalWalletAddresses} />

      <Card
        className={styles.tableCard}
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <WalletOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontSize: '16px', fontWeight: 600 }}>钱包地址列表</span>
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #f0f0f0',
            padding: '16px 24px',
          },
          body: {
            padding: '24px',
          }
        }}>
        <WalletTableToolbar
          searchText={searchText}
          setSearchText={setSearchText}
          filters={filters}
          setFilters={setFilters}
          handleSearch={handleSearch}
          handleReset={handleReset}
          onAddNewWallet={() => {
            // Placeholder for "新建钱包" functionality
            console.log('新建钱包 clicked');
            // Typically, this would open a modal or navigate to a form
          }}
          onRefresh={loadWalletAddresses}
          onExport={handleExport}
          loading={loading}
        />

        <Table
          columns={columns}
          dataSource={walletAddresses}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalWalletAddresses,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个钱包地址`,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              if (pageSize) setPageSize(pageSize);
            },
          }}
        />
      </Card>
    </div>
  );
};

export default WalletAddressList;
