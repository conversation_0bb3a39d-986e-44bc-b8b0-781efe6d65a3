import { useState, useCallback } from 'react';
import { message } from 'antd';
import moment from 'moment';
import { getAddress } from '../../../services/api/address/address';
import { GetWalletTransactionRecordStatus } from '../../../services/api/model/getWalletTransactionRecordStatus';
import { GetWalletTransactionRecordCoin } from '../../../services/api/model/getWalletTransactionRecordCoin';

import type { GetWalletTransactionRecordSortOrder } from '../../../services/api/model/getWalletTransactionRecordSortOrder';
import {
  GetWalletTransactionRecordChain,
  GetWalletTransactionRecordParams,
  GetWalletTransactionRecordType,
} from '../../../services/api/model';
import { Transaction } from '../../../types/transaction';

const { getWalletTransactionRecord, postWalletExportTransactionRecord } = getAddress();

const MIN_LOADING_TIME_MS = 500;


export interface FiltersState {
  type?: GetWalletTransactionRecordType;
  status?: GetWalletTransactionRecordStatus;
  dateRange?: [moment.Moment, moment.Moment] | null;
  chain?: GetWalletTransactionRecordChain;
  coin?: GetWalletTransactionRecordCoin;
}

export interface PaginationState {
  current: number;
  pageSize: number;
  total: number;
}

const useTransactionData = (
  initialPagination: PaginationState,
  initialFilters: FiltersState,
  initialSearchText: string = ''
) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationState>(initialPagination);
  const [searchText, setSearchText] = useState(initialSearchText);
  const [filters, setFilters] = useState<FiltersState>(initialFilters);
  const [exportLoading, setExportLoading] = useState(false);

  const loadTransactions = useCallback(async (currentPage?: number, currentLimit?: number) => {
    setLoading(true);
    const startTime = Date.now();
    try {
      let dateRangeParam = undefined;
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        dateRangeParam = `${filters.dateRange[0].format('YYYY-MM-DD')},${filters.dateRange[1].format('YYYY-MM-DD')}`;
      }

      const params: GetWalletTransactionRecordParams = {
        page: currentPage || pagination.current,
        limit: currentLimit || pagination.pageSize,
        address: searchText || undefined,
        type: filters.type || undefined,
        status: filters.status,
        coin: filters.coin || undefined,
        chain: filters.chain || undefined,
        date_range: dateRangeParam,
        sort_field: 'transaction_time',
        sort_order: 'desc' as GetWalletTransactionRecordSortOrder,
      };

      Object.keys(params).forEach((key) => {
        if (params[key as keyof typeof params] === undefined) {
          delete params[key as keyof typeof params];
        }
      });

      const response = await getWalletTransactionRecord(params);

      if (response && response.list) {
        setTransactions(response.list as Transaction[]);
        if (response.page) {
          setPagination(prev => ({
            ...prev,
            current: response.page?.current || 1,
            total: response.page?.total || 0,
          }));
        }
      } else {
        setTransactions([]);
        setPagination(prev => ({ ...prev, total: 0 }));
      }
    } catch (error) {
      console.error('获取交易记录失败:', error);
      message.error('获取交易记录失败');
      setTransactions([]);
      setPagination(prev => ({ ...prev, total: 0 }));
    } finally {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime < MIN_LOADING_TIME_MS) {
        setTimeout(() => setLoading(false), MIN_LOADING_TIME_MS - elapsedTime);
      } else {
        setLoading(false);
      }
    }
  }, [filters, searchText, pagination]);


  const handleSearch = (newSearchText: string) => {
    setSearchText(newSearchText);
    setPagination(prev => ({ ...prev, current: 1 }));
    // loadTransactions will be called by useEffect in component due to searchText change
  };

  const handleFilterChange = (newFilters: Partial<FiltersState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, current: 1 }));
    // loadTransactions will be called by useEffect in component due to filters change
  };

  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }));
    // loadTransactions will be called by useEffect in component due to pagination change
  };

  const handleResetFilters = () => {
    setSearchText('');
    setFilters({});
    setPagination(prev => ({ ...prev, current: 1 }));
    // loadTransactions will be called by useEffect in component
  };

  const handleExport = async () => {
    setExportLoading(true);
    try {
      let dateRangeParam = undefined;
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        dateRangeParam = `${filters.dateRange[0].format('YYYY-MM-DD')},${filters.dateRange[1].format('YYYY-MM-DD')}`;
      }

      const params: GetWalletTransactionRecordParams = {
        page: 1, // For export, start from page 1
        limit: 10000, // Assuming a large enough limit for export, or API handles full export
        address: searchText || undefined,
        type: filters.type || undefined,
        status: filters.status,
        coin: filters.coin,
        chain: filters.chain,
        date_range: dateRangeParam,
        sort_field: 'transaction_time',
        sort_order: 'desc' as any, // API model might need update for sort order type
      };

      Object.keys(params).forEach((key) => {
        if (params[key as keyof typeof params] === undefined) {
          delete params[key as keyof typeof params];
        }
      });

      await postWalletExportTransactionRecord(params);
      message.success('导出请求已发送，文件将自动下载');
    } catch (error) {
      console.error('导出交易记录失败:', error);
      message.error('导出交易记录失败');
    } finally {
      setExportLoading(false);
    }
  };


  return {
    transactions,
    loading,
    pagination,
    searchText,
    filters,
    exportLoading,
    loadTransactions,
    handleSearch,
    handleFilterChange,
    handlePaginationChange,
    handleResetFilters,
    handleExport,
    setFilters, // expose setFilters directly for RangePicker
    setSearchText, // expose setSearchText for direct input control
  };
};

export default useTransactionData;
