.transactionList {
    margin-bottom: 24px;
}

.statsCard {
    margin-bottom: 24px;
}

.tableCard {
    margin-bottom: 24px;
}

.tableHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.searchFilters {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.txHashContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.txHash {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.statusBadge {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.detailsDrawer .ant-descriptions-item {
    padding-bottom: 12px;
}

@media (max-width: 768px) {
    .tableHeader {
        flex-direction: column;
        align-items: flex-start;
    }

    .searchFilters {
        margin-bottom: 16px;
        width: 100%;
    }

    .actions {
        width: 100%;
    }
}