import React from 'react';
import { Tag, Toolt<PERSON>, Button, Space } from 'antd'; // Removed message
import { ColumnsType } from 'antd/es/table';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  SwapOutlined,
  EyeOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { Transaction } from '../../../types/transaction'; // 调整路径
import {
  GetWalletTransactionRecordStatus,
  GetWalletTransactionRecordType,
  // DemogfInternalUtilityCryptoTronCost, // Removed as fee logic is handled by parent
} from '../../../services/api/model'; // 调整路径
// import { getNode } from '../../../services/api/node/node'; // Removed as fee logic is handled by parent

interface ColumnDefinitionParams {
  openInExplorer: (type: 'tx' | 'address', value: string, network: string) => void;
  showTransactionDetails: (record: Transaction) => void;
  onShowFeeDetails: (record: Transaction) => void; // 用于触发手续费弹窗和加载数据
}

export const getTransactionTableColumns = ({
  openInExplorer,
  showTransactionDetails,
  onShowFeeDetails,
}: ColumnDefinitionParams): ColumnsType<Transaction> => [
  {
    title: '类型',
    dataIndex: 'transaction_type',
    key: 'transaction_type',
    render: (text: string, record: Transaction) => {
      let icon;
      let color;
      let label;

      switch (text) {
        case GetWalletTransactionRecordType.deposit:
          icon = <ArrowDownOutlined />;
          color = 'green';
          label = '充值';
          break;
        case GetWalletTransactionRecordType.miner_fee:
          icon = <ArrowUpOutlined />;
          color = 'red';
          label = '矿工费';
          break;
        case GetWalletTransactionRecordType.withdraw:
          icon = <ArrowUpOutlined />;
          color = 'orange';
          label = '归集';
          break;
        case GetWalletTransactionRecordType.transfer:
          icon = <SwapOutlined />;
          color = 'blue';
          label = '转账';
          break;
        default:
          icon = <SwapOutlined />;
          color = 'default';
          label = text || '-';
      }
      return (
        <Tag icon={icon} color={color}>
          {label}
        </Tag>
      );
    },
  },
  {
    title: '交易哈希',
    dataIndex: 'transaction_hash',
    key: 'transaction_hash',
    ellipsis: {
      showTitle: false,
    },
    render: (text, record) => {
      if (!text && record.block_hash) {
        text = record.block_hash;
      }
      return text ? (
        <Tooltip title={text}>
          <Button type="link" onClick={() => openInExplorer('tx', text, record.chain || '')}>
            {text.substring(0, 10)}...{text.substring(text.length - 10)}
          </Button>
        </Tooltip>
      ) : (
        '-'
      );
    },
  },
  {
    title: '链',
    key: 'chain',
    dataIndex: 'chain',
    render: (text, record) => (
      <Space>
        <Tag color={text === 'ETH' ? 'blue' : text === 'TRON' ? 'red' : 'gold'}>{text || record.network || '-'}</Tag>
      </Space>
    ),
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    render: (text: string | undefined, record: Transaction) => {
      const amount = text || '0';
      const tokenDisplay = record.token_name || record.currency || record.chain || '';
      return (
        <span // Using span instead of Text for simplicity here, can be Text if Typography.Text is preferred
          style={{
            fontWeight: 'bold',
            color:
              record.transaction_type === GetWalletTransactionRecordType.deposit
                ? '#52c41a'
                : record.transaction_type === GetWalletTransactionRecordType.miner_fee
                  ? '#f5222d'
                  : '#1890ff',
          }}
        >
          {record.transaction_type === GetWalletTransactionRecordType.deposit ? '+' : record.transaction_type === GetWalletTransactionRecordType.miner_fee ? '-' : ''} {amount}{' '}
          {tokenDisplay}
        </span>
      );
    },
  },
  {
    title: '手续费',
    dataIndex: 'transaction_fee',
    key: 'transaction_fee',
    render: (text, record) => {
      const fee = text || record.fee || '-';
      const showFeeDetailsButton = record.chain !== 'ETH'; // Only show for non-ETH for now as per original logic

      return (
        <Space>
          {record.chain === 'ETH' && `${Number(fee).toFixed(6)} ${record.chain}`}
          {showFeeDetailsButton && (
            <Tooltip title="查看手续费详情">
              <InfoCircleOutlined
                onClick={() => onShowFeeDetails(record)}
                style={{ cursor: 'pointer', color: '#1890ff' }}
              />
            </Tooltip>
          )}
        </Space>
      );
    },
  },
  {
    title: '发送方',
    dataIndex: 'sender_address',
    key: 'sender_address',
    ellipsis: {
      showTitle: false,
    },
    render: (text, record) => {
      const address = text || record.from || '-';
      return address !== '-' ? (
        <Tooltip title={address}>
          <Button type="link" onClick={() => openInExplorer('address', address, record.chain || '')}>
            {address.substring(0, 6)}...{address.substring(address.length - 4)}
          </Button>
        </Tooltip>
      ) : (
        '-'
      );
    },
  },
  {
    title: '接收方',
    dataIndex: 'receiver_address',
    key: 'receiver_address',
    ellipsis: {
      showTitle: false,
    },
    render: (text, record) => {
      const address = text || record.to || '-';
      return address !== '-' ? (
        <Tooltip title={address}>
          <Button type="link" onClick={() => openInExplorer('address', address, record.chain || '')}>
            {address.substring(0, 6)}...{address.substring(address.length - 4)}
          </Button>
        </Tooltip>
      ) : (
        '-'
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'transaction_status',
    key: 'transaction_status',
    render: (status: GetWalletTransactionRecordStatus) => {
      const statusMap: Record<GetWalletTransactionRecordStatus, { color: string; text: string }> = {
        [GetWalletTransactionRecordStatus.completed]: { color: 'green', text: '完成' },
        [GetWalletTransactionRecordStatus.failed]: { color: 'red', text: '失败' },
        [GetWalletTransactionRecordStatus.pending]: { color: 'yellow', text: '处理中' },
        [GetWalletTransactionRecordStatus.canceled]: { color: 'red', text: '取消' },
        [GetWalletTransactionRecordStatus.processing]: { color: 'yellow', text: '处理中' },
      };
      const { color, text } = statusMap[status] || { color: 'default', text: status || '-' };
      return <Tag color={color}>{text}</Tag>;
    },
  },
  {
    title: '时间',
    dataIndex: 'transaction_time',
    key: 'transaction_time',
    render: (text) => {
      if (!text) return '-';
      try {
        return new Date(Number(text) * 1000).toLocaleString();
      } catch (e: any) {
        console.log(e.message);
        return '-';
      }
    },
  },
  {
    title: '区块号',
    dataIndex: 'block_number',
    key: 'block_number',
    render: (text) => text || '-',
    sorter: (a, b) => (a.block_number || 0) - (b.block_number || 0),
  },
  {
    title: '操作',
    key: 'action',
    render: (_, record) => (
      <Space size="middle">
        <Button type="link" onClick={() => showTransactionDetails(record)}>
          <EyeOutlined /> 详情
        </Button>
      </Space>
    ),
  },
];