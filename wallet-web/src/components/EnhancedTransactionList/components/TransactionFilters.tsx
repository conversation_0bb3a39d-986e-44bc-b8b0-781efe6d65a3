import React from 'react';
import { Input, Button, DatePicker } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import styles from '../EnhancedTransactionList.module.css'; // 复用父组件的样式
import { RangePickerProps } from 'antd/es/date-picker';
import moment, { Moment } from 'moment';
import dayjs, { Dayjs } from 'dayjs';
import {
  GetWalletTransactionRecordType,
  GetWalletTransactionRecordStatus,
  GetWalletTransactionRecordChain,
  GetWalletTransactionRecordCoin,
} from '../../../services/api/model'; // 调整路径
import { FiltersState } from '../hooks/useTransactionData'; // 调整路径

const { RangePicker } = DatePicker;

interface TransactionFiltersProps {
  searchText: string;
  filters: FiltersState;
  onSearchTextChange: (text: string) => void;
  onFilterChange: (newFilters: Partial<FiltersState>) => void;
  onDateRangeChange: (dates: [moment.Moment, moment.Moment] | null) => void;
  onSearch: () => void;
  onReset: () => void;
}

const TransactionFilters: React.FC<TransactionFiltersProps> = ({
  searchText,
  filters,
  onSearchTextChange,
  onFilterChange,
  onDateRangeChange,
  onSearch,
  onReset,
}) => {
  const momentToDayjs = (moments?: [Moment, Moment] | null): [Dayjs, Dayjs] | null => { // 参数类型接受 undefined
    if (!moments) return null;
    return [dayjs(moments[0].toISOString()), dayjs(moments[1].toISOString())];
  };

  const dayjsToMoment = (dayjsArray?: [Dayjs, Dayjs] | null): [Moment, Moment] | null => { // 参数类型接受 undefined
    if (!dayjsArray) return null;
    return [moment(dayjsArray[0].toISOString()), moment(dayjsArray[1].toISOString())];
  };

  const handleInternalDateRangeChange: RangePickerProps['onChange'] = (dates, dateStrings) => {
    onDateRangeChange(dayjsToMoment(dates as [Dayjs, Dayjs] | null)); // dates from AntD RangePicker can be [Dayjs | null, Dayjs | null] or null
  };

  const displayDateRange = momentToDayjs(filters.dateRange);

  return (
    <div className={styles.searchFilters}>
      <Input
        placeholder="搜索交易哈希或地址"
        value={searchText}
        onChange={(e) => onSearchTextChange(e.target.value)}
        onPressEnter={onSearch}
        prefix={<SearchOutlined />}
        style={{ width: 250 }}
      />
      <select
        style={{ width: 120, height: 32, marginLeft: 8, borderRadius: 2, borderColor: '#d9d9d9' }}
        value={filters.type || ''}
        onChange={(e) =>
          onFilterChange({ type: (e.target.value as GetWalletTransactionRecordType) || undefined })
        }
      >
        <option value="">交易类型</option>
        <option value={GetWalletTransactionRecordType.deposit}>充值</option>
        <option value={GetWalletTransactionRecordType.miner_fee}>矿工费</option>
        <option value={GetWalletTransactionRecordType.withdraw}>归集</option>
        <option value={GetWalletTransactionRecordType.transfer}>转账</option>
      </select>
      <select
        style={{ width: 120, height: 32, marginLeft: 8, borderRadius: 2, borderColor: '#d9d9d9' }}
        value={filters.status || ''}
        onChange={(e) =>
          onFilterChange({ status: (e.target.value as GetWalletTransactionRecordStatus) || undefined })
        }
      >
        <option value="">状态</option>
        <option value={GetWalletTransactionRecordStatus.completed}>已完成</option>
        <option value={GetWalletTransactionRecordStatus.pending}>处理中</option>
        <option value={GetWalletTransactionRecordStatus.processing}>处理中</option>
        <option value={GetWalletTransactionRecordStatus.failed}>失败</option>
      </select>
      <select
        style={{ width: 120, height: 32, marginLeft: 8, borderRadius: 2, borderColor: '#d9d9d9' }}
        value={filters.chain || ''}
        onChange={(e) =>
          onFilterChange({ chain: (e.target.value as GetWalletTransactionRecordChain) || undefined })
        }
      >
        <option value="">网络</option>
        <option value={GetWalletTransactionRecordChain.ETH}>{GetWalletTransactionRecordChain.ETH}</option>
        <option value={GetWalletTransactionRecordChain.TRON}>{GetWalletTransactionRecordChain.TRON}</option>
      </select>
      <select
        style={{ width: 120, height: 32, marginLeft: 8, borderRadius: 2, borderColor: '#d9d9d9' }}
        value={filters.coin || ''}
        onChange={(e) =>
          onFilterChange({ coin: (e.target.value as GetWalletTransactionRecordCoin) || undefined })
        }
      >
        <option value="">代币</option>
        <option value={GetWalletTransactionRecordCoin.ETH}>{GetWalletTransactionRecordCoin.ETH}</option>
        <option value={GetWalletTransactionRecordCoin.TRX}>{GetWalletTransactionRecordCoin.TRX}</option>
        <option value={GetWalletTransactionRecordCoin.USDT}>{GetWalletTransactionRecordCoin.USDT}</option>
      </select>
      <RangePicker
        onChange={handleInternalDateRangeChange}
        style={{ width: 240, marginLeft: 8 }}
        value={displayDateRange}
      />
      <Button type="primary" onClick={onSearch} icon={<FilterOutlined />} style={{ marginLeft: 8 }}>
        筛选
      </Button>
      <Button onClick={onReset} style={{ marginLeft: 8 }}>
        重置
      </Button>
    </div>
  );
};

export default TransactionFilters;