import React from 'react';
import { Card, Statistic, Row, Col } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, SwapOutlined } from '@ant-design/icons';
import styles from '../EnhancedTransactionList.module.css'; // 复用父组件的样式

interface StatisticsData {
  totalDeposit: string;
  totalTrxDeposit: string;
  totalEthDeposit: string;
  totalCollect: string;
  totalTrxCollect: string;
  totalEthCollect: string;
  gasCount: number;
  pendingCount: number;
}

interface TransactionStatsCardProps {
  statistics: StatisticsData;
}

const TransactionStatsCard: React.FC<TransactionStatsCardProps> = ({ statistics }) => {
  return (
    <Card className={styles.statsCard}>
      <Row gutter={24}>
        <Col span={6}>
          <Statistic
            title="USDT充值总额"
            value={statistics.totalDeposit}
            precision={4}
            valueStyle={{ color: '#3f8600' }}
            prefix={<ArrowDownOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="TRX充值总额"
            value={statistics.totalTrxDeposit}
            precision={4}
            valueStyle={{ color: '#3f8600' }}
            prefix={<ArrowDownOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="ETH充值总额"
            value={statistics.totalEthDeposit}
            precision={4}
            valueStyle={{ color: '#3f8600' }}
            prefix={<ArrowDownOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="矿工费订单数"
            value={statistics.gasCount}
            valueStyle={{ color: '#cf1322' }}
            prefix={<ArrowUpOutlined />}
          />
        </Col>
      </Row>
      <Row gutter={24} style={{ marginTop: 16 }}>
        <Col span={6}>
          <Statistic
            title="USDT归集总额"
            value={statistics.totalCollect}
            precision={4}
            valueStyle={{ color: '#1890ff' }}
            prefix={<SwapOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="TRX归集总额"
            value={statistics.totalTrxCollect}
            precision={4}
            valueStyle={{ color: '#1890ff' }}
            prefix={<SwapOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="ETH归集总额"
            value={statistics.totalEthCollect}
            precision={4}
            valueStyle={{ color: '#1890ff' }}
            prefix={<SwapOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic title="待处理交易" value={statistics.pendingCount} valueStyle={{ color: '#faad14' }} />
        </Col>
      </Row>
    </Card>
  );
};

export default TransactionStatsCard;