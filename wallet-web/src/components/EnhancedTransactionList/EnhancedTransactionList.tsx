import React, { useState, useEffect } from 'react'; // 移除了 useCallback
import {
  Table,
  Card,
  // Input, DatePicker are now used in TransactionFilters
  Button,
  // Space, // No longer used directly here
  // Tag, // No longer used directly here
  // Tooltip, // No longer used directly here
  message, // message is still used for fee loading errors
  Modal,
  Descriptions,
  // Statistic, Row, Col are now used in TransactionStatsCard
  Spin,
} from 'antd';
import CustomPagination from '../common/CustomPagination';
import {
  // SearchOutlined, FilterOutlined are now used in TransactionFilters
  ExportOutlined,
  ReloadOutlined,
  // EyeOutlined, // No longer used directly here
  // ArrowUpOutlined, // No longer used directly here
  // ArrowDownOutlined, // No longer used directly here
  // SwapOutlined, // No longer used directly here
  // InfoCircleOutlined, // No longer used directly here
} from '@ant-design/icons';
// import { ColumnsType } from 'antd/es/table'; // No longer used directly here
import TransactionStatsCard from './components/TransactionStatsCard'; // 导入新组件
import TransactionFilters from './components/TransactionFilters'; // 导入新组件
import { getTransactionTableColumns } from './components/columnDefinitions'; // 导入列定义函数
import styles from './EnhancedTransactionList.module.css';
// RangePickerProps and moment are now used in TransactionFilters or not at all
// import moment from 'moment'; // Removed if not used elsewhere in this file
import { Transaction } from '../../types/transaction'; // 导入 Transaction 类型
import useTransactionData, { FiltersState, PaginationState } from './hooks/useTransactionData'; // 导入自定义 Hook
import { getAddress } from '../../services/api/address/address';
// import { GetWalletTransactionRecordStatus } from '../../services/api/model/getWalletTransactionRecordStatus'; // Now used in columnDefinitions
// import { GetWalletTransactionRecordCoin } from '../../services/api/model/getWalletTransactionRecordCoin'; // No longer used directly here

import {
  // DemogfApiWalletV1TransactionTypeTransactionType, // Now used in columnDefinitions
  WalletApiInternalUtilityCryptoTronCost, // Needed for fee modal
  // GetWalletTransactionRecordChain, // No longer used directly here
  // GetWalletTransactionRecordType, // No longer used directly here
  // GetWalletTransactionRecordStatus, // This was already commented out, ensure it's not needed.
} from '../../services/api/model';
// import { getNode } from '../../services/api/node/node'; // No longer needed after commenting out fee fetching
import { openInExplorer } from '../../utils/explorerUtils'; // 修正路径

const { getWalletTransactionRecordStatistic } = getAddress();
// 移除了 GetWalletTransactionRecordSortOrder
// const { RangePicker } = DatePicker; // RangePicker is now in TransactionFilters

const EnhancedTransactionList: React.FC = () => {
  const initialPaginationState: PaginationState = {
    current: 1,
    pageSize: 10,
    total: 0,
  };

  const initialFiltersState: FiltersState = {};

  const {
    transactions,
    loading,
    pagination,
    searchText,
    filters,
    exportLoading,
    loadTransactions,
    handleFilterChange,
    handlePaginationChange,
    handleResetFilters,
    handleExport,
    setSearchText, // 从 Hook 中获取 setSearchText
  } = useTransactionData(initialPaginationState, initialFiltersState);

  // 添加手续费详情模态框状态
  const [feeModalVisible, setFeeModalVisible] = useState(false);
  const [feeDetailsTransaction, setFeeDetailsTransaction] = useState<Transaction | null>(null);
  const [feeLoading, setFeeLoading] = useState(false);

  const [cost, setCost] = useState<WalletApiInternalUtilityCryptoTronCost | null>(null);

  const [statistics, setStatistics] = useState({
    totalDeposit: '0',
    totalTrxDeposit: '0',
    totalEthDeposit: '0',
    totalCollect: '0',
    totalTrxCollect: '0',
    totalEthCollect: '0',
    gasCount: 0,
    pendingCount: 0,
  });

  const showTransactionDetails = (record: Transaction) => {
    // setCurrentTransaction(record);
    // setIsModalVisible(true);

    openInExplorer('tx', record.transaction_hash || '', record.chain || '');
  };

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await getWalletTransactionRecordStatistic();
      if (response) {
        setStatistics({
          totalDeposit: response.total_usdt_deposit_amount || '0',
          totalTrxDeposit: response.total_trx_deposit_amount || '0',
          totalEthDeposit: response.total_eth_deposit_amount || '0',
          totalCollect: response.total_usdt_withdraw_amount || '0',
          totalTrxCollect: response.total_trx_withdraw_amount || '0',
          totalEthCollect: response.total_eth_withdraw_amount || '0',
          gasCount: response.miner_fee_order_count || 0,
          pendingCount: response.pending_confirmation_count || 0,
        });
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // handleDateRangeChange is now managed by TransactionFilters and useTransactionData hook
  // The setFilters from useTransactionData will be passed to TransactionFilters for date changes.

  useEffect(() => {
    loadTransactions();
    loadStatistics();
  }, [loadTransactions]); // 依赖项更新

  const handleShowFeeDetails = (record: Transaction) => {
    setFeeDetailsTransaction(record);
    setCost(null);
    setFeeModalVisible(true);
    if (record.chain === 'TRON' && record.transaction_hash) {
      setFeeLoading(true);
      // getNode()
      //   .getNodeGetTronTransactionFee({ hash: record.transaction_hash })
      //   .then((result: { cost?: WalletApiInternalUtilityCryptoTronCost }) => {
      //     // The original code had a loop to convert number to string,
      //     // assuming result.cost is already in the correct format or WalletApiInternalUtilityCryptoTronCost handles it.
      //     setCost(result.cost || null);
      //   })
      //   .catch((error: any) => {
      //     console.error('获取Tron交易手续费详情失败:', error);
      //     message.error('获取交易手续费信息失败');
      //   })
      //   .finally(() => {
      //     setFeeLoading(false);
      //   });
      // 暂时注释掉，因为 getNodeGetTronTransactionFee 接口不存在
      // 需要后端确认新的获取方式
      message.warning('TRON 交易手续费详情功能暂时不可用');
      setFeeLoading(false); //确保loading状态被重置
    }
  };

  const columns = getTransactionTableColumns({
    openInExplorer,
    showTransactionDetails,
    onShowFeeDetails: handleShowFeeDetails,
  });

  return (
    <div className={styles.transactionList}>
      <TransactionStatsCard statistics={statistics} />

      <Card className={styles.tableCard}>
        <div className={styles.tableHeader}>
          <TransactionFilters
            searchText={searchText}
            filters={filters}
            onSearchTextChange={setSearchText}
            onFilterChange={handleFilterChange}
            onDateRangeChange={(dates) => handleFilterChange({ dateRange: dates })}
            onSearch={() => loadTransactions()}
            onReset={handleResetFilters}
          />
          <div className={styles.actions}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => loadTransactions()}
              loading={loading}
              style={{ marginRight: 8 }}
            >
              刷新
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport} loading={exportLoading}>
              导出
            </Button>
          </div>
        </div>

        <Table columns={columns} dataSource={transactions} rowKey="id" loading={{ spinning: loading, tip: '正在加载交易记录...' }} pagination={false} />

        <CustomPagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          onChange={handlePaginationChange} // 使用 Hook 中的 handlePaginationChange
          showSizeChanger={true}
          showQuickJumper={true}
          showTotal={(total) => `共 ${total} 笔交易`}
        />
      </Card>

      <Modal title="" open={feeModalVisible} onCancel={() => setFeeModalVisible(false)} footer={null} width={600}>
        {feeLoading ? (
          <div style={{ textAlign: 'center', padding: '30px 0' }}>
            <Spin size="large" />
            <p style={{ marginTop: 16 }}>加载交易手续费信息...</p>
          </div>
        ) : (
          feeDetailsTransaction && (
            <Descriptions title={`${feeDetailsTransaction.chain || ''} 手续费信息`} bordered column={1}>
              <Descriptions.Item label="net_fee_cost">{cost?.net_fee_cost || '-'}</Descriptions.Item>
              <Descriptions.Item label="fee">{cost?.fee || '-'}</Descriptions.Item>
              <Descriptions.Item label="energy_fee_cost">{cost?.energy_fee_cost || '-'}</Descriptions.Item>
              <Descriptions.Item label="net_usage">{cost?.net_usage || '-'}</Descriptions.Item>
              <Descriptions.Item label="multi_sign_fee">{cost?.multi_sign_fee || '-'}</Descriptions.Item>
              <Descriptions.Item label="net_fee">{cost?.net_fee || '-'}</Descriptions.Item>
              <Descriptions.Item label="energy_penalty_total">{cost?.energy_penalty_total || '-'}</Descriptions.Item>
              <Descriptions.Item label="energy_usage">{cost?.energy_usage || '-'}</Descriptions.Item>
              <Descriptions.Item label="energy_fee">{cost?.energy_fee || '-'}</Descriptions.Item>
              <Descriptions.Item label="energy_usage_total">{cost?.energy_usage_total || '-'}</Descriptions.Item>
              <Descriptions.Item label="memoFee">{cost?.memoFee || '-'}</Descriptions.Item>
              <Descriptions.Item label="origin_energy_usage">{cost?.origin_energy_usage || '-'}</Descriptions.Item>
              <Descriptions.Item label="account_create_fee">{cost?.account_create_fee || '-'}</Descriptions.Item>
            </Descriptions>
          )
        )}
      </Modal>
    </div>
  );
};

export default EnhancedTransactionList;
