.rechargeRecordList {
  width: 100%;
  animation: fadeIn 0.5s ease-out;
}

.tableCard {
  margin-top: 24px;
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  flex-wrap: wrap;
  padding: 8px 0;
}

.filterContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  max-width: 100%;
}

.searchInput {
  width: 220px;
}

.filterButton {
  margin-right: 8px;
}

.actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.statusTag {
  min-width: 70px;
  text-align: center;
  border-radius: 4px;
}

.amountText {
  font-weight: 500;
  color: #1890ff;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.statCard {
  margin-bottom: 16px;
}

.statRow {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.statItem {
  flex: 1;
  min-width: 150px;
}

/* 自定义表格样式 */
:global(.recharge-table) :global(.ant-table) {
  border-radius: 12px;
  overflow: hidden;
}

:global(.recharge-table) :global(.ant-table-thead > tr > th) {
  background-color: #f5f7fa;
  color: #262626;
  font-weight: 600;
  padding: 16px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:global(.recharge-table) :global(.ant-table-tbody > tr > td) {
  padding: 16px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:global(.recharge-table) :global(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background-color: #f0f5ff;
}

:global(.recharge-table) :global(.ant-pagination.ant-pagination) {
  margin: 16px 0;
  padding: 16px 16px;
}

:global(.recharge-table) :global(.ant-pagination-item-active) {
  border-color: #1890ff;
  font-weight: 600;
}

:global(.recharge-table) :global(.ant-pagination-item:hover) {
  border-color: #1890ff;
}

:global(.recharge-table) :global(.ant-pagination-item-active a) {
  color: #1890ff;
}

:global(.recharge-table-row) {
  transition: all 0.3s ease;
}

:global(.recharge-table-row:hover) {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 82, 204, 0.08);
}

@media (max-width: 768px) {
  .tableHeader {
    flex-direction: column;
  }

  .actions {
    margin-top: 16px;
  }

  .filterContainer {
    width: 100%;
  }

  .searchInput {
    width: 100%;
    margin-right: 0;
    margin-bottom: 8px;
  }
}
