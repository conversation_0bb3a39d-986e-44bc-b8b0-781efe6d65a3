import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { getRecharge } from '../../../services/api/recharge/recharge';
import type { WalletApiApiWalletV1RechargeRecord } from '../../../services/api/model/walletApiApiWalletV1RechargeRecord';
import type { GetWalletRechargeRecordChain } from '../../../services/api/model/getWalletRechargeRecordChain';
import type { GetWalletRechargeRecordStatus } from '../../../services/api/model/getWalletRechargeRecordStatus';
import type { GetWalletRechargeRecordCoin } from '../../../services/api/model/getWalletRechargeRecordCoin';
import type { GetWalletRechargeRecordSortOrder } from '../../../services/api/model/getWalletRechargeRecordSortOrder';

const MIN_LOADING_TIME_MS = 500;

export interface RechargeRecord extends WalletApiApiWalletV1RechargeRecord {
  key?: string;
}

export interface PaginationState {
  current: number;
  pageSize: number;
  total: number;
}

export interface FiltersState {
  address?: string;
  chain?: GetWalletRechargeRecordChain;
  status?: GetWalletRechargeRecordStatus;
  coin?: GetWalletRechargeRecordCoin;
  dateRange?: string;
  sortField?: string;
  sortOrder?: GetWalletRechargeRecordSortOrder;
}

export interface RechargeStatistics {
  totalRechargeCount: number;
  pendingCount: number;
  completedCount: number;
  totalUsdtAmount: string;
  totalTrxAmount: string;
  totalEthAmount: string;
}

const useRechargeData = (
  initialPagination: PaginationState,
  initialFilters: FiltersState
) => {
  const [rechargeRecords, setRechargeRecords] = useState<RechargeRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationState>(initialPagination);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState<FiltersState>(initialFilters);
  const [statistics, setStatistics] = useState<RechargeStatistics>({
    totalRechargeCount: 0,
    pendingCount: 0,
    completedCount: 0,
    totalUsdtAmount: '0',
    totalTrxAmount: '0',
    totalEthAmount: '0',
  });

  // API functions are now directly imported.

  const loadRechargeRecords = useCallback(async () => {
    setLoading(true);
    const startTime = Date.now();
    try {
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        address: searchText || filters.address,
        chain: filters.chain,
        status: filters.status,
        coin: filters.coin,
        date_range: filters.dateRange,
        sort_field: filters.sortField,
        sort_order: filters.sortOrder,
      };

      const { getWalletRechargeRecord } = getRecharge();
      const response = await getWalletRechargeRecord(params);

      if (response) {
        const records = response.list || [];
        setRechargeRecords(records.map((record: WalletApiApiWalletV1RechargeRecord) => ({
          ...record,
          key: String(record.recharges_id),
        })));

        // Update total without recreating the entire pagination object
        // This prevents the infinite loop caused by the dependency cycle
        if (response.page?.total !== pagination.total) {
          setPagination(prev => ({
            ...prev,
            total: response.page?.total || 0,
          }));
        }
      }
    } catch (error) {
      console.error('Failed to load recharge records:', error);
      message.error('加载充值记录失败');
    } finally {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime < MIN_LOADING_TIME_MS) {
        setTimeout(() => setLoading(false), MIN_LOADING_TIME_MS - elapsedTime);
      } else {
        setLoading(false);
      }
    }
  }, [
    pagination, // Include the entire pagination object
    searchText,
    filters,
    // getWalletRechargeRecord, // Directly imported, stable reference
  ]);

  const loadStatistics = useCallback(async () => {
    try {
      const { getWalletRechargeRecordStatistic } = getRecharge();
      const response = await getWalletRechargeRecordStatistic();

      if (response) {
        setStatistics({
          totalRechargeCount: response.total_recharge_count || 0,
          pendingCount: response.pending_count || 0,
          completedCount: response.completed_count || 0,
          totalUsdtAmount: response.total_usdt_amount || '0',
          totalTrxAmount: response.total_trx_amount || '0',
          totalEthAmount: response.total_eth_amount || '0',
        });
      }
    } catch (error) {
      console.error('Failed to load recharge statistics:', error);
      message.error('加载充值统计数据失败');
    }
  }, [/* getWalletRechargeRecordStatistic, // Directly imported, stable reference */]);

  const handleExport = async () => {
    setExportLoading(true);
    try {
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        address: searchText || filters.address,
        chain: filters.chain,
        status: filters.status,
        coin: filters.coin,
        date_range: filters.dateRange,
        sort_field: filters.sortField,
        sort_order: filters.sortOrder,
      };

      const { postWalletExportRechargeRecord } = getRecharge();
      const response = await postWalletExportRechargeRecord(params);

      if (response && response.success) {
        message.success('充值记录导出成功');
      } else {
        message.error('充值记录导出失败');
      }
    } catch (error) {
      console.error('Failed to export recharge records:', error);
      message.error('导出充值记录失败');
    } finally {
      setExportLoading(false);
    }
  };

  const handleFilterChange = (newFilters: Partial<FiltersState>) => {
    setFilters({ ...filters, ...newFilters });
  };

  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }));
  };

  const handleResetFilters = () => {
    setFilters(initialFilters);
    setSearchText('');
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // Load recharge records when component mounts or when dependencies change
  useEffect(() => {
    loadRechargeRecords();
  }, [loadRechargeRecords]);

  // Load statistics when component mounts
  useEffect(() => {
    loadStatistics();
  }, [loadStatistics]);

  return {
    rechargeRecords,
    loading,
    pagination,
    searchText,
    filters,
    statistics,
    exportLoading,
    loadRechargeRecords,
    handleFilterChange,
    handlePaginationChange,
    handleResetFilters,
    handleExport,
    setSearchText,
  };
};

export default useRechargeData;
