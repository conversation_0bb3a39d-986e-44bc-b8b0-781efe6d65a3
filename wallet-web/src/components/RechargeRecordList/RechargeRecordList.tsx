import React from 'react';
import { openInExplorer as openGlobalExplorer } from '../../utils/explorerUtils'; // 导入全局函数并重命名以避免冲突
import { Table, Card, Button, message } from 'antd';
import { ReloadOutlined, WalletOutlined } from '@ant-design/icons';
import CustomPagination from '../common/CustomPagination';
import RechargeStatsCard from './components/RechargeStatsCard';
import RechargeFilters from './components/RechargeFilters';
import { getRechargeTableColumns } from './components/columnDefinitions';
import useRechargeData, { FiltersState, PaginationState } from './hooks/useRechargeData';
import styles from './RechargeRecordList.module.css';

const RechargeRecordList: React.FC = () => {
  const initialPaginationState: PaginationState = {
    current: 1,
    pageSize: 10,
    total: 0,
  };

  const initialFiltersState: FiltersState = {};

  const {
    rechargeRecords,
    loading,
    pagination,
    searchText,
    filters,
    statistics,
    loadRechargeRecords,
    handleFilterChange,
    handlePaginationChange,
    handleResetFilters,
    setSearchText,
  } = useRechargeData(initialPaginationState, initialFiltersState);

  // 使用全局的 openGlobalExplorer 函数
  const columns = getRechargeTableColumns({
    openInExplorer: (type: string, hash: string, chain: string) => {
      // getRechargeTableColumns 期望 type 为 string
      // 全局的 openGlobalExplorer 期望 type 为 'tx' | 'address'
      if (type === 'tx' || type === 'address') {
        openGlobalExplorer(type, hash, chain);
      } else {
        // 如果 type 不是 'tx' 或 'address'，可以给出警告或不执行操作
        // 这取决于实际业务逻辑，这里我们选择警告
        message.warning(`不支持的区块浏览器链接类型: ${type}`);
        // 或者，如果确定调用者只会传入 'tx' 或 'address'，可以简化为：
        // openGlobalExplorer(type as 'tx' | 'address', hash, chain);
      }
    },
  });

  return (
    <div className={`${styles.rechargeRecordList} fade-in`}>
      <RechargeStatsCard statistics={statistics} />

      <Card
        className={`${styles.tableCard} hover-shadow`}
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
          border: 'none',
          overflow: 'hidden',
          marginTop: '24px',
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <WalletOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontSize: '16px', fontWeight: 600 }}>充值记录列表</span>
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #f0f0f0',
            padding: '16px 24px',
          },
          body: {
            padding: '24px',
          }
        }}
      >
        <div className={styles.tableHeader}>
          <RechargeFilters
            searchText={searchText}
            filters={filters}
            onSearchTextChange={setSearchText}
            onFilterChange={handleFilterChange}
            onDateRangeChange={(dateRange) => handleFilterChange({ dateRange })}
            onSearch={() => loadRechargeRecords()}
            onReset={handleResetFilters}
          />
          <div className={styles.actions}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => loadRechargeRecords()}
              loading={loading}
              style={{
                marginRight: 12,
                borderRadius: '8px',
                height: '32px',
              }}
              className="transition-all"
            >
              刷新
            </Button>
            {/* <Button
              icon={<ExportOutlined />}
              onClick={handleExport}
              loading={exportLoading}
              style={{
                borderRadius: '8px',
                height: '32px',
              }}
              className="transition-all"
            >
              导出
            </Button> */}
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={rechargeRecords}
          rowKey="recharges_id"
          loading={{ spinning: loading, tip: '正在加载充值记录...' }}
          pagination={false}
          scroll={{ x: 1500 }}
          className="recharge-table"
          style={{ marginTop: '16px' }}
          rowClassName={() => 'recharge-table-row transition-all'}
        />

        <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
          <CustomPagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onChange={handlePaginationChange}
            showSizeChanger={true}
            showQuickJumper={true}
            showTotal={(total) => `共 ${total} 笔充值记录`}
          />
        </div>
      </Card>
    </div>
  );
};

export default RechargeRecordList;
