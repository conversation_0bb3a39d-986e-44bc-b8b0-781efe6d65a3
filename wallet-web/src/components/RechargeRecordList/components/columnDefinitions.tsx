import React from 'react';
import { Tag, Tooltip, Button } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { RechargeRecord } from '../hooks/useRechargeData';
import styles from '../RechargeRecordList.module.css';

interface ColumnDefinitionProps {
  openInExplorer: (type: string, hash: string, chain: string) => void;
}

export const getRechargeTableColumns = ({ openInExplorer }: ColumnDefinitionProps): ColumnsType<RechargeRecord> => [
  {
    title: '充值ID',
    dataIndex: 'recharges_id',
    key: 'recharges_id',
    width: 100,
  },
  {
    title: '币种',
    dataIndex: 'name',
    key: 'name',
    width: 100,
    render: (text: string) => text || '-',
  },
  {
    title: '链',
    dataIndex: 'chain',
    key: 'chain',
    width: 100,
    render: (text: string) => text || '-',
  },
  {
    title: '来源地址',
    dataIndex: 'from_address',
    key: 'from_address',
    render: (text: string) => (
      <Tooltip title={text}>
        <span>{text || '-'}</span>
      </Tooltip>
    ),
  },
  {
    title: '目标地址',
    dataIndex: 'to_address',
    key: 'to_address',
    render: (text: string) => (
      <Tooltip title={text}>
        <span>{text || '-'}</span>
      </Tooltip>
    ),
  },
  {
    title: '充值数量',
    dataIndex: 'amount',
    key: 'amount',
    width: 150,
    render: (text: string, record) => (
      <span className={styles.amountText}>
        {text || '0'} {record.name}
      </span>
    ),
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
    width: 120,
    render: (state: number) => {
      let color = '';
      let text = '';

      switch (state) {
        case 1:
          color = 'orange';
          text = '待确认';
          break;
        case 2:
          color = 'green';
          text = '已完成';
          break;
        default:
          color = 'default';
          text = '未知';
      }

      return <Tag color={color} className={styles.statusTag}>{text}</Tag>;
    },
  },
  {
    title: '确认数',
    dataIndex: 'confirmations',
    key: 'confirmations',
    width: 100,
    render: (text: number) => text || 0,
  },
  {
    title: '交易哈希',
    dataIndex: 'tx_hash',
    key: 'tx_hash',
    render: (text: string) => (
      <Tooltip title={text}>
        <span>{text || '-'}</span>
      </Tooltip>
    ),
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
    render: (text: string) => text ? new Date(text).toLocaleString() : '-',
  },
  {
    title: '完成时间',
    dataIndex: 'completed_at',
    key: 'completed_at',
    width: 180,
    render: (text: string) => text ? new Date(text).toLocaleString() : '-',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 100,
    render: (_, record) => (
      <Button
        type="link"
        icon={<EyeOutlined />}
        onClick={() => record.tx_hash && openInExplorer('tx', record.tx_hash, record.chain || '')}
        disabled={!record.tx_hash}
      >
        查看
      </Button>
    ),
  },
];
