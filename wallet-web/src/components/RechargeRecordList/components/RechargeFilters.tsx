import React from 'react';
import { Input, <PERSON><PERSON>, DatePicker, Space } from 'antd';
import NativeFilterSelect from '../../common/NativeFilterSelect/NativeFilterSelect';
import { SearchOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import type { FiltersState } from '../hooks/useRechargeData';
import { GetWalletRechargeRecordChain } from '../../../services/api/model/getWalletRechargeRecordChain';
import { GetWalletRechargeRecordStatus } from '../../../services/api/model/getWalletRechargeRecordStatus';
import { GetWalletRechargeRecordCoin } from '../../../services/api/model/getWalletRechargeRecordCoin';
import styles from '../RechargeRecordList.module.css';

const { RangePicker } = DatePicker;

interface RechargeFiltersProps {
  searchText: string;
  filters: FiltersState;
  onSearchTextChange: (text: string) => void;
  onFilterChange: (filters: Partial<FiltersState>) => void;
  onDateRangeChange: (dateRange: string | undefined) => void;
  onSearch: () => void;
  onReset: () => void;
}

const RechargeFilters: React.FC<RechargeFiltersProps> = ({
  searchText,
  filters,
  onSearchTextChange,
  onFilterChange,
  onDateRangeChange,
  onSearch,
  onReset,
}) => {
  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {
    if (dates) {
      const dateRange = `${dateStrings[0]},${dateStrings[1]}`;
      onDateRangeChange(dateRange);
    } else {
      onDateRangeChange(undefined);
    }
  };

  return (
    <div className={`${styles.filterContainer} fade-in`}>
      <Input
        placeholder="搜索地址"
        value={searchText}
        onChange={(e) => onSearchTextChange(e.target.value)}
        onPressEnter={onSearch}
        prefix={<SearchOutlined style={{ color: '#1890ff' }} />}
        className={`${styles.searchInput} hover-shadow transition-all`}
        style={{
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
        }}
      />

      <NativeFilterSelect
        placeholder="选择链"
        value={filters.chain || ''}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onFilterChange({ chain: e.target.value as GetWalletRechargeRecordChain || undefined })}
        options={Object.values(GetWalletRechargeRecordChain).map(chain => ({ text: chain, value: chain }))}
        style={{ width: 120 }}
        className="hover-shadow"
      />

      <NativeFilterSelect
        placeholder="选择状态"
        value={filters.status || ''}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onFilterChange({ status: e.target.value as GetWalletRechargeRecordStatus || undefined })}
        options={[
          { text: '待确认', value: GetWalletRechargeRecordStatus.pending },
          { text: '已完成', value: GetWalletRechargeRecordStatus.completed },
        ]}
        style={{ width: 120 }}
        className="hover-shadow"
      />

      <NativeFilterSelect
        placeholder="选择币种"
        value={filters.coin || ''}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onFilterChange({ coin: e.target.value as GetWalletRechargeRecordCoin || undefined })}
        options={Object.values(GetWalletRechargeRecordCoin).map(coin => ({ text: coin, value: coin }))}
        style={{ width: 120 }}
        className="hover-shadow"
      />

      <RangePicker
        onChange={handleDateRangeChange}
        style={{
          borderRadius: '8px',
        }}
        className="hover-shadow transition-all"
      />

      <Space>
        <Button
          type="primary"
          icon={<FilterOutlined />}
          onClick={onSearch}
          className={`${styles.filterButton} hover-shadow transition-all`}
          style={{
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)',
            height: '32px',
          }}
        >
          筛选
        </Button>

        <Button
          icon={<ReloadOutlined />}
          onClick={onReset}
          className="transition-all"
          style={{
            borderRadius: '8px',
            height: '32px',
          }}
        >
          重置
        </Button>
      </Space>
    </div>
  );
};

export default RechargeFilters;
