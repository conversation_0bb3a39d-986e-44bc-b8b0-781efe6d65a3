import React from 'react';
import { Card, Row, Col, Statistic, Typography } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  WalletOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import type { RechargeStatistics } from '../hooks/useRechargeData';
import styles from '../RechargeRecordList.module.css';

const { Text } = Typography;

interface RechargeStatsCardProps {
  statistics: RechargeStatistics;
}

const RechargeStatsCard: React.FC<RechargeStatsCardProps> = ({ statistics }) => {
  return (
    <Card
      className={`${styles.statCard} fade-in hover-shadow`}
      style={{
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
        border: 'none',
        overflow: 'hidden',
      }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <BarChartOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>充值统计</span>
        </div>
      }
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '24px',
        }
      }}
    >
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>充值订单总数</Text>
              }
              value={statistics.totalRechargeCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#1890ff' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <WalletOutlined style={{ color: '#1890ff', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>待确认</Text>
              }
              value={statistics.pendingCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#faad14' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <ClockCircleOutlined style={{ color: '#faad14', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>已完成</Text>
              }
              value={statistics.completedCount}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#52c41a' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        <Col xs={24} sm={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>累计USDT充值</Text>
              }
              value={statistics.totalUsdtAmount}
              precision={2}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#722ed1' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <DollarOutlined style={{ color: '#722ed1', fontSize: 18 }} />
                </div>
              }
              suffix="USDT"
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>累计TRX充值</Text>
              }
              value={statistics.totalTrxAmount}
              precision={2}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#eb2f96' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #fff0f6 0%, #ffd6e7 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <WalletOutlined style={{ color: '#eb2f96', fontSize: 18 }} />
                </div>
              }
              suffix="TRX"
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>累计ETH充值</Text>
              }
              value={statistics.totalEthAmount}
              precision={4}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#13c2c2' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #e6fffb 0%, #b5f5ec 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <WalletOutlined style={{ color: '#13c2c2', fontSize: 18 }} />
                </div>
              }
              suffix="ETH"
            />
          </Card>
        </Col>
      </Row>
    </Card>
  );
};

export default RechargeStatsCard;
