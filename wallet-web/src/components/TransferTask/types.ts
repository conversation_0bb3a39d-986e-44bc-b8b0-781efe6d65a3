// src/components/TransferTask/types.ts

import { WalletApiApiWalletV1TaskList } from '../../services/api/model/walletApiApiWalletV1TaskList';

// 转账任务类型定义
export type TransferTask = WalletApiApiWalletV1TaskList;
// 转账明细类型定义
export interface TransferDetail {
  id: string;
  taskId: string;
  fromAddress: string;
  toAddress: string;
  amount: string;
  currency: string;
  network: string;
  fee: string;
  status: 'success' | 'failed' | 'pending';
  txHash?: string;
  createdAt: string;
  completedAt?: string;
  errorMessage?: string;
}