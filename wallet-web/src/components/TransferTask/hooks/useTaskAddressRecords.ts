import React, { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { getCollect } from '../../../services/api/collect/collect';
import type { WalletApiApiWalletV1TaskAddressRecord } from '../../../services/api/model/walletApiApiWalletV1TaskAddressRecord';
import type { GetWalletTaskAddressRecordStatus } from '../../../services/api/model/getWalletTaskAddressRecordStatus';
// DemogfApiWalletV1Page was unused

interface UseTaskAddressRecordsProps {
  taskId?: number;
  initialPageSize?: number;
}

interface TaskAddressRecordsState {
  loading: boolean;
  addressRecords: WalletApiApiWalletV1TaskAddressRecord[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  statusFilter: string;
  addressFilter: string;
}

interface TaskAddressRecordsActions {
  setStatusFilter: React.Dispatch<React.SetStateAction<string>>;
  setAddressFilter: React.Dispatch<React.SetStateAction<string>>;
  fetchAddressRecords: (params?: any) => Promise<void>;
  handlePageChange: (page: number, pageSize?: number) => void;
  handleSearch: () => void;
  handleReset: () => void;
  handleRefresh: () => void;
  handleExport: () => Promise<void>;
}

const useTaskAddressRecords = ({
  taskId,
  initialPageSize = 10,
}: UseTaskAddressRecordsProps): TaskAddressRecordsState & TaskAddressRecordsActions => {
  const [loading, setLoading] = useState<boolean>(false);
  const [addressRecords, setAddressRecords] = useState<WalletApiApiWalletV1TaskAddressRecord[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: initialPageSize,
    total: 0,
  });
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [addressFilter, setAddressFilter] = useState<string>('');

  const fetchAddressRecords = useCallback(async (params: any = {}) => {
    if (!taskId || taskId === 0) {
      setAddressRecords([]);
      setPagination(prev => ({ ...prev, current: 1, total: 0 }));
      return;
    }

    try {
      setLoading(true);
      const filteredParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== undefined && value !== null && value !== ''),
      );

      const apiParams: {
        task_id: number;
        page: number;
        limit: number;
        status?: GetWalletTaskAddressRecordStatus;
        address?: string;
      } = {
        task_id: taskId,
        page: Number(filteredParams.page) || pagination.current,
        limit: Number(filteredParams.limit) || pagination.pageSize,
      };

      if (filteredParams.status && filteredParams.status !== '') {
        apiParams.status = filteredParams.status as GetWalletTaskAddressRecordStatus;
      }
      if (filteredParams.address && filteredParams.address !== '') {
        apiParams.address = filteredParams.address as string;
      }

      const response = await getCollect().getWalletTaskAddressRecord(apiParams);

      if (response) {
        setAddressRecords(response.list as WalletApiApiWalletV1TaskAddressRecord[] || []);
        if (response.page) {
          setPagination({
            current: response.page.current || 1,
            pageSize: pagination.pageSize, // Keep current page size unless changed by page change handler
            total: response.page.total || 0,
          });
        } else {
           setPagination(prev => ({ ...prev, total: response.list?.length || 0 }));
        }
      } else {
        setAddressRecords([]);
        setPagination(prev => ({ ...prev, current: 1, total: 0 }));
      }
    } catch (error) {
      console.error('获取任务地址记录失败:', error);
      message.error('获取任务地址记录失败');
      setAddressRecords([]);
      setPagination(prev => ({ ...prev, current: 1, total: 0 }));
    } finally {
      setLoading(false);
    }
  }, [taskId, pagination]); // Ensure pagination values are dependencies

  useEffect(() => {
    if (taskId) {
      // Reset to page 1 when taskId changes or on initial load with taskId
      setPagination(prev => ({ ...prev, current: 1 }));
      fetchAddressRecords({ page: 1, limit: pagination.pageSize, address: addressFilter, status: statusFilter });
    } else {
      setAddressRecords([]);
      setPagination(prev => ({ ...prev, current: 1, total: 0 }));
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskId]); // fetchAddressRecords is not needed here as it's called directly. addressFilter and statusFilter are for subsequent fetches.

  // Effect for re-fetching when filters change, but not on initial load if taskId is not present
   useEffect(() => {
    if (taskId) { // Only fetch if taskId is present
        fetchAddressRecords({ page: 1, address: addressFilter, status: statusFilter });
    }
   // eslint-disable-next-line react-hooks/exhaustive-deps
   }, [addressFilter, statusFilter, taskId]); // Removed fetchAddressRecords from deps as it causes re-fetch loop with its own deps


  const handlePageChange = (page: number, pageSize?: number) => {
    const newPageSize = pageSize || pagination.pageSize;
    setPagination(prev => ({ ...prev, current: page, pageSize: newPageSize }));
    fetchAddressRecords({
      page,
      limit: newPageSize,
      address: addressFilter,
      status: statusFilter,
    });
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 })); // Reset to page 1 on new search
    fetchAddressRecords({
      page: 1,
      address: addressFilter,
      status: statusFilter,
    });
  };

  const handleReset = () => {
    setAddressFilter('');
    setStatusFilter('');
    setPagination(prev => ({ ...prev, current: 1 })); // Reset to page 1
    fetchAddressRecords({
      page: 1,
      limit: pagination.pageSize,
    });
  };

  const handleRefresh = () => {
    fetchAddressRecords({
      page: pagination.current,
      limit: pagination.pageSize,
      address: addressFilter,
      status: statusFilter,
    });
  };

  const handleExport = async () => {
    if (!taskId || taskId === 0) {
      message.error('任务ID不存在，无法导出');
      return;
    }

    try {
      message.loading('正在导出数据，请稍候...', 0);
      const exportParams: any = { // Use 'any' for flexibility or define a more specific type
        task_id: taskId,
        // Export all matching records, not just the current page.
        // API might need to support 'all' or specific pagination for export.
        // For now, let's assume it exports based on filters, not current view's pagination.
        // page: 1, // Or remove if API exports all by default
        // limit: 0, // Or a very large number, or remove if API exports all
      };
      if (addressFilter && addressFilter !== '') {
        exportParams.address = addressFilter;
      }
      if (statusFilter && statusFilter !== '') {
        exportParams.status = statusFilter as GetWalletTaskAddressRecordStatus;
      }

      const response = await getCollect().getWalletExportTaskAddressRecord(exportParams);

      message.destroy();
      if (response) {
        message.success('导出请求已提交，请在下载列表中查看');
      } else {
        message.error('导出失败，请稍后重试');
      }
    } catch (error) {
      message.destroy();
      message.error('导出出错: ' + (error instanceof Error ? error.message : '未知错误'));
      console.error('导出错误:', error);
    }
  };

  return {
    loading,
    addressRecords,
    pagination,
    statusFilter,
    addressFilter,
    setStatusFilter,
    setAddressFilter,
    fetchAddressRecords,
    handlePageChange,
    handleSearch,
    handleReset,
    handleRefresh,
    handleExport,
  };
};

export default useTaskAddressRecords;