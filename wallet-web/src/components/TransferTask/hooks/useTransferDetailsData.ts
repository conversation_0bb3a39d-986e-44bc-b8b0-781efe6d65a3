import { useState, useEffect, useCallback } from 'react';
import { TransferDetail } from '../types'; // Updated import path

// 模拟 API 调用，实际项目中应替换为真实 API
const fetchTransferDetailsAPI = (
  taskId: string,
  currentPage: number,
  pageSize: number,
  filters: { status?: string },
  searchText: string, // searchText is now passed to API
): Promise<{ data: TransferDetail[]; total: number }> => {
  console.log('Fetching details for task:', taskId, currentPage, pageSize, filters, searchText);
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockDetails: TransferDetail[] = Array(30)
        .fill(null)
        .map((_, index) => {
          const statusOptions: Array<'success' | 'failed' | 'pending'> = ['success', 'failed', 'pending'];
          const status = statusOptions[Math.floor(Math.random() * 3)];
          const networkOptions = ['ETH', 'TRX', 'BSC'];
          const network = networkOptions[Math.floor(Math.random() * 3)];
          const currency =
            network === 'ETH'
              ? ['ETH', 'USDT', 'USDC'][Math.floor(Math.random() * 3)]
              : network === 'TRX'
                ? ['TRX', 'USDT'][Math.floor(Math.random() * 2)]
                : ['BNB', 'BUSD', 'USDT'][Math.floor(Math.random() * 3)];
          const amount = (Math.random() * 10).toFixed(4);
          const fee = (Math.random() * 0.01).toFixed(6);
          const createdAt = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString();
          const completedAt =
            status !== 'pending'
              ? new Date(new Date(createdAt).getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString()
              : undefined;
          const txHash =
            status !== 'pending'
              ? `0x${Array(64)
                  .fill(0)
                  .map(() => Math.floor(Math.random() * 16).toString(16))
                  .join('')}`
              : undefined;
          const errorMessage =
            status === 'failed'
              ? ['余额不足', '网络拥堵', '交易超时', '地址无效', '合约执行失败'][Math.floor(Math.random() * 5)]
              : undefined;

          return {
            id: `detail-${taskId}-${currentPage}-${index + 1}`, // More unique ID
            taskId,
            fromAddress: `0x${Array(40).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('')}`,
            toAddress: `0x${Array(40).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('')}`,
            amount,
            currency,
            network,
            fee,
            status,
            txHash,
            createdAt,
            completedAt,
            errorMessage,
          };
        })
        // Simulate filtering based on searchText (e.g., fromAddress, toAddress, txHash)
        .filter(detail => {
            if (!searchText) return true;
            const lowerSearchText = searchText.toLowerCase();
            return (
                detail.fromAddress.toLowerCase().includes(lowerSearchText) ||
                detail.toAddress.toLowerCase().includes(lowerSearchText) ||
                (detail.txHash && detail.txHash.toLowerCase().includes(lowerSearchText))
            );
        })
        // Simulate filtering based on status
        .filter(detail => {
            if (!filters.status) return true;
            return detail.status === filters.status;
        });

      // Simulate pagination
      const paginatedDetails = mockDetails.slice(0, pageSize); // In real API, pagination is done server-side

      resolve({ data: paginatedDetails, total: mockDetails.length > 0 ? 100 : 0 }); // Adjust total based on filtered results for mock
    }, 500);
  });
};


export const useTransferDetailsData = (taskId: string) => {
  const [details, setDetails] = useState<TransferDetail[]>([]);
  const [totalDetails, setTotalDetails] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState<{ status?: string }>({});
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [selectedError, setSelectedError] = useState<string | undefined>(undefined);

  const loadDetails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetchTransferDetailsAPI(taskId, currentPage, pageSize, filters, searchText);
      setDetails(response.data);
      setTotalDetails(response.total);
    } catch (error) {
      console.error("Failed to load transfer details:", error);
      // Handle error appropriately, e.g., show a message to the user
      setDetails([]);
      setTotalDetails(0);
    } finally {
      setLoading(false);
    }
  }, [taskId, currentPage, pageSize, filters, searchText]);

  useEffect(() => {
    loadDetails();
  }, [loadDetails]); // loadDetails is now a dependency

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page for new search/filter
    // loadDetails will be called by useEffect due to currentPage change
  };

  const handleReset = () => {
    setSearchText('');
    setFilters({});
    setCurrentPage(1);
    // loadDetails will be called by useEffect
  };

  const handlePageChange = (page: number, newPageSize?: number) => {
    setCurrentPage(page);
    if (newPageSize && newPageSize !== pageSize) {
      setPageSize(newPageSize);
    }
  };
  
  const showErrorDetails = (errorMessage: string | undefined) => {
    if (errorMessage) {
      setSelectedError(errorMessage);
      setErrorModalVisible(true);
    }
  };

  return {
    details,
    totalDetails,
    loading,
    currentPage,
    pageSize,
    searchText,
    filters,
    errorModalVisible,
    selectedError,
    setSearchText,
    setFilters,
    loadDetails, // Expose if manual refresh is needed
    handleSearch,
    handleReset,
    handlePageChange,
    showErrorDetails,
    setErrorModalVisible, // For closing the modal from the main component if needed
  };
};