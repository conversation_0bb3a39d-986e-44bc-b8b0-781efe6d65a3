import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import type { Dayjs } from 'dayjs'; // Import Dayjs type
import { useMemoizedFn } from 'ahooks';
// import moment from 'moment'; // Remove moment import

import { getCollect } from '../../../services/api/collect/collect';
import type { WalletApiApiWalletV1TaskList } from '../../../services/api/model/walletApiApiWalletV1TaskList';
import { GetWalletCollectTaskListParams } from '../../../services/api/model/getWalletCollectTaskListParams';
import type { WalletApiApiWalletV1CollectRecordStatisticRes } from '../../../services/api/model/walletApiApiWalletV1CollectRecordStatisticRes';
import { GetWalletCollectRecordStatisticParams } from '../../../services/api/model/getWalletCollectRecordStatisticParams';

const MIN_LOADING_TIME_MS = 500;

export interface UseTransferTasksProps {
  initialPageSize?: number;
}

export const useTransferTasks = (props?: UseTransferTasksProps) => {
  const { initialPageSize = 10 } = props || {};

  const [tasks, setTasks] = useState<WalletApiApiWalletV1TaskList[]>([]);
  const [totalTasks, setTotalTasks] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [searchText, setSearchText] = useState('');
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null); // Use Dayjs type
  const [statistics, setStatistics] = useState<WalletApiApiWalletV1CollectRecordStatisticRes | null>(null);

  const [taskParams, setTaskParams] = useState<GetWalletCollectTaskListParams>({
    page: currentPage,
    limit: pageSize,
    address: searchText,
  });

  const loadTasksWithParams = useCallback(async (params: GetWalletCollectTaskListParams) => {
    setLoading(true);
    const startTime = Date.now();
    try {
      const filteredParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== undefined && value !== null && value !== ''),
      );
      const response = await getCollect().getWalletCollectTaskList(
        filteredParams as GetWalletCollectTaskListParams,
      );
      if (response.list && response.page) {
        setTasks(response.list as WalletApiApiWalletV1TaskList[]);
        setTotalTasks(response.page.total || 0);
      } else {
        setTasks([]);
        setTotalTasks(0);
      }
    } catch (error) {
      console.error('获取归集任务列表失败:', error);
      message.error('获取归集任务列表失败');
      setTasks([]);
      setTotalTasks(0);
    } finally {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime < MIN_LOADING_TIME_MS) {
        setTimeout(() => setLoading(false), MIN_LOADING_TIME_MS - elapsedTime);
      } else {
        setLoading(false);
      }
    }
  }, []);

  const loadStatistics = useCallback(async (params?: Partial<GetWalletCollectRecordStatisticParams>) => {
    try {
      const statisticParams: GetWalletCollectRecordStatisticParams = {
        page: 1,
        limit: 10, // Statistics usually don't need full pagination
        address: params?.address || taskParams.address,
        date_range: params?.date_range || taskParams.date_range,
        status: (params?.status as any) || (taskParams.status as any),
        coin: (params?.coin as any) || (taskParams.coin as any),
        task_type: (params?.task_type as any) || (taskParams.task_type as any),
      };
      const filteredParams = Object.fromEntries(
        Object.entries(statisticParams).filter(([_, value]) => value !== undefined && value !== null && value !== ''),
      );
      const response = await getCollect().getWalletCollectRecordStatistic(
        filteredParams as GetWalletCollectRecordStatisticParams,
      );
      setStatistics(response);
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    }
  }, [taskParams]);

  const loadInitialData = useMemoizedFn(() => {
    loadTasksWithParams(taskParams);
    loadStatistics(taskParams);
  });

  useEffect(() => {
    loadInitialData();
  }, [currentPage, pageSize, loadInitialData]); // Dependencies for re-fetching

  const handleSearch = useMemoizedFn(() => {
    const searchParams = {
      ...taskParams,
      page: 1,
      address: searchText,
      // date_range is already part of taskParams through handleDateRangeChange
    };
    setCurrentPage(1); // Reset to first page for new search
    setTaskParams(searchParams);
    loadTasksWithParams(searchParams);
    loadStatistics(searchParams);
  });

  const handleReset = useMemoizedFn(() => {
    setSearchText('');
    setDateRange(null);
    const resetParams: GetWalletCollectTaskListParams = {
      page: 1,
      limit: pageSize,
      address: '',
      date_range: undefined,
      status: undefined,
      coin: undefined,
      task_type: undefined,
      execute_type: undefined,
    };
    setCurrentPage(1);
    setTaskParams(resetParams);
    loadTasksWithParams(resetParams);
    loadStatistics(resetParams);
  });

  const handleExport = useMemoizedFn(async () => {
    try {
      message.loading('正在导出数据，请稍候...', 0);
      const exportParams = {
        ...taskParams,
        page: 1, // Usually export all matching, or current view with large limit
        limit: 10000,
      };
      const filteredParams = Object.fromEntries(
        Object.entries(exportParams).filter(([_, value]) => value !== undefined && value !== null && value !== ''),
      );
      await getCollect().getWalletExportTaskRecord(filteredParams as any);
      message.destroy();
      message.success('导出成功');
    } catch (error) {
      message.destroy();
      console.error('导出失败:', error);
      message.error('导出失败，请稍后重试');
    }
  });

  const handlePageChange = useMemoizedFn((page: number, newPageSize?: number) => {
    const updatedPageSize = newPageSize || pageSize;
    setCurrentPage(page);
    if (newPageSize) {
      setPageSize(updatedPageSize);
    }
    const newParams = {
      ...taskParams,
      page: page,
      limit: updatedPageSize,
    };
    setTaskParams(newParams);
    // Data will be re-fetched by useEffect due to currentPage/pageSize change
  });

  const handleDateRangeChange = useMemoizedFn((dates: [Dayjs | null, Dayjs | null] | null) => { // Accept Dayjs
    setDateRange(dates);
    setTaskParams(prevParams => ({
      ...prevParams,
      date_range:
        dates && dates[0] && dates[1] ? `${dates[0].format('YYYY-MM-DD')},${dates[1].format('YYYY-MM-DD')}` : undefined, // Format Dayjs
    }));
  });

  const handleFilterChange = useMemoizedFn((filterType: keyof GetWalletCollectTaskListParams, value: any) => {
    setTaskParams(prevParams => ({
        ...prevParams,
        [filterType]: value || undefined,
        page: 1 // Reset to page 1 when filters change
    }));
    setCurrentPage(1); // Ensure UI reflects page 1
     // Data will be re-fetched by useEffect due to taskParams change if it's a dependency,
    // or we can call loadTasksWithParams and loadStatistics here directly.
    // For simplicity and to ensure immediate feedback, let's call them.
    const newParams = {
        ...taskParams,
        [filterType]: value || undefined,
        page: 1
    };
    loadTasksWithParams(newParams);
    loadStatistics(newParams);
  });


  // Expose state and handlers
  return {
    tasks,
    totalTasks,
    loading,
    currentPage,
    pageSize,
    searchText,
    setSearchText,
    dateRange,
    statistics,
    taskParams, // mainly for filter components to bind to
    setTaskParams, // for direct manipulation if needed, though handleFilterChange is preferred
    loadTasks: loadInitialData, // Renamed for clarity
    handleSearch,
    handleReset,
    handleExport,
    handlePageChange,
    handleDateRangeChange,
    handleFilterChange, // Generic filter change handler
  };
};