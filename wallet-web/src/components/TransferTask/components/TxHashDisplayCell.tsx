import React from 'react';
import { Typo<PERSON>, Button, Tooltip, Space } from 'antd';
import { CopyOutlined, LinkOutlined } from '@ant-design/icons';
import { formatAddress as utilFormatAddress } from '../../../utils/format';
import { copyToClipboard } from '../../../utils/clipboardUtils';
import { openInExplorer } from '../../../utils/explorerUtils';
import styles from '../TransferTask.module.css'; // Assuming styles might be shared or specific

const { Text } = Typography;

interface TxHashDisplayCellProps {
  txHash?: string;
  network: string;
}

const TxHashDisplayCell: React.FC<TxHashDisplayCellProps> = ({ txHash, network }) => {
  if (!txHash) {
    return <Text type="secondary">-</Text>;
  }

  const handleOpenExplorer = (e: React.MouseEvent) => {
    e.stopPropagation();
    openInExplorer('tx', txHash, network === 'TRX' ? 'TRON' : network);
  };

  const handleCopyToClipboard = (e: React.MouseEvent) => {
    e.stopPropagation();
    copyToClipboard(txHash);
  };

  return (
    <div className={styles.txHashContainer}>
      <Tooltip title={txHash}>
        <Text ellipsis className={styles.txHash}>
          {utilFormatAddress(txHash, 8, 8)} {/* Show more for txHash if desired */}
        </Text>
      </Tooltip>
      <Space>
        <Button
          type="text"
          icon={<CopyOutlined />}
          onClick={handleCopyToClipboard}
          size="small"
        />
        <Button
          type="text"
          icon={<LinkOutlined />}
          onClick={handleOpenExplorer}
          size="small"
        />
      </Space>
    </div>
  );
};

export default TxHashDisplayCell;