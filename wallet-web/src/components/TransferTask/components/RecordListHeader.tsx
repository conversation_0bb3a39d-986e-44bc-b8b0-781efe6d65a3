import React from 'react';
import { Input, But<PERSON>, Select } from 'antd'; // Removed Space
import { SearchOutlined, SyncOutlined, ExportOutlined, FilterOutlined } from '@ant-design/icons';
import styles from '../TransferTask.module.css'; // Assuming styles can be shared or adjusted

const { Option } = Select;

interface RecordListHeaderProps {
  addressFilter: string;
  setAddressFilter: (value: string) => void;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  onSearch: () => void;
  onReset: () => void;
  onRefresh: () => void;
  onExport: () => void;
}

const RecordListHeader: React.FC<RecordListHeaderProps> = ({
  addressFilter,
  setAddressFilter,
  statusFilter,
  setStatusFilter,
  onSearch,
  onReset,
  onRefresh,
  onExport,
}) => {
  return (
    <div className={styles.tableHeader}>
      <div className={styles.searchFilters}>
        <Input
          placeholder="输入地址搜索"
          prefix={<SearchOutlined />}
          allowClear
          value={addressFilter}
          onChange={(e) => setAddressFilter(e.target.value)}
          style={{ width: 200, marginRight: 8 }}
        />
        <Select
          value={statusFilter}
          onChange={(value) => setStatusFilter(value)}
          style={{ width: 120, marginRight: 8 }}
          placeholder="选择状态"
          allowClear
        >
          <Option value="">全部状态</Option>
          <Option value="completed">完成</Option>
          <Option value="processing">处理中</Option>
          <Option value="pending">等待中</Option>
          <Option value="failed">失败</Option>
          <Option value="canceled">已取消</Option>
        </Select>
        <Button type="primary" onClick={onSearch} icon={<FilterOutlined />} style={{ marginRight: 8 }}>
          筛选
        </Button>
        <Button onClick={onReset}>重置</Button>
      </div>
      <div className={styles.actions}>
        <Button icon={<SyncOutlined />} onClick={onRefresh} style={{ marginRight: 8 }}>
          刷新
        </Button>
        <Button icon={<ExportOutlined />} onClick={onExport}>
          导出
        </Button>
      </div>
    </div>
  );
};

export default RecordListHeader;