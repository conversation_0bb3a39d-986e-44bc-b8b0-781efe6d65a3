import React from 'react';
import { Space, Tag, Typography } from 'antd';
import type { WalletApiApiWalletV1TaskAddressRecord } from '../../../services/api/model/walletApiApiWalletV1TaskAddressRecord';

const { Text } = Typography;

interface NetworkCurrencyDisplayProps {
  record: WalletApiApiWalletV1TaskAddressRecord;
}

const NetworkCurrencyDisplay: React.FC<NetworkCurrencyDisplayProps> = ({ record }) => {
  const networkType = record.task_info?.network_type;
  const tokenType = record.task_info?.token_type;

  let networkColor = 'gold'; // Default color
  if (networkType === 'ETH') {
    networkColor = 'blue';
  } else if (networkType === 'TRON') {
    networkColor = 'red';
  }

  return (
    <Space>
      {networkType && <Tag color={networkColor}>{networkType}</Tag>}
      {tokenType && <Text>{tokenType}</Text>}
      {!networkType && !tokenType && <Text>-</Text>}
    </Space>
  );
};

export default NetworkCurrencyDisplay;