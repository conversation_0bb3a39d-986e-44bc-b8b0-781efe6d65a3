import React from 'react';
import { Input, DatePicker, Button, Space } from 'antd'; // Select removed
import { FilterOutlined } from '@ant-design/icons';
import type { Dayjs } from 'dayjs';

import { GetWalletCollectTaskListStatus } from '../../../services/api/model/getWalletCollectTaskListStatus';
import { GetWalletCollectTaskListCoin } from '../../../services/api/model/getWalletCollectTaskListCoin';
import { GetWalletCollectTaskListTaskType } from '../../../services/api/model/getWalletCollectTaskListTaskType';
import { GetWalletCollectTaskListExecuteType } from '../../../services/api/model'; // Assuming this is the correct path
import { GetWalletCollectTaskListParams } from '../../../services/api/model/getWalletCollectTaskListParams';
import NativeFilterSelect from '../../common/NativeFilterSelect/NativeFilterSelect'; // Import NativeFilterSelect

const { RangePicker } = DatePicker;
// const { Option } = Select; // Option removed

interface TaskFiltersProps {
  searchText: string;
  setSearchText: (text: string) => void;
  dateRange: [Dayjs | null, Dayjs | null] | null;
  taskParams: GetWalletCollectTaskListParams; // To bind select values
  handleFilterChange: (filterType: keyof GetWalletCollectTaskListParams, value: any) => void;
  handleDateRangeChange: (dates: [Dayjs | null, Dayjs | null] | null, dateStrings: [string, string]) => void;
  onSearch: () => void;
  onReset: () => void;
}

const TaskFilters: React.FC<TaskFiltersProps> = ({
  searchText,
  setSearchText,
  dateRange,
  taskParams,
  handleFilterChange,
  handleDateRangeChange,
  onSearch,
  onReset,
}) => {
  const selectStyle = { // This style will be applied to a wrapper div
    width: 120,
    marginRight: 8,
    display: 'inline-block', // To allow marginRight to work as expected
  };

  const statusOptions = [
    { label: '已完成', value: GetWalletCollectTaskListStatus.completed },
    { label: '处理中', value: GetWalletCollectTaskListStatus.processing },
    { label: '失败', value: GetWalletCollectTaskListStatus.failed },
    { label: '等待中', value: GetWalletCollectTaskListStatus.pending },
    { label: '已取消', value: GetWalletCollectTaskListStatus.canceled },
  ];

  const coinOptions = [
    { label: 'USDT', value: GetWalletCollectTaskListCoin.USDT },
    { label: 'ETH', value: GetWalletCollectTaskListCoin.ETH },
    { label: 'TRX', value: GetWalletCollectTaskListCoin.TRX },
  ];

  const taskTypeOptions = [
    { label: '单转多', value: GetWalletCollectTaskListTaskType.one2many },
    { label: '多转单', value: GetWalletCollectTaskListTaskType.many2one },
  ];

  const executeTypeOptions = [
    { label: '手动归集', value: GetWalletCollectTaskListExecuteType.manual },
    { label: '自动归集', value: GetWalletCollectTaskListExecuteType.auto },
  ];

  return (
    <Space wrap style={{ marginBottom: 16 }}>
      <Input
        placeholder="搜索地址"
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        onPressEnter={onSearch}
        style={{ width: 200, marginRight: 8 }}
      />
      <div style={selectStyle}>
        <NativeFilterSelect
          value={taskParams.status || ''} // Ensure value is not undefined for select
          onChange={(e) => handleFilterChange('status', e.target.value as GetWalletCollectTaskListStatus || undefined)}
          options={statusOptions.map(opt => ({ text: opt.label, value: opt.value }))}
          placeholder="状态"
          // NativeFilterSelect handles this by having an empty value option if desired, or by onChange passing undefined
        />
      </div>
      <div style={selectStyle}>
        <NativeFilterSelect
          value={taskParams.coin || ''}
          onChange={(e) => handleFilterChange('coin', e.target.value as GetWalletCollectTaskListCoin || undefined)}
          options={coinOptions.map(opt => ({ text: opt.label, value: opt.value }))}
          placeholder="币种"
        />
      </div>
      <div style={selectStyle}>
        <NativeFilterSelect
          value={taskParams.task_type || ''}
          onChange={(e) => handleFilterChange('task_type', e.target.value as GetWalletCollectTaskListTaskType || undefined)}
          options={taskTypeOptions.map(opt => ({ text: opt.label, value: opt.value }))}
          placeholder="任务类型"
        />
      </div>
      <div style={selectStyle}>
        <NativeFilterSelect
          value={taskParams.execute_type || ''}
          onChange={(e) => handleFilterChange('execute_type', e.target.value as GetWalletCollectTaskListExecuteType || undefined)}
          options={executeTypeOptions.map(opt => ({ text: opt.label, value: opt.value }))}
          placeholder="归集类型"
        />
      </div>
      <RangePicker value={dateRange} onChange={handleDateRangeChange} style={{ marginRight: 8 }} />
      <Button type="primary" onClick={onSearch} icon={<FilterOutlined />}>
        筛选
      </Button>
      <Button onClick={onReset}>重置</Button>
    </Space>
  );
};

export default TaskFilters;