import React from 'react';
import { Card, Row, Col, Statistic } from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import type { WalletApiApiWalletV1CollectRecordStatisticRes } from '../../../services/api/model/walletApiApiWalletV1CollectRecordStatisticRes';
import styles from '../TransferTask.module.css'; // Assuming styles can be shared or adjusted

interface TaskStatisticsCardProps {
  statistics: WalletApiApiWalletV1CollectRecordStatisticRes | null;
}

const TaskStatisticsCard: React.FC<TaskStatisticsCardProps> = ({ statistics }) => {
  return (
    <Card className={styles.statsCard}>
      <Row gutter={24} style={{ marginTop: 16 }}>
        <Col span={4}>
          <Statistic
            title="已完成任务数"
            value={statistics?.completed_task_count || 0}
            valueStyle={{ color: '#52c41a' }}
            prefix={<CheckCircleOutlined />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="处理中任务数"
            value={statistics?.processing_task_count || 0}
            valueStyle={{ color: '#1890ff' }}
            prefix={<ClockCircleOutlined />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="失败任务数"
            value={statistics?.failed_task_count || 0}
            valueStyle={{ color: '#f5222d' }}
            prefix={<CloseCircleOutlined />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="等待中任务数"
            value={statistics?.pending_task_count || 0}
            valueStyle={{ color: '#faad14' }}
            prefix={<PauseCircleOutlined />}
          />
        </Col>
        <Col span={4}>
          <Statistic
            title="已取消任务数"
            value={statistics?.canceled_task_count || 0}
            valueStyle={{ color: '#8c8c8c' }}
            prefix={<StopOutlined />}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default TaskStatisticsCard;