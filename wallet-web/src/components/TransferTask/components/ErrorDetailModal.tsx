import React from 'react';
import { Modal, Button } from 'antd';

interface ErrorDetailModalProps {
  visible: boolean;
  errorMessage?: string;
  onClose: () => void;
}

const ErrorDetailModal: React.FC<ErrorDetailModalProps> = ({
  visible,
  errorMessage,
  onClose,
}) => {
  return (
    <Modal
      title="错误详情"
      visible={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
    >
      <p>{errorMessage || '没有可显示的错误信息。'}</p>
    </Modal>
  );
};

export default ErrorDetailModal;