import React from 'react';
import { Input, Button, Select } from 'antd'; // Removed Space
import { SearchOutlined } from '@ant-design/icons';
import styles from '../TransferTask.module.css'; // Assuming styles might be shared or specific

const { Option } = Select;

interface TransferDetailsFiltersProps {
  searchText: string;
  onSearchTextChange: (text: string) => void;
  statusFilter?: string;
  onStatusFilterChange: (status?: string) => void;
  onSearch: () => void;
  onReset: () => void;
}

const TransferDetailsFilters: React.FC<TransferDetailsFiltersProps> = ({
  searchText,
  onSearchTextChange,
  statusFilter,
  onStatusFilterChange,
  onSearch,
  onReset,
}) => {
  return (
    <div className={styles.tableHeader}> {/* Assuming this class is for the whole header area */}
      <div className={styles.searchFilters}> {/* Assuming this class is for filters specifically */}
        <Input
          placeholder="搜索地址或交易哈希"
          value={searchText}
          onChange={(e) => onSearchTextChange(e.target.value)}
          onPressEnter={onSearch}
          prefix={<SearchOutlined />}
          style={{ width: 250 }}
          allowClear
        />
        <Select
          placeholder="状态"
          allowClear
          style={{ width: 120 }}
          value={statusFilter}
          onChange={onStatusFilterChange}
        >
          <Option value="success">成功</Option>
          <Option value="failed">失败</Option>
          <Option value="pending">处理中</Option>
        </Select>
        <Button type="primary" onClick={onSearch}>
          筛选
        </Button>
        <Button onClick={onReset}>重置</Button>
      </div>
      {/* Actions like export button will remain in the main component or be moved to a separate actions component */}
    </div>
  );
};

export default TransferDetailsFilters;