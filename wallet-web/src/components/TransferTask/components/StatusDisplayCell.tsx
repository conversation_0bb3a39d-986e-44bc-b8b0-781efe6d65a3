import React from 'react';
import { Typo<PERSON>, Button, Space, Badge } from 'antd';
import { getStatusText, getStatusColor } from '../../../utils/statusUtils';
// import styles from '../TransferTask.module.css'; // If specific styles are needed

const { Text } = Typography;

interface StatusDisplayCellProps {
  status: 'success' | 'failed' | 'pending';
  errorMessage?: string;
  onErrorClick?: (errorMessage: string) => void;
}

const StatusDisplayCell: React.FC<StatusDisplayCellProps> = ({ status, errorMessage, onErrorClick }) => {
  return (
    <Space>
      <Badge status={getStatusColor(status) as any} />
      <Text>{getStatusText(status)}</Text>
      {status === 'failed' && errorMessage && onErrorClick && (
        <Button
          type="text"
          danger
          size="small"
          onClick={(e) => {
            e.stopPropagation(); // Prevent row click if any
            onErrorClick(errorMessage);
          }}
        >
          查看错误
        </Button>
      )}
    </Space>
  );
};

export default StatusDisplayCell;