import React from 'react';
import { Drawer, Descriptions, Badge, Tag } from 'antd';
import { TransferTask } from '../types';
import TaskAddressRecordList from '../TaskAddressRecordList'; // Assuming this is the correct path
import { getTaskStatusText, getTaskStatusColor } from '../utils';
import { GetWalletCollectTaskListStatus } from '../../../services/api/model/getWalletCollectTaskListStatus';
import styles from '../TransferTask.module.css'; // Assuming styles can be shared or adjusted


interface TaskDetailsDrawerProps {
  visible: boolean;
  onClose: () => void;
  task: TransferTask | null;
}

const TaskDetailsDrawer: React.FC<TaskDetailsDrawerProps> = ({ visible, onClose, task }) => {
  if (!task) {
    return null;
  }

  return (
    <Drawer
      title="归集任务详情"
      placement="right"
      closable={true}
      onClose={onClose}
      open={visible}
      width="90%"
      className={styles.detailsDrawer}
    >
      <>
        <Descriptions column={2} bordered>
          <Descriptions.Item label="任务ID" span={2}>
            {task.id}
          </Descriptions.Item>
          <Descriptions.Item label="任务名称" span={2}>
            {task.task_name}
          </Descriptions.Item>
          <Descriptions.Item label="网络">{task.network_type}</Descriptions.Item>
          <Descriptions.Item label="币种">{task.token_type}</Descriptions.Item>
          <Descriptions.Item label="总金额">
            {task.total_amount} {task.token_type}
          </Descriptions.Item>
          <Descriptions.Item label="手续费">
            {task.total_fee}{' '}
            {task.network_type === 'ETH' ? 'ETH' : task.network_type === 'TRON' ? 'TRX' : ''}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <Badge
              status={
                getTaskStatusColor(
                  (task.task_status as GetWalletCollectTaskListStatus) ||
                    GetWalletCollectTaskListStatus.pending,
                ) as any
              }
              text={getTaskStatusText(
                (task.task_status as GetWalletCollectTaskListStatus) ||
                  GetWalletCollectTaskListStatus.pending,
              )}
            />
          </Descriptions.Item>
          <Descriptions.Item label="类型">
            <Tag color={task.execute_type === 'manual' ? 'green' : 'blue'}>
              {task.execute_type === 'manual' ? '手动归集' : '自动归集'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {task.create_at ? new Date(task.create_at).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {task.update_at ? new Date(task.update_at).toLocaleString() : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="成功地址数">{task.success_address_count || 0}</Descriptions.Item>
          <Descriptions.Item label="失败地址数">{task.failed_address_count || 0}</Descriptions.Item>
          <Descriptions.Item label="进行中地址数">{task.pending_address_count || 0}</Descriptions.Item>
          <Descriptions.Item label="撤销地址数">{task.canceled_address_count || 0}</Descriptions.Item>
        </Descriptions>
        {task && <TaskAddressRecordList taskId={task.id || 0} />}
      </>
    </Drawer>
  );
};

export default TaskDetailsDrawer;