import React from 'react';
import { Tag } from 'antd';

interface StatusTagProps {
  status?: string; // status can be undefined
}

// 状态映射到标签颜色和文本
const statusDisplayMap: Record<string, { color: string; text: string }> = {
  completed: { color: 'success', text: '完成' },
  processing: { color: 'processing', text: '处理中' },
  pending: { color: 'warning', text: '等待中' },
  failed: { color: 'error', text: '失败' },
  canceled: { color: 'default', text: '已取消' },
};

const StatusTag: React.FC<StatusTagProps> = ({ status }) => {
  if (!status || !statusDisplayMap[status]) {
    return <Tag color="default">-</Tag>;
  }
  const { color, text } = statusDisplayMap[status];
  return <Tag color={color}>{text}</Tag>;
};

export default StatusTag;