import React from 'react';
import { Typo<PERSON>, Button, Tooltip, Space } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { formatAddress as utilFormatAddress } from '../../../utils/format';
import { copyToClipboard } from '../../../utils/clipboardUtils';
import styles from '../TransferTask.module.css'; // Assuming styles might be shared or specific

const { Text } = Typography;

interface AddressDisplayCellProps {
  address: string;
}

const AddressDisplayCell: React.FC<AddressDisplayCellProps> = ({ address }) => {
  if (!address) {
    return <Text type="secondary">-</Text>;
  }

  return (
    <Tooltip title={address}>
      <div className={styles.addressContainer}>
        <Text ellipsis className={styles.address}>
          {utilFormatAddress(address, 6, 6)}
        </Text>
        <Space>
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={(e) => {
              e.stopPropagation(); // Prevent row click if any
              copyToClipboard(address);
            }}
            size="small"
          />
        </Space>
      </div>
    </Tooltip>
  );
};

export default AddressDisplayCell;