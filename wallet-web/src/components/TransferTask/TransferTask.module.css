.transferTaskList {
    margin-bottom: 24px;
}

.statsCard {
    margin-bottom: 24px;
}

.tableCard {
    margin-bottom: 24px;
}

.tableHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.searchFilters {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.taskIdContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.taskId {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.statusBadge {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.detailsDrawer .ant-descriptions-item {
    padding-bottom: 12px;
}

.transferDetailsList {
    margin-top: 16px;
}

.transferForm {
    padding: 24px;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-bottom: 24px;
}

.formSection {
    margin-bottom: 24px;
}

.formTitle {
    margin-bottom: 16px;
    font-weight: bold;
}

.addressInput {
    width: 100%;
}

.amountInput {
    width: 100%;
}

.gasSettings {
    margin-top: 16px;
}

.submitButton {
    margin-top: 24px;
}

.recipientRow {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.recipientActions {
    margin-left: 8px;
}

.addRecipientButton {
    margin-top: 8px;
}

.tabContent {
    padding-top: 16px;
}

@media (max-width: 768px) {
    .tableHeader {
        flex-direction: column;
        align-items: flex-start;
    }

    .searchFilters {
        margin-bottom: 16px;
        width: 100%;
    }

    .actions {
        width: 100%;
    }
}