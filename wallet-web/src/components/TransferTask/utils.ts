import { GetWalletCollectTaskListStatus } from '../../services/api/model/getWalletCollectTaskListStatus';

// 获取状态文本
export const getTaskStatusText = (status: GetWalletCollectTaskListStatus): string => {
  switch (status) {
    case GetWalletCollectTaskListStatus.completed:
      return '已完成';
    case GetWalletCollectTaskListStatus.processing:
      return '处理中';
    case GetWalletCollectTaskListStatus.failed:
      return '失败';
    case GetWalletCollectTaskListStatus.pending:
      return '等待中';
    case GetWalletCollectTaskListStatus.canceled:
      return '已取消';
    default: {
      //  确保有返回值，即使是未知的状态
      const exhaustiveCheck: never = status;
      return `未知状态: ${exhaustiveCheck}`;
    }
  }
};

// 获取状态颜色
export const getTaskStatusColor = (status: GetWalletCollectTaskListStatus): string => {
  switch (status) {
    case GetWalletCollectTaskListStatus.completed:
      return 'success';
    case GetWalletCollectTaskListStatus.processing:
      return 'processing';
    case GetWalletCollectTaskListStatus.failed:
      return 'error';
    case GetWalletCollectTaskListStatus.pending:
      return 'warning';
    case GetWalletCollectTaskListStatus.canceled:
      return 'default';
    default: {
      // 确保有返回值
      return 'default';
    }
  }
};