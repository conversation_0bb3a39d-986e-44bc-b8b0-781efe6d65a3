import React from 'react';
// Removed Input, Button, SearchOutlined, SyncOutlined, ExportOutlined, FilterOutlined, Tag, Space from antd imports
import { Table, Card, Tooltip, Typography, Row, Col } from 'antd';
// getWalletAfter and message are now handled by the hook
import type { WalletApiApiWalletV1TaskAddressRecord } from '../../services/api/model/walletApiApiWalletV1TaskAddressRecord';
import CustomPagination from '../common/CustomPagination';
import type { ColumnsType } from 'antd/es/table';
// GetWalletTaskAddressRecordStatus is used by the hook
// import styles from './TransferTask.module.css'; // styles are now used in RecordListHeader
import useTaskAddressRecords from './hooks/useTaskAddressRecords'; // Import the custom hook
import RecordListHeader from './components/RecordListHeader'; // Import the new header component
import StatusTag from './components/StatusTag'; // Import StatusTag component
import NetworkCurrencyDisplay from './components/NetworkCurrencyDisplay'; // Import NetworkCurrencyDisplay component

const { Title } = Typography;

interface TaskAddressRecordListProps {
  taskId?: number;
}

/**
 * 任务地址记录列表组件
 */
const TaskAddressRecordList: React.FC<TaskAddressRecordListProps> = ({ taskId }) => {
  const {
    loading,
    addressRecords,
    pagination,
    statusFilter,
    addressFilter,
    setStatusFilter,
    setAddressFilter,
    // fetchAddressRecords, // Not directly called from component anymore, but through handlers
    handlePageChange,
    handleSearch,
    handleReset,
    handleRefresh,
    handleExport,
  } = useTaskAddressRecords({ taskId });

  // statusColorMap is now encapsulated within StatusTag.tsx

  // 表格列定义
  const columns: ColumnsType<WalletApiApiWalletV1TaskAddressRecord> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '转出地址',
      dataIndex: 'sender_address',
      key: 'sender_address',
      render: (address) => (
        <Tooltip placement="topLeft" title={address}>
          <span>{address}</span>
        </Tooltip>
      ),
    },
    {
      title: '接收地址',
      dataIndex: 'receiver_address',
      key: 'receiver_address',
      render: (address) => (
        <Tooltip placement="topLeft" title={address}>
          <span>{address}</span>
        </Tooltip>
      ),
    },
    {
      title: '转账金额',
      dataIndex: 'amount',
      key: 'amount',
    },
    {
      title: '手续费',
      dataIndex: 'fee',
      key: 'fee',
    },
    {
      title: '网络/币种',
      key: 'networkCurrency',
      render: (_, record: WalletApiApiWalletV1TaskAddressRecord) => <NetworkCurrencyDisplay record={record} />,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <StatusTag status={status} />,
    },
    // {
    //   title: '失败原因',
    //   dataIndex: 'fail_reason',
    //   key: 'fail_reason',
    //   render: (text) => text || '-',
    // },
    {
      title: '交易哈希',
      dataIndex: 'transaction_hash',
      key: 'transaction_hash',
      render: (hash) =>
        hash ? (
          <Tooltip placement="topLeft" title={hash}>
            <span>{hash}</span>
          </Tooltip>
        ) : (
          '-'
        ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_at',
      key: 'create_at',
      render: (text: string | undefined) => (text ? new Date(text).toLocaleString() : '-'),
    },
    {
      title: '更新时间',
      dataIndex: 'update_at',
      key: 'update_at',
      render: (text: string | undefined) => (text ? new Date(text).toLocaleString() : '-'),
    },
  ];

  // All data fetching, state management, and handlers are now in useTaskAddressRecords hook.
  // useEffect for initial load and taskId changes is handled within the hook.

  return (
    <Card title={<Title level={5}>任务地址记录</Title>} style={{ marginTop: 16 }}>
      <RecordListHeader
        addressFilter={addressFilter}
        setAddressFilter={setAddressFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        onSearch={handleSearch}
        onReset={handleReset}
        onRefresh={handleRefresh}
        onExport={handleExport}
      />

      <Table
        columns={columns}
        dataSource={addressRecords}
        rowKey="id"
        loading={loading}
        pagination={false}
        scroll={{ x: 1300 }}
      />

      <Row justify="end" style={{ marginTop: 16 }}>
        <Col>
          <CustomPagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onChange={handlePageChange}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default TaskAddressRecordList;
