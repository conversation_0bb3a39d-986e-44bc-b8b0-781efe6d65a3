import React from 'react'; // Removed useState, useEffect
import { Table, Card, Button, Space, Tag, Select, Typography } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import styles from './TransferTask.module.css';
import { getStatusText } from '../../utils/statusUtils';
import { exportDataToCsv } from '../../utils/exportUtils';
import AddressDisplayCell from './components/AddressDisplayCell';
import StatusDisplayCell from './components/StatusDisplayCell';
import TxHashDisplayCell from './components/TxHashDisplayCell';
import TransferDetailsFilters from './components/TransferDetailsFilters';
import ErrorDetailModal from './components/ErrorDetailModal';
import { useTransferDetailsData } from './hooks/useTransferDetailsData';
import { TransferDetail } from './types'; // Import TransferDetail from new location
import NativeFilterSelect from '../common/NativeFilterSelect/NativeFilterSelect'; // 新增导入

const { Text } = Typography;
const { Option } = Select; // Option might still be needed if filters are constructed here, or moved to TransferDetailsFilters

interface TransferDetailsListProps {
  taskId: string;
}

const TransferDetailsList: React.FC<TransferDetailsListProps> = ({ taskId }) => {
  const {
    details,
    totalDetails,
    loading,
    currentPage,
    pageSize,
    searchText,
    filters,
    errorModalVisible,
    selectedError,
    setSearchText,
    setFilters,
    // loadDetails, // Not directly called from component anymore
    handleSearch,
    handleReset,
    handlePageChange,
    showErrorDetails,
    setErrorModalVisible,
  } = useTransferDetailsData(taskId);

  const handleExport = () => {
    const dataToExport = details.map((detail) => ({
      id: detail.id,
      taskId: detail.taskId,
      fromAddress: detail.fromAddress,
      toAddress: detail.toAddress,
      amount: detail.amount,
      currency: detail.currency,
      network: detail.network,
      fee: detail.fee,
      status: getStatusText(detail.status),
      txHash: detail.txHash || '-',
      createdAt: new Date(detail.createdAt).toLocaleString(),
      completedAt: detail.completedAt ? new Date(detail.completedAt).toLocaleString() : '-',
      errorMessage: detail.errorMessage || '-',
    }));

    const headersMap = {
      id: '明细ID',
      taskId: '任务ID',
      fromAddress: '发送地址',
      toAddress: '接收地址',
      amount: '金额',
      currency: '币种',
      network: '网络',
      fee: '手续费',
      status: '状态',
      txHash: '交易哈希',
      createdAt: '创建时间',
      completedAt: '完成时间',
      errorMessage: '错误信息',
    };

    exportDataToCsv(dataToExport, `转账明细记录_${taskId}`, headersMap);
  };

  const columns: ColumnsType<TransferDetail> = [
    {
      title: '发送地址',
      dataIndex: 'fromAddress',
      key: 'fromAddress',
      render: (text: string) => <AddressDisplayCell address={text} />,
    },
    {
      title: '接收地址',
      dataIndex: 'toAddress',
      key: 'toAddress',
      render: (text: string) => <AddressDisplayCell address={text} />,
    },
    {
      title: '金额',
      key: 'amount',
      render: (_, record: TransferDetail) => (
        <Text strong>
          {record.amount} {record.currency}
        </Text>
      ),
      sorter: (a: TransferDetail, b: TransferDetail) => parseFloat(a.amount) - parseFloat(b.amount),
    },
    {
      title: '网络',
      dataIndex: 'network',
      key: 'network',
      render: (text: string) => <Tag color={text === 'ETH' ? 'blue' : text === 'TRX' ? 'red' : 'gold'}>{text}</Tag>,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
        const networkOptions = [
          { text: 'ETH', value: 'ETH' },
          { text: 'TRX', value: 'TRX' },
          { text: 'BSC', value: 'BSC' },
        ];
        return (
          <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
            <NativeFilterSelect
              id="transfer-details-network-filter"
              placeholder="选择网络"
              options={networkOptions}
              value={selectedKeys[0] as string | number | undefined}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              style={{ marginBottom: 8, display: 'block', width: 188 }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  confirm();
                  close();
                }}
                size="small"
                style={{ width: 90 }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  if (clearFilters) {
                    clearFilters();
                  }
                  setSelectedKeys([]);
                  confirm();
                  close();
                }}
                size="small"
                style={{ width: 90 }}
              >
                重置
              </Button>
            </Space>
          </div>
        );
      },
      onFilter: (value: any, record: TransferDetail) => record.network === value,
    },
    {
      title: '手续费',
      key: 'fee',
      render: (_, record: TransferDetail) => (
        <Text>
          {record.fee} {record.network === 'ETH' ? 'ETH' : record.network === 'TRX' ? 'TRX' : 'BNB'}
        </Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: TransferDetail['status'], record: TransferDetail) => (
        <StatusDisplayCell
          status={status}
          errorMessage={record.errorMessage}
          onErrorClick={showErrorDetails}
        />
      ),
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
        const statusOptions = [
          { text: '成功', value: 'success' },
          { text: '失败', value: 'failed' },
          { text: '处理中', value: 'pending' },
        ];
        return (
          <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
            <NativeFilterSelect
              id="transfer-details-status-filter"
              placeholder="选择状态"
              options={statusOptions}
              value={selectedKeys[0] as string | number | undefined}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              style={{ marginBottom: 8, display: 'block', width: 188 }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  confirm();
                  close();
                }}
                size="small"
                style={{ width: 90 }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  if (clearFilters) {
                    clearFilters();
                  }
                  setSelectedKeys([]);
                  confirm();
                  close();
                }}
                size="small"
                style={{ width: 90 }}
              >
                重置
              </Button>
            </Space>
          </div>
        );
      },
      onFilter: (value: any, record: TransferDetail) => record.status === value,
    },
    {
      title: '交易哈希',
      dataIndex: 'txHash',
      key: 'txHash',
      render: (txHash: string | undefined, record: TransferDetail) => (
        <TxHashDisplayCell txHash={txHash} network={record.network} />
      ),
    },
    {
      title: '时间',
      key: 'time',
      render: (_, record: TransferDetail) => (
        <Space direction="vertical" size={0}>
          <Text type="secondary">创建: {new Date(record.createdAt).toLocaleString()}</Text>
          {record.completedAt && <Text type="secondary">完成: {new Date(record.completedAt).toLocaleString()}</Text>}
        </Space>
      ),
      sorter: (a: TransferDetail, b: TransferDetail) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
  ];

  return (
    <div className={styles.transferDetailsList}>
      <Card className={styles.tableCard}>
        <TransferDetailsFilters
          searchText={searchText}
          onSearchTextChange={setSearchText}
          statusFilter={filters.status}
          onStatusFilterChange={(value) => setFilters({ ...filters, status: value })}
          onSearch={handleSearch}
          onReset={handleReset}
        />
        {/* The export button is part of the header actions, let's keep it separate for now or create a dedicated HeaderActions component later */}
        <div className={styles.actionsContainer} style={{ padding: '16px', textAlign: 'right' }}> {/* Added a simple container for actions */}
            <Button icon={<ExportOutlined />} onClick={handleExport}>
                导出明细
            </Button>
        </div>

        <Table
          columns={columns}
          dataSource={details}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalDetails,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 笔转账`,
            onChange: handlePageChange, // Use handlePageChange from hook
          }}
        />
      </Card>

      <ErrorDetailModal
        visible={errorModalVisible}
        errorMessage={selectedError}
        onClose={() => setErrorModalVisible(false)} // This can also be a function returned from the hook if preferred
      />
    </div>
  );
};

export default TransferDetailsList;
