import React, { useState } from 'react'; // Removed useEffect as it's in the hook
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Typography,
  // Row, // Moved to TaskStatisticsCard
  // Col, // Moved to TaskStatisticsCard
  // Statistic, // Moved to TaskStatisticsCard
  message,
  // Drawer, // Moved to TaskDetailsDrawer
  // Descriptions, // Moved to TaskDetailsDrawer
  Badge, // Keep Badge for column rendering
  Modal,
} from 'antd';
import {
  ExportOutlined,
  ReloadOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import styles from './TransferTask.module.css';
// import { RangePickerProps } from 'antd/es/date-picker'; // Not needed
// import moment from 'moment'; // Not needed
// 导入归集任务API
// import { getWalletAfter } from '../../services/api/wallet-after/wallet-after'; // Moved to hook
// import { DemogfApiWalletV1TaskList } from '../../services/api/model/demogfApiWalletV1TaskList'; // Moved to types.ts
// import { GetWalletCollectTaskListParams } from '../../services/api/model/getWalletCollectTaskListParams'; // Not needed, hook manages params
import { GetWalletCollectTaskListStatus } from '../../services/api/model/getWalletCollectTaskListStatus'; // Used in columns render
// import { GetWalletCollectTaskListCoin } from '../../services/api/model/getWalletCollectTaskListCoin'; // Moved to TaskFilters
// import { GetWalletCollectTaskListTaskType } from '../../services/api/model/getWalletCollectTaskListTaskType'; // Moved to TaskFilters
// import { GetWalletCollectTaskListExecuteType } from '../../services/api/model'; // Moved to TaskFilters
// 导入统计数据接口
// import { DemogfApiWalletV1CollectRecordStatisticRes } from '../../services/api/model/demogfApiWalletV1CollectRecordStatisticRes'; // Moved to hook
// import { GetWalletCollectRecordStatisticParams } from '../../services/api/model/getWalletCollectRecordStatisticParams'; // Moved to hook
// 导入自定义分页组件
import CustomPagination from '../common/CustomPagination';
// import TaskAddressRecordList from './TaskAddressRecordList'; // Moved to TaskDetailsDrawer
// import { useMemoizedFn } from 'ahooks'; // Moved to hook
import { TransferTask } from './types'; // Import TransferTask from types.ts
import { getTaskStatusText, getTaskStatusColor } from './utils'; // Import utility functions
import { useTransferTasks } from './hooks/useTransferTasks'; // Import the new hook
import TaskStatisticsCard from './components/TaskStatisticsCard'; // Import the new statistics card component
import TaskFilters from './components/TaskFilters'; // Import the new filters component
import TaskDetailsDrawer from './components/TaskDetailsDrawer'; // Import the new details drawer component

const { Text } = Typography;
// const { RangePicker } = DatePicker; // Moved to TaskFilters

// 转账任务类型定义
// type TransferTask = DemogfApiWalletV1TaskList; // Moved to types.ts

const TransferTaskList: React.FC = () => {
  const {
    tasks,
    totalTasks,
    loading,
    currentPage,
    pageSize,
    searchText,
    setSearchText,
    dateRange,
    statistics,
    taskParams, // from hook
    // setTaskParams, // from hook, if direct manipulation is needed
    loadTasks: reloadTasks, // renamed from hook's loadInitialData
    handleSearch,
    handleReset,
    handleExport,
    handlePageChange,
    handleDateRangeChange,
    handleFilterChange, // from hook
  } = useTransferTasks();

  const [detailsVisible, setDetailsVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TransferTask | null>(null);
  const [cancelModalVisible, setCancelModalVisible] = useState(false);
  const [taskToCancel, setTaskToCancel] = useState<string | null>(null);

  // All data loading, state management for tasks, stats, pagination, and basic filters are now in useTransferTasks.
  // Event handlers like handleSearch, handleReset, handleExport, handlePageChange, handleDateRangeChange are from the hook.

  const showTaskDetails = (task: TransferTask) => {
    setSelectedTask(task);
    setDetailsVisible(true);
  };

  // 取消任务 - This might need to call an API and then reload tasks.
  // If API call is generic, it can be part of the hook. Otherwise, keep it here.
  // For now, assuming it might involve specific UI logic beyond just data.
  const handleCancelTask = () => {
    if (taskToCancel) {
      // TODO: Call API to cancel task
      // Example: await cancelTaskApi(taskToCancel);
      message.success('任务已取消');
      setCancelModalVisible(false);
      setTaskToCancel(null);
      reloadTasks(); // Reload tasks after cancellation
    }
  };

  // getStatusText and getStatusColor are imported from ./utils.ts

  // handleTableChange is not used for now, can be removed or implemented if table-specific filters are needed.
  const handleTableChange = (pagination: any, filters: any) => {
    // console.log('Table filters:', filters);
  };

  // Specific filter handlers like handleStatusChange are now covered by handleFilterChange from the hook.
  // Example: onChange={(e) => handleFilterChange('status', e.target.value)}

  // useEffect for initial load is now inside the hook.

  const columns: ColumnsType<TransferTask> = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: number | undefined) => (
        <div className={styles.taskIdContainer}>
          <Text ellipsis className={styles.taskId}>
            {text}
          </Text>
        </div>
      ),
    },
    {
      title: '任务名称',
      dataIndex: 'task_name',
      key: 'task_name',
      render: (text: string | undefined) => <Text strong>{text}</Text>,
    },
    {
      title: '网络/币种',
      key: 'networkCurrency',
      render: (_, record: TransferTask) => (
        <Space>
          <Tag color={record.network_type === 'ETH' ? 'blue' : record.network_type === 'TRON' ? 'red' : 'gold'}>
            {record.network_type}
          </Tag>
          <Text>{record.token_type}</Text>
        </Space>
      ),
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (text: string | undefined, record: TransferTask) => (
        <Tag color={record.task_type === 'one2many' ? 'green' : 'blue'}>
          {text === 'one2many' ? '单转多' : '多转单'}
        </Tag>
      ),
    },
    {
      title: '成功地址数',
      key: 'success_address_count',
      dataIndex: 'success_address_count',
      render: (count: number | undefined) => <Text>{count || 0}</Text>,
      sorter: (a: TransferTask, b: TransferTask) => (a.success_address_count || 0) - (b.success_address_count || 0),
    },
    {
      title: '状态',
      dataIndex: 'task_status',
      key: 'task_status',
      render: (text: GetWalletCollectTaskListStatus | undefined) => (
        <Space>
          <Badge status={getTaskStatusColor(text || GetWalletCollectTaskListStatus.pending) as any} />
          <Text>{getTaskStatusText(text || GetWalletCollectTaskListStatus.pending)}</Text>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_at',
      key: 'create_at',
      render: (text: string | undefined) => (text ? new Date(text).toLocaleString() : ''),
      sorter: (a: TransferTask, b: TransferTask) =>
        new Date(a.create_at || '').getTime() - new Date(b.create_at || '').getTime(),
    },
    {
      title: '归集类型',
      dataIndex: 'execute_type',
      key: 'execute_type',
      render: (type: string | undefined) => (
        <Tag color={type === 'manual' ? 'green' : 'blue'}>{type === 'manual' ? '手动归集' : '自动归集'}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: TransferTask) => (
        <Space>
          <Button type="text" icon={<EyeOutlined />} onClick={() => showTaskDetails(record)} title="查看详情" />
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.transferTaskList}>
      <TaskStatisticsCard statistics={statistics} />

      <Card className={styles.tableCard}>
        <div className={styles.tableHeader}>
          <TaskFilters
            searchText={searchText}
            setSearchText={setSearchText}
            dateRange={dateRange}
            taskParams={taskParams}
            handleFilterChange={handleFilterChange}
            handleDateRangeChange={handleDateRangeChange as any} // Cast if types are complex from hook
            onSearch={handleSearch}
            onReset={handleReset}
          />
          <div className={styles.actions}>
            <Button
              icon={<ReloadOutlined />}
              onClick={reloadTasks} // Use reloadTasks from hook
              loading={loading}
              style={{ marginRight: 8 }}
            >
              刷新
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport}>
              导出
            </Button>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          loading={{ spinning: loading, tip: '正在加载归集任务...' }}
          onChange={handleTableChange}
          pagination={false}
        />

        {/* 使用自定义分页组件 */}
        <CustomPagination
          current={currentPage}
          pageSize={pageSize}
          total={totalTasks}
          onChange={handlePageChange}
          showSizeChanger={true}
          showQuickJumper={true}
          showTotal={(total) => `共 ${total} 个任务`}
        />
      </Card>

      <TaskDetailsDrawer
        visible={detailsVisible}
        onClose={() => setDetailsVisible(false)}
        task={selectedTask}
      />

      {/* 取消任务确认对话框 */}
      <Modal
        title="取消任务"
        open={cancelModalVisible}
        onOk={handleCancelTask}
        onCancel={() => setCancelModalVisible(false)}
        okText="确认取消"
        cancelText="返回"
      >
        <p>确定要取消该转账任务吗？取消后无法恢复。</p>
      </Modal>
    </div>
  );
};

export default TransferTaskList;
