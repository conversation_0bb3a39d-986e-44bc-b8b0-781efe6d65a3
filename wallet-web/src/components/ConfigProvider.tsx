import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import { configService } from '../services/config';

interface ConfigProviderProps {
  children: React.ReactNode;
}

/**
 * Configuration Provider Component
 * Loads application configuration before rendering children
 */
const ConfigProvider: React.FC<ConfigProviderProps> = ({ children }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadConfig = async () => {
      try {
        await configService.loadConfig();
        console.log('Configuration loaded successfully');
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load configuration';
        setError(errorMessage);
        console.error('Failed to load configuration:', err);
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, []);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <div>Loading configuration...</div>
      </div>
    );
  }

  if (error) {
    console.warn('Configuration loading failed, but continuing with fallback:', error);
    // Don't block the app, just log the error and continue
  }

  return <>{children}</>;
};

export default ConfigProvider;
