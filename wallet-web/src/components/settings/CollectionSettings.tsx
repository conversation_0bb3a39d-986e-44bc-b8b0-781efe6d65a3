import React, { useState, useEffect, useCallback } from 'react';
import { Form, message, Spin } from 'antd'; // <PERSON><PERSON>, Divider, Switch removed
// SaveOutlined removed as it's used in child components
import { getWalletInfo, updateWalletSettings } from '../../services/walletService';
import GoogleAuthWithPasswordModal from '../common/GoogleAuthWithPasswordModal';
import BasicSettingsForm from './CollectionSettings/BasicSettingsForm';
import StrategySettingsForm from './CollectionSettings/StrategySettingsForm';
// import CronSettingsForm from './CollectionSettings/CronSettingsForm';
import FeeSettingsForm from './CollectionSettings/FeeSettingsForm'; // Import FeeSettingsForm
import './Settings.css';
import { WalletApiApiWalletV1WalletInfo } from '../../services/api/model';

const CollectionSettings: React.FC = () => {
  const [basicForm] = Form.useForm();
  const [strategyForm] = Form.useForm();
  // const [cronForm] = Form.useForm(); // Cron form removed
  const [feeForm] = Form.useForm(); // Add feeForm instance

  const [basicLoading, setBasicLoading] = useState(false);
  const [strategyLoading, setStrategyLoading] = useState(false);
  // const [cronLoading, setCronLoading] = useState(false); // Cron loading removed
  const [feeLoading, setFeeLoading] = useState(false); // Add feeLoading state
  const [infoLoading, setInfoLoading] = useState(true);

  const [walletInfo, setWalletInfo] = useState<WalletApiApiWalletV1WalletInfo | null>(null);
  // const [cronEnabled, setCronEnabled] = useState(false); // Cron enabled removed
  const [googleModalVisible, setGoogleModalVisible] = useState(false);
  const [currentAction, setCurrentAction] = useState<'basic' | 'strategy' | 'fee'>('basic'); // Removed 'cron' from CurrentAction
  const [formValues, setFormValues] = useState<any>(null);

  // 表单变化状态
  const [basicFormChanged, setBasicFormChanged] = useState(false);
  const [strategyFormChanged, setStrategyFormChanged] = useState(false);
  // const [cronFormChanged, setCronFormChanged] = useState(false); // Cron form changed removed
  const [feeFormChanged, setFeeFormChanged] = useState(false); // Add feeFormChanged state

  // 跟踪表单中哪些字段发生了变化
  const [changedFields, setChangedFields] = useState<Record<string, any>>({
    basic: {},
    strategy: {},
    // cron: {}, // Cron changed fields removed
    fee: {}, // Add fee to changedFields
  });

  // 添加一个用于标记组件是否可见的状态

  // 添加一个存储初始数据的状态
  const [initialValues, setInitialValues] = useState<{
    trxCollectAddress?: string;
    ethCollectAddress?: string;
  }>({});

  // 定义获取钱包信息的函数
  const fetchWalletInfo = useCallback(async () => {
    try {
      setInfoLoading(true);
      const info = await getWalletInfo();
      setWalletInfo(info.wallet_info || null);

      if (info.wallet_info) {

        // 设置定时归集开关状态 - Cron removed
        // setCronEnabled(info.wallet_info.cron_collect_switch === 1);

        // 保存初始地址值用于后续比较
        setInitialValues({
          trxCollectAddress: info.wallet_info.trx_collect_address || '',
          ethCollectAddress: info.wallet_info.eth_collect_address || '',
        });

        // 表单初始值现在由子组件自己处理
        // basicForm.setFieldsValue(...)
        // strategyForm.setFieldsValue(...)
        // cronForm.setFieldsValue(...)

        // 重置表单变化状态
        setBasicFormChanged(false);
        setStrategyFormChanged(false);
        // setCronFormChanged(false); // Cron form changed removed
        setFeeFormChanged(false); // Reset feeFormChanged

        // 重置变化字段记录
        setChangedFields({
          basic: {},
          strategy: {},
          // cron: {}, // Cron changed fields removed
          fee: {}, // Reset changedFields.fee
        });
      }
    } catch (error) {
      console.error('获取钱包信息失败:', error);
      message.error('获取钱包信息失败，请稍后重试');
    } finally {
      setInfoLoading(false);
    }
  }, []); // Removed form instances from dependencies

  // 监听组件生命周期
  useEffect(() => {
    // 组件挂载时设置为可见

    // 每次组件可见时获取钱包信息
    fetchWalletInfo();
  }, [fetchWalletInfo]);

  // 跟踪表单字段变化，只记录与接口数据不同的字段
  const handleBasicFormChange = (changedValues: any, allValues: any) => {
    // 检查地址字段是否与初始值不同
    const addressChanged =
      (changedValues.trxCollectAddress !== undefined &&
        changedValues.trxCollectAddress !== initialValues.trxCollectAddress) ||
      (changedValues.ethCollectAddress !== undefined &&
        changedValues.ethCollectAddress !== initialValues.ethCollectAddress);

    // 检查私钥字段是否有输入
    const privateKeyChanged =
      (changedValues.trxFeePrivateKey !== undefined && changedValues.trxFeePrivateKey.trim() !== '') ||
      (changedValues.ethFeePrivateKey !== undefined && changedValues.ethFeePrivateKey.trim() !== '');

    // 只有当地址发生变化或私钥有输入时才标记表单变化
    if (addressChanged || privateKeyChanged) {
      setBasicFormChanged(true);

      // 过滤掉与初始值相同的地址字段
      const filteredChanges = { ...changedValues };
      if (filteredChanges.trxCollectAddress === initialValues.trxCollectAddress) {
        delete filteredChanges.trxCollectAddress;
      }
      if (filteredChanges.ethCollectAddress === initialValues.ethCollectAddress) {
        delete filteredChanges.ethCollectAddress;
      }

      // 只记录真正变化的字段
      if (Object.keys(filteredChanges).length > 0) {
        setChangedFields((prev) => ({
          ...prev,
          basic: {
            ...prev.basic,
            ...filteredChanges,
          },
        }));
      }
    }
  };

  const handleStrategyFormChange = (changedValues: any, allValues: any) => {
    setStrategyFormChanged(true);
    // 更新变化的字段
    setChangedFields((prev) => ({
      ...prev,
      strategy: {
        ...prev.strategy,
        ...changedValues,
      },
    }));
  };

  // const handleCronFormChange = (changedValues: any, allValues: any) => { // Cron form change handler removed
  //   setCronFormChanged(true);
  //   // 更新变化的字段
  //   setChangedFields((prev) => ({
  //     ...prev,
  //     cron: {
  //       ...prev.cron,
  //       ...changedValues,
  //     },
  //   }));
  // };

  const handleFeeFormChange = (changedValues: any, allValues: any) => {
    setFeeFormChanged(true);
    setChangedFields((prev) => ({
      ...prev,
      fee: {
        ...prev.fee,
        ...changedValues,
      },
    }));
  };

  // 基本设置提交前，验证字段是否真正发生变化
  const handleBasicFormSubmit = async (values: any) => {
    try {
      await basicForm.validateFields();

      // 获取当前表单所有值
      const currentValues = basicForm.getFieldsValue();

      // 过滤掉未变化的地址字段
      const realChanges = { ...changedFields.basic };
      if (currentValues.trxCollectAddress === initialValues.trxCollectAddress) {
        delete realChanges.trxCollectAddress;
      }
      if (currentValues.ethCollectAddress === initialValues.ethCollectAddress) {
        delete realChanges.ethCollectAddress;
      }

      // 保留所有私钥字段（如果有值）
      if (currentValues.trxFeePrivateKey && currentValues.trxFeePrivateKey.trim() !== '') {
        realChanges.trxFeePrivateKey = currentValues.trxFeePrivateKey;
      }
      if (currentValues.ethFeePrivateKey && currentValues.ethFeePrivateKey.trim() !== '') {
        realChanges.ethFeePrivateKey = currentValues.ethFeePrivateKey;
      }

      // 检查是否有实际变化
      if (Object.keys(realChanges).length === 0) {
        message.info('没有检测到变化，无需保存');
        return;
      }

      // 只提交变化了的字段
      setFormValues(realChanges);
      setCurrentAction('basic');
      setGoogleModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 策略归集设置提交前，打开谷歌验证码弹窗
  const handleStrategyFormSubmit = async (values: any) => {
    try {
      await strategyForm.validateFields();
      // 只提交变化了的字段
      const changedValues = changedFields.strategy;
      setFormValues(changedValues);
      setCurrentAction('strategy');
      setGoogleModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 定时归集设置提交前，打开谷歌验证码弹窗 - Cron form submit handler removed
  // const handleCronFormSubmit = async () => {
  //   try {
  //     await cronForm.validateFields();
  //     // 只提交变化了的字段
  //     const changedValues = changedFields.cron;
  //     setFormValues(changedValues);
  //     setCurrentAction('cron');
  //     setGoogleModalVisible(true);
  //   } catch (error) {
  //     console.error('表单验证失败:', error);
  //   }
  // };

  const handleFeeFormSubmit = async (values: any) => {
    try {
      await feeForm.validateFields();
      const changedValues = changedFields.fee;
      if (Object.keys(changedValues).length === 0 && !feeFormChanged) { // Also check feeFormChanged for direct toggles/changes not in changedValues
        message.info('没有检测到手续费设置变化，无需保存');
        return;
      }
      setFormValues(changedValues);
      setCurrentAction('fee');
      setGoogleModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // --- Helper functions for saving settings ---
  const handleBasicSettingsSave = async (googleCode: string, password: string) => {
    setBasicLoading(true);
    const params: any = {
      google_code: googleCode,
      password: password,
    };

    if (formValues.trxCollectAddress !== undefined) {
      params.trx_collect_address = formValues.trxCollectAddress;
    }
    if (formValues.ethCollectAddress !== undefined) {
      params.eth_collect_address = formValues.ethCollectAddress;
    }
    if (formValues.trxFeePrivateKey && formValues.trxFeePrivateKey.trim() !== '') {
      params.trx_fee_private_key = formValues.trxFeePrivateKey;
    }
    if (formValues.ethFeePrivateKey && formValues.ethFeePrivateKey.trim() !== '') {
      params.eth_fee_private_key = formValues.ethFeePrivateKey;
    }

    await updateWalletSettings(params);
    setBasicLoading(false);
    message.success('基本设置已保存');
  };

  const handleStrategySettingsSave = async (googleCode: string, password: string) => {
    setStrategyLoading(true);
    const params: any = {
      strategy_collect_switch: true, // 默认开启策略归集
      google_code: googleCode,
      password: password,
    };

    // 处理阈值设置
    if (formValues.ethThreshold !== undefined) {
      params.eth_collect_threshold = Number(formValues.ethThreshold) || 0;
    } else if (strategyFormChanged) {
      params.eth_collect_threshold = Number(strategyForm.getFieldValue('ethThreshold')) || 0;
    }

    if (formValues.trxThreshold !== undefined) {
      params.trx_collect_threshold = Number(formValues.trxThreshold) || 0;
    } else if (strategyFormChanged) {
      params.trx_collect_threshold = Number(strategyForm.getFieldValue('trxThreshold')) || 0;
    }

    if (formValues.usdtThreshold !== undefined) {
      params.usdt_collect_threshold = Number(formValues.usdtThreshold) || 0;
    } else if (strategyFormChanged) {
      params.usdt_collect_threshold = Number(strategyForm.getFieldValue('usdtThreshold')) || 0;
    }

    await updateWalletSettings(params);
    setStrategyLoading(false);
    message.success('策略归集设置已保存');
  };

  // const handleCronSettingsSave = async (googleCode: string, password: string) => { // Cron settings save handler removed
  //   setCronLoading(true);
  //   const walletAfterApi = getSetting();
  //   const params: any = {
  //     switch: cronEnabled,
  //     google_code: googleCode,
  //     password: password,
  //   };
  //
  //   if (cronEnabled) {
  //     if (formValues.cronTime !== undefined) {
  //       params.time = formValues.cronTime;
  //     } else if (cronFormChanged) {
  //       params.time = cronForm.getFieldValue('cronTime') || '';
  //     }
  //   }
  //   await walletAfterApi.postWalletCronCollectSetting(params);
  //   setCronLoading(false);
  //   message.success('定时归集设置已保存');
  // };

  const handleFeeSettingsSave = async (googleCode: string, password: string) => {
    setFeeLoading(true);
    const params: any = {
      google_code: googleCode,
      password: password,
      ...formValues, // Spread changed fee values
    };

    // Ensure numeric types for API if they are not already
    if (params.eth_fee_max !== undefined) params.eth_fee_max = Number(params.eth_fee_max);
    if (params.erc20_fee_max !== undefined) params.erc20_fee_max = Number(params.erc20_fee_max);
    if (params.eth_fee_amount !== undefined) params.eth_fee_amount = Number(params.eth_fee_amount);
    if (params.trx_fee_amount !== undefined) params.trx_fee_amount = Number(params.trx_fee_amount);
    if (params.eth_gas_price !== undefined) params.eth_gas_price = Number(params.eth_gas_price);
    if (params.eth_gas_limit !== undefined) params.eth_gas_limit = Number(params.eth_gas_limit);
    if (params.erc20_gas_price !== undefined) params.erc20_gas_price = Number(params.erc20_gas_price);
    if (params.erc20_gas_limit !== undefined) params.erc20_gas_limit = Number(params.erc20_gas_limit);
    if (params.trx_fee_max !== undefined) params.trx_fee_max = Number(params.trx_fee_max);
    if (params.trx_activate_amount !== undefined) params.trx_activate_amount = Number(params.trx_activate_amount);
    if (params.trx_keep_amount !== undefined) params.trx_keep_amount = Number(params.trx_keep_amount);
    if (params.eth_keep_amount !== undefined) params.eth_keep_amount = Number(params.eth_keep_amount);
    if (params.trc20_min_required_energy !== undefined) params.trc20_min_required_energy = Number(params.trc20_min_required_energy);
    if (params.trc20_max_energy_fee !== undefined) params.trc20_max_energy_fee = Number(params.trc20_max_energy_fee);
    if (params.trc20_min_required_bandwidth !== undefined) params.trc20_min_required_bandwidth = Number(params.trc20_min_required_bandwidth);

    // Remove gas price and limit if mode is auto (1)
    if (params.eth_fee_mode === 1) {
      delete params.eth_gas_price;
      delete params.eth_gas_limit;
      delete params.eth_fee_amount;
    }

    // Remove TRX fee amount if mode is auto (1)
    if (params.trx_fee_mode === 1) {
      delete params.trx_fee_amount;
    }

    await updateWalletSettings(params);
    setFeeLoading(false);
    message.success('手续费设置已保存');
  };

  // --- Main Google Verify Handler ---
  const handleGoogleVerify = async (googleCode: string, password: string) => {
    try {
      switch (currentAction) {
        case 'basic':
          await handleBasicSettingsSave(googleCode, password);
          break;
        case 'strategy':
          await handleStrategySettingsSave(googleCode, password);
          break;
        // case 'cron': // Cron case removed
        //   await handleCronSettingsSave(googleCode, password);
        //   break;
        case 'fee':
          await handleFeeSettingsSave(googleCode, password);
          break;
      }
      setFormValues(null);
      setGoogleModalVisible(false);
      fetchWalletInfo();
    } catch (error: any) {
      console.error('保存设置失败:', error);
      message.error(error.message || '操作失败，请稍后重试');
      // Ensure loading states are reset on error for the specific action
      if (currentAction === 'basic') setBasicLoading(false);
      if (currentAction === 'strategy') setStrategyLoading(false);
      // if (currentAction === 'cron') setCronLoading(false); // Cron loading reset removed
      if (currentAction === 'fee') setFeeLoading(false); // Reset feeLoading on error
    }
  };

  // 移除策略归集开关处理函数

  // const handleCronToggle = (checked: boolean) => { // Cron toggle handler removed
  //   setCronEnabled(checked);
  //   setCronFormChanged(true);
  //   // 记录开关状态变化
  //   setChangedFields((prev) => ({
  //     ...prev,
  //     cron: {
  //       ...prev.cron,
  //       switch: checked,
  //     },
  //   }));
  // };

  if (infoLoading) {
    return (
      <div className="collection-settings-loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="collection-settings">
      <h2 className="settings-title">归集设置</h2>

      <BasicSettingsForm
        form={basicForm}
        initialValues={initialValues}
        walletInfo={walletInfo}
        loading={basicLoading}
        formChanged={basicFormChanged}
        onSubmit={handleBasicFormSubmit}
        onValuesChange={handleBasicFormChange}
      />

      <StrategySettingsForm
        form={strategyForm}
        walletInfo={walletInfo}
        loading={strategyLoading}
        formChanged={strategyFormChanged}
        onSubmit={handleStrategyFormSubmit}
        onValuesChange={handleStrategyFormChange}
      />
{/*
      <CronSettingsForm
        form={cronForm}
        walletInfo={walletInfo}
        cronEnabled={cronEnabled}
        loading={cronLoading}
        formChanged={cronFormChanged}
        onSubmit={handleCronFormSubmit}
        onValuesChange={handleCronFormChange}
        onToggle={handleCronToggle}
      /> */}

      <FeeSettingsForm
        form={feeForm}
        walletInfo={walletInfo}
        loading={feeLoading}
        formChanged={feeFormChanged}
        onSubmit={handleFeeFormSubmit}
        onValuesChange={handleFeeFormChange}
      />

      {/* 使用带密码的谷歌验证码组件 */}
      <GoogleAuthWithPasswordModal
        visible={googleModalVisible}
        onConfirm={handleGoogleVerify}
        onCancel={() => setGoogleModalVisible(false)}
        promptText="请输入谷歌验证码和密码完成设置"
        confirmText="确认保存"
      />
    </div>
  );
};

export default CollectionSettings;
