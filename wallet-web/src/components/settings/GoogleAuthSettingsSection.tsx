import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Modal, Typography, Spin, QRCode } from 'antd';
import { SafetyOutlined, CopyOutlined, EditOutlined } from '@ant-design/icons';
import GoogleAuthWithPasswordModal from '../common/GoogleAuthWithPasswordModal';
import { generateGoogleAuthCode } from '../../services/walletService';
import { getAuth } from '../../services/api/auth/auth';
import { getWallet } from '../../services/api/wallet/wallet'; // Import getWallet
import './SecuritySettings.css'; // 复用部分样式
import { copyToClipboard } from '../../utils/clipboardUtils';

const { Text } = Typography;

interface GoogleAuthSettingsSectionProps {
  // Potentially, pass the API instance or specific functions if preferred
}

const GoogleAuthSettingsSection: React.FC<GoogleAuthSettingsSectionProps> = () => {
  const [googleCodeForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isGoogleAuthEnabled, setIsGoogleAuthEnabled] = useState(false); // New state

  // 修改谷歌验证器相关
  const [modifyGoogleModalVisible, setModifyGoogleModalVisible] = useState(false);
  const [newGoogleAuthData, setNewGoogleAuthData] = useState<{ secret: string; qrCode: string }>({
    secret: '',
    qrCode: '',
  });
  // const [verifyGoogleModalVisible, setVerifyGoogleModalVisible] = useState(false); // Removed
  const [finalConfirmModalVisible, setFinalConfirmModalVisible] = useState(false);
  const [newGoogleCode, setNewGoogleCode] = useState('');

  // Function to fetch wallet info and update isGoogleAuthEnabled state
  const internalFetchWalletInfo = async () => {
    try {
      const walletApi = getWallet(); // Get wallet API instance
      const response = await walletApi.getWalletInfo(); // Call getWalletInfo without arguments
      if (response && response.wallet_info) { // Access wallet_info directly from response
        setIsGoogleAuthEnabled(response.wallet_info.google_code_switch || false); // Provide a default value
      } else {
        message.error('获取钱包信息失败: 数据格式错误');
      }
    } catch (error: any) {
      message.error('获取钱包信息失败: ' + (error.message || '未知错误'));
    }
  };

  // Fetch wallet info on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await internalFetchWalletInfo();
      setLoading(false);
    };
    fetchData();
  }, []);

  // 修改谷歌验证器 - 直接生成新的谷歌验证码
  const handleModifyGoogleAuth = () => {
    // Regardless of whether it's enabled or not, directly proceed to generate new auth details.
    // The final confirmation step (GoogleAuthWithPasswordModal) will handle verification if it was previously enabled.
    handleGenerateNewGoogleAuth();
  };

  // Removed handleVerifyOldGoogleCode function

  // 生成新的谷歌验证码
  const handleGenerateNewGoogleAuth = async () => {
    setLoading(true);
    try {
      const data = await generateGoogleAuthCode();
      if (!data || (!data.secret && !data.qrCode)) {
        throw new Error('返回的谷歌验证码数据无效');
      }
      const qrCodeUrl =
        data.qrCode && data.qrCode.startsWith('otpauth://')
          ? data.qrCode
          : data.qrCode
            ? decodeURIComponent(data.qrCode)
            : '';
      setNewGoogleAuthData({
        secret: data.secret || '',
        qrCode: qrCodeUrl,
      });
      setModifyGoogleModalVisible(true);
    } catch (error: any) {
      console.error('获取新谷歌验证码失败:', error);
      message.error('获取新谷歌验证码失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 复制Secret Key到剪贴板
  const handleCopySecretKey = () => {
    copyToClipboard(newGoogleAuthData.secret);
  };

  // 处理谷歌验证码提交 - 收集新验证码，然后显示最终确认对话框
  const handleBindNewGoogleCode = async () => {
    try {
      const values = await googleCodeForm.validateFields();
      setNewGoogleCode(values.googleCode);
      setModifyGoogleModalVisible(false);
      googleCodeForm.resetFields(); // Reset for next use
      setFinalConfirmModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 最终提交处理函数 - 使用GoogleAuthWithPasswordModal组件
  const handleFinalSubmit = async (currentGoogleCode: string, password: string) => {
    try {
      setLoading(true);
      const walletAfterApi = getAuth();
      await walletAfterApi.postWalletRebindGoogleCode({
        code: currentGoogleCode, // This is the current/old google code for verification
        new_secret: newGoogleAuthData.secret,
        new_code: newGoogleCode, // This is the code from the new authenticator app
        password: password,
      });
      message.success('谷歌验证码已成功更新');
      setFinalConfirmModalVisible(false);
      setNewGoogleCode('');
      await internalFetchWalletInfo(); // Re-fetch wallet info to update isGoogleAuthEnabled state
    } catch (error: any) {
      console.error('重新绑定谷歌验证码失败:', error);
      message.error(error.message || '操作失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const cancelAndResetGoogleForms = () => {
    // setVerifyGoogleModalVisible(false); // Removed
    setModifyGoogleModalVisible(false);
    setFinalConfirmModalVisible(false);
    googleCodeForm.resetFields();
    setNewGoogleCode('');
    // setNewGoogleAuthData({ secret: '', qrCode: '' }); // Optionally reset this if modal is fully destroyed on close
  };


  return (
    <>
      <div className="security-section">
        <div className="two-factor-header">
          <h3 className="security-section-title">谷歌验证器</h3>
          <Button type="primary" icon={<EditOutlined />} onClick={handleModifyGoogleAuth}>
            {isGoogleAuthEnabled ? '修改' : '启用'} {/* Use new state */}
          </Button>
        </div>
        <p className="security-section-desc">
          {isGoogleAuthEnabled /* Use new state */
            ? '谷歌验证器已启用，为保障账户安全，敏感操作需要使用谷歌验证码。'
            : '谷歌验证器未启用，建议启用以增强账户安全。'}
        </p>
      </div>

      {/* Removed the initial "验证当前谷歌验证码Modal" as it's redundant */}

      {/* 修改/设置新谷歌验证器Modal */}
      <Modal
        title={<div style={{ textAlign: 'center', fontSize: '18px', fontWeight: 500 }}>{isGoogleAuthEnabled ? '设置新的谷歌验证器' : '启用谷歌验证器'}</div>}
        open={modifyGoogleModalVisible}
        footer={null}
        onCancel={() => !loading && cancelAndResetGoogleForms()}
        width={550}
        centered
        bodyStyle={{ padding: '24px 24px 12px' }}
        style={{ borderRadius: '12px', overflow: 'hidden' }}
        destroyOnClose
        className="google-auth-modal"
      >
        <div style={{ textAlign: 'center', marginBottom: '20px' }}>
          <SafetyOutlined style={{ fontSize: '36px', color: '#1890ff', backgroundColor: '#e6f7ff', padding: '12px', borderRadius: '50%' }} />
        </div>
        <div style={{ background: 'white', padding: '16px', borderRadius: '8px' }}>
          <p style={{ fontSize: '14px', color: '#666', marginBottom: '16px' }}>请使用谷歌验证器 (Google Authenticator) 应用扫描下方二维码：</p>
          <div style={{ padding: '16px', borderRadius: '12px', background: '#f8f8f8', marginBottom: '20px', display: 'flex', justifyContent: 'center' }}>
            {loading && !newGoogleAuthData.qrCode ? <Spin size="large" style={{ display: 'block', margin: '40px auto' }} /> : null}
            {!loading && newGoogleAuthData.qrCode ? <QRCode value={newGoogleAuthData.qrCode} size={160} /> : null}
            {!loading && !newGoogleAuthData.qrCode ? <Text type="danger">无法加载二维码，请重试。</Text> : null}
          </div>
          <div style={{ marginBottom: '20px' }}>
            <Text strong style={{ display: 'block', marginBottom: '8px', fontSize: '14px' }}>密钥</Text>
            <div style={{ display: 'flex', alignItems: 'center', background: '#f8f8f8', padding: '4px', borderRadius: '8px', boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.05)' }}>
              <Input value={newGoogleAuthData.secret} readOnly style={{ background: 'transparent', border: 'none', boxShadow: 'none', fontFamily: 'monospace', fontSize: '15px', fontWeight: 500, padding: '8px 12px', color: '#333' }} />
              <Button type="primary" icon={<CopyOutlined />} onClick={handleCopySecretKey} style={{ flexShrink: 0, borderRadius: '6px', marginRight: '4px', height: '36px' }} />
            </div>
          </div>
          <div style={{ textAlign: 'center', marginBottom: '16px' }}>
            <p style={{ fontSize: '14px', color: '#666' }}>打开验证器应用，输入显示的6位验证码</p>
          </div>
          <Form form={googleCodeForm} layout="vertical" onFinish={handleBindNewGoogleCode}>
            <Form.Item name="googleCode" rules={[{ required: true, message: '请输入验证码' }, { pattern: /^\d{6}$/, message: '验证码必须是6位数字' }]}>
              <Input prefix={<SafetyOutlined style={{ color: '#bfbfbf' }} />} placeholder="请输入6位验证码" maxLength={6} size="large" style={{ width: '100%', height: '50px', borderRadius: '8px', fontSize: '16px', textAlign: 'center', letterSpacing: '8px' }} />
            </Form.Item>
            <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
              <Button onClick={() => cancelAndResetGoogleForms()} style={{ flex: 1, height: '44px', borderRadius: '8px' }} disabled={loading}>取消</Button>
              <Button type="primary" htmlType="submit" loading={loading} style={{ flex: 1, height: '44px', borderRadius: '8px' }}>下一步</Button>
            </div>
          </Form>
        </div>
      </Modal>

      {/* 最终确认对话框 - 使用GoogleAuthWithPasswordModal组件 */}
      <GoogleAuthWithPasswordModal
        visible={finalConfirmModalVisible}
        onConfirm={handleFinalSubmit}
        onCancel={() => !loading && cancelAndResetGoogleForms()}
        title={<div style={{ textAlign: 'center', fontSize: '18px', fontWeight: 500 }}>安全验证</div>}
        promptText="请完成以下安全验证，以修改您的谷歌验证器设置"
        passwordLabel="钱包密码"
        googleCodeLabel="当前谷歌验证码" // Label changed for clarity
        confirmText="确认修改"
      />
    </>
  );
};

export default GoogleAuthSettingsSection;