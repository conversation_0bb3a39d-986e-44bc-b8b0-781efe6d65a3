import React, { useState, useEffect } from 'react';
import { Form, Switch, Button, message, Divider } from 'antd';
import {
  BellOutlined,
  SafetyOutlined,
  DollarOutlined,
  MailOutlined,
  MessageOutlined,
  NotificationOutlined,
} from '@ant-design/icons';
import {
  getUserNotificationSettings,
  updateUserNotificationSettings,
  NotificationSettings as NotificationSettingsType,
} from '../../services/userService';
import './NotificationSettings.css';

const NotificationSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    const fetchNotificationSettings = async () => {
      try {
        const settings = await getUserNotificationSettings();
        form.setFieldsValue(settings);
      } catch (error) {
        console.error('获取通知设置失败:', error);
        message.error('获取通知设置失败，请稍后重试');
      } finally {
        setInitialLoading(false);
      }
    };

    fetchNotificationSettings();
  }, [form]);

  const handleSubmit = async (values: NotificationSettingsType) => {
    try {
      setLoading(true);
      await updateUserNotificationSettings(values);
      message.success('通知设置更新成功');
    } catch (error) {
      console.error('更新通知设置失败:', error);
      message.error('更新通知设置失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return <div>加载中...</div>;
  }

  return (
    <div className="notification-settings">
      <h2 className="notification-settings-title">通知设置</h2>
      <Form form={form} layout="vertical" onFinish={handleSubmit} className="settings-form">
        <div className="notification-section">
          <h3 className="notification-section-title">通知方式</h3>
          <Form.Item name="emailNotifications" valuePropName="checked" className="notification-form-item">
            <div className="notification-switch-item">
              <div className="notification-switch-label">
                <MailOutlined className="notification-icon" />
                <span>电子邮件通知</span>
              </div>
              <Switch />
            </div>
          </Form.Item>
          <Form.Item name="smsNotifications" valuePropName="checked" className="notification-form-item">
            <div className="notification-switch-item">
              <div className="notification-switch-label">
                <MessageOutlined className="notification-icon" />
                <span>短信通知</span>
              </div>
              <Switch />
            </div>
          </Form.Item>
          <Form.Item name="pushNotifications" valuePropName="checked" className="notification-form-item">
            <div className="notification-switch-item">
              <div className="notification-switch-label">
                <NotificationOutlined className="notification-icon" />
                <span>应用推送通知</span>
              </div>
              <Switch />
            </div>
          </Form.Item>
        </div>

        <Divider />

        <div className="notification-section">
          <h3 className="notification-section-title">通知类型</h3>
          <Form.Item name="transactionAlerts" valuePropName="checked" className="notification-form-item">
            <div className="notification-switch-item">
              <div className="notification-switch-label">
                <DollarOutlined className="notification-icon" />
                <span>交易提醒</span>
              </div>
              <Switch />
            </div>
          </Form.Item>
          <Form.Item name="securityAlerts" valuePropName="checked" className="notification-form-item">
            <div className="notification-switch-item">
              <div className="notification-switch-label">
                <SafetyOutlined className="notification-icon" />
                <span>安全提醒</span>
              </div>
              <Switch />
            </div>
          </Form.Item>
          <Form.Item name="marketingAlerts" valuePropName="checked" className="notification-form-item">
            <div className="notification-switch-item">
              <div className="notification-switch-label">
                <BellOutlined className="notification-icon" />
                <span>营销信息</span>
              </div>
              <Switch />
            </div>
          </Form.Item>
        </div>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} className="settings-form-button">
            保存设置
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default NotificationSettings;
