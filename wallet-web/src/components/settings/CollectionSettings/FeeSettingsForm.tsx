import React, { useEffect } from 'react';
import { Form, InputNumber, Radio, Button, Typography, Tooltip, Space, Card, Divider } from 'antd';
import {
  SaveOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined,
  DollarOutlined,
  SettingOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import type { FormInstance } from 'antd/es/form';
import { WalletApiApiWalletV1WalletInfo } from '../../../services/api/model';

const { Title, Text, Paragraph } = Typography;

interface FeeSettingsFormProps {
  form: FormInstance;
  walletInfo: WalletApiApiWalletV1WalletInfo | null;
  loading: boolean;
  formChanged: boolean;
  onSubmit: (values: any) => void;
  onValuesChange: (changedValues: any, _allValues: any) => void;
  networkFees?: any;
}

const FeeSettingsForm: React.FC<FeeSettingsFormProps> = ({
  form,
  walletInfo,
  loading,
  formChanged,
  onSubmit,
  onValuesChange,
  networkFees,
}) => {
  const ethFeeMode = Form.useWatch('eth_fee_mode', form);
  const trxFeeMode = Form.useWatch('trx_fee_mode', form);

  useEffect(() => {
    if (walletInfo) {
      form.setFieldsValue({
        eth_fee_mode: walletInfo.eth_fee_mode === 2 ? 2 : 1, // Default to 1 (auto) if not 2
        trx_fee_mode: walletInfo.trx_fee_mode === 2 ? 2 : 1, // Default to 1 (auto) if not 2
        eth_fee_max: walletInfo.eth_fee_max ? Number(walletInfo.eth_fee_max) : undefined,
        erc20_fee_max: walletInfo.erc20_fee_max ? Number(walletInfo.erc20_fee_max) : undefined,
        eth_fee_amount: walletInfo.eth_fee_amount ? Number(walletInfo.eth_fee_amount) : undefined,
        trx_fee_amount: walletInfo.trx_fee_amount ? Number(walletInfo.trx_fee_amount) : undefined,
        trx_activate_amount: walletInfo.trx_activate_amount ? Number(walletInfo.trx_activate_amount) : undefined,
        eth_gas_price: walletInfo.eth_gas_price ? Number(walletInfo.eth_gas_price) : undefined,
        eth_gas_limit: walletInfo.eth_gas_limit ? Number(walletInfo.eth_gas_limit) : undefined,
        erc20_gas_price: walletInfo.erc20_gas_price ? Number(walletInfo.erc20_gas_price) : undefined,
        erc20_gas_limit: walletInfo.erc20_gas_limit ? Number(walletInfo.erc20_gas_limit) : undefined,
        trx_fee_max: walletInfo.trx_fee_max ? Number(walletInfo.trx_fee_max) : undefined,
        trc20_max_energy_fee: walletInfo.trc20_max_energy_fee ? Number(walletInfo.trc20_max_energy_fee) : undefined,
        trc20_trigger_fee_amount: walletInfo.trc20_trigger_fee_amount ? Number(walletInfo.trc20_trigger_fee_amount) : undefined,
      });
    }
  }, [walletInfo, form]);

  // 根据当前Gas Price和Gas Limit计算ETH费用
  const calculateEthFee = (gasPrice: number, gasLimit: number) => {
    if (!gasPrice || !gasLimit) return null;
    // Gas Price单位是Gwei，需要转换为ETH
    const gweiToEth = 0.000000001;
    return (gasPrice * gasLimit * gweiToEth).toFixed(8);
  };

  // 获取当前表单中的Gas Price和Gas Limit
  const currentGasPrice = Form.useWatch('eth_gas_price', form);
  const currentGasLimit = Form.useWatch('eth_gas_limit', form);

  // 计算当前设置下的预估费用
  const estimatedFee = ethFeeMode === 2 && currentGasPrice && currentGasLimit
    ? calculateEthFee(currentGasPrice, currentGasLimit)
    : null;

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onSubmit}
      onValuesChange={onValuesChange}
      className="settings-form"
    >
      <Card
        className="fee-settings-card"
        title={
          <Space>
            <ThunderboltOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
            <span style={{ fontWeight: 600 }}>ETH 手续费设置</span>
          </Space>
        }
      >
        <Paragraph type="secondary" style={{ marginBottom: '20px' }}>
          以太坊网络的交易手续费由Gas Price和Gas Limit决定，合理设置可以在保证交易确认速度的同时降低成本
        </Paragraph>

        <Form.Item
          label={
            <Space>
              <span>手续费模式</span>
              <Tooltip title="自动模式将根据网络状况自动调整Gas费用，手动模式允许您精确控制Gas Price和Gas Limit。">
                <QuestionCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            </Space>
          }
          name="eth_fee_mode"
          rules={[{ required: true, message: '请选择ETH手续费模式' }]}
        >
          <Radio.Group buttonStyle="solid">
            <Radio.Button value={1}>
              <Space>
                <ThunderboltOutlined />
                自动
              </Space>
            </Radio.Button>
            {/* <Radio.Button value={2}>
              <Space>
                <SettingOutlined />
                手动
              </Space>
            </Radio.Button> */}
          </Radio.Group>
        </Form.Item>

        <Divider style={{ margin: '24px 0' }} />

        <Form.Item
          label={
            <Space>
              <span>ETH 手续费上限 (ETH)</span>
              <Tooltip title="单次ETH归集允许消耗的最高手续费，以ETH为单位。超过此限制的交易将不会执行。">
                <QuestionCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            </Space>
          }
          name="eth_fee_max"
          extra={
            <Text type="secondary">
              建议设置一个合理的上限值，以防止在网络拥堵时消耗过高的手续费
            </Text>
          }
        >
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            precision={6}
            placeholder="例如: 0.1"
            addonAfter="ETH"
            prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
          />
        </Form.Item>

        <Form.Item
          label={
            <Space>
              <span>ERC20 手续费上限 (ETH)</span>
              <Tooltip title="单次ERC20代币归集允许消耗的最高手续费，以ETH为单位。超过此限制的交易将不会执行。">
                <QuestionCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            </Space>
          }
          name="erc20_fee_max"
          extra={
            <Text type="secondary">
              ERC20代币交易通常比ETH交易消耗更多Gas，建议设置更高的上限
            </Text>
          }
        >
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            precision={6}
            placeholder="例如: 0.02"
            addonAfter="ETH"
            prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
          />
        </Form.Item>

        {/* {ethFeeMode === 2 && (
          <Form.Item
            label={
              <Space>
                <span>ETH 固定手续费金额 (ETH)</span>
                <Tooltip title="手动模式下，每笔ETH交易将使用此固定金额作为手续费。">
                  <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            name="eth_fee_amount"
            rules={[{ required: true, message: '请输入ETH固定手续费金额' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={6}
              placeholder="例如: 0.01"
              addonAfter="ETH"
            />
          </Form.Item>
        )} */}

        {ethFeeMode === 2 && (
          <div className="manual-fee-settings">
            <Form.Item
              label={
                <Space>
                  <span>ETH Gas Price (Gwei)</span>
                  <Tooltip title="Gas Price决定了交易的优先级，价格越高，交易被确认的速度越快">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="eth_gas_price"
              rules={[{ required: true, message: '请输入ETH Gas Price' }]}
              extra={
                networkFees && networkFees.average ? (
                  <Text type="secondary">
                    当前网络建议值: <Text type="success">慢速 {networkFees.slow?.gwei || '-'}</Text> /
                    <Text type="warning">标准 {networkFees.average?.gwei || '-'}</Text> /
                    <Text type="danger">快速 {networkFees.fast?.gwei || '-'}</Text> Gwei
                  </Text>
                ) : null
              }
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                placeholder="例如: 5"
                addonAfter="Gwei"
                prefix={<ThunderboltOutlined style={{ color: '#1890ff' }} />}
              />
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  <span>ETH Gas Limit</span>
                  <Tooltip title="Gas Limit决定了交易可以消耗的最大计算资源，设置过低可能导致交易失败">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="eth_gas_limit"
              rules={[{ required: true, message: '请输入ETH Gas Limit' }]}
              extra={
                <Text type="secondary">
                  标准ETH转账建议值: <Text strong>21000</Text>
                </Text>
              }
            >
              <InputNumber
                style={{ width: '100%' }}
                min={21000}
                placeholder="例如: 21000"
                prefix={<SettingOutlined style={{ color: '#1890ff' }} />}
              />
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  <span>ERC20 Gas Price (Gwei)</span>
                  <Tooltip title="ERC20代币交易的Gas Price，决定了交易的优先级">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="erc20_gas_price"
              rules={[{ required: true, message: '请输入ERC20 Gas Price' }]}
              extra={
                networkFees && networkFees.average ? (
                  <Text type="secondary">
                    当前网络建议值: <Text type="success">慢速 {networkFees.slow?.gwei || '-'}</Text> /
                    <Text type="warning">标准 {networkFees.average?.gwei || '-'}</Text> /
                    <Text type="danger">快速 {networkFees.fast?.gwei || '-'}</Text> Gwei
                  </Text>
                ) : null
              }
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                placeholder="例如: 5"
                addonAfter="Gwei"
                prefix={<ThunderboltOutlined style={{ color: '#1890ff' }} />}
              />
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  <span>ERC20 Gas Limit</span>
                  <Tooltip title="ERC20代币交易执行所需的最大计算量，通常高于ETH转账">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="erc20_gas_limit"
              rules={[{ required: true, message: '请输入ERC20 Gas Limit' }]}
              extra={
                <Text type="secondary">
                  ERC20代币转账建议值: <Text strong>65000-100000</Text>
                </Text>
              }
            >
              <InputNumber
                style={{ width: '100%' }}
                min={21000}
                placeholder="例如: 65000"
                prefix={<SettingOutlined style={{ color: '#1890ff' }} />}
              />
            </Form.Item>

            {estimatedFee && (
              <div className="fee-estimate">
                <InfoCircleOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
                <div>
                  <Text>预估费用: <Text strong type="warning">{estimatedFee} ETH</Text></Text>
                  <br />
                  <Text type="secondary">
                    根据当前设置的Gas Price和Gas Limit计算得出的预估费用。
                    实际费用可能因网络状况而有所变化。
                  </Text>
                </div>
              </div>
            )}
          </div>
        )}
      </Card>

      <Card
        className="fee-settings-card"
        title={
          <Space>
            <ThunderboltOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
            <span style={{ fontWeight: 600 }}>TRX 手续费设置</span>
          </Space>
        }
      >
        <Paragraph type="secondary" style={{ marginBottom: '20px' }}>
          波场网络的交易手续费相对稳定，合理设置可以确保交易顺利执行并控制成本
        </Paragraph>

        <Form.Item
          label={
            <Space>
              <span>手续费模式</span>
              <Tooltip title="自动模式将根据网络状况自动调整TRX费用，手动模式允许您精确控制费用金额。">
                <QuestionCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            </Space>
          }
          name="trx_fee_mode"
          rules={[{ required: true, message: '请选择TRX手续费模式' }]}
        >
          <Radio.Group buttonStyle="solid">
            <Radio.Button value={1}>
              <Space>
                <ThunderboltOutlined />
                自动
              </Space>
            </Radio.Button>
            {/* <Radio.Button value={2}>
              <Space>
                <SettingOutlined />
                手动
              </Space>
            </Radio.Button> */}
          </Radio.Group>
        </Form.Item>

        <Divider style={{ margin: '24px 0' }} />

            <Form.Item
              label={
                <Space>
                  <span>TRX 激活账户金额 (TRX)</span>
                  <Tooltip title="激活新TRX账户所需的TRX数量。">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="trx_activate_amount"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                placeholder="例如: 10"
                addonAfter="TRX"
              />
            </Form.Item>

        <Form.Item
          label={
            <Space>
              <span>TRX 手续费上限 (TRX)</span>
              <Tooltip title="单次TRX归集允许消耗的最高手续费，以TRX为单位。超过此限制的交易将不会执行。">
                <QuestionCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            </Space>
          }
          name="trx_fee_max"
          extra={
            <Text type="secondary">
              TRX网络的手续费相对稳定，通常每笔交易消耗几个TRX
            </Text>
          }
        >
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            placeholder="例如: 100"
            addonAfter="TRX"
            prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
          />
        </Form.Item>

        <Form.Item
          label={
            <Space>
              <span>TRC20 最大能量费 (TRX)</span>
              <Tooltip title="购买TRX能量时允许支付的最大金额，用于TRC20代币交易">
                <QuestionCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            </Space>
          }
          name="trc20_max_energy_fee"
          extra={
            <Text type="secondary">
              TRC20代币交易需要消耗能量，设置合理的上限可以控制成本
            </Text>
          }
        >
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            placeholder="例如: 50"
            addonAfter="TRX"
            prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
          />
        </Form.Item>

        {trxFeeMode === 1 && (
          <Form.Item
            label={
              <Space>
                <span>TRX 矿工费金额 (TRX)</span>
                <Tooltip title="trc20 交易需要一些trx 进行保底，不然大概率交易失败。">
                  <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            name="trx_fee_amount"
            rules={[{ required: true, message: '请输入TRX固定手续费金额' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="例如: 10"
              addonAfter="TRX"
            />
          </Form.Item>
        )}

        <Form.Item
          label={
            <Space>
              <span>TRC20USDT 触发手续费转账金额 (USDT)</span>
              <Tooltip title="低于此金额，低于350带宽的USDT归集会标记失败。设置合理的触发金额可以避免小额USDT归集时因带宽不足导致的失败。">
                <QuestionCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            </Space>
          }
          name="trc20_trigger_fee_amount"
          extra={
            <Text type="secondary">
              建议设置为350以上，确保有足够的带宽进行TRC20 USDT转账
            </Text>
          }
        >
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            placeholder="例如: 350"
            addonAfter="USDT"
            prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
          />
        </Form.Item>

        {trxFeeMode === 2 && (
          <>
            <Title level={5} style={{ marginTop: '24px', marginBottom: '16px' }}>TRX 高级设置</Title>

            <Form.Item
              label={
                <Space>
                  <span>TRX 激活账户金额 (TRX)</span>
                  <Tooltip title="激活新TRX账户所需的TRX数量。">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="trx_activate_amount"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                placeholder="例如: 10"
                addonAfter="TRX"
              />
            </Form.Item>

            {/* <Form.Item
              label={
                <Space>
                  <span>TRX 归集保留金额 (TRX)</span>
                  <Tooltip title="归集时保留在账户中的TRX数量，防止交易失败。">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="trx_keep_amount"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                placeholder="例如: 5"
                addonAfter="TRX"
              />
            </Form.Item> */}

            {/* <Form.Item
              label={
                <Space>
                  <span>ETH 归集预留金额 (ETH)</span>
                  <Tooltip title="ETH归集时预留在账户中的ETH数量。">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="eth_keep_amount"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                precision={6}
                placeholder="例如: 0.01"
                addonAfter="ETH"
              />
            </Form.Item> */}

            <Form.Item
              label={
                <Space>
                  <span>TRC20 最低能量需求</span>
                  <Tooltip title="转账USDT最低需要的能量，手动模式设置。">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="trc20_min_required_energy"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                placeholder="例如: 100000"
              />
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  <span>TRC20 最低带宽需求</span>
                  <Tooltip title="TRC20交易所需的最低带宽。">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
              name="trc20_min_required_bandwidth"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                placeholder="例如: 300"
              />
            </Form.Item>
          </>
        )}
      </Card>

      <div className="settings-actions">
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          disabled={!formChanged && !loading}
          icon={<SaveOutlined />}
          className="settings-form-button"
          size="large"
        >
          保存手续费设置
        </Button>
      </div>
    </Form>
  );
};

export default FeeSettingsForm;