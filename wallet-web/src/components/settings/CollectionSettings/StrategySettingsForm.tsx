import React from 'react';
import { Form, Button, InputNumber, Row, Col, Tooltip, Space } from 'antd';
import { SaveOutlined, SettingOutlined, InfoCircleOutlined, DollarOutlined } from '@ant-design/icons';
import '../Settings.css'; // Assuming common styles are in Settings.css
import { WalletApiApiWalletV1WalletInfo } from '../../../services/api/model';

interface StrategySettingsFormProps {
  form: any; // Ant Design FormInstance
  walletInfo: WalletApiApiWalletV1WalletInfo | null;
  loading: boolean;
  formChanged: boolean;
  onSubmit: (values: any) => void;
  onValuesChange: (changedValues: any, allValues: any) => void;
}

const StrategySettingsForm: React.FC<StrategySettingsFormProps> = ({
  form,
  walletInfo,
  loading,
  formChanged,
  onSubmit,
  onValuesChange,
}) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3 className="section-title">
          <SettingOutlined style={{ fontSize: '20px', color: '#1890ff' }} />
          策略归集
        </h3>
      </div>
      <div className="section-desc">
        设置各币种的归集阈值，当余额达到阈值时系统将自动触发归集操作
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={onSubmit}
        className="settings-form"
        onValuesChange={onValuesChange}
        initialValues={{ // Set initial values for the form directly
          ethThreshold: walletInfo?.eth_collect_threshold || '0',
          trxThreshold: walletInfo?.trx_collect_threshold || '0',
          usdtThreshold: walletInfo?.usdt_collect_threshold || '0',
        }}
      >
        <div className="threshold-settings">
          <Row gutter={[24, 24]}>
            <Col xs={24} md={8}>
              <Form.Item
                name="ethThreshold"
                label={
                  <Space>
                    <span>ETH归集阈值</span>
                    <Tooltip title="当ETH余额达到此阈值时，系统将自动触发归集">
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: '请设置ETH归集阈值' }]}
              >
                <InputNumber
                  min={0}
                  step={0.001}
                  precision={8}
                  style={{ width: '100%' }}
                  prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
                  placeholder="例如: 0.1"
                  addonAfter="ETH"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="trxThreshold"
                label={
                  <Space>
                    <span>TRX归集阈值</span>
                    <Tooltip title="当TRX余额达到此阈值时，系统将自动触发归集">
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: '请设置TRX归集阈值' }]}
              >
                <InputNumber
                  min={0}
                  step={1}
                  precision={8}
                  style={{ width: '100%' }}
                  prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
                  placeholder="例如: 1000"
                  addonAfter="TRX"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="usdtThreshold"
                label={
                  <Space>
                    <span>USDT归集阈值</span>
                    <Tooltip title="当USDT余额达到此阈值时，系统将自动触发归集">
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: '请设置USDT归集阈值' }]}
              >
                <InputNumber
                  min={0}
                  step={1}
                  precision={8}
                  style={{ width: '100%' }}
                  prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
                  placeholder="例如: 100"
                  addonAfter="USDT"
                />
              </Form.Item>
            </Col>
          </Row>

          <div className="settings-actions">
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={!formChanged}
              icon={<SaveOutlined />}
              className="settings-form-button"
              size="large"
            >
              保存设置
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default StrategySettingsForm;
