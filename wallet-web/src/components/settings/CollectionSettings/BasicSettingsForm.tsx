import React from 'react';
import { Form, Input, Button, Tooltip, Typography, Space } from 'antd';
import { SaveOutlined, KeyOutlined, WalletOutlined, InfoCircleOutlined } from '@ant-design/icons';
import '../Settings.css'; // Assuming common styles are in Settings.css
import { WalletApiApiWalletV1WalletInfo } from '../../../services/api/model';

const { Text } = Typography;

interface BasicSettingsFormProps {
  form: any; // Ant Design FormInstance
  initialValues: {
    trxCollectAddress?: string;
    ethCollectAddress?: string;
    trx_fee_address?: string;
    eth_fee_address?: string;
  };
  walletInfo: WalletApiApiWalletV1WalletInfo | null;
  loading: boolean;
  formChanged: boolean;
  onSubmit: (values: any) => void;
  onValuesChange: (changedValues: any, allValues: any) => void;
}

const BasicSettingsForm: React.FC<BasicSettingsFormProps> = ({
  form,
  initialValues,
  walletInfo,
  loading,
  formChanged,
  onSubmit,
  onValuesChange,
}) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3 className="section-title">
          <WalletOutlined style={{ fontSize: '20px', color: '#1890ff' }} />
          基本设置
        </h3>
      </div>

      <div className="section-desc">
        配置您的钱包基本参数，包括归集地址和矿工费设置
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={onSubmit}
        className="settings-form"
        onValuesChange={onValuesChange}
        initialValues={{ // Set initial values for the form directly
          trxCollectAddress: initialValues.trxCollectAddress || '',
          ethCollectAddress: initialValues.ethCollectAddress || '',
          trxFeePrivateKey: '', // Do not pre-fill private keys
          ethFeePrivateKey: '', // Do not pre-fill private keys
          trx_fee_address: initialValues.trx_fee_address || '',
          eth_fee_address: initialValues.eth_fee_address || '',
        }}
      >
        <h4 className="section-subtitle">
          TRX链设置
        </h4>
        <div className="chain-settings-group">
          <Form.Item
            name="trxCollectAddress"
            label={
              <Space>
                <span>TRX归集地址</span>
                <Tooltip title="所有TRX和TRC20代币将被归集到此地址">
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              {
                required: walletInfo?.trx_fee_private_key_is_set !== true,
                message: '请输入TRX归集地址或设置TRX矿工费私钥',
              },
            ]}
          >
            <Input
              placeholder="请输入TRX归集地址"
              prefix={<WalletOutlined style={{ color: '#1890ff' }} />}
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="trxFeePrivateKey"
            label={
              <Space>
                <span>TRX矿工费私钥</span>
                <Tooltip title="用于支付TRX链上交易的矿工费，请确保私钥安全">
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              {
                required: !walletInfo?.trx_collect_address,
                message: '请输入TRX矿工费私钥或设置TRX归集地址',
              },
            ]}
            extra={
              <Text type={walletInfo?.trx_fee_private_key_is_set ? "success" : "secondary"} style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                {walletInfo?.trx_fee_private_key_is_set ?
                  <><span style={{ fontSize: '16px' }}>✓</span> 矿工费私钥已设置</> :
                  '请设置矿工费私钥以支付交易手续费'}
              </Text>
            }
          >
            <Input.Password
              placeholder={
                walletInfo?.trx_fee_private_key_is_set ? '已设置，如需修改请输入新私钥' : '请输入TRX矿工费私钥'
              }
              prefix={<KeyOutlined style={{ color: '#1890ff' }} />}
              visibilityToggle={false}
            />
          </Form.Item>


           <Form.Item
            name="trx_fee_address"
            label={
              <Space>
                <span>TRX矿工费地址</span>
                <Tooltip title="用于支付TRX链上交易的矿工费地址">
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              {
                required: !walletInfo?.trx_collect_address,
                message: '请输入TRX矿工费地址',
              },
            ]}
          >
            <Input
              placeholder='请输入TRX矿工费地址'
              prefix={<WalletOutlined style={{ color: '#1890ff' }} />}
              allowClear
            />
          </Form.Item>
        </div>

        <h4 className="section-subtitle">
          ETH链设置
        </h4>
        <div className="chain-settings-group">
          <Form.Item
            name="ethCollectAddress"
            label={
              <Space>
                <span>ETH归集地址</span>
                <Tooltip title="所有ETH和ERC20代币将被归集到此地址">
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              {
                required: walletInfo?.eth_fee_private_key_is_set !== true,
                message: '请输入ETH归集地址或设置ETH矿工费私钥',
              },
            ]}
          >
            <Input
              placeholder="请输入ETH归集地址"
              prefix={<WalletOutlined style={{ color: '#1890ff' }} />}
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="ethFeePrivateKey"
            label={
              <Space>
                <span>ETH矿工费私钥</span>
                <Tooltip title="用于支付ETH链上交易的矿工费，请确保私钥安全">
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              {
                required: !walletInfo?.eth_collect_address,
                message: '请输入ETH矿工费私钥或设置ETH归集地址',
              },
            ]}
            extra={
              <Text type={walletInfo?.eth_fee_private_key_is_set ? "success" : "secondary"} style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                {walletInfo?.eth_fee_private_key_is_set ?
                  <><span style={{ fontSize: '16px' }}>✓</span> 矿工费私钥已设置</> :
                  '请设置矿工费私钥以支付交易手续费'}
              </Text>
            }
          >
            <Input.Password
              placeholder={
                walletInfo?.eth_fee_private_key_is_set ? '已设置，如需修改请输入新私钥' : '请输入ETH矿工费私钥'
              }
              prefix={<KeyOutlined style={{ color: '#1890ff' }} />}
              visibilityToggle={false}
            />
          </Form.Item>


           <Form.Item
            name="eth_fee_address"
            label={
              <Space>
                <span>ETH矿工费地址</span>
                <Tooltip title="用于支付ETH链上交易的矿工费地址">
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
            rules={[
              {
                required: !walletInfo?.eth_collect_address,
                message: '请输入ETH矿工费地址',
              },
            ]}
          >
            <Input
              placeholder='请输入ETH矿工费地址'
              prefix={<WalletOutlined style={{ color: '#1890ff' }} />}
              allowClear
            />
          </Form.Item>
        </div>

        <div className="settings-actions">
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<SaveOutlined />}
            disabled={!formChanged}
            className="settings-form-button"
            size="large"
          >
            保存设置
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default BasicSettingsForm;