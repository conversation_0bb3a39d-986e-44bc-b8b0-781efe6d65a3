import React from 'react';
import { <PERSON>, Button, Switch, Divider } from 'antd';
import { SaveOutlined } from '@ant-design/icons';
import '../Settings.css'; // Assuming common styles are in Settings.css
import { WalletApiApiWalletV1WalletInfo } from '../../../services/api/model';

interface CronSettingsFormProps {
  form: any; // Ant Design FormInstance
  walletInfo: WalletApiApiWalletV1WalletInfo | null;
  cronEnabled: boolean;
  loading: boolean;
  formChanged: boolean;
  onSubmit: () => void; // Changed from (values: any) => void as cron form submit doesn't pass values directly
  onValuesChange: (changedValues: any, allValues: any) => void;
  onToggle: (checked: boolean) => void;
}

const CronSettingsForm: React.FC<CronSettingsFormProps> = ({
  form,
  walletInfo,
  cronEnabled,
  loading,
  formChanged,
  onSubmit,
  onValuesChange,
  onToggle,
}) => {
  return (
    <div className="settings-section">
      <Divider />
      <div className="section-header">
        <h3 className="section-title">定时归集</h3>
        <Switch checked={cronEnabled} onChange={onToggle} />
      </div>
      <p className="section-desc">按照指定时间自动归集</p>

      <Form
        form={form}
        layout="vertical"
        onFinish={onSubmit}
        className="settings-form"
        onValuesChange={onValuesChange}
        initialValues={{ // Set initial values for the form directly
          cronTime: walletInfo?.cron_collect_time || '',
        }}
      >
        <Form.Item name="cronTime" label="归集时间" rules={[{ required: cronEnabled, message: '请选择归集时间' }]}>
          <input
            type="time"
            className="time-input" // Ensure this class is defined in your CSS
            disabled={!cronEnabled}
            style={{
              width: '100%',
              height: '32px',
              padding: '4px 11px',
              borderRadius: '6px',
              border: '1px solid #d9d9d9',
              fontSize: '14px',
            }}
          />
        </Form.Item>

        <div className="settings-actions">
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            disabled={!cronEnabled || !formChanged}
            icon={<SaveOutlined />}
          >
            保存
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default CronSettingsForm;