import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Avatar, Upload } from 'antd';
import { UserOutlined, UploadOutlined } from '@ant-design/icons';
import { useAppSelector } from '../../hooks/useAppSelector';
import { updateUserProfile } from '../../services/userService';
import './ProfileSettings.css';

interface ProfileFormValues {
  username: string;
  email: string;
  nickname?: string;
  avatar?: string;
}

const ProfileSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const user = useAppSelector((state) => state.auth.user);

  useEffect(() => {
    if (user) {
      form.setFieldsValue({
        username: user.username,
        email: user.email,
        nickname: user.nickname || '',
      });
    }
  }, [user, form]);

  const handleSubmit = async (values: ProfileFormValues) => {
    try {
      setLoading(true);
      await updateUserProfile(values);
      message.success('个人资料更新成功');
    } catch (error) {
      console.error('更新个人资料失败:', error);
      message.error('更新个人资料失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const uploadProps = {
    name: 'avatar',
    action: '/api/upload',
    headers: {
      authorization: `Bearer ${localStorage.getItem('accessToken')}`,
    },
    onChange(info: any) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        form.setFieldsValue({ avatar: info.file.response.url });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <div className="profile-settings">
      <h2 className="profile-settings-title">个人资料设置</h2>
      <div className="profile-avatar-container">
        <Avatar size={100} icon={<UserOutlined />} src={user?.avatar} className="profile-avatar" />
        <Upload {...uploadProps} showUploadList={false}>
          <Button icon={<UploadOutlined />}>更换头像</Button>
        </Upload>
      </div>
      <Form form={form} layout="vertical" onFinish={handleSubmit} className="settings-form">
        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
          className="settings-form-item"
        >
          <Input disabled />
        </Form.Item>
        <Form.Item
          name="email"
          label="邮箱"
          rules={[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
          className="settings-form-item"
        >
          <Input />
        </Form.Item>
        <Form.Item name="nickname" label="昵称" className="settings-form-item">
          <Input />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} className="settings-form-button">
            保存更改
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ProfileSettings;
