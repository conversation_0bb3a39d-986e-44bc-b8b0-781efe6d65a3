import React, { useEffect } from 'react';
import { Form, Button, InputNumber, Row, Col, Tooltip, Space } from 'antd';
import { SaveOutlined, SettingOutlined, InfoCircleOutlined, DollarOutlined } from '@ant-design/icons';
import '../Settings.css';
import { WalletApiApiWalletV1WalletInfo } from '../../../services/api/model';

// Extended interface for WalletInfo to include deposit settings
interface ExtendedWalletInfo extends Omit<WalletApiApiWalletV1WalletInfo, 'eth_min_take_amount' | 'trx_min_take_amount' | 'usdt_min_take_amount'> {
  // API returns these fields in snake_case, override the types to allow string | number
  eth_min_take_amount?: string | number;
  trx_min_take_amount?: string | number;
  usdt_min_take_amount?: string | number;
}

interface DepositSettingsFormProps {
  form: any; // Ant Design FormInstance
  walletInfo: ExtendedWalletInfo | null;
  loading: boolean;
  formChanged: boolean;
  onSubmit: (values: any) => void;
  onValuesChange: (changedValues: any, allValues: any) => void;
}

const DepositSettingsForm: React.FC<DepositSettingsFormProps> = ({
  form,
  walletInfo,
  loading,
  formChanged,
  onSubmit,
  onValuesChange,
}) => {
  // Update form values when walletInfo is loaded or changed
  useEffect(() => {
    if (walletInfo) {
      form.setFieldsValue({
        trxMinTakeAmount: walletInfo.trx_min_take_amount || '0',
        ethMinTakeAmount: walletInfo.eth_min_take_amount || '0',
        usdtMinTakeAmount: walletInfo.usdt_min_take_amount || '0'
      });
    }
  }, [form, walletInfo]);
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3 className="section-title">
          <SettingOutlined style={{ fontSize: '20px', color: '#1890ff' }} />
          充值设置
        </h3>
      </div>
      <div className="section-desc">
        设置各币种的最小入账金额，小于此金额的充值将不会入账
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={onSubmit}
        className="settings-form"
        onValuesChange={onValuesChange}
        initialValues={{ // Default initial values
          trxMinTakeAmount: '0',
          ethMinTakeAmount: '0',
          usdtMinTakeAmount: '0'
        }}
      >
        <div className="threshold-settings">
          <Row gutter={[24, 24]}>
            <Col xs={24} md={8}>
              <Form.Item
                name="trxMinTakeAmount"
                label={
                  <Space>
                    <span>TRX最小入账金额</span>
                    <Tooltip title="小于此金额的TRX充值将不会入账">
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: '请设置TRX最小入账金额' }]}
              >
                <InputNumber
                  min={0}
                  step={0.00001}
                  precision={8}
                  style={{ width: '100%' }}
                  prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
                  placeholder="例如: 0.0001"
                  addonAfter="TRX"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="ethMinTakeAmount"
                label={
                  <Space>
                    <span>ETH最小入账金额</span>
                    <Tooltip title="小于此金额的ETH充值将不会入账">
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: '请设置ETH最小入账金额' }]}
              >
                <InputNumber
                  min={0}
                  step={0.001}
                  precision={8}
                  style={{ width: '100%' }}
                  prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
                  placeholder="例如: 0.01"
                  addonAfter="ETH"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="usdtMinTakeAmount"
                label={
                  <Space>
                    <span>USDT最小入账金额</span>
                    <Tooltip title="小于此金额的USDT充值将不会入账">
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: '请设置USDT最小入账金额' }]}
              >
                <InputNumber
                  min={0}
                  step={1}
                  precision={8}
                  style={{ width: '100%' }}
                  prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
                  placeholder="例如: 10"
                  addonAfter="USDT"
                />
              </Form.Item>
            </Col>
          </Row>

          <div className="settings-actions">
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={!formChanged}
              icon={<SaveOutlined />}
              className="settings-form-button"
              size="large"
            >
              保存设置
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default DepositSettingsForm;
