import React from 'react'; // Removed useState, useEffect
import { Divider } from 'antd'; // Removed message, Spin
import {
  // getWalletInfo, // No longer needed here
  changeWalletPassword,
} from '../../services/walletService';
// API client instances are called directly within child components.
import PasswordChangeSection from './PasswordChangeSection';
import GoogleAuthSettingsSection from './GoogleAuthSettingsSection';
import './SecuritySettings.css';

// WalletInfo interface might still be useful if fetchWalletInfo returns a complex object
// and we want to type it, though it's not directly used for a state variable here anymore.
/*
interface WalletInfo {
  google_code_switch?: number;
  collect_address?: string;
  miner_private_key_set?: boolean;
  // 其他钱包信息字段...
}
*/

const SecuritySettings: React.FC = () => {
  // infoLoading and twoFactorEnabled state are removed as GoogleAuthSettingsSection handles its own state.
  // fetchWalletInfo is also removed for the same reason.

  // If PasswordChangeSection needs to trigger a global refresh or specific actions,
  // a different mechanism (e.g., context, Redux, or a more specific callback) would be needed.
  // For now, assuming GoogleAuthSettingsSection's internal refresh is sufficient.
  const handlePasswordChanged = () => {
    // Potentially, if other parts of SecuritySettings needed an update,
    // that logic would go here. For now, this can be a no-op or
    // trigger a generic notification if desired.
    // message.success('密码已成功更改。'); // Example notification
  };

  return (
    <div className="security-settings">
      <h2 className="security-settings-title">安全设置</h2>

      <PasswordChangeSection
        changeWalletPassword={changeWalletPassword}
        onPasswordChanged={handlePasswordChanged} // Updated callback
      />

      <Divider />

      <GoogleAuthSettingsSection />
    </div>
  );
};

export default SecuritySettings;
