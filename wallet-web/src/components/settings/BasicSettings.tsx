import React, { useState, useEffect, useCallback } from 'react';
import { Form, message, Spin } from 'antd';
import { getWalletInfo, updateWalletSettings } from '../../services/walletService';
import GoogleAuthWithPasswordModal from '../common/GoogleAuthWithPasswordModal';
import BasicSettingsForm from './CollectionSettings/BasicSettingsForm';
import './Settings.css';
import { WalletApiApiWalletV1WalletInfo } from '../../services/api/model';

const BasicSettings: React.FC = () => {
  const [basicForm] = Form.useForm();
  const [basicLoading, setBasicLoading] = useState(false);
  const [infoLoading, setInfoLoading] = useState(true);
  const [walletInfo, setWalletInfo] = useState<WalletApiApiWalletV1WalletInfo | null>(null);
  const [googleModalVisible, setGoogleModalVisible] = useState(false);
  const [formValues, setFormValues] = useState<any>(null);
  const [basicFormChanged, setBasicFormChanged] = useState(false);
  const [changedFields, setChangedFields] = useState<Record<string, any>>({
    basic: {},
  });

  // 添加一个存储初始数据的状态
  const [initialValues, setInitialValues] = useState<{
    trxCollectAddress?: string;
    ethCollectAddress?: string;
    trx_fee_address?: string;
    eth_fee_address?: string;
  }>({});

  // 定义获取钱包信息的函数
  const fetchWalletInfo = useCallback(async () => {
    try {
      setInfoLoading(true);
      const info = await getWalletInfo();
      setWalletInfo(info.wallet_info || null);

      if (info.wallet_info) {
        // 保存初始地址值用于后续比较
        setInitialValues({
          trxCollectAddress: info.wallet_info.trx_collect_address || '',
          ethCollectAddress: info.wallet_info.eth_collect_address || '',
          trx_fee_address: info.wallet_info.trx_fee_address || '',
          eth_fee_address: info.wallet_info.eth_fee_address || '',
        });

        // 重置表单变化状态
        setBasicFormChanged(false);

        // 重置变化字段记录
        setChangedFields({
          basic: {},
        });
      }
    } catch (error) {
      console.error('获取钱包信息失败:', error);
      message.error('获取钱包信息失败，请稍后重试');
    } finally {
      setInfoLoading(false);
    }
  }, []);

  // 监听组件生命周期
  useEffect(() => {
    fetchWalletInfo();
  }, [fetchWalletInfo]);

  // 跟踪表单字段变化，只记录与接口数据不同的字段
  const handleBasicFormChange = (changedValues: any, allValues: any) => {
    // 检查地址字段是否与初始值不同
    const addressChanged =
      (changedValues.trxCollectAddress !== undefined &&
        changedValues.trxCollectAddress !== initialValues.trxCollectAddress) ||
      (changedValues.ethCollectAddress !== undefined &&
        changedValues.ethCollectAddress !== initialValues.ethCollectAddress) ||
      (changedValues.trx_fee_address !== undefined &&
        changedValues.trx_fee_address !== initialValues.trx_fee_address) ||
      (changedValues.eth_fee_address !== undefined &&
        changedValues.eth_fee_address !== initialValues.eth_fee_address);

    // 检查私钥字段是否有输入
    const privateKeyChanged =
      (changedValues.trxFeePrivateKey !== undefined && changedValues.trxFeePrivateKey.trim() !== '') ||
      (changedValues.ethFeePrivateKey !== undefined && changedValues.ethFeePrivateKey.trim() !== '');

    // 只有当地址发生变化或私钥有输入时才标记表单变化
    if (addressChanged || privateKeyChanged) {
      setBasicFormChanged(true);

      // 过滤掉与初始值相同的地址字段
      const filteredChanges = { ...changedValues };
      if (filteredChanges.trxCollectAddress === initialValues.trxCollectAddress) {
        delete filteredChanges.trxCollectAddress;
      }
      if (filteredChanges.ethCollectAddress === initialValues.ethCollectAddress) {
        delete filteredChanges.ethCollectAddress;
      }
      if (filteredChanges.trx_fee_address === initialValues.trx_fee_address) {
        delete filteredChanges.trx_fee_address;
      }
      if (filteredChanges.eth_fee_address === initialValues.eth_fee_address) {
        delete filteredChanges.eth_fee_address;
      }

      // 只记录真正变化的字段
      if (Object.keys(filteredChanges).length > 0) {
        setChangedFields((prev) => ({
          ...prev,
          basic: {
            ...prev.basic,
            ...filteredChanges,
          },
        }));
      }
    }
  };

  // 基本设置提交前，验证字段是否真正发生变化
  const handleBasicFormSubmit = async (values: any) => {
    try {
      await basicForm.validateFields();

      // 获取当前表单所有值
      const currentValues = basicForm.getFieldsValue();

      // 过滤掉未变化的地址字段
      const realChanges = { ...changedFields.basic };
      if (currentValues.trxCollectAddress === initialValues.trxCollectAddress) {
        delete realChanges.trxCollectAddress;
      }
      if (currentValues.ethCollectAddress === initialValues.ethCollectAddress) {
        delete realChanges.ethCollectAddress;
      }
      if (currentValues.trx_fee_address === initialValues.trx_fee_address) {
        delete realChanges.trx_fee_address;
      }
      if (currentValues.eth_fee_address === initialValues.eth_fee_address) {
        delete realChanges.eth_fee_address;
      }

      // 保留所有私钥字段（如果有值）
      if (currentValues.trxFeePrivateKey && currentValues.trxFeePrivateKey.trim() !== '') {
        realChanges.trxFeePrivateKey = currentValues.trxFeePrivateKey;
      }
      if (currentValues.ethFeePrivateKey && currentValues.ethFeePrivateKey.trim() !== '') {
        realChanges.ethFeePrivateKey = currentValues.ethFeePrivateKey;
      }

      // 检查是否有实际变化
      if (Object.keys(realChanges).length === 0) {
        message.info('没有检测到变化，无需保存');
        return;
      }

      // 只提交变化了的字段
      setFormValues(realChanges);
      setGoogleModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // --- Helper functions for saving settings ---
  const handleBasicSettingsSave = async (googleCode: string, password: string) => {
    setBasicLoading(true);
    const params: any = {
      google_code: googleCode,
      password: password,
    };

    if (formValues.trxCollectAddress !== undefined) {
      params.trx_collect_address = formValues.trxCollectAddress;
    }
    if (formValues.ethCollectAddress !== undefined) {
      params.eth_collect_address = formValues.ethCollectAddress;
    }
    if (formValues.trxFeePrivateKey && formValues.trxFeePrivateKey.trim() !== '') {
      params.trx_fee_private_key = formValues.trxFeePrivateKey;
    }
    if (formValues.ethFeePrivateKey && formValues.ethFeePrivateKey.trim() !== '') {
      params.eth_fee_private_key = formValues.ethFeePrivateKey;
    }
    if (formValues.trx_fee_address !== undefined) {
      params.trx_fee_address = formValues.trx_fee_address;
    }
    if (formValues.eth_fee_address !== undefined) {
      params.eth_fee_address = formValues.eth_fee_address;
    }

    try {
      // 使用新的统一接口更新钱包设置
      await updateWalletSettings(params);
      setBasicLoading(false);
      message.success('基本设置已保存');
      setFormValues(null);
      setGoogleModalVisible(false);
      fetchWalletInfo();
    } catch (error: any) {
      console.error('保存设置失败:', error);
      message.error(error.message || '操作失败，请稍后重试');
      setBasicLoading(false);
    }
  };

  if (infoLoading) {
    return (
      <div className="collection-settings-loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="collection-settings">
      <h2 className="settings-title">基本设置</h2>

      <BasicSettingsForm
        form={basicForm}
        initialValues={initialValues}
        walletInfo={walletInfo}
        loading={basicLoading}
        formChanged={basicFormChanged}
        onSubmit={handleBasicFormSubmit}
        onValuesChange={handleBasicFormChange}
      />

      {/* 使用带密码的谷歌验证码组件 */}
      <GoogleAuthWithPasswordModal
        visible={googleModalVisible}
        onConfirm={handleBasicSettingsSave}
        onCancel={() => setGoogleModalVisible(false)}
        promptText="请输入谷歌验证码和密码完成设置"
        confirmText="确认保存"
      />
    </div>
  );
};

export default BasicSettings;
