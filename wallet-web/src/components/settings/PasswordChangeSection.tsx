import React, { useState } from 'react';
import { Form, Input, Button, message, Modal } from 'antd';
import { LockOutlined, EditOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import GoogleAuthWithPasswordModal from '../common/GoogleAuthWithPasswordModal';
import './SecuritySettings.css'; // 复用部分样式，或者可以创建独立的样式文件

interface PasswordFormValues {
  currentPassword: string; // 虽然UI上移除了，但逻辑上可能还需要，或者可以简化
  newPassword: string;
  confirmPassword: string;
}

interface PasswordChangeSectionProps {
  // API function to change password
  changeWalletPassword: (
    currentPassword: string,
    newPassword: string,
    googleCode: string
  ) => Promise<any>;
  // Function to be called after password change to refresh info, if needed
  onPasswordChanged?: () => void;
}

const PasswordChangeSection: React.FC<PasswordChangeSectionProps> = ({
  changeWalletPassword,
  onPasswordChanged,
}) => {
  const [passwordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 修改密码相关
  const [dangerModalVisible, setDangerModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [googleVerifyModalVisible, setGoogleVerifyModalVisible] = useState(false);
  const [passwordValues, setPasswordValues] = useState<Omit<PasswordFormValues, 'currentPassword'> | null>(null);


  // 处理密码修改表单提交
  const handlePasswordFormSubmit = async () => {
    try {
      const values = await passwordForm.validateFields();
      // 保存新密码表单的值，并打开密码验证弹窗
      setPasswordValues({ newPassword: values.newPassword, confirmPassword: values.confirmPassword });
      setPasswordModalVisible(false);
      setGoogleVerifyModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理最终密码修改提交
  const handlePasswordChangeConfirm = async (googleCode: string, currentPasswordVal: string) => {
    if (!passwordValues) return;

    try {
      setLoading(true);
      // 调用接口修改密码
      await changeWalletPassword(currentPasswordVal, passwordValues.newPassword, googleCode);
      message.success('密码修改成功');

      // 关闭弹窗并重置表单
      setGoogleVerifyModalVisible(false);
      passwordForm.resetFields();
      setPasswordValues(null);
      if (onPasswordChanged) {
        onPasswordChanged();
      }
    } catch (error: any) {
      console.error('修改密码失败:', error);
      message.error(error.message || '操作失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 取消修改密码，关闭所有相关弹窗并重置状态
  const handleCancelPasswordChange = () => {
    setDangerModalVisible(false);
    setPasswordModalVisible(false);
    setGoogleVerifyModalVisible(false);
    setPasswordValues(null);
    passwordForm.resetFields();
  };

  // 处理危险警告确认
  const handleDangerConfirm = () => {
    setDangerModalVisible(false);
    setPasswordModalVisible(true);
  };

  return (
    <>
      <div className="security-section">
        <div className="section-header">
          <h3 className="security-section-title">钱包密码</h3>
          <Button type="primary" icon={<EditOutlined />} onClick={() => setDangerModalVisible(true)}>
            修改
          </Button>
        </div>
        <p className="security-section-desc">钱包密码用于保护您的钱包安全，建议定期修改密码并使用强密码。</p>
      </div>

      {/* 危险警告确认对话框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#ff4d4f' }}>
            <ExclamationCircleOutlined style={{ fontSize: '20px' }} />
            <span>重要安全提醒</span>
          </div>
        }
        open={dangerModalVisible}
        onCancel={() => setDangerModalVisible(false)}
        centered
        destroyOnClose
        footer={[
          <Button key="cancel" onClick={() => setDangerModalVisible(false)}>
            取消
          </Button>,
          <Button key="confirm" type="primary" danger onClick={handleDangerConfirm}>
            我已了解，继续修改
          </Button>
        ]}
      >
        <div style={{ padding: '20px 0' }}>
          <p style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 500 }}>
            修改密码后，整个钱包将被重新加密
          </p>
          <div style={{ background: '#fff2e8', border: '1px solid #ffbb96', borderRadius: '4px', padding: '16px', marginBottom: '16px' }}>
            <p style={{ margin: 0, color: '#d4380d', fontWeight: 500, marginBottom: '8px' }}>
              <ExclamationCircleOutlined style={{ marginRight: '8px' }} />
              请注意以下重要事项：
            </p>
            <ul style={{ margin: '8px 0 0 24px', padding: 0, color: '#d4380d' }}>
              <li style={{ marginBottom: '4px' }}>修改密码后，钱包数据将使用新密码重新加密</li>
              <li style={{ marginBottom: '4px' }}>所有正在运行的程序和服务需要手动重启</li>
              <li style={{ marginBottom: '4px' }}>请确保您记住新密码，忘记密码将无法恢复钱包</li>
              <li>建议在修改密码前备份好助记词</li>
            </ul>
          </div>
          <p style={{ margin: 0, color: '#8c8c8c', fontSize: '14px' }}>
            请确保您已经充分了解以上风险，并已做好相应准备。
          </p>
        </div>
      </Modal>

      {/* 修改密码对话框 - 移除当前密码输入框 */}
      <Modal
        title="修改钱包密码"
        open={passwordModalVisible}
        onCancel={handleCancelPasswordChange}
        footer={null}
        centered
        destroyOnClose
      >
        <Form form={passwordForm} layout="vertical" preserve={false}>
          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码长度不能少于8个字符' },
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请确认新密码" />
          </Form.Item>

          <div style={{ display: 'flex', gap: '10px', marginTop: '24px' }}>
            <Button onClick={handleCancelPasswordChange} style={{ flex: 1 }} disabled={loading}>
              取消
            </Button>
            <Button type="primary" onClick={handlePasswordFormSubmit} style={{ flex: 1 }} loading={loading}>
              下一步
            </Button>
          </div>
        </Form>
      </Modal>

      {/* 谷歌验证码与密码确认对话框（用于修改密码），使用GoogleAuthWithPasswordModal组件 */}
      <GoogleAuthWithPasswordModal
        visible={googleVerifyModalVisible}
        onConfirm={handlePasswordChangeConfirm}
        onCancel={handleCancelPasswordChange}
        title={<div style={{ textAlign: 'center', fontSize: '18px', fontWeight: 500 }}>验证身份</div>}
        promptText="请输入当前密码和谷歌验证码完成密码修改"
        passwordLabel="当前密码"
        googleCodeLabel="谷歌验证码"
        confirmText="确认修改"
        // loading={loading} // GoogleAuthWithPasswordModal manages its own loading state
      />
    </>
  );
};

export default PasswordChangeSection;