/* 通用设置样式 */
.settings-page {
    padding: 24px;
    background-color: transparent;
    min-height: calc(100vh - 64px);
    animation: fadeIn 0.5s ease-out;
}

.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    border-radius: 16px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.settings-title {
    margin-bottom: 24px !important;
    color: #262626;
    font-weight: 600;
    font-size: 22px;
}

.settings-tabs {
    min-height: 600px;
}

.settings-tabs .ant-tabs-tab {
    margin: 0 0 12px 0 !important;
    padding: 16px 20px !important;
    transition: all 0.3s ease;
    border-radius: 10px !important;
    border: none !important;
    background-color: rgba(24, 144, 255, 0.02);
}

.settings-tabs .ant-tabs-tab:hover {
    background-color: rgba(24, 144, 255, 0.08);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.settings-tabs .ant-tabs-tab-active {
    background-color: rgba(24, 144, 255, 0.1) !important;
    border: none !important;
}

.settings-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #1890ff !important;
    font-weight: 500;
}

.settings-tabs .ant-tabs-content {
    padding: 0 24px;
}

.settings-form {
    max-width: 700px;
}

.settings-section {
    margin-bottom: 36px;
    padding: 28px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 82, 204, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 82, 204, 0.03);
    position: relative;
    overflow: hidden;
}

.settings-section:hover {
    box-shadow: 0 8px 24px rgba(0, 82, 204, 0.12);
    transform: translateY(-3px);
    border-color: rgba(0, 82, 204, 0.08);
}

.settings-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: linear-gradient(to bottom, #1890ff, #096dd9);
    opacity: 0.8;
    transition: all 0.3s ease;
}

.settings-section:hover::before {
    opacity: 1;
}

.section-title {
    font-size: 18px;
    margin-bottom: 16px;
    font-weight: 600;
    color: #262626;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.section-desc {
    margin-bottom: 24px;
    color: #595959;
    font-size: 14px;
    line-height: 1.6;
    background-color: #f9f9f9;
    padding: 12px 16px;
    border-radius: 8px;
    border-left: 3px solid #1890ff;
}

.settings-actions {
    margin-top: 36px;
    display: flex;
    justify-content: flex-start;
    gap: 16px;
}

.settings-form-button {
    min-width: 140px;
    height: 44px;
    border-radius: 10px;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.settings-form-button.ant-btn-primary {
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.settings-form-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
}

.settings-form-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 表单元素样式 */
.settings-form {
    width: 100%;
}

.settings-form .ant-form-item {
    margin-bottom: 28px;
}

.settings-form .ant-form-item-label {
    font-weight: 500;
    margin-bottom: 10px;
}

.settings-form .ant-form-item-label > label {
    color: #262626;
    font-size: 15px;
    height: 24px;
    display: flex;
    align-items: center;
}

.settings-form .ant-radio-button-wrapper {
    height: 36px;
    padding: 0 16px;
    line-height: 34px;
    font-size: 14px;
    transition: all 0.3s;
}

.settings-form .ant-radio-button-wrapper:hover {
    color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.settings-form .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.settings-form .ant-input,
.settings-form .ant-input-password,
.settings-form .ant-select-selector,
.settings-form .ant-input-number,
.settings-form .ant-picker {
    border-radius: 10px;
    padding: 12px 16px;
    height: auto;
    box-shadow: 0 2px 8px rgba(0, 82, 204, 0.05);
    border: 1px solid #d9d9d9;
    transition: all 0.3s ease;
    font-size: 15px;
}

.settings-form .ant-input-number-input {
    height: 30px;
    font-size: 15px;
}

.settings-form .ant-input-affix-wrapper {
    padding: 0 11px;
}

.settings-form .ant-input-affix-wrapper .ant-input {
    font-size: 15px;
}

.settings-form .ant-input-affix-wrapper-focused {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.settings-form .ant-input:hover,
.settings-form .ant-input-password:hover,
.settings-form .ant-select-selector:hover,
.settings-form .ant-input-number:hover,
.settings-form .ant-picker:hover {
    border-color: #40a9ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 82, 204, 0.08);
}

.settings-form .ant-input:focus,
.settings-form .ant-input-password:focus,
.settings-form .ant-select-selector:focus,
.settings-form .ant-input-number:focus,
.settings-form .ant-picker:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    transform: translateY(-1px);
}

.settings-form .ant-form-item-extra {
    margin-top: 8px;
    color: #8c8c8c;
    font-size: 13px;
    line-height: 1.6;
}

/* 手续费设置特定样式 */
.fee-settings-card {
    margin-bottom: 28px;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 82, 204, 0.08);
    border: 1px solid rgba(0, 82, 204, 0.03);
    overflow: hidden;
    transition: all 0.3s ease;
}

.fee-settings-card:hover {
    box-shadow: 0 8px 24px rgba(0, 82, 204, 0.12);
    transform: translateY(-2px);
}

.fee-settings-card .ant-card-head {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
}

.fee-settings-card .ant-card-head-title {
    font-size: 16px;
    font-weight: 600;
}

.fee-settings-card .ant-card-body {
    padding: 24px;
}

.manual-fee-settings {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 12px;
    margin-top: 16px;
    border: 1px solid #f0f0f0;
    animation: fadeIn 0.5s ease-out;
}

.fee-estimate {
    margin: 16px 0;
    padding: 12px 16px;
    background-color: #e6f7ff;
    border-radius: 8px;
    border-left: 3px solid #1890ff;
    display: flex;
    align-items: center;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .settings-page {
        padding: 16px 12px;
    }

    .settings-container {
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);
    }

    .settings-title {
        font-size: 20px !important;
        margin-bottom: 16px !important;
        text-align: center;
    }

    .settings-tabs {
        min-height: 500px;
    }

    .settings-tabs .ant-tabs-nav {
        margin-bottom: 24px;
    }

    .settings-tabs .ant-tabs-content {
        padding: 0 8px;
    }

    .settings-section {
        padding: 16px;
        margin-bottom: 24px;
    }

    .section-title {
        font-size: 16px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .settings-actions {
        flex-direction: column;
        width: 100%;
    }

    .settings-form-button {
        width: 100%;
        margin-bottom: 12px;
    }

    .fee-settings-card .ant-card-head,
    .fee-settings-card .ant-card-body {
        padding: 16px;
    }

    .manual-fee-settings {
        padding: 16px;
    }
}

/* 安全设置特定样式 */
.security-settings {
    padding: 24px;
    animation: fadeIn 0.5s ease-out;
}

.security-settings-title {
    font-size: 22px;
    margin-bottom: 28px;
    font-weight: 600;
    color: #262626;
    position: relative;
    display: inline-block;
}

.security-settings-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, #1890ff, #096dd9);
    border-radius: 3px;
}

.security-section {
    margin-bottom: 36px;
    padding: 28px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 82, 204, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 82, 204, 0.03);
    position: relative;
    overflow: hidden;
}

.security-section:hover {
    box-shadow: 0 8px 24px rgba(0, 82, 204, 0.12);
    transform: translateY(-3px);
    border-color: rgba(0, 82, 204, 0.08);
}

.security-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: linear-gradient(to bottom, #52c41a, #389e0d);
    opacity: 0.8;
    transition: all 0.3s ease;
}

.security-section:hover::before {
    opacity: 1;
}

.security-section-title {
    font-size: 18px;
    margin-bottom: 16px;
    font-weight: 600;
    color: #262626;
    display: flex;
    align-items: center;
    gap: 10px;
}

.security-section-desc {
    margin-bottom: 24px;
    color: #595959;
    font-size: 14px;
    line-height: 1.6;
    background-color: #f9f9f9;
    padding: 12px 16px;
    border-radius: 8px;
    border-left: 3px solid #52c41a;
}

.settings-form-item {
    margin-bottom: 28px;
}

.two-factor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 24px 0;
    animation: fadeIn 0.5s ease-out;
}

.qrcode-image {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 16px;
    background-color: #fff;
    box-shadow: 0 4px 16px rgba(0, 82, 204, 0.08);
    transition: all 0.3s ease;
}

.qrcode-image:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 82, 204, 0.12);
}

.secret-key {
    margin: 20px 0;
    padding: 16px;
    width: 100%;
    background-color: #f5f7fa;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 16px;
    color: #262626;
    box-shadow: 0 2px 8px rgba(0, 82, 204, 0.05);
    transition: all 0.3s ease;
}

.secret-key:hover {
    background-color: #e6f7ff;
    box-shadow: 0 4px 12px rgba(0, 82, 204, 0.08);
}

/* 归集设置特定样式 */
.collection-settings {
    padding: 24px;
    animation: fadeIn 0.5s ease-out;
}

.collection-settings-title {
    font-size: 22px;
    margin-bottom: 28px;
    font-weight: 600;
    color: #262626;
    position: relative;
    display: inline-block;
}

.collection-settings-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, #1890ff, #096dd9);
    border-radius: 3px;
}

.threshold-settings {
    margin-top: 20px;
    padding: 24px;
    background-color: #f9f9f9;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 82, 204, 0.05);
    border: 1px solid #f0f0f0;
}

.threshold-settings:hover {
    box-shadow: 0 4px 16px rgba(0, 82, 204, 0.08);
    transform: translateY(-2px);
    border-color: #d9d9d9;
}

.chain-settings-group {
    position: relative;
    padding: 20px;
    background-color: #fafafa;
    border-radius: 12px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.chain-settings-group:hover {
    background-color: #f5f7fa;
    box-shadow: 0 4px 12px rgba(0, 82, 204, 0.05);
}

.section-subtitle {
    position: relative;
    display: inline-block;
    margin-bottom: 16px !important;
    padding-left: 12px;
}

.section-subtitle::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: #1890ff;
    border-radius: 2px;
}

/* 加载状态 */
.security-settings-loading,
.collection-settings-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    gap: 24px;
    animation: fadeIn 0.5s ease-out;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 82, 204, 0.08);
}

.security-settings-loading .ant-spin,
.collection-settings-loading .ant-spin {
    transform: scale(1.2);
}

.security-settings-loading .ant-spin-text,
.collection-settings-loading .ant-spin-text {
    margin-top: 16px;
    font-size: 16px;
    color: #1890ff;
}