import React, { useState, useEffect, useCallback } from 'react';
import { Form, message, Spin } from 'antd';
import { getWalletInfo, updateWalletSettings } from '../../services/walletService';
import GoogleAuthWithPasswordModal from '../common/GoogleAuthWithPasswordModal';
import DepositSettingsForm from './DepositSettings/DepositSettingsForm';
import './Settings.css';
import { WalletApiApiWalletV1WalletInfo } from '../../services/api/model';

const DepositSettings: React.FC = () => {
  const [depositForm] = Form.useForm();
  const [depositLoading, setDepositLoading] = useState(false);
  const [infoLoading, setInfoLoading] = useState(true);
  const [walletInfo, setWalletInfo] = useState<WalletApiApiWalletV1WalletInfo | null>(null);
  const [googleModalVisible, setGoogleModalVisible] = useState(false);
  const [formValues, setFormValues] = useState<any>(null);
  const [depositFormChanged, setDepositFormChanged] = useState(false);

  // 定义获取钱包信息的函数
  const fetchWalletInfo = useCallback(async () => {
    try {
      setInfoLoading(true);
      const info = await getWalletInfo();
      setWalletInfo(info.wallet_info || null);

      if (info.wallet_info) {
        // 重置表单变化状态
        setDepositFormChanged(false);
      }
    } catch (error) {
      console.error('获取钱包信息失败:', error);
      message.error('获取钱包信息失败，请稍后重试');
    } finally {
      setInfoLoading(false);
    }
  }, []);

  // 监听组件生命周期
  useEffect(() => {
    fetchWalletInfo();
  }, [fetchWalletInfo]);

  const handleDepositFormChange = (changedValues: any, allValues: any) => {
    setDepositFormChanged(true);
  };

  const handleDepositFormSubmit = async (values: any) => {
    try {
      await depositForm.validateFields();
      // 直接使用当前表单的所有值
      setFormValues(values);
      setGoogleModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleDepositSettingsSave = async (googleCode: string, password: string) => {
    setDepositLoading(true);
    const params: any = {
      google_code: googleCode,
      password: password,
    };

    // 安全检查确保 formValues 存在
    if (formValues) {
      // 处理充值最小入账设置
      if (formValues.trxMinTakeAmount !== undefined) {
        params.trx_min_take_amount = Number(formValues.trxMinTakeAmount);
      } else if (depositFormChanged) {
        params.trx_min_take_amount = Number(depositForm.getFieldValue('trxMinTakeAmount'));
      }

      if (formValues.ethMinTakeAmount !== undefined) {
        params.eth_min_take_amount = Number(formValues.ethMinTakeAmount);
      } else if (depositFormChanged) {
        params.eth_min_take_amount = Number(depositForm.getFieldValue('ethMinTakeAmount'));
      }

      if (formValues.usdtMinTakeAmount !== undefined) {
        params.usdt_min_take_amount = Number(formValues.usdtMinTakeAmount);
      } else if (depositFormChanged) {
        params.usdt_min_take_amount = Number(depositForm.getFieldValue('usdtMinTakeAmount'));
      }
    } else {
      // 如果 formValues 为空，直接从表单获取当前值
      params.trx_min_take_amount = Number(depositForm.getFieldValue('trxMinTakeAmount'));
      params.eth_min_take_amount = Number(depositForm.getFieldValue('ethMinTakeAmount'));
      params.usdt_min_take_amount = Number(depositForm.getFieldValue('usdtMinTakeAmount'));
    }

    try {
      // 使用统一接口更新钱包设置
      await updateWalletSettings(params);
      setDepositLoading(false);
      message.success('充值设置已保存');
      setFormValues(null);
      setGoogleModalVisible(false);
      fetchWalletInfo();
    } catch (error: any) {
      console.error('保存设置失败:', error);
      message.error(error.message || '操作失败，请稍后重试');
      setDepositLoading(false);
    }
  };

  if (infoLoading) {
    return (
      <div className="collection-settings-loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="collection-settings">
      <h2 className="settings-title">充值设置</h2>

      <DepositSettingsForm
        form={depositForm}
        walletInfo={walletInfo}
        loading={depositLoading}
        formChanged={depositFormChanged}
        onSubmit={handleDepositFormSubmit}
        onValuesChange={handleDepositFormChange}
      />

      {/* 使用带密码的谷歌验证码组件 */}
      <GoogleAuthWithPasswordModal
        visible={googleModalVisible}
        onConfirm={handleDepositSettingsSave}
        onCancel={() => setGoogleModalVisible(false)}
        promptText="请输入谷歌验证码和密码完成设置"
        confirmText="确认保存"
      />
    </div>
  );
};

export default DepositSettings;
