import React, { useState, useEffect, useCallback } from 'react';
import { Form, message, Spin } from 'antd';
import { getWalletInfo, updateWalletSettings } from '../../services/walletService';
import GoogleAuthWithPasswordModal from '../common/GoogleAuthWithPasswordModal';
import StrategySettingsForm from './CollectionSettings/StrategySettingsForm';
import './Settings.css';
import { WalletApiApiWalletV1WalletInfo } from '../../services/api/model';

const StrategyCollectionSettings: React.FC = () => {
  const [strategyForm] = Form.useForm();
  const [strategyLoading, setStrategyLoading] = useState(false);
  const [infoLoading, setInfoLoading] = useState(true);
  const [walletInfo, setWalletInfo] = useState<WalletApiApiWalletV1WalletInfo | null>(null);
  const [googleModalVisible, setGoogleModalVisible] = useState(false);
  const [formValues, setFormValues] = useState<any>(null);
  const [strategyFormChanged, setStrategyFormChanged] = useState(false);
  const [changedFields, setChangedFields] = useState<Record<string, any>>({
    strategy: {},
  });

  // 定义获取钱包信息的函数
  const fetchWalletInfo = useCallback(async () => {
    try {
      setInfoLoading(true);
      const info = await getWalletInfo();
      setWalletInfo(info.wallet_info || null);

      if (info.wallet_info) {
        // 重置表单变化状态
        setStrategyFormChanged(false);

        // 重置变化字段记录
        setChangedFields({
          strategy: {},
        });
      }
    } catch (error) {
      console.error('获取钱包信息失败:', error);
      message.error('获取钱包信息失败，请稍后重试');
    } finally {
      setInfoLoading(false);
    }
  }, []);

  // 监听组件生命周期
  useEffect(() => {
    fetchWalletInfo();
  }, [fetchWalletInfo]);

  const handleStrategyFormChange = (changedValues: any, allValues: any) => {
    setStrategyFormChanged(true);
    // 更新变化的字段
    setChangedFields((prev) => ({
      ...prev,
      strategy: {
        ...prev.strategy,
        ...changedValues,
      },
    }));
  };

  const handleStrategyFormSubmit = async (values: any) => {
    try {
      await strategyForm.validateFields();
      // 只提交变化了的字段
      const changedValues = changedFields.strategy;
      setFormValues(changedValues);
      setGoogleModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleStrategySettingsSave = async (googleCode: string, password: string) => {
    setStrategyLoading(true);
    const params: any = {
      google_code: googleCode,
      password: password,
      strategy_collect_switch: true, // 默认开启策略归集
    };

    // 处理阈值设置
    if (formValues.ethThreshold !== undefined) {
      params.eth_collect_threshold = Number(formValues.ethThreshold) || 0;
    } else if (strategyFormChanged) {
      params.eth_collect_threshold = Number(strategyForm.getFieldValue('ethThreshold')) || 0;
    }

    if (formValues.trxThreshold !== undefined) {
      params.trx_collect_threshold = Number(formValues.trxThreshold) || 0;
    } else if (strategyFormChanged) {
      params.trx_collect_threshold = Number(strategyForm.getFieldValue('trxThreshold')) || 0;
    }

    if (formValues.usdtThreshold !== undefined) {
      params.usdt_collect_threshold = Number(formValues.usdtThreshold) || 0;
    } else if (strategyFormChanged) {
      params.usdt_collect_threshold = Number(strategyForm.getFieldValue('usdtThreshold')) || 0;
    }

    try {
      // 使用新的统一接口更新钱包设置
      await updateWalletSettings(params);
      setStrategyLoading(false);
      message.success('策略归集设置已保存');
      setFormValues(null);
      setGoogleModalVisible(false);
      fetchWalletInfo();
    } catch (error: any) {
      console.error('保存设置失败:', error);
      message.error(error.message || '操作失败，请稍后重试');
      setStrategyLoading(false);
    }
  };

  if (infoLoading) {
    return (
      <div className="collection-settings-loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="collection-settings">
      <h2 className="settings-title">策略归集</h2>

      <StrategySettingsForm
        form={strategyForm}
        walletInfo={walletInfo}
        loading={strategyLoading}
        formChanged={strategyFormChanged}
        onSubmit={handleStrategyFormSubmit}
        onValuesChange={handleStrategyFormChange}
      />

      {/* 使用带密码的谷歌验证码组件 */}
      <GoogleAuthWithPasswordModal
        visible={googleModalVisible}
        onConfirm={handleStrategySettingsSave}
        onCancel={() => setGoogleModalVisible(false)}
        promptText="请输入谷歌验证码和密码完成设置"
        confirmText="确认保存"
      />
    </div>
  );
};

export default StrategyCollectionSettings;
