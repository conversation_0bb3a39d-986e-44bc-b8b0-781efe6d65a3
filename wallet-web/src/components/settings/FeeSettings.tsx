import React, { useState, useEffect, useCallback } from 'react';
import { Form, message, Spin, Typography, Card } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { getWalletInfo, updateWalletSettings } from '../../services/walletService';
import { getNetworkFees } from '../../services/networkService';
import GoogleAuthWithPasswordModal from '../common/GoogleAuthWithPasswordModal';
import FeeSettingsForm from './CollectionSettings/FeeSettingsForm';
import './Settings.css';
import {
  WalletApiApiWalletV1WalletInfo
} from '../../services/api/model';

const FeeSettings: React.FC = () => {
  const [feeForm] = Form.useForm();
  const [feeLoading, setFeeLoading] = useState(false);
  const [infoLoading, setInfoLoading] = useState(true);
  const [walletInfo, setWalletInfo] = useState<WalletApiApiWalletV1WalletInfo | null>(null);
  const [googleModalVisible, setGoogleModalVisible] = useState(false);
  const [formValues, setFormValues] = useState<any>(null);
  const [feeFormChanged, setFeeFormChanged] = useState(false);
  const [networkFees, setNetworkFees] = useState<any>(null);
  // Removed unused networkFeesLoading state
  const [changedFields, setChangedFields] = useState<Record<string, any>>({
    fee: {},
  });

  // 定义获取钱包信息的函数
  const fetchWalletInfo = useCallback(async () => {
    try {
      setInfoLoading(true);
      const info = await getWalletInfo();
      setWalletInfo(info.wallet_info || null);

      if (info.wallet_info) {
        // 重置表单变化状态
        setFeeFormChanged(false);

        // 重置变化字段记录
        setChangedFields({
          fee: {},
        });
      }
    } catch (error) {
      console.error('获取钱包信息失败:', error);
      message.error('获取钱包信息失败，请稍后重试');
    } finally {
      setInfoLoading(false);
    }
  }, []);

  // 获取网络费用信息
  const fetchNetworkFees = useCallback(async () => {
    try {
      // Removed unused networkFeesLoading state
      const response = await getNetworkFees('eth');
      setNetworkFees(response);
    } catch (error) {
      console.error('获取网络费用失败:', error);
      // 不显示错误消息，因为这不是关键功能
    }
  }, []);

  // 监听组件生命周期
  useEffect(() => {
    fetchWalletInfo();
    fetchNetworkFees();
  }, [fetchWalletInfo, fetchNetworkFees]);

  const handleFeeFormChange = (changedValues: any, _allValues: any) => {
    setFeeFormChanged(true);
    setChangedFields((prev) => ({
      ...prev,
      fee: {
        ...prev.fee,
        ...changedValues,
      },
    }));
  };

  const handleFeeFormSubmit = async (_values: any) => {
    try {
      await feeForm.validateFields();
      const changedValues = changedFields.fee;
      if (Object.keys(changedValues).length === 0 && !feeFormChanged) {
        message.info('没有检测到手续费设置变化，无需保存');
        return;
      }
      setFormValues(changedValues);
      setGoogleModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleFeeSettingsSave = async (googleCode: string, password: string) => {
    setFeeLoading(true);
    const params: any = {
      google_code: googleCode,
      password: password,
      ...formValues,
    };

    // Ensure numeric types for API if they are not already
    if (params.eth_fee_max !== undefined) params.eth_fee_max = Number(params.eth_fee_max);
    if (params.eth_gas_price !== undefined) params.eth_gas_price = Number(params.eth_gas_price);
    if (params.eth_gas_limit !== undefined) params.eth_gas_limit = Number(params.eth_gas_limit);
    if (params.trx_fee_max !== undefined) params.trx_fee_max = Number(params.trx_fee_max);

    // Remove gas price and limit if mode is auto (1)
    if (params.eth_fee_mode === 1) {
      delete params.eth_gas_price;
      delete params.eth_gas_limit;
    }

    try {
      // 使用新的统一接口更新钱包设置
      await updateWalletSettings(params);
      setFeeLoading(false);
      message.success('手续费设置已保存');
      setFormValues(null);
      setGoogleModalVisible(false);
      fetchWalletInfo();
    } catch (error: any) {
      console.error('保存设置失败:', error);
      message.error(error.message || '操作失败，请稍后重试');
      setFeeLoading(false);
    }
  };

  if (infoLoading) {
    return (
      <div className="collection-settings-loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="collection-settings">
      <h2 className="settings-title">手续费设置</h2>

      {/* 手续费设置表单 */}
      <Card className="settings-section">
        <Typography.Title level={5} style={{ marginTop: 0, marginBottom: '16px' }}>
          <SettingOutlined style={{ marginRight: '8px' }} />
          手续费参数设置
        </Typography.Title>
        <FeeSettingsForm
          form={feeForm}
          walletInfo={walletInfo}
          loading={feeLoading}
          formChanged={feeFormChanged}
          onSubmit={handleFeeFormSubmit}
          onValuesChange={handleFeeFormChange}
          networkFees={networkFees}
        />
      </Card>

      {/* 使用带密码的谷歌验证码组件 */}
      <GoogleAuthWithPasswordModal
        visible={googleModalVisible}
        onConfirm={handleFeeSettingsSave}
        onCancel={() => setGoogleModalVisible(false)}
        promptText="请输入谷歌验证码和密码完成设置"
        confirmText="确认保存"
      />
    </div>
  );
};

export default FeeSettings;
