.collection-settings {
    padding: 20px 0;
}

.collection-settings-title {
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: 500;
}

.settings-form {
    width: 100%;
}

.settings-card {
    margin-bottom: 24px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.card-header {
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    font-weight: 500;
    font-size: 16px;
}

.card-body {
    padding: 16px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #40a9ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-text {
    display: block;
    margin-top: 4px;
    color: #666;
    font-size: 12px;
}

.input-group {
    display: flex;
    width: 100%;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    flex: 1;
}

.input-group-append {
    display: flex;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0 12px;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-left: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    color: #666;
}

.auto-collection-switch {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.collection-section {
    margin-bottom: 16px;
}

.collection-section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
}

.threshold-settings {
    background-color: #f9f9f9;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 16px;
}

.threshold-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
}

.threshold-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 16px;
}

.divider {
    margin: 24px 0;
    border: 0;
    border-top: 1px solid #e8e8e8;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.3s;
}

.btn-primary {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
}

.btn-primary:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}

.btn-primary:disabled {
    background-color: #bae7ff;
    border-color: #bae7ff;
    cursor: not-allowed;
}

/* 自定义开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 22px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
}

input:checked+.slider {
    background-color: #1890ff;
}

input:focus+.slider {
    box-shadow: 0 0 1px #1890ff;
}

input:checked+.slider:before {
    transform: translateX(22px);
}

.slider.round {
    border-radius: 22px;
}

.slider.round:before {
    border-radius: 50%;
}