.collectionPage {
    padding: 0 0 24px 0;
    background-color: #f5f7fa;
}

.header {
    margin-bottom: 24px;
}

.header h2 {
    font-weight: 600;
    margin-bottom: 8px;
}

.formCard {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.helpCard {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.contentWrapper {
    width: 65%;
    margin: 0 auto;
    padding: 12px 0;
}

.collectionTypeGroup {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
}

.collectionTypeButton {
    width: 100%;
    height: auto;
    padding: 20px 16px;
    text-align: center;
    border-radius: 8px;
    transition: all 0.3s;
    margin-right: 12px;
    border: 1px solid #e8e8e8;
}

.collectionTypeButton:last-child {
    margin-right: 0;
}

.collectionTypeButton:hover {
    border-color: #1890ff;
    background-color: #f0f7ff;
}

.collectionTypeContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.collectionTypeIcon {
    margin-bottom: 12px;
    color: #1890ff;
    transition: transform 0.3s ease;
}

.collectionTypeButton:hover .collectionTypeIcon {
    transform: scale(1.1);
}

.collectionTypeLabel {
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

.collectionTypeNote {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 4px;
}

/* 选中状态 */
.collectionTypeButton[class*="ant-radio-button-wrapper-checked"] {
    background-color: #e6f7ff;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.collectionTypeButton[class*="ant-radio-button-wrapper-checked"] .collectionTypeIcon {
    color: #0062cc;
}

.collectionTypeButton[class*="ant-radio-button-wrapper-checked"] .collectionTypeLabel {
    color: #0062cc;
}

/* 表单样式优化 */
form :global(.ant-form-item-label)>label {
    font-weight: 500;
    color: #333;
}

form :global(.ant-form-item) {
    margin-bottom: 20px;
}

/* 修改Select相关样式，确保可点击 */
form :global(.ant-select-selector) {
    border-radius: 6px !important;
    padding: 4px 12px !important;
    cursor: pointer !important;
}

form :global(.ant-select) {
    cursor: pointer !important;
}

form :global(.ant-select-selection-search-input) {
    cursor: pointer !important;
    opacity: 1 !important;
}

form :global(.ant-select-arrow) {
    pointer-events: none !important;
}

/* 确保下拉菜单可见和可交互 */
:global(.ant-select-dropdown) {
    z-index: 9999 !important;
    pointer-events: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* 确保下拉菜单项可点击 */
:global(.ant-select-item) {
    pointer-events: auto !important;
    cursor: pointer !important;
}

form :global(.ant-input) {
    border-radius: 6px;
    padding: 8px 12px;
}

form :global(.ant-btn-primary) {
    height: 44px;
    font-size: 16px;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

form :global(.ant-select:not(.ant-select-disabled):hover .ant-select-selector) {
    border-color: #40a9ff;
}

form :global(.ant-input:hover) {
    border-color: #40a9ff;
}

form :global(.ant-input:focus),
form :global(.ant-input-focused) {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 分步表单样式 */
.formContentContainer {
    display: flex;
    flex-direction: column;
    min-height: 400px;
    position: relative;
}

.stepsContent {
    min-height: 300px;
    margin-top: 24px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    flex: 1;
    overflow: auto;
    margin-bottom: 70px;
    /* 为底部按钮留出空间 */
}

.stepsAction {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 16px 0;
    background-color: #fff;
    bottom: 0;
    width: 100%;
    z-index: 10;
}

.stepFormCard {
    width: 100%;
    margin: 0 auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .contentWrapper {
        width: 100%;
    }

    .collectionTypeIcon svg {
        width: 32px;
        height: 32px;
    }

    .collectionTypeLabel {
        font-size: 13px;
    }

    .stepsContent {
        margin-bottom: 80px;
    }

    .stepsAction {
        padding: 12px 0;
    }
}

/* 自定义select样式 */
.customSelectWrapper {
    position: relative;
    width: 100%;
}

.customSelectWrapper::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #999;
    pointer-events: none;
}

.customSelect {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 100%;
    height: 40px;
    padding: 8px 12px;
    padding-left: 40px;
    /* 为图标留出空间 */
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    background-color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

.customSelect:hover {
    border-color: #40a9ff;
}

.customSelect:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.selectIcons {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

.selectIcon {
    width: 20px;
    height: 20px;
    display: block;
}

/* 禁用状态 */
.customSelect:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.25);
}

.customSelect option {
    padding: 8px;
}