import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Row, Col, Spin } from 'antd'; // Removed Card
// LoadingOutlined is now used within TransactionsTable
import styles from './ConfirmInformation.styles';
// getNode 已移至 useAccountBalances hook
// getWalletAfter 已移至 useCollectionSubmit
// DemogfApiWalletV1SubmitCollectTaskReq 已移至 useCollectionSubmit
import { useNetworkFees, NetworkFeeData } from './hooks/useNetworkFees';
import { useTransactionData, TxItem } from './hooks/useTransactionData';
import { useAccountBalances } from './hooks/useAccountBalances';
import { useGasSettings } from './hooks/useGasSettings';
import { useCollectionSubmit } from './hooks/useCollectionSubmit';
import { TransactionSummaryCard } from './components/TransactionSummaryCard';
import { AmountSettingsCard } from './components/AmountSettingsCard';
import { NetworkFeeSettingsCard } from './components/NetworkFeeSettingsCard';
import { TransactionsTable } from './components/TransactionsTable';

// Crypto icons are now used within TransactionsTable
// import ethIcon from '../../../assets/images/eth.svg';
// import usdtIcon from '../../../assets/images/usdt.svg';
// import trxIcon from '../../../assets/images/tron.svg';
// useMemoizedFn 已不再直接在此文件使用


// TransactionItem 接口已不再需要，因为我们使用从 useTransactionData 导入的 TxItem
// interface TransactionItem {
//   fromAddress: string;
//   toAddress: string;
//   amount: number;
//   id: string;
//   balance: string;
//   usdtBalance: string;
//   balanceLoading?: boolean;
//   usdtBalanceLoading?: boolean;
// }

interface ConfirmInformationProps {
  parentForm?: any; // 父组件传入的表单实例
  onSubmit?: () => void; // 添加提交回调函数属性
}

// NetworkFee 接口现在由 useNetworkFees.ts 中的 NetworkFeeData 替代

// u7c7bu578bu6269u5c55uff0cu4f7fu5f97ConfirmInformationu53efu4ee5u5177u6709u9759u6001u65b9u6cd5
const ConfirmInformation = forwardRef((props: ConfirmInformationProps, ref) => {
  // amountType, specificAmount 仍然在组件内管理，因为它们直接影响UI交互和 useTransactionData 的输入
  const [amountType, setAmountType] = useState<string>('specific');
  const [specificAmount, setSpecificAmount] = useState<string>('10');
  // submitting 状态和 handleSubmitCollectTask 逻辑移至 useCollectionSubmit
  // totalAmount, transactions, fetchTransactionData, getAddressCount, handleDeleteTransaction 移至 useTransactionData
  // gasLimit, showGasLimitInput, tronFeeLimit, showTronFeeLimitInput 移至 useGasSettings

  // 获取表单中的网络和币种信息
  const network = props.parentForm?.getFieldValue('network') || '';
  const currency = props.parentForm?.getFieldValue('currency') || '';
  const collectionType = props.parentForm?.getFieldValue('collectionType') || '';

  // 使用 useNetworkFees 钩子
  const {
    feeType,
    customFee,
    feeData,
    loadingFeeData, // 重命名 loading 为 loadingFeeData 以避免冲突
    handleFeeTypeChange,
    handleCustomFeeChange,
    handleRefreshGasFee,
  } = useNetworkFees(network); // 传递 network 给钩子

  // 使用 useTransactionData 钩子
  const {
    transactions,
    setTransactions, // 接收 setTransactions 以便后续 useAccountBalances 更新
    totalAmount,
    // fetchTransactionData, // fetchTransactionData 由钩子内部的 useEffect 调用
    getAddressCount,
    handleDeleteTransaction,
  } = useTransactionData({
    parentForm: props.parentForm,
    collectionType,
    amountType,
    specificAmount,
    // initialTransactions: [], // 初始为空，后续由 useAccountBalances 填充和更新
  });


  const [pageFullyLoaded, setPageFullyLoaded] = useState<boolean>(false);

  // 使用 useAccountBalances 钩子
  const {
    // addressBalances, // addressBalances 的直接使用已改为从 tx.balance 读取
    isLoadingBalances, // 这个是从 useAccountBalances 获取的整体加载状态
    currentLoadingAddress, // 这个是从 useAccountBalances 获取的当前正在加载的地址
  } = useAccountBalances({
    transactions: transactions as TxItem[],
    setTransactions,
    network,
    currency,
    pageFullyLoaded,
  });

  // 使用 useGasSettings 钩子
  const {
    gasLimit,
    showGasLimitInput,
    handleGasLimitClick,
    handleGasLimitChange,
    handleGasLimitBlur,
    getGasLimitTip,
    tronFeeLimit, // TRON网络的费用限制
    showTronFeeLimitInput,
    handleTronFeeLimitClick,
    handleTronFeeLimitChange,
    handleTronFeeLimitBlur,
  } = useGasSettings({ network, currency });

  // 使用 useCollectionSubmit 钩子
  const {
    handleSubmitCollectTask,
  } = useCollectionSubmit({
    transactions: transactions as TxItem[],
    network,
    currency,
    collectionType,
    amountType,
    specificAmount,
    gasLimit,
    feeType,
    customFee,
    feeData,
    tronFeeLimit,
    parentForm: props.parentForm,
    onSubmitSuccess: props.onSubmit, // 将父组件的 onSubmit 作为成功回调传递
  });

  // fetchNetworkFees, handleFeeTypeChange, handleCustomFeeChange, handleRefreshGasFee 已移至 useNetworkFees
  // getBalance, balanceLoading, loadedAddressesRef, isLoadingRef, 和相关的 useEffects (如余额加载逻辑) 已移至 useAccountBalances
  // GasLimit 和 TronFeeLimit 相关的 useEffect 和状态已移至 useGasSettings
  // handleSubmitCollectTask 和 submitting 状态已移至 useCollectionSubmit

  // 计算总费用 (依赖 getAddressCount from useTransactionData and gasLimit from useGasSettings)
  const calculateTotalFee = () => {
    const addressCount = getAddressCount();
    const gweiValue = (feeData as NetworkFeeData)[feeType as keyof NetworkFeeData]?.gwei || customFee || 0; // 确保自定义费用也考虑
    return (addressCount * gweiValue * gasLimit) / **********;
  };

  // gasLimit 设置的 effect 已移至 useGasSettings

  useEffect(() => {
    setPageFullyLoaded(true);
  }, []);
  
  // 所有与余额获取、状态管理（如 loadedAddressesRef, isLoadingRef, balanceLoading map, setAddressBalances）
  // 以及相关的 useEffects (包括基于 pageFullyLoaded, transactions 触发余额加载的逻辑，
  // 和基于 network, currency 清理缓存的逻辑) 都已移至 useAccountBalances 钩子。
  // getBalance 函数本身也移至该钩子内部。

  // 处理转账数量类型变更
  const handleAmountTypeChange = (key: string) => {
    setAmountType(key);
  };

  // 处理特定数量变更
  const handleSpecificAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSpecificAmount(e.target.value);
  };

  // handleDeleteTransaction 已移至 useTransactionData

  // 格式化地址显示
  const formatAddress = (address: string) => {
    if (!address) return '';
    return address;
    // if (address.length < 20) return address;
    // return `${address.substring(0, 8)}...${address.substring(address.length - 8)}`;
  };

  // handleGasLimitClick, handleGasLimitChange, handleGasLimitBlur, getGasLimitTip 已移至 useGasSettings
  // handleFeeLimitClick (for TRON), handleFeeLimitChange, handleFeeLimitBlur 已移至 useGasSettings (并重命名为 tron 相关)

  // handleRefreshGasFee 已移至 useNetworkFees

  // 组件挂载时获取网络费用数据的 useEffect 已在 useNetworkFees 中处理

  // handleSubmitCollectTask 逻辑已移至 useCollectionSubmit
  // submitting 状态也由 useCollectionSubmit 管理 (isSubmitting)

  useImperativeHandle(ref, () => ({
    handleSubmitCollectTask: handleSubmitCollectTask, // handleSubmitCollectTask 来自 useCollectionSubmit
  }));

  // TRON Fee Limit 的点击、修改、失焦处理函数已移至 useGasSettings (handleTronFeeLimitClick 等)
  // 注意：原先的 setSubmitting(true/false) 逻辑现在由 useCollectionSubmit 内部的 setIsSubmitting 处理

  return (
    <div style={styles.container}>
      {loadingFeeData && ( // 使用 loadingFeeData
        <div style={styles.loadingContainer}>
          <Spin tip="加载Gas费用中..." />
        </div>
      )}
      <TransactionSummaryCard
        network={network}
        currency={currency}
        addressCount={getAddressCount()}
        totalAmount={totalAmount}
        showGasLimitInput={showGasLimitInput}
        gasLimit={gasLimit}
        handleGasLimitChange={handleGasLimitChange}
        handleGasLimitBlur={handleGasLimitBlur}
        handleGasLimitClick={handleGasLimitClick}
        getGasLimitTip={getGasLimitTip}
        showTronFeeLimitInput={showTronFeeLimitInput}
        tronFeeLimit={tronFeeLimit}
        handleTronFeeLimitChange={handleTronFeeLimitChange}
        handleTronFeeLimitBlur={handleTronFeeLimitBlur}
        handleTronFeeLimitClick={handleTronFeeLimitClick}
        feeType={feeType}
        feeData={feeData}
        customFee={customFee}
        loadingFeeData={loadingFeeData}
        handleRefreshGasFee={handleRefreshGasFee}
        calculateTotalFee={calculateTotalFee}
      />
      <Row gutter={16}>
        <Col span={12}>
          <AmountSettingsCard
            amountType={amountType}
            handleAmountTypeChange={handleAmountTypeChange}
            specificAmount={specificAmount}
            handleSpecificAmountChange={handleSpecificAmountChange}
            currency={currency}
            collectionType={collectionType}
          />
        </Col>

        {network === 'ETH' && (
          <Col span={12}>
            <NetworkFeeSettingsCard
              feeType={feeType}
              handleFeeTypeChange={handleFeeTypeChange}
              feeData={feeData}
              customFee={customFee}
              handleCustomFeeChange={handleCustomFeeChange}
            />
          </Col>
        )}
      </Row>

      <TransactionsTable
        transactions={transactions as TxItem[]}
        amountType={amountType}
        currency={currency}
        collectionType={collectionType} // Pass collectionType
        isLoadingBalances={isLoadingBalances}
        currentLoadingAddress={currentLoadingAddress}
        handleDeleteTransaction={handleDeleteTransaction}
        formatAddress={formatAddress}
      />

      {/* 提交按钮已移除，改用CollectionForm组件中的提交按钮 */}
    </div>
  );
});

export default ConfirmInformation;
