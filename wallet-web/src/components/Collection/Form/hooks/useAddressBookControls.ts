import { useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import type { FormInstance } from 'antd';
import type { CollectionType } from '../AddTransferAddresses'; // 从主组件导入

export type AddressType = 'from' | 'to';

interface UseAddressBookControlsProps {
  form: FormInstance;
  collectionType: CollectionType;
}

interface UseAddressBookControlsReturn {
  addressBookVisible: boolean;
  addressType: AddressType;
  addressField: string;
  openAddressBook: (type: AddressType, field: string) => void;
  handleAddressSelect: (address: string) => void;
  closeAddressBook: () => void;
}

export const useAddressBookControls = ({
  form,
  collectionType,
}: UseAddressBookControlsProps): UseAddressBookControlsReturn => {
  const [addressBookVisible, setAddressBookVisible] = useState(false);
  const [addressType, setAddressType] = useState<AddressType>('from');
  const [addressField, setAddressField] = useState<string>('');

  const openAddressBook = useMemoizedFn((type: AddressType, field: string) => {
    // 一对多模式下，不允许修改 fromAddress
    if (collectionType === 'oneToMany' && field === 'fromAddress') {
      return;
    }
    // 多对一模式下，不允许修改 toAddress
    if (collectionType === 'manyToOne' && field === 'toAddress') {
      return;
    }

    setAddressType(type);
    setAddressField(field);
    setAddressBookVisible(true);
  });

  const handleAddressSelect = useMemoizedFn((address: string) => {
    if (addressField === 'fromAddress' || addressField === 'toAddress') {
      // 单地址字段，直接设置值
      form.setFieldsValue({
        [addressField]: address,
      });
    } else {
      // 对于多地址的情况 (toAddresses, fromAddresses, transactions)
      // 直接使用选择的地址列表覆盖，因为地址簿通常返回的是最终选择的列表
      const addresses = address.split('\n').filter((addr: string) => addr.trim());
      form.setFieldsValue({
        [addressField]: addresses.join('\n'),
      });
    }
    setAddressBookVisible(false);
  });

  const closeAddressBook = useMemoizedFn(() => {
    setAddressBookVisible(false);
  });

  return {
    addressBookVisible,
    addressType,
    addressField,
    openAddressBook,
    handleAddressSelect,
    closeAddressBook,
  };
};