import { useState } from 'react';
import { message } from 'antd';
import { getCollect } from '../../../../services/api/collect/collect'; // Updated import
import { WalletApiApiWalletV1SubmitCollectTaskReq } from '../../../../services/api/model';
import { TxItem } from './useTransactionData';
import { NetworkFeeData } from './useNetworkFees';

interface UseCollectionSubmitProps {
  transactions: TxItem[];
  network: string;
  currency: string;
  collectionType: string;
  amountType: string;
  specificAmount: string;
  gasLimit: number; // 来自 useGasSettings
  feeType: string; // 来自 useNetworkFees
  customFee: number; // 来自 useNetworkFees
  feeData: NetworkFeeData; // 来自 useNetworkFees
  tronFeeLimit: number; // 来自 useGasSettings
  parentForm: any; // 用于获取某些特定情况下的表单值，例如 oneToMany 的 fromAddress
  onSubmitSuccess?: () => void; // 成功回调
}

export const useCollectionSubmit = ({
  transactions,
  network,
  currency,
  collectionType,
  amountType,
  specificAmount,
  gasLimit,
  feeType,
  customFee,
  feeData,
  tronFeeLimit,
  parentForm,
  onSubmitSuccess,
}: UseCollectionSubmitProps) => {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const handleSubmitCollectTask = async () => {
    if (transactions.length === 0) {
      message.error('没有可提交的交易记录');
      return;
    }

    setIsSubmitting(true);
    try {
      let currentGasPrice = '';
      let currentApiFeeLimit = ''; // API期望的fee_limit

      if (network === 'ETH') {
        if (feeType === 'custom') {
          currentGasPrice = customFee.toString();
        } else {
          const currentFeeEntry = feeData[feeType as keyof NetworkFeeData];
          currentGasPrice = currentFeeEntry ? currentFeeEntry.gwei.toString() : '1.5'; // 默认Gwei
        }
        currentApiFeeLimit = gasLimit.toString(); // ETH的API fee_limit 通常是 gasLimit (单位：gas)
                                                 // 或者，如果API期望的是Wei，则是 gasPrice * gasLimit
                                                 // 这里假设API的fee_limit是指gasLimit本身
                                                 // **重要**: 需要根据后端API的具体要求来确定此值
      } else if (network === 'TRON') {
        currentGasPrice = ''; // TRON 通常不直接使用 gasPrice 概念，而是能量和带宽
        currentApiFeeLimit = tronFeeLimit.toString(); // TRON 的 fee_limit (单位: SUN, 1 TRX = 1,000,000 SUN)
      }


      const requestParams: WalletApiApiWalletV1SubmitCollectTaskReq = {
        type: network,
        coin: currency,
        trans_type: collectionType === 'oneToMany' ? 'one2many' : (collectionType === 'manyToOne' ? 'many2one' : 'many2many'),
        from_address: [],
        to_address: [],
        is_all_amount: amountType === 'all',
        gas_limit: gasLimit.toString(), // ETH的Gas Limit
        gas_price: currentGasPrice,     // ETH的Gas Price (Gwei)
        fee_limit: currentApiFeeLimit,  // ETH的Gas Limit 或 TRON的Fee Limit (SUN)
      };

      if (!requestParams.is_all_amount && amountType === 'specific') {
        requestParams.amount = specificAmount;
      }

      if (collectionType === 'oneToMany') {
        const formFromAddress = parentForm?.getFieldValue('fromAddress');
        requestParams.from_address = formFromAddress ? [formFromAddress] : [];
        requestParams.to_address = transactions.map((tx) => tx.toAddress);
      } else if (collectionType === 'manyToOne') {
        requestParams.from_address = transactions.map((tx) => tx.fromAddress);
        const formToAddress = parentForm?.getFieldValue('toAddress');
        requestParams.to_address = formToAddress ? [formToAddress] : [];
      } else if (collectionType === 'manyToMany') {
        // 假设API对于多对多，from_address 和 to_address 是一一对应的列表
        // 并且金额由 is_all_amount 或 specificAmount (如果适用) 控制
        requestParams.from_address = transactions.map(tx => tx.fromAddress);
        requestParams.to_address = transactions.map(tx => tx.toAddress);
        // 如果 manyToMany 模式下每笔交易金额不同，API设计需要支持更复杂的结构
        // 当前的 DemogfApiWalletV1SubmitCollectTaskReq 结构可能不支持此场景
        if (requestParams.is_all_amount === false && amountType === 'specific') {
            // 如果是指定金额的多对多，且API只接受一个全局amount，这可能不符合预期
            // 除非API约定这个amount应用于所有交易，或者有其他方式指定每个交易的金额
            // 暂时按全局amount处理
        }
      }
      
      // 过滤掉空的 from_address 或 to_address，以防意外提交
      requestParams.from_address = requestParams.from_address.filter((addr: string) => !!addr);
      requestParams.to_address = requestParams.to_address.filter((addr: string) => !!addr);

      if (requestParams.from_address.length === 0 || requestParams.to_address.length === 0) {
          message.error('发送或接收地址不能为空');
          setIsSubmitting(false);
          return;
      }

      const collectApi = getCollect(); // Updated to use getCollect
      const result = await collectApi.postWalletSubmitCollectTask(requestParams);

      if (result.success) {
        message.success(`归集任务提交成功，任务ID: ${result.task_id}`);
        if (onSubmitSuccess) {
          onSubmitSuccess();
        }
      } else {
        // DemogfApiWalletV1SubmitCollectTaskRes 类型上没有 message 属性
        // 需要根据实际的错误返回结构来获取错误信息
        // 例如，可能是 result.error 或 result.msg 等
        // 作为临时处理，显示通用错误
        message.error('归集任务提交失败，请检查网络或联系管理员。');
      }
    } catch (error: any) {
      console.error('提交归集任务失败', error);
      message.error(error.message || '提交归集任务失败');
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    handleSubmitCollectTask,
  };
};