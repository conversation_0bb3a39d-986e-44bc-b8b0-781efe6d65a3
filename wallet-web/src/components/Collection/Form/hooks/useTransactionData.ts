import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { useMemoizedFn } from 'ahooks';
// TransactionItem 接口定义，如果 ConfirmInformation.tsx 中的定义不导出，则在此重新定义
// 为了独立性，暂时在此重新定义，后续可以考虑提取到共享文件
export interface TxItem {
  fromAddress: string;
  toAddress: string;
  amount: number;
  id: string;
  balance: string; // 这些余额相关的字段后续可能会移到 useAccountBalances 钩子
  usdtBalance: string;
  balanceLoading?: boolean;
  usdtBalanceLoading?: boolean;
}

interface UseTransactionDataProps {
  parentForm: any; // 父组件传入的表单实例
  collectionType: string;
  amountType: string;
  specificAmount: string;
  // 外部的 transactions 和 setTransactions，用于同步余额加载状态
  // 但更好的做法是将余额管理也分离出去
  initialTransactions?: TxItem[]; 
}

export const useTransactionData = ({
  parentForm,
  collectionType,
  amountType,
  specificAmount,
  initialTransactions = [],
}: UseTransactionDataProps) => {
  const [transactions, setTransactions] = useState<TxItem[]>(initialTransactions);
  const [totalAmount, setTotalAmount] = useState<number>(0);

  const fetchTransactionData = useMemoizedFn(() => {
    if (!parentForm) return;

    try {
      const latestCollectionType = parentForm.getFieldValue('collectionType') || collectionType;
      const newTransactions: TxItem[] = [];
      const existingBalances: Record<
        string,
        { balance: string; usdtBalance: string; balanceLoading?: boolean; usdtBalanceLoading?: boolean }
      > = {};

      // 使用传入的 initialTransactions (如果它代表了最新的、包含余额加载状态的列表)
      // 或者，如果余额管理完全分离，这里就不需要 initialTransactions
      const currentTransactionsForBalance = transactions.length > 0 ? transactions : initialTransactions;

      currentTransactionsForBalance.forEach((tx) => {
        if (tx.balance !== '--' && tx.usdtBalance !== '--' && !tx.balanceLoading && !tx.usdtBalanceLoading) {
          existingBalances[tx.fromAddress] = {
            balance: tx.balance,
            usdtBalance: tx.usdtBalance,
            balanceLoading: tx.balanceLoading,
            usdtBalanceLoading: tx.usdtBalanceLoading,
          };
        }
      });
      
      if (latestCollectionType === 'oneToMany') {
        const fromAddress = parentForm.getFieldValue('fromAddress') || '';
        const toAddresses = (parentForm.getFieldValue('toAddresses') || '').split('\n').filter(Boolean);
        const amounts = (parentForm.getFieldValue('amounts') || '').split('\n').filter(Boolean);

        toAddresses.forEach((toAddress: string, index: number) => {
          const existingBalance = existingBalances[fromAddress] || {
            balance: '--',
            usdtBalance: '--',
            balanceLoading: true, // 默认为 true，让 useAccountBalances 去加载
            usdtBalanceLoading: true,
          };
          newTransactions.push({
            fromAddress,
            toAddress,
            amount: parseFloat(amounts[index] || specificAmount),
            id: `${fromAddress}-${toAddress}-${index}`,
            ...existingBalance,
          });
        });
      } else if (latestCollectionType === 'manyToOne') {
        const toAddress = parentForm.getFieldValue('toAddress') || '';
        const fromAddresses = (parentForm.getFieldValue('fromAddresses') || '').split('\n').filter(Boolean);

        fromAddresses.forEach((fromAddress: string, index: number) => {
          const existingBalance = existingBalances[fromAddress] || {
            balance: '--',
            usdtBalance: '--',
            balanceLoading: true,
            usdtBalanceLoading: true,
          };
          newTransactions.push({
            fromAddress,
            toAddress,
            amount: amountType === 'specific' ? parseFloat(specificAmount) : 0,
            id: `${fromAddress}-${toAddress}-${index}`,
            ...existingBalance,
          });
        });
      } else if (latestCollectionType === 'manyToMany') {
        const txLines = (parentForm.getFieldValue('transactions') || '').split('\n').filter(Boolean);
        txLines.forEach((line: string, index: number) => {
          const parts = line.split(',');
          if (parts.length >= 3) {
            const fromAddress = parts[0].trim();
            const existingBalance = existingBalances[fromAddress] || {
              balance: '--',
              usdtBalance: '--',
              balanceLoading: true,
              usdtBalanceLoading: true,
            };
            newTransactions.push({
              fromAddress,
              toAddress: parts[1].trim(),
              amount: parseFloat(parts[2].trim()) || 0,
              id: `tx-${index}`,
              ...existingBalance,
            });
          }
        });
      }
      setTransactions(newTransactions);
    } catch (error) {
      console.error('解析交易数据失败', error);
      message.error('解析交易数据失败');
    }
  });

  const getAddressCount = useCallback(() => {
    return transactions.length;
  }, [transactions]);

  const calculateTotalTransferAmount = useMemoizedFn(() => {
    if (transactions.length === 0) return 0;
    return transactions.reduce((sum, tx) => sum + tx.amount, 0);
  });

  useEffect(() => {
    if (parentForm) {
      fetchTransactionData();
    }
  }, [parentForm, collectionType, amountType, specificAmount, fetchTransactionData]);

  useEffect(() => {
    setTotalAmount(calculateTotalTransferAmount());
  }, [transactions, calculateTotalTransferAmount]);

  const handleDeleteTransaction = useMemoizedFn((id: string) => {
    const updatedTransactions = transactions.filter((tx) => tx.id !== id);
    setTransactions(updatedTransactions);

    if (!parentForm) return;
    const targetTransaction = transactions.find((tx) => tx.id === id);
    if (!targetTransaction) return;

    const currentCollectionType = parentForm.getFieldValue('collectionType') || collectionType;

    if (currentCollectionType === 'oneToMany') {
      const toAddresses = (parentForm.getFieldValue('toAddresses') || '').split('\n').filter(Boolean);
      const amounts = (parentForm.getFieldValue('amounts') || '').split('\n').filter(Boolean);
      const fromAddress = parentForm.getFieldValue('fromAddress');
      const idx = toAddresses.findIndex(
        (addr: string, index: number) =>
          `${fromAddress}-${addr}-${index}` === id ||
          (addr === targetTransaction.toAddress && fromAddress === targetTransaction.fromAddress),
      );
      if (idx !== -1) {
        const newToAddresses = [...toAddresses];
        const newAmounts = [...amounts];
        newToAddresses.splice(idx, 1);
        newAmounts.splice(idx, 1);
        parentForm.setFieldsValue({
          toAddresses: newToAddresses.join('\n'),
          amounts: newAmounts.join('\n'),
        });
      }
    } else if (currentCollectionType === 'manyToOne') {
      const fromAddresses = (parentForm.getFieldValue('fromAddresses') || '').split('\n').filter(Boolean);
      const toAddress = parentForm.getFieldValue('toAddress');
      const idx = fromAddresses.findIndex(
        (addr: string, index: number) =>
          `${addr}-${toAddress}-${index}` === id ||
          (addr === targetTransaction.fromAddress && toAddress === targetTransaction.toAddress),
      );
      if (idx !== -1) {
        const newFromAddresses = [...fromAddresses];
        newFromAddresses.splice(idx, 1);
        parentForm.setFieldsValue({
          fromAddresses: newFromAddresses.join('\n'),
        });
      }
    } else if (currentCollectionType === 'manyToMany') {
      const txLines = (parentForm.getFieldValue('transactions') || '').split('\n').filter(Boolean);
      const updatedTxLines = txLines.filter((line: string, index: number) => `tx-${index}` !== id);
      parentForm.setFieldsValue({
        transactions: updatedTxLines.join('\n'),
      });
    }
    message.success('已删除交易记录');
    // 延迟重新获取数据以确保父表单更新后UI同步
    // 这一步可能需要调整，或者通过更可靠的方式同步状态
    window.setTimeout(() => {
        fetchTransactionData();
    }, 100);
  });

  return {
    transactions,
    setTransactions, // 暴露以便 useAccountBalances 可以更新它
    totalAmount,
    fetchTransactionData,
    getAddressCount,
    handleDeleteTransaction,
  };
};