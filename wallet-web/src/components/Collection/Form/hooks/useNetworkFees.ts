import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { getNode } from '../../../../services/api/node/node';

// NetworkFeeData 接口定义，用于描述网络费用数据的结构
export interface NetworkFeeData {
  slow: { value: number; usd: number; time: string; gwei: number };
  average: { value: number; usd: number; time: string; gwei: number };
  fast: { value: number; usd: number; time: string; gwei: number };
  custom?: { value: number; usd: number; time: string; gwei: number };
}

const initialFeeData: NetworkFeeData = {
  slow: { value: 0.00000215, usd: 0.03, time: '~30分钟', gwei: 1.25 },
  average: { value: 0.00000431, usd: 0.07, time: '~5分钟', gwei: 1.52 },
  fast: { value: 0.00000897, usd: 0.15, time: '~1分钟', gwei: 3.28 },
};

export const useNetworkFees = (network?: string) => {
  const [feeType, setFeeType] = useState<string>('average');
  const [customFee, setCustomFee] = useState<number>(1.52); // 默认自定义Gwei值
  const [feeData, setFeeData] = useState<NetworkFeeData>(initialFeeData);
  const [loading, setLoading] = useState<boolean>(false);

  const fetchNetworkFees = useCallback(async () => {
    if (network !== 'ETH') { // 仅ETH网络需要获取Gas费用
      setFeeData(initialFeeData); // 其他网络使用默认或不显示
      return;
    }
    try {
      setLoading(true);
      const nodeApi = getNode();
      const gasData = await nodeApi.getNodeGetGastracker();

      if (gasData) {
        const safeGasPrice = gasData.SafeGasPrice || 1.25;
        const proposeGasPrice = gasData.ProposeGasPrice || 1.52;
        const fastGasPrice = gasData.FastGasPrice || 3.28;

        const updatedFeeData: NetworkFeeData = {
          slow: {
            value: safeGasPrice * 0.00000172, // 这些转换系数可能需要根据实际情况调整
            usd: safeGasPrice * 0.024,
            time: '~30分钟',
            gwei: safeGasPrice,
          },
          average: {
            value: proposeGasPrice * 0.00000284,
            usd: proposeGasPrice * 0.046,
            time: '~5分钟',
            gwei: proposeGasPrice,
          },
          fast: {
            value: fastGasPrice * 0.00000274,
            usd: fastGasPrice * 0.046,
            time: '~1分钟',
            gwei: fastGasPrice,
          },
        };
        setFeeData(updatedFeeData);
        // 如果当前选中的是自定义，并且自定义的gwei值与更新后的平均值不同，则保留自定义值
        if (feeType === 'custom' && updatedFeeData.custom?.gwei !== customFee) {
            setFeeData(prev => ({
                ...prev,
                custom: {
                    value: customFee * 0.00000284, // 使用 customFee 计算
                    usd: customFee * 0.046,
                    time: '',
                    gwei: customFee,
                }
            }));
        } else if (feeType !== 'custom') {
             // 如果不是自定义，则确保 customFee 与 average gwei 一致，以便切换到自定义时有个合理的初始值
            setCustomFee(updatedFeeData.average.gwei);
        }

        message.success('Gas费用数据已更新');
      } else {
        setFeeData(initialFeeData); // 获取失败则使用初始值
        message.warning('获取Gas费用数据失败，使用默认值');
      }
    } catch (error) {
      console.error('获取网络费用失败', error);
      setFeeData(initialFeeData);
      message.error('获取网络费用失败，使用默认值');
    } finally {
      setLoading(false);
    }
  }, [network, feeType, customFee]); // 添加 feeType 和 customFee 作为依赖项，以确保在它们变化时能正确处理自定义费用

  useEffect(() => {
    fetchNetworkFees();
  }, [fetchNetworkFees]); // fetchNetworkFees 已经是 useCallback 包裹的

  const handleFeeTypeChange = (e: any) => { // antd RadioChangeEvent
    const newFeeType = e.target.value;
    setFeeType(newFeeType);
    if (newFeeType === 'custom') {
      // 当切换到自定义时，如果 feeData.custom 不存在或其 gwei 与 customFee 不同，则更新
      if (!feeData.custom || feeData.custom.gwei !== customFee) {
        setFeeData(prev => ({
            ...prev,
            custom: {
                value: customFee * 0.00000284,
                usd: customFee * 0.046,
                time: '',
                gwei: customFee,
            }
        }));
      }
    }
  };

  const handleCustomFeeChange = (value: number | null) => {
    if (value !== null) {
      setCustomFee(value);
      // 当自定义费用变化时，更新 feeData 中的 custom 部分
      setFeeData(prev => ({
        ...prev,
        custom: {
          value: value * 0.00000284, // 使用新的 value 计算
          usd: value * 0.046,
          time: '', // 自定义时间通常不显示
          gwei: value,
        }
      }));
    }
  };

  const handleRefreshGasFee = () => {
    fetchNetworkFees();
  };

  return {
    feeType,
    customFee,
    feeData,
    loadingFeeData: loading,
    handleFeeTypeChange,
    handleCustomFeeChange,
    handleRefreshGasFee,
    setFeeType, // 暴露 setFeeType 以便外部可以重置
    setCustomFee, // 暴露 setCustomFee
  };
};