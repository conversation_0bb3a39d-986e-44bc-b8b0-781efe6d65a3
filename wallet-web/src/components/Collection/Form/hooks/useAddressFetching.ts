import { useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import { getCollect } from '../../../../services/api/collect/collect';
import { isEthLikeNetwork, isTronLikeNetwork } from '../../../../utils/networkUtils';
import type { FormInstance } from 'antd';

import type { CollectionType } from '../AddTransferAddresses';

interface UseAddressFetchingProps {
  collectionType: CollectionType;
  selectedNetwork?: string;
  form: FormInstance; // 接收 Form 实例用于设置表单值
}

interface UseAddressFetchingReturn {
  loadingMinerAddress: boolean;
  loadingCollectionAddress: boolean;
  fetchMinerFeeAddress: () => Promise<void>;
  fetchCollectionAddress: () => Promise<void>;
}

export const useAddressFetching = ({
  collectionType,
  selectedNetwork,
  form,
}: UseAddressFetchingProps): UseAddressFetchingReturn => {
  const [loadingMinerAddress, setLoadingMinerAddress] = useState(false);
  const [loadingCollectionAddress, setLoadingCollectionAddress] = useState(false);

  const fetchMinerFeeAddress = useMemoizedFn(async () => {
    if (collectionType !== 'oneToMany' || !selectedNetwork) return;

    setLoadingMinerAddress(true);
    try {
      const collectApi = getCollect();
      const response = await collectApi.getWalletGetFeeAddress();

      let minerFeeAddress = '';
      if (isEthLikeNetwork(selectedNetwork)) {
        minerFeeAddress = response?.eth_fee_address || '';
      } else if (isTronLikeNetwork(selectedNetwork)) {
        minerFeeAddress = response?.trx_fee_address || '';
      }

      if (minerFeeAddress) {
        form.setFieldsValue({ fromAddress: minerFeeAddress });
      }
    } catch (error) {
      console.error('获取矿工费地址失败:', error);
      // 可以在这里添加错误提示，例如使用 Ant Design 的 message 组件
      // message.error('获取矿工费地址失败');
    } finally {
      setLoadingMinerAddress(false);
    }
  });

  const fetchCollectionAddress = useMemoizedFn(async () => {
    if (collectionType !== 'manyToOne' || !selectedNetwork) return;

    setLoadingCollectionAddress(true);
    try {
      const collectApi = getCollect();
      const response = await collectApi.getWalletGetCollectAddress();

      let collectionAddress = '';
      if (isEthLikeNetwork(selectedNetwork)) {
        collectionAddress = response?.eth_collect_address || '';
      } else if (isTronLikeNetwork(selectedNetwork)) {
        collectionAddress = response?.trx_collect_address || '';
      }

      if (collectionAddress) {
        form.setFieldsValue({ toAddress: collectionAddress });
      }
    } catch (error) {
      console.error('获取归集地址失败:', error);
      // message.error('获取归集地址失败');
    } finally {
      setLoadingCollectionAddress(false);
    }
  });

  // 自动获取地址的逻辑将移到主组件的 useEffect 中，
  // 因为这个 Hook 本身不应该决定何时执行 fetch，而是提供 fetch 方法。

  return {
    loadingMinerAddress,
    loadingCollectionAddress,
    fetchMinerFeeAddress,
    fetchCollectionAddress,
  };
};