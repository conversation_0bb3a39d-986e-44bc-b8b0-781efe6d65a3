import React, { useState, useEffect, useRef } from 'react'; // 添加 React 导入, 移除未使用的 useCallback
import { useMemoizedFn } from 'ahooks';
import { getNode } from '../../../../services/api/node/node';
import { TxItem } from './useTransactionData'; // 使用从 useTransactionData 导出的 TxItem

interface UseAccountBalancesProps {
  transactions: TxItem[];
  setTransactions: React.Dispatch<React.SetStateAction<TxItem[]>>; // React.Dispatch 和 React.SetStateAction 需要 React 导入
  network: string;
  currency: string; // currency 用于判断是否需要获取USDT余额等（如果适用）
  pageFullyLoaded: boolean; // 确保页面完全加载后再开始获取余额
}

export const useAccountBalances = ({
  transactions,
  setTransactions,
  network,
  currency,
  pageFullyLoaded,
}: UseAccountBalancesProps) => {
  const [addressBalances, setAddressBalances] = useState<Record<string, string>>({});
  const [isLoadingBalances, setIsLoadingBalances] = useState<boolean>(false);
  const [currentLoadingAddress, setCurrentLoadingAddress] = useState<string>('');
  
  const loadedAddressesRef = useRef<Set<string>>(new Set());
  const isLoadingRef = useRef<boolean>(false); // 防止并发加载

  // 获取单个地址的主币余额
  const getNativeBalance = useMemoizedFn(async (address: string, currentNetwork: string) => {
    // 注意：这里的 currency 参数可能需要根据实际API调整，API可能只关心 network (ETH/TRON)
    // 而USDT余额的获取可能是另一个API调用或参数不同
    try {
      const nodeApi = getNode();
      const response = await nodeApi.getNodeGetTokenBalance({
        address: address,
        type: currentNetwork, // API 需要的是链类型，如 ETH, TRON
        // coin: currency, // getNodeGetTokenBalance API 定义中似乎没有 coin 参数，它通常返回主币余额
      });
      return response?.tokenBalance || '0';
    } catch (error) {
      console.error(`获取地址 ${address} 在网络 ${currentNetwork} 的余额失败:`, error);
      return '0'; // 出错时返回 '0'
    }
  });

  // TODO: 如果需要，可以添加获取代币余额的函数，例如 getUsdtBalance

  useEffect(() => {
    // 当网络或币种变化时，清除已加载的地址缓存和现有余额记录
    loadedAddressesRef.current.clear();
    setAddressBalances({});
    // 同时，可能需要重置 transactions 列表中的余额信息
    setTransactions(prevTxs => 
        prevTxs.map(tx => ({
            ...tx,
            balance: '--', // 重置为主币余额占位符
            usdtBalance: tx.usdtBalance ? '--' : tx.usdtBalance, // 如果有USDT余额字段也重置
            balanceLoading: true, // 标记为需要重新加载
            usdtBalanceLoading: tx.usdtBalanceLoading !== undefined ? true : undefined,
        }))
    );
  }, [network, currency, setTransactions]);

  useEffect(() => {
    if (!pageFullyLoaded || transactions.length === 0 || isLoadingRef.current) {
      return;
    }

    const loadNewBalances = async () => {
      if (isLoadingRef.current) return;
      isLoadingRef.current = true;
      setIsLoadingBalances(true);

      try {
        const uniqueFromAddresses = Array.from(new Set(transactions.map(tx => tx.fromAddress)));
        const addressesToLoad = uniqueFromAddresses.filter(
          (address) => !loadedAddressesRef.current.has(address)
        );

        if (addressesToLoad.length === 0) {
          setIsLoadingBalances(false);
          isLoadingRef.current = false;
          // 即使没有新地址加载，也确保所有现有交易的加载状态是 false
          setTransactions(prevTxs => prevTxs.map(tx => ({
            ...tx,
            balanceLoading: false,
            // usdtBalanceLoading: false, // 如果有USDT
          })));
          return;
        }
        
        // 更新需要加载的地址的 loading 状态
        setTransactions(prevTxs => prevTxs.map(tx => 
            addressesToLoad.includes(tx.fromAddress) 
            ? { ...tx, balanceLoading: true } 
            : tx
        ));

        for (const address of addressesToLoad) {
          if (!address) continue; // 跳过空地址
          setCurrentLoadingAddress(address);
          const newBalance = await getNativeBalance(address, network);
          
          setAddressBalances(prev => ({ ...prev, [address]: newBalance }));
          
          // 更新 transactions 数组中对应地址的余额和加载状态
          setTransactions(prevTxs =>
            prevTxs.map(tx =>
              tx.fromAddress === address
                ? { ...tx, balance: newBalance, balanceLoading: false }
                : tx
            )
          );
          loadedAddressesRef.current.add(address);
          // 在请求之间添加延迟，避免过于频繁的API调用
          await new Promise(resolve => setTimeout(resolve, 300)); 
        }
      } catch (error) {
        console.error('加载余额时出错:', error);
        // 出错时，也应该将所有正在加载的地址的 loading 状态设为 false
        setTransactions(prevTxs => prevTxs.map(tx => 
            isLoadingRef.current && loadedAddressesRef.current.has(tx.fromAddress) // 仅针对已开始但可能失败的
            ? { ...tx, balanceLoading: false } 
            : tx
        ));
      } finally {
        setCurrentLoadingAddress('');
        setIsLoadingBalances(false);
        isLoadingRef.current = false;
        // 确保所有交易项的 balanceLoading 都被设置为 false
        setTransactions(prevTxs => prevTxs.map(tx => ({ ...tx, balanceLoading: false })));
      }
    };

    loadNewBalances();
  }, [pageFullyLoaded, transactions, network, getNativeBalance, setTransactions]);

  return {
    addressBalances, // 主要用于直接显示某个地址的余额（如果UI需要）
    isLoadingBalances,
    currentLoadingAddress,
    // getBalance: getNativeBalance, // 如果外部仍需单独调用
  };
};