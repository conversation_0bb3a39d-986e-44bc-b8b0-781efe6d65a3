import { useState, useEffect } from 'react';

interface UseGasSettingsProps {
  network: string;
  currency: string;
}

export const useGasSettings = ({ network, currency }: UseGasSettingsProps) => {
  const [gasLimit, setGasLimit] = useState<number>(100000);
  const [showGasLimitInput, setShowGasLimitInput] = useState<boolean>(false);

  const [tronFeeLimit, setTronFeeLimit] = useState<number>(100); // TRON 的手续费限制
  const [showTronFeeLimitInput, setShowTronFeeLimitInput] = useState<boolean>(false);

  useEffect(() => {
    if (network === 'ETH') {
      if (currency === 'ETH') {
        setGasLimit(21000);
      } else if (currency === 'USDT') {
        setGasLimit(65000); // 一个更常见的USDT转账Gas Limit参考值
      } else {
        setGasLimit(100000); // 其他ERC20代币的默认值
      }
    } else if (network === 'TRON') {
      // TRON 通常不需要用户设置 Gas Limit，但有 Fee Limit
      // Fee Limit 的默认值和逻辑可能需要根据实际业务调整
      setTronFeeLimit(100); // 示例默认值
    } else {
      setGasLimit(100000); // 其他网络或未知情况
      setTronFeeLimit(100);
    }
  }, [network, currency]);

  const handleGasLimitClick = () => setShowGasLimitInput(true);
  const handleGasLimitChange = (value: number | null) => {
    if (value !== null) setGasLimit(value);
  };
  const handleGasLimitBlur = () => setShowGasLimitInput(false);

  const getGasLimitTip = () => {
    if (network === 'ETH' && currency === 'ETH') {
      return 'ETH转账建议设置为21000';
    } else if (network === 'ETH' && currency === 'USDT') {
      return 'USDT代币转账建议设置为50000-100000 (通常65000足够)';
    }
    return '根据不同币种和网络设置合适的Gas Limit';
  };

  const handleTronFeeLimitClick = () => setShowTronFeeLimitInput(true);
  const handleTronFeeLimitChange = (value: number | null) => {
    if (value !== null) setTronFeeLimit(value);
  };
  const handleTronFeeLimitBlur = () => setShowTronFeeLimitInput(false);

  return {
    // ETH Gas Limit 相关
    gasLimit,
    showGasLimitInput,
    handleGasLimitClick,
    handleGasLimitChange,
    handleGasLimitBlur,
    getGasLimitTip,
    setGasLimit, // 暴露以便外部可以按需设置

    // TRON Fee Limit 相关
    tronFeeLimit,
    showTronFeeLimitInput,
    handleTronFeeLimitClick,
    handleTronFeeLimitChange,
    handleTronFeeLimitBlur,
    setTronFeeLimit, // 暴露以便外部可以按需设置
  };
};