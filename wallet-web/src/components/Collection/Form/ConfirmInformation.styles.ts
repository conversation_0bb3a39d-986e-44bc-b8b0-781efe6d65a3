import { CSSProperties } from 'react';

export const styles: Record<string, CSSProperties> = {
  container: {
    marginTop: 16,
  },
  infoItem: {
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
  },
  infoLabel: {
    color: 'rgba(0, 0, 0, 0.45)',
    fontSize: 14,
  },
  infoValue: {
    fontSize: 16,
    display: 'flex',
    alignItems: 'center',
    gap: 8,
  },
  addressValue: {
    wordBreak: 'break-all',
  },
  networkTag: {
    marginRight: 8,
  },
  totalFee: {
    marginTop: 8,
  },
  summaryCard: {
    marginTop: 16,
    marginBottom: 16,
  },
  summaryRow: {
    width: '100%',
  },
  summaryCol: {
    display: 'flex',
    alignItems: 'center',
  },
  summarySpace: {
    flexWrap: 'nowrap' as const,
  },
  feeTag: {
    minWidth: 80,
    textAlign: 'center' as const,
  },
  feeOption: {
    flex: 1,
    textAlign: 'center',
    padding: '4px 8px',
    height: 'auto',
  },
  feeOptionContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 4,
  },
  amountTabs: {
    marginTop: -16,
  },
  tabContent: {
    padding: '8px 0',
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
  },
  reserveHint: {
    fontSize: 12,
    marginTop: 4,
    display: 'flex',
    alignItems: 'center',
    gap: 4,
  },
  cardTitle: {
    fontWeight: 'bold',
    fontSize: '16px',
  },
  card: {
    height: '100%',
    boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
  },
  cardBody: {
    padding: '16px',
  },
  cardContent: {
    padding: '10px 0',
  },
  radioGroup: {
    width: '100%',
    marginBottom: '16px',
  },
  radioSpace: {
    width: '100%',
  },
  feeRadioGroup: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
  },
  radioButton: {
    flex: '1',
    textAlign: 'center',
    margin: '0 4px',
  },
  radioButtonContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '4px 0',
  },
  radioButtonTitle: {
    marginBottom: '4px',
    fontWeight: '500',
  },
  radioButtonTime: {
    fontSize: '12px',
    color: 'rgba(0,0,0,0.45)',
  },
  customFeeContainer: {
    marginTop: '60px',
    paddingTop: '8px',
  },
  inputLabel: {
    marginBottom: '8px',
    fontWeight: '500',
  },
  fullWidthInput: {
    width: '100%',
  },
  amountContainer: {
    marginTop: '31px',
  },
  hintText: {
    display: 'block',
    marginTop: '8px',
    fontSize: '12px',
  },
  tableCard: {
    marginTop: '16px',
    boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
  },
  tableCardBody: {
    padding: '0',
  },
  table: {
    width: '100%',
    borderCollapse: 'collapse',
  },
  tableHeader: {
    backgroundColor: '#f5f5f5',
  },
  tableHeaderCell: {
    padding: '12px 16px',
    textAlign: 'left',
    fontWeight: 'normal',
    color: 'rgba(0,0,0,0.65)',
  },
  tableHeaderCellRight: {
    padding: '12px 16px',
    textAlign: 'right',
    fontWeight: 'normal',
    color: 'rgba(0,0,0,0.65)',
  },
  tableHeaderCellCenter: {
    padding: '12px 16px',
    textAlign: 'center',
    fontWeight: 'normal',
    color: 'rgba(0,0,0,0.65)',
  },
  tableRow: {
    borderBottom: '1px solid #f0f0f0',
  },
  tableCell: {
    padding: '12px 16px',
    color: '#1890ff',
  },
  tableCellRight: {
    padding: '12px 16px',
    textAlign: 'right',
  },
  tableCellCenter: {
    padding: '12px 16px',
    textAlign: 'center',
  },
  deleteButton: {
    cursor: 'pointer',
    color: '#ff4d4f',
  },
  badgeStyle: {
    backgroundColor: '#1890ff',
  },
};

export default styles;
