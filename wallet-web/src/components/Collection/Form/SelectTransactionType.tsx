import React from 'react';
import { Typography, Form, Radio, Row, Col } from 'antd';
import styles from '../Collection.module.css';
import ethIcon from '../../../assets/images/eth.svg';
import tronIcon from '../../../assets/images/tron.svg';
import usdtIcon from '../../../assets/images/usdt.svg';

const { Title, Paragraph } = Typography;

// 网络选项
const networkOptions = [
    { label: 'Ethereum', value: 'ETH', icon: ethIcon },
    { label: 'TRON', value: 'TRON', icon: tronIcon }
];

// 币种选项
const currencyOptions = {
    ETH: [
        { label: 'ETH', value: 'ETH', icon: ethIcon },
        { label: 'USDT (ERC20)', value: 'USDT', icon: usdtIcon }
    ],
    TRON: [
        { label: 'TRX', value: 'TRX', icon: tronIcon },
        { label: 'USDT (TRC20)', value: 'USDT', icon: usdtIcon }
    ]
};

interface SelectTransactionTypeProps {
    selectedNetwork: string;
    selectedCurrency: string;
    handleNetworkChange: (value: string) => void;
    handleCurrencyChange: (value: string) => void;
    handleCollectionTypeChange: (e: any) => void;
}

const SelectTransactionType: React.FC<SelectTransactionTypeProps> = ({
    selectedNetwork,
    selectedCurrency,
    handleNetworkChange,
    handleCurrencyChange,
    handleCollectionTypeChange
}) => {
    // 获取当前网络的币种选项
    const getCurrentCurrencyOptions = () => {
        if (!selectedNetwork) return [];
        return currencyOptions[selectedNetwork as keyof typeof currencyOptions] || [];
    };

    // 获取当前选中网络的图标
    const getSelectedNetworkIcon = () => {
        if (!selectedNetwork) return null;
        const option = networkOptions.find(opt => opt.value === selectedNetwork);
        return option ? option.icon : null;
    };

    // 获取当前选中币种的图标
    const getSelectedCurrencyIcon = () => {
        if (!selectedCurrency) return null;
        const option = getCurrentCurrencyOptions().find(opt => opt.value === selectedCurrency);
        return option ? option.icon : null;
    };

    return (
        <>
            <Title level={4}>选择交易类型</Title>
            <Paragraph type="secondary">选择网络和币种，以及交易方式</Paragraph>

            <Row gutter={24}>
                <Col span={12}>
                    <Form.Item
                        label="选择网络"
                        name="network"
                        rules={[{ required: true, message: '请选择网络' }]}
                    >
                        <div className={styles.customSelectWrapper}>
                            <select
                                className={styles.customSelect}
                                onChange={(e) => handleNetworkChange(e.target.value)}
                                value={selectedNetwork}
                            >
                                <option value="">选择网络</option>
                                {networkOptions.map(option => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </select>
                            <div className={styles.selectIcons}>
                                {selectedNetwork && (
                                    <img
                                        src={getSelectedNetworkIcon() || ''}
                                        alt={selectedNetwork}
                                        className={styles.selectIcon}
                                    />
                                )}
                            </div>
                        </div>
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item
                        label="选择币种"
                        name="currency"
                        rules={[{ required: true, message: '请选择币种或输入合约地址' }]}
                    >
                        <div className={styles.customSelectWrapper}>
                            <select
                                className={styles.customSelect}
                                onChange={(e) => handleCurrencyChange(e.target.value)}
                                disabled={!selectedNetwork}
                                value={selectedCurrency}
                            >
                                <option value="">{selectedNetwork ? "选择币种或输入合约地址" : "请先选择网络"}</option>
                                {getCurrentCurrencyOptions().map(option => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </select>
                            <div className={styles.selectIcons}>
                                {selectedCurrency && (
                                    <img
                                        src={getSelectedCurrencyIcon() || ''}
                                        alt={selectedCurrency}
                                        className={styles.selectIcon}
                                    />
                                )}
                            </div>
                        </div>
                    </Form.Item>
                </Col>
            </Row>

            <Form.Item
                label="选择交易方式"
                name="collectionType"
                initialValue="oneToMany"
                rules={[{ required: true, message: '请选择交易方式' }]}
            >
                <Radio.Group onChange={handleCollectionTypeChange} className={styles.collectionTypeGroup}>
                    <Radio.Button value="oneToMany" className={styles.collectionTypeButton}>
                        <div className={styles.collectionTypeContent}>
                            <div className={styles.collectionTypeIcon}>
                                <svg viewBox="0 0 24 24" width="40" height="40">
                                    <path d="M12,2 L12,8 M12,8 L8,12 M12,8 L16,12" fill="none" stroke="currentColor" strokeWidth="2" />
                                    <circle cx="12" cy="4" r="2" fill="currentColor" />
                                    <circle cx="8" cy="14" r="2" fill="currentColor" />
                                    <circle cx="16" cy="14" r="2" fill="currentColor" />
                                    <circle cx="12" cy="20" r="2" fill="currentColor" />
                                    <path d="M8,16 L8,18 M16,16 L16,18 M12,16 L12,18" fill="none" stroke="currentColor" strokeWidth="2" />
                                </svg>
                            </div>
                            <div className={styles.collectionTypeLabel}>一对多转账</div>
                            <div className={styles.collectionTypeNote}>分发矿工费</div>
                        </div>
                    </Radio.Button>
                    <Radio.Button value="manyToOne" className={styles.collectionTypeButton}>
                        <div className={styles.collectionTypeContent}>
                            <div className={styles.collectionTypeIcon}>
                                <svg viewBox="0 0 24 24" width="40" height="40">
                                    <path d="M8,4 L8,10 M16,4 L16,10 M12,4 L12,10 M8,10 L12,14 M16,10 L12,14" fill="none" stroke="currentColor" strokeWidth="2" />
                                    <circle cx="8" cy="4" r="2" fill="currentColor" />
                                    <circle cx="16" cy="4" r="2" fill="currentColor" />
                                    <circle cx="12" cy="4" r="2" fill="currentColor" />
                                    <circle cx="12" cy="16" r="2" fill="currentColor" />
                                    <path d="M12,18 L12,20" fill="none" stroke="currentColor" strokeWidth="2" />
                                </svg>
                            </div>
                            <div className={styles.collectionTypeLabel}>多对一转账</div>
                            <div className={styles.collectionTypeNote}>归集资金</div>
                        </div>
                    </Radio.Button>
                </Radio.Group>
            </Form.Item>
        </>
    );
};

export default SelectTransactionType; 