import React from 'react';
import { Form, Input, Row, Col, Button, Spin } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import type { AddressType } from '../hooks/useAddressBookControls'; // 导入 AddressType

const { TextArea } = Input;

interface OneToManyFieldsProps {
  loadingMinerAddress: boolean;
  openAddressBook: (type: AddressType, field: string) => void;
  // form 实例可以通过 Form.Item 自动从父级 Form 组件获取，这里暂时不传递
}

const OneToManyFields: React.FC<OneToManyFieldsProps> = ({
  loadingMinerAddress,
  openAddressBook,
}) => {
  return (
    <Row gutter={24}>
      <Col span={12}>
        <div style={{ position: 'relative' }}>
          <Form.Item
            label="发送地址（矿工费地址）"
            name="fromAddress"
            rules={[{ required: true, message: '请输入发送地址' }]}
          >
            <Input
              placeholder={loadingMinerAddress ? '正在获取矿工费地址...' : '矿工费地址'}
              disabled={true}
              suffix={loadingMinerAddress ? <Spin size="small" /> : null}
            />
          </Form.Item>
          <Button
            icon={<CopyOutlined />}
            style={{
              position: 'absolute',
              right: 0,
              top: 0,
              color: '#d9d9d9',
              cursor: 'not-allowed',
            }}
            type="text"
            disabled={true}
          >
            地址簿
          </Button>
        </div>
      </Col>
      <Col span={12}>
        <div style={{ position: 'relative' }}>
          <Form.Item label="接收地址" name="toAddresses" rules={[{ required: true, message: '请输入接收地址' }]}>
            <TextArea
              placeholder="1. 输入或从地址簿导入地址"
              autoSize={{ minRows: 6, maxRows: 12 }}
              style={{ maxHeight: '300px', overflowY: 'auto' }}
            />
          </Form.Item>
          <Button
            icon={<CopyOutlined />}
            style={{ position: 'absolute', right: 0, top: 0 }}
            type="text"
            onClick={() => openAddressBook('to', 'toAddresses')}
          >
            地址簿
          </Button>
        </div>
      </Col>
    </Row>
  );
};

export default OneToManyFields;