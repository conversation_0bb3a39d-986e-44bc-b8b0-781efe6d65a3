import React from 'react';
import { Form, Input, Button, Alert } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import type { AddressType } from '../hooks/useAddressBookControls';

const { TextArea } = Input;

interface ManyToManyFieldsProps {
  openAddressBook: (type: AddressType, field: string) => void;
}

const ManyToManyFields: React.FC<ManyToManyFieldsProps> = ({ openAddressBook }) => {
  return (
    <>
      <Alert
        message="多对多转账说明"
        description="自由组合交易，每笔交易都是独立的交易。每行输入一笔交易，格式：发送地址,接收地址,金额(可选)"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
      <div style={{ position: 'relative' }}>
        <Form.Item label="交易列表" name="transactions" rules={[{ required: true, message: '请输入交易列表' }]}>
          <TextArea
            placeholder="每行输入一笔交易，格式：发送地址,接收地址,金额(可选)"
            autoSize={{ minRows: 6, maxRows: 12 }}
            style={{ maxHeight: '300px', overflowY: 'auto' }}
          />
        </Form.Item>
        <Button
          icon={<CopyOutlined />}
          style={{ position: 'absolute', right: 0, top: 0 }}
          type="text"
          onClick={() => openAddressBook('from', 'transactions')} // 'from' or 'to' doesn't strictly matter here as it's a generic list
        >
          地址簿
        </Button>
      </div>
    </>
  );
};

export default ManyToManyFields;