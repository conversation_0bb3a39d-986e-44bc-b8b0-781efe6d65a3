import React from 'react';
import { Form, Input, Row, Col, Button, Spin } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import type { AddressType } from '../hooks/useAddressBookControls';

const { TextArea } = Input;

interface ManyToOneFieldsProps {
  loadingCollectionAddress: boolean;
  openAddressBook: (type: AddressType, field: string) => void;
}

const ManyToOneFields: React.FC<ManyToOneFieldsProps> = ({
  loadingCollectionAddress,
  openAddressBook,
}) => {
  return (
    <Row gutter={24}>
      <Col span={12}>
        <div style={{ position: 'relative' }}>
          <Form.Item label="发送地址" name="fromAddresses" rules={[{ required: true, message: '请输入发送地址' }]}>
            <TextArea
              placeholder="输入或从地址簿导入地址"
              autoSize={{ minRows: 6, maxRows: 12 }}
              style={{ maxHeight: '300px', overflowY: 'auto' }}
            />
          </Form.Item>
          <Button
            icon={<CopyOutlined />}
            style={{ position: 'absolute', right: 0, top: 0 }}
            type="text"
            onClick={() => openAddressBook('from', 'fromAddresses')}
          >
            地址簿
          </Button>
        </div>
      </Col>
      <Col span={12}>
        <div style={{ position: 'relative' }}>
          <Form.Item
            label="接收地址（归集地址）"
            name="toAddress"
            rules={[{ required: true, message: '请输入接收地址' }]}
          >
            <Input
              placeholder={loadingCollectionAddress ? '正在获取归集地址...' : '归集地址'}
              disabled={true}
              suffix={loadingCollectionAddress ? <Spin size="small" /> : null}
            />
          </Form.Item>
          <Button
            icon={<CopyOutlined />}
            style={{
              position: 'absolute',
              right: 0,
              top: 0,
              color: '#d9d9d9',
              cursor: 'not-allowed',
            }}
            type="text"
            disabled={true}
          >
            地址簿
          </Button>
        </div>
      </Col>
    </Row>
  );
};

export default ManyToOneFields;