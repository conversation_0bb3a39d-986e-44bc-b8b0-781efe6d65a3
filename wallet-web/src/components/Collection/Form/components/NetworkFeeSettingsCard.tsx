import React from 'react';
import { Card, Radio, InputNumber } from 'antd';
import { styles as confirmStyles } from '../ConfirmInformation.styles';
import { NetworkFeeData } from '../hooks/useNetworkFees';

interface NetworkFeeSettingsCardProps {
  feeType: string;
  handleFeeTypeChange: (e: any) => void; // antd RadioChangeEvent
  feeData: NetworkFeeData;
  customFee: number;
  handleCustomFeeChange: (value: number | null) => void;
}

export const NetworkFeeSettingsCard: React.FC<NetworkFeeSettingsCardProps> = ({
  feeType,
  handleFeeTypeChange,
  feeData,
  customFee,
  handleCustomFeeChange,
}) => {
  return (
    <Card title={<div style={confirmStyles.cardTitle}>网络费用</div>} style={confirmStyles.card} bodyStyle={confirmStyles.cardBody}>
      <div style={confirmStyles.cardContent}>
        <Radio.Group value={feeType} onChange={handleFeeTypeChange} style={confirmStyles.feeRadioGroup}>
          <Radio.Button value="slow" style={confirmStyles.radioButton}>
            <div style={confirmStyles.radioButtonContent}>
              <div style={confirmStyles.radioButtonTitle}>慢</div>
              <div>{feeData.slow.gwei.toFixed(4)} Gwei</div>
              <div style={confirmStyles.radioButtonTime}>{feeData.slow.time}</div>
            </div>
          </Radio.Button>
          <Radio.Button value="average" style={confirmStyles.radioButton}>
            <div style={confirmStyles.radioButtonContent}>
              <div style={confirmStyles.radioButtonTitle}>平均</div>
              <div>{feeData.average.gwei.toFixed(4)} Gwei</div>
              <div style={confirmStyles.radioButtonTime}>{feeData.average.time}</div>
            </div>
          </Radio.Button>
          <Radio.Button value="fast" style={confirmStyles.radioButton}>
            <div style={confirmStyles.radioButtonContent}>
              <div style={confirmStyles.radioButtonTitle}>快</div>
              <div>{feeData.fast.gwei.toFixed(4)} Gwei</div>
              <div style={confirmStyles.radioButtonTime}>{feeData.fast.time}</div>
            </div>
          </Radio.Button>
          <Radio.Button value="custom" style={confirmStyles.radioButton}>
            <div style={confirmStyles.radioButtonContent}>
              <div style={confirmStyles.radioButtonTitle}>自定义</div>
              {/* 对于自定义选项，显示 customFee state 的值 */}
              <div>{customFee.toFixed(4)} Gwei</div>
            </div>
          </Radio.Button>
        </Radio.Group>
        {feeType === 'custom' && (
          <div style={confirmStyles.customFeeContainer}>
            <div style={confirmStyles.inputLabel}>自定义费用</div>
            <InputNumber
              value={customFee}
              onChange={handleCustomFeeChange}
              addonAfter="Gwei"
              style={confirmStyles.fullWidthInput}
              size="large"
              min={0.1} // 设置一个合理的最小值
              step={0.1}
            />
          </div>
        )}
        {/* 移除了原先的 FeeLimit (ETH) 输入框，因为它现在通过 GasLimit 和 GasPrice (Gwei) 间接控制，
            并且 TRON 的 FeeLimit 已在 TransactionSummaryCard 中处理。
            如果需要独立的ETH总费用上限控制，可以重新添加。
        */}
      </div>
    </Card>
  );
};