import React from 'react';
import { Card, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { styles as confirmStyles } from '../ConfirmInformation.styles';
import { TxItem } from '../hooks/useTransactionData';

// 导入加密货币图标 - 这些路径需要相对于此文件的位置进行调整
// 或者，更好的做法是将图标作为 props 传递进来，或者使用一个集中的图标管理方案
import ethIcon from '../../../../assets/images/eth.svg';
import usdtIcon from '../../../../assets/images/usdt.svg';
import trxIcon from '../../../../assets/images/tron.svg';

const { Text } = Typography;

interface TransactionsTableProps {
  transactions: TxItem[];
  amountType: string; // 'specific' or 'all'
  currency: string;
  collectionType: string; // Added collectionType here
  isLoadingBalances: boolean; // Overall loading state for balances
  currentLoadingAddress?: string; // Specific address being loaded
  handleDeleteTransaction: (id: string) => void;
  formatAddress: (address: string) => string;
}

export const TransactionsTable: React.FC<TransactionsTableProps> = ({
  transactions,
  amountType,
  currency,
  collectionType, // Destructure collectionType
  isLoadingBalances,
  currentLoadingAddress,
  handleDeleteTransaction,
  formatAddress,
}) => {
  const getCurrencyIcon = (currentCurrency: string) => {
    if (currentCurrency === 'USDT') return usdtIcon;
    if (currentCurrency === 'ETH') return ethIcon;
    if (currentCurrency === 'TRX') return trxIcon;
    return undefined;
  };

  return (
    <Card style={confirmStyles.tableCard} bodyStyle={confirmStyles.tableCardBody}>
      {transactions.length > 0 ? (
        <table style={confirmStyles.table}>
          <thead>
            <tr style={confirmStyles.tableHeader}>
              <th style={confirmStyles.tableHeaderCell}>发送地址</th>
              <th style={confirmStyles.tableHeaderCell}>接收地址</th>
              <th style={confirmStyles.tableCellCenter}>发送数量</th>
              <th style={confirmStyles.tableCellCenter}>币种</th>
              <th style={confirmStyles.tableCellCenter}>当前余额</th>
              <th style={confirmStyles.tableHeaderCellCenter}>操作</th>
            </tr>
          </thead>
          <tbody>
            {transactions.map((tx) => (
              <tr key={tx.id} style={confirmStyles.tableRow}>
                <td style={confirmStyles.tableCell}>{formatAddress(tx.fromAddress)}</td>
                <td style={confirmStyles.tableCell}>{formatAddress(tx.toAddress)}</td>
                <td style={confirmStyles.tableCellCenter}>
                  {(amountType === 'all' && collectionType === 'manyToOne') ? '全部' : tx.amount} {/* 修正：仅 manyToOne 才有全部选项 */}
                </td>
                <td style={confirmStyles.tableCellCenter}>{currency}</td>
                <td style={confirmStyles.tableCellCenter}>
                  {tx.balanceLoading || (isLoadingBalances && currentLoadingAddress === tx.fromAddress) ? (
                    <span>
                      <LoadingOutlined style={{ marginRight: 5 }} spin />
                      加载中
                    </span>
                  ) : (
                    tx.balance || '--'
                  )}
                  &nbsp;
                  {getCurrencyIcon(currency) && (
                    <img src={getCurrencyIcon(currency)} alt={currency} width={20} style={{ marginTop: 10, verticalAlign: 'middle' }} />
                  )}
                </td>
                <td style={confirmStyles.tableCellCenter}>
                  <span style={confirmStyles.deleteButton} onClick={() => handleDeleteTransaction(tx.id)}>
                    <svg
                      viewBox="64 64 896 896"
                      focusable="false"
                      data-icon="delete"
                      width="1em"
                      height="1em"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"></path>
                    </svg>
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <div style={confirmStyles.emptyTable}>
          <Text type="secondary">暂无交易数据</Text>
        </div>
      )}
    </Card>
  );
};
// Removed TransactionsTableWithCollectionType as the main component now handles collectionType