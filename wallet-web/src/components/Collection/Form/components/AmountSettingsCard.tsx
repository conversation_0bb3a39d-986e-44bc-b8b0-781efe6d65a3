import React from 'react';
import { Card, Radio, Space, Input, Typography } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { styles as confirmStyles } from '../ConfirmInformation.styles';

const { Text } = Typography;

interface AmountSettingsCardProps {
  amountType: string;
  handleAmountTypeChange: (key: string) => void;
  specificAmount: string;
  handleSpecificAmountChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  currency: string;
  collectionType: string; // 用于判断是否显示“全部余额”选项
}

export const AmountSettingsCard: React.FC<AmountSettingsCardProps> = ({
  amountType,
  handleAmountTypeChange,
  specificAmount,
  handleSpecificAmountChange,
  currency,
  collectionType,
}) => {
  return (
    <Card title={<div style={confirmStyles.cardTitle}>转账数量</div>} style={confirmStyles.card} bodyStyle={confirmStyles.cardBody}>
      <div style={confirmStyles.cardContent}>
        <Radio.Group
          value={amountType}
          onChange={(e) => handleAmountTypeChange(e.target.value)}
          style={confirmStyles.radioGroup}
        >
          <Space direction="vertical" style={confirmStyles.radioSpace}>
            <Radio value="specific">指定数量</Radio>
            {collectionType === 'manyToOne' && <Radio value="all">全部余额</Radio>}
          </Space>
        </Radio.Group>

        <div style={confirmStyles.amountContainer}>
          <div style={confirmStyles.inputLabel}>发送数量 {amountType === 'specific' ? '(每个地址)' : ''}</div>
          <Input
            value={amountType === 'specific' ? specificAmount : '全部余额'}
            onChange={amountType === 'specific' ? handleSpecificAmountChange : undefined}
            addonAfter={currency || 'USDC'}
            style={confirmStyles.fullWidthInput}
            size="large"
            disabled={amountType === 'all'}
          />
          {amountType === 'all' && (
            <Text type="secondary" style={confirmStyles.hintText}>
              <InfoCircleOutlined /> 适用归集资金
            </Text>
          )}
          {amountType === 'specific' && (
            <Text type="secondary" style={confirmStyles.hintText}>
              <InfoCircleOutlined /> 适用分发矿工费
            </Text>
          )}
        </div>
      </div>
    </Card>
  );
};