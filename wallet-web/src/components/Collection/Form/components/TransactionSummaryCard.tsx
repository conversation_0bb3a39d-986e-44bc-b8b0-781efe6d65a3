import React from 'react';
import { Card, Row, Col, Space, Tag, Badge, Typography, InputNumber, Tooltip } from 'antd';
import { QuestionCircleOutlined, SyncOutlined } from '@ant-design/icons';
import { NetworkFeeData } from '../hooks/useNetworkFees';
import { styles as confirmStyles } from '../ConfirmInformation.styles'; // 导入主组件的样式

const { Text } = Typography;

interface TransactionSummaryCardProps {
  network: string;
  currency: string;
  addressCount: number;
  totalAmount: number;
  // ETH Gas Limit props
  showGasLimitInput: boolean;
  gasLimit: number;
  handleGasLimitChange: (value: number | null) => void;
  handleGasLimitBlur: () => void;
  handleGasLimitClick: () => void;
  getGasLimitTip: () => string;
  // TRON Fee Limit props
  showTronFeeLimitInput: boolean;
  tronFeeLimit: number;
  handleTronFeeLimitChange: (value: number | null) => void;
  handleTronFeeLimitBlur: () => void;
  handleTronFeeLimitClick: () => void;
  // Network Fee props
  feeType: string;
  feeData: NetworkFeeData;
  customFee: number; // for custom gwei display
  loadingFeeData: boolean;
  handleRefreshGasFee: () => void;
  calculateTotalFee: () => number; // 总费用计算函数
}

export const TransactionSummaryCard: React.FC<TransactionSummaryCardProps> = ({
  network,
  currency,
  addressCount,
  totalAmount,
  showGasLimitInput,
  gasLimit,
  handleGasLimitChange,
  handleGasLimitBlur,
  handleGasLimitClick,
  getGasLimitTip,
  showTronFeeLimitInput,
  tronFeeLimit,
  handleTronFeeLimitChange,
  handleTronFeeLimitBlur,
  handleTronFeeLimitClick,
  feeType,
  feeData,
  customFee,
  loadingFeeData,
  handleRefreshGasFee,
  calculateTotalFee,
}) => {
  return (
    <Card className="summary-line-card" bordered={false} style={confirmStyles.summaryCard}>
      <Row align="middle" justify="space-between" gutter={8}>
        <Col>
          <Space size="large">
            <Space>
              <Text type="secondary">网络:</Text>
              <Tag color="blue">{network}</Tag>
            </Space>
            <Space>
              <Text type="secondary">币种:</Text>
              <Text strong>{currency}</Text>
            </Space>
            <Space>
              <Text type="secondary">地址数:</Text>
              <Badge count={addressCount} showZero style={confirmStyles.badgeStyle} />
            </Space>
            <Space>
              <Text type="secondary">总金额:</Text>
              <Text strong>
                {totalAmount.toFixed(6)} {currency}
              </Text>
            </Space>
            {network === 'TRON' && (
              <Space>
                <Text type="secondary">单笔手续费限制(点击修改):</Text>
                {showTronFeeLimitInput ? (
                  <InputNumber
                    value={tronFeeLimit}
                    onChange={handleTronFeeLimitChange}
                    onBlur={handleTronFeeLimitBlur}
                    autoFocus
                    size="small"
                    min={1}
                    addonAfter="TRX"
                  />
                ) : (
                  <Text strong style={{ cursor: 'pointer' }} onClick={handleTronFeeLimitClick}>
                    {tronFeeLimit} TRX
                  </Text>
                )}
              </Space>
            )}
          </Space>
        </Col>
        <Col>
          {network === 'ETH' && (
            <Space>
              <Text type="secondary">Gas Limit :</Text>
              {showGasLimitInput ? (
                <InputNumber
                  value={gasLimit}
                  onChange={handleGasLimitChange}
                  onBlur={handleGasLimitBlur}
                  autoFocus
                  min={21000}
                  step={1000}
                  style={{ width: 120 }}
                />
              ) : (
                <Text strong style={{ cursor: 'pointer' }} onClick={handleGasLimitClick}>
                  {gasLimit}
                  <Tooltip title={getGasLimitTip()}>
                    <QuestionCircleOutlined style={{ marginLeft: 5, fontSize: 12 }} />
                  </Tooltip>
                </Text>
              )}
              <Text type="secondary">网络费用:</Text>
              <Tag
                color={
                  feeType === 'slow'
                    ? 'green'
                    : feeType === 'average'
                      ? 'blue'
                      : feeType === 'fast'
                        ? 'orange'
                        : 'default'
                }
              >
                {(feeData[feeType as keyof NetworkFeeData] || feeData.custom)?.gwei.toFixed(4) || customFee.toFixed(4)} Gwei
              </Tag>
              <Tooltip title="刷新Gas费用">
                <SyncOutlined
                  onClick={handleRefreshGasFee}
                  spin={loadingFeeData}
                  style={{
                    cursor: 'pointer',
                    marginLeft: 5,
                    fontSize: 14,
                    color: '#1890ff',
                  }}
                />
              </Tooltip>
              <Text type="secondary">总费用:</Text>
              <Text strong>{calculateTotalFee().toFixed(8)} ETH</Text>
            </Space>
          )}
        </Col>
      </Row>
    </Card>
  );
};