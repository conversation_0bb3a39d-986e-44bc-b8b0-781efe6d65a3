import React, { useEffect } from 'react';
import { Typography, Form } from 'antd';
import AddressBookModal from '../../common/AddressBookModal';
import { useAddressFetching } from './hooks/useAddressFetching';
import { useAddressBookControls } from './hooks/useAddressBookControls';
import OneToManyFields from './fields/OneToManyFields';
import ManyToOneFields from './fields/ManyToOneFields';
import ManyToManyFields from './fields/ManyToManyFields';

const { Title, Paragraph } = Typography;

// CollectionType 已导出，供 Hooks 和其他组件使用
export type CollectionType = 'oneToMany' | 'manyToOne' | 'manyToMany';

interface AddTransferAddressesProps {
  collectionType: CollectionType;
  form?: any; // 接收父组件传递的form
  selectedNetwork?: string;
}

const AddTransferAddresses: React.FC<AddTransferAddressesProps> = ({
  collectionType,
  form: parentForm,
  selectedNetwork,
}) => {
  const [localForm] = Form.useForm();
  const form = parentForm || localForm;

  const {
    loadingMinerAddress,
    loadingCollectionAddress,
    fetchMinerFeeAddress,
    fetchCollectionAddress,
  } = useAddressFetching({ collectionType, selectedNetwork, form });

  const {
    addressBookVisible,
    addressType,
    openAddressBook,
    handleAddressSelect,
    closeAddressBook,
  } = useAddressBookControls({ form, collectionType });

  useEffect(() => {
    // 清空可能存在的旧地址和交易数据
    form.setFieldsValue({
      fromAddress: '',
      toAddress: '',
      toAddresses: '',
      fromAddresses: '',
      transactions: '',
    });

    if (selectedNetwork) {
      if (collectionType === 'oneToMany') {
        fetchMinerFeeAddress();
      } else if (collectionType === 'manyToOne') {
        fetchCollectionAddress();
      }
    }
  }, [form, collectionType, selectedNetwork, fetchMinerFeeAddress, fetchCollectionAddress]);

  return (
    <>
      <Title level={4}>添加转账地址</Title>
      <Paragraph type="secondary">
        {collectionType === 'oneToMany' && '一个发送地址转账到多个接收地址'}
        {collectionType === 'manyToOne' && '多个发送地址转账到一个接收地址'}
        {collectionType === 'manyToMany' && '多个发送地址转账到多个接收地址，自由组合交易'}
      </Paragraph>

      {collectionType === 'oneToMany' && (
        <OneToManyFields
          loadingMinerAddress={loadingMinerAddress}
          openAddressBook={openAddressBook}
        />
      )}

      {collectionType === 'manyToOne' && (
        <ManyToOneFields
          loadingCollectionAddress={loadingCollectionAddress}
          openAddressBook={openAddressBook}
        />
      )}

      {collectionType === 'manyToMany' && (
        <ManyToManyFields openAddressBook={openAddressBook} />
      )}

      <AddressBookModal
        visible={addressBookVisible}
        onCancel={closeAddressBook}
        onSelect={handleAddressSelect}
        addressType={addressType}
        selectedNetwork={selectedNetwork}
      />
    </>
  );
};

export default AddTransferAddresses;
