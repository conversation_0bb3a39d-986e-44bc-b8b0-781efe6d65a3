import React from 'react';
import { Typography, Button } from 'antd';
import { CheckOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

interface CompletionStepProps {
  onReset: () => void;
}

const CompletionStep: React.FC<CompletionStepProps> = ({ onReset }) => {
  return (
    <div style={{ textAlign: 'center', padding: '30px 0' }}>
      <CheckOutlined style={{ fontSize: 72, color: '#52c41a', marginBottom: 24 }} />
      <Title level={3}>归集任务已提交</Title>
      <Paragraph>您的归集任务已成功提交，系统将自动执行归集操作。您可以在归集记录中查看任务进度。</Paragraph>
      <Button type="primary" onClick={onReset} style={{ marginTop: 16 }}>
        创建新归集任务
      </Button>
    </div>
  );
};

export default CompletionStep;
