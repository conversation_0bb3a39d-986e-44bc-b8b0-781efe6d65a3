import React, { useState, useEffect, useRef } from 'react';
import { Form, Button, Steps, Card, message } from 'antd';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import styles from '../Collection.module.css';

import SelectTransactionType from './SelectTransactionType';
import AddTransferAddresses from './AddTransferAddresses';
import ConfirmInformation from './ConfirmInformation';
import CompletionStep from './CompletionStep';

const { Step } = Steps;

// 归集操作类型
type CollectionType = 'oneToMany' | 'manyToOne' | 'manyToMany';

const CollectionForm: React.FC = () => {
  const [form] = Form.useForm();
  const [collectionType, setCollectionType] = useState<CollectionType>('oneToMany');
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [selectedCurrency, setSelectedCurrency] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const confirmInfoRef = useRef<any>(null); // 添加ConfirmInformation组件的引用

  // 添加特殊地址状态
  const [minerFeeAddress, setMinerFeeAddress] = useState<string>('');
  const [collectionAddress, setCollectionAddress] = useState<string>('');

  // 处理网络变更
  const handleNetworkChange = (value: string) => {
    setSelectedNetwork(value);
    form.setFieldsValue({ currency: undefined });
    setSelectedCurrency('');
  };

  // 处理归集类型变更
  const handleCollectionTypeChange = (e: any) => {
    setCollectionType(e.target.value);
  };

  // 监听表单值变化，保存特殊地址
  useEffect(() => {
    const subscription = form.getFieldsValue(['fromAddress', 'toAddress']);

    if (collectionType === 'oneToMany' && subscription.fromAddress) {
      setMinerFeeAddress(subscription.fromAddress);
    }

    if (collectionType === 'manyToOne' && subscription.toAddress) {
      setCollectionAddress(subscription.toAddress);
    }

    return () => {
      // 清理订阅
    };
  }, [form, collectionType]);

  // 提交表单
  const handleSubmit = async (values: any) => {
    setLoading(true);

    // 确保特殊地址被正确包含
    if (collectionType === 'oneToMany' && minerFeeAddress) {
      values.fromAddress = minerFeeAddress;
    }

    if (collectionType === 'manyToOne' && collectionAddress) {
      values.toAddress = collectionAddress;
    }

    console.log('提交归集操作:', values);

    try {
      // 调用ConfirmInformation组件的handleSubmitCollectTask方法
      if (confirmInfoRef.current && confirmInfoRef.current.handleSubmitCollectTask) {
        await confirmInfoRef.current.handleSubmitCollectTask();
      } else {
        message.error('提交失败，无法获取提交函数');
      }
    } catch (error) {
      console.error('提交失败', error);
      message.error('提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 下一步
  const next = async () => {
    try {
      if (currentStep === 0) {
        // 验证第一步的表单
        await form.validateFields(['network', 'currency', 'collectionType']);
      } else if (currentStep === 1) {
        // 验证第二步的表单
        if (collectionType === 'oneToMany') {
          // 一对多模式下，fromAddress是自动填充的，只需验证toAddresses
          console.log('fromAddress', form.getFieldValue('fromAddress'));
          if (!form.getFieldValue('fromAddress')) {
            message.error('矿工费地址获取失败，请重试');
            return;
          }
          await form.validateFields(['toAddresses']);
        } else if (collectionType === 'manyToOne') {
          // 多对一模式下，toAddress是自动填充的，只需验证fromAddresses
          if (!form.getFieldValue('toAddress')) {
            message.error('归集地址获取失败，请重试');
            return;
          }
          await form.validateFields(['fromAddresses']);
        } else {
          await form.validateFields(['transactions']);
        }
      }
      setCurrentStep(currentStep + 1);
    } catch (error) {
      console.log('表单验证失败:', error);
    }
  };

  // 上一步
  const prev = () => {
    setCurrentStep(currentStep - 1);
  };

  // 重置表单
  const resetForm = () => {
    form.resetFields();
    setCurrentStep(0);
    setSelectedNetwork('');
    setSelectedCurrency('');
    setCollectionType('oneToMany');
    setMinerFeeAddress('');
    setCollectionAddress('');
  };

  // 步骤内容
  const steps = [
    {
      title: '选择交易类型',
      content: (
        <SelectTransactionType
          selectedNetwork={selectedNetwork}
          selectedCurrency={selectedCurrency}
          handleNetworkChange={handleNetworkChange}
          handleCurrencyChange={setSelectedCurrency}
          handleCollectionTypeChange={handleCollectionTypeChange}
        />
      ),
    },
    {
      title: '添加转账地址',
      content: <AddTransferAddresses collectionType={collectionType} form={form} selectedNetwork={selectedNetwork} />,
    },
    {
      title: '输入转账数量',
      content: <ConfirmInformation ref={confirmInfoRef} parentForm={form} onSubmit={() => setCurrentStep(3)} />,
    },
    {
      title: '完成',
      content: <CompletionStep onReset={resetForm} />,
    },
  ];

  return (
    <Card className={styles.formCard}>
      <div className={styles.contentWrapper}>
        <Steps current={currentStep} style={{ marginBottom: 20 }}>
          {steps.map((item) => (
            <Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          requiredMark="optional"
          className={styles.stepFormCard}
          onClick={(e) => e.stopPropagation()}
          style={{ display: 'flex', flexDirection: 'column', minHeight: '500px' }}
        >
          <div className={styles.formContentContainer}>
            <div className={styles.stepsContent}>{steps[currentStep].content}</div>

            <div className={styles.stepsAction}>
              {currentStep > 0 && currentStep < 3 && (
                <Button icon={<ArrowLeftOutlined />} onClick={prev}>
                  上一步
                </Button>
              )}

              {currentStep < 2 && (
                <Button type="primary" onClick={next} style={{ marginLeft: 'auto' }}>
                  下一步 <ArrowRightOutlined />
                </Button>
              )}

              {currentStep === 2 && (
                <Button type="primary" onClick={() => form.submit()} loading={loading} style={{ marginLeft: 'auto' }}>
                  提交归集任务
                </Button>
              )}
            </div>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default CollectionForm;
