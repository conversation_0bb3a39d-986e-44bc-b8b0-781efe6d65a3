import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  // Input, // Kept for potential future use of search/actions - removing for now
  // Button, // Kept for potential future use of search/actions - removing for now
  // Typography, // Used by getWalletTableColumns indirectly - removing for now as it's an indirect dep
  message,
} from 'antd';
import { useAppDispatch } from '../../hooks/useAppDispatch';
import { useAppSelector } from '../../hooks/useAppSelector';
import { fetchWalletAddresses } from '../../store/slices/walletSlice';
import styles from './WalletList.module.css';
import { useMemoizedFn } from 'ahooks';
import { copyToClipboard } from '../../utils/clipboardUtils';

import WalletStatsCard from './components/WalletStatsCard';
import { getWalletTableColumns, WalletItem } from './walletTableColumns';
import WalletTagManagerModal from './components/WalletTagManagerModal';

// const { Text } = Typography; // Text is used within getWalletTableColumns, not directly here.

const WalletList: React.FC = () => {
  const dispatch = useAppDispatch();
  const { walletAddresses, totalWalletAddresses, loading } = useAppSelector((state) => state.wallet);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText] = useState(''); // setSearchText removed, kept for potential future filter
  const [filters] = useState<{ // setFilters removed, kept for potential future filter
    type?: string;
    status?: string;
    tags?: string[];
  }>({});

  // 标签管理相关状态
  const [tagModalVisible, setTagModalVisible] = useState(false);
  const [selectedWalletForTagging, setSelectedWalletForTagging] = useState<WalletItem | null>(null);

  const loadWalletAddresses = useMemoizedFn(() => {
    dispatch(
      fetchWalletAddresses({
        page: currentPage,
        pageSize,
        filters: {
          ...filters,
          search: searchText || undefined,
        },
      }),
    );
  });

  // Commented out due to unused variable warning
  // const handleSearch = () => {
  //   setCurrentPage(1); // 重置到第一页
  //   loadWalletAddresses();
  // };

  // Commented out due to unused variable warning
  // const handleReset = () => {
  //   setSearchText('');
  //   setFilters({});
  //   setCurrentPage(1);
  //   dispatch(
  //     fetchWalletAddresses({
  //       page: 1,
  //       pageSize,
  //       filters: {},
  //     }),
  //   );
  // };

  // Commented out due to unused variable warning
  // const handleExport = () => {
  //   // 导出功能实现
  //   const data = walletAddresses.map((addr) => ({
  //     名称: addr.name,
  //     类型: addr.type === 'main' ? '主钱包' : addr.type === 'savings' ? '储蓄钱包' : '投资钱包',
  //     状态: addr.status === 'active' ? '活跃' : '非活跃',
  //     ETH余额: (addr as WalletItem).ethBalance || '0',
  //     'ERC20 USDT余额': (addr as WalletItem).erc20UsdtBalance || '0',
  //     TRX余额: (addr as WalletItem).trxBalance || '0',
  //     'TRC20 USDT余额': (addr as WalletItem).trc20UsdtBalance || '0',
  //     创建时间: new Date(addr.createdAt).toLocaleString(),
  //     最后使用: new Date(addr.lastUsed).toLocaleString(),
  //   }));

  //   // 创建CSV内容
  //   const headers = Object.keys(data[0]).join(',');
  //   const csvContent = data.map((row) => Object.values(row).join(',')).join('\n');
  //   const csv = `${headers}\n${csvContent}`;

  //   // 创建下载链接
  //   const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  //   const url = URL.createObjectURL(blob);
  //   const link = document.createElement('a');
  //   link.setAttribute('href', url);
  //   link.setAttribute('download', `钱包列表_${new Date().toISOString()}.csv`);
  //   link.style.visibility = 'hidden';
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
  // };

  const handleCopyToClipboard = (text: string) => {
    copyToClipboard(text);
  };

  // 打开标签管理模态框
  const openTagModal = (wallet: WalletItem) => {
    setSelectedWalletForTagging(wallet);
    setTagModalVisible(true);
  };

  // 保存标签 - This function is now a callback for WalletTagManagerModal
  const handleSaveTagsInList = (walletId: string, tags: string[]) => {
    // 在实际应用中，这里会调用API来保存标签
    console.log(`保存钱包 ${walletId} 的标签:`, tags);
    // 模拟API调用成功
    message.success('标签已成功更新 (模拟)');
    // 关闭模态框并刷新列表
    setTagModalVisible(false);
    setSelectedWalletForTagging(null); // 清除选中的钱包
    loadWalletAddresses();
  };

  // handleAddTag is removed as it's managed within WalletTagManagerModal

  // 计算统计数据
  const statistics = {
    totalEthBalance: (walletAddresses || [])
      .reduce((sum, addr) => sum + parseFloat((addr as WalletItem).ethBalance || '0'), 0)
      .toFixed(4),
    totalErc20UsdtBalance: (walletAddresses || [])
      .reduce((sum, addr) => sum + parseFloat((addr as WalletItem).erc20UsdtBalance || '0'), 0)
      .toFixed(2),
    totalTrxBalance: (walletAddresses || [])
      .reduce((sum, addr) => sum + parseFloat((addr as WalletItem).trxBalance || '0'), 0)
      .toFixed(4),
    totalTrc20UsdtBalance: (walletAddresses || [])
      .reduce((sum, addr) => sum + parseFloat((addr as WalletItem).trc20UsdtBalance || '0'), 0)
      .toFixed(2),
    activeWallets: (walletAddresses || []).filter((addr) => addr.status === 'active').length,
    mainWallets: (walletAddresses || []).filter((addr) => addr.type === 'main').length,
    savingsWallets: (walletAddresses || []).filter((addr) => addr.type === 'savings').length,
    investmentWallets: (walletAddresses || []).filter((addr) => addr.type === 'investment').length,
  };

  useEffect(() => {
    loadWalletAddresses();
  }, [currentPage, pageSize, filters, loadWalletAddresses]);

  // Columns definition is now handled by getWalletTableColumns
  const tableColumns = getWalletTableColumns({
    onCopyToClipboard: handleCopyToClipboard,
    onOpenTagModal: openTagModal,
    // Pass other necessary callbacks here if any, e.g., for activate/deactivate
  });

  return (
    <div className={styles.walletList}>
      {loading && !walletAddresses.length ? ( // Show full page loading only if no data yet
        <div className={styles.loadingContainer}>
          <h3>加载钱包数据中...</h3>
        </div>
      ) : (
        <>
          <WalletStatsCard
            loading={loading && !walletAddresses.length} // Pass loading state for initial load
            statistics={statistics}
            totalWalletAddresses={totalWalletAddresses}
          />

          <Card className={styles.tableCard}>
            <div className={styles.tableHeader}>
              <div className={styles.searchFilters}>
                {/* 搜索和筛选UI的注释代码可以保留或删除 */}
                {/* <Input placeholder="搜索钱包名称" ... /> */}
                {/* <Select placeholder="钱包类型" ... /> */}
                {/* <Button type="primary" ...>筛选</Button> */}
                {/* <Button ...>重置</Button> */}
              </div>
              <div className={styles.actions}>
                {/* 操作按钮的注释代码可以保留或删除 */}
                {/* <Button type="primary" icon={<PlusOutlined />}>新建钱包</Button> */}
                {/* <Button icon={<ReloadOutlined />} onClick={loadWalletAddresses}>刷新</Button> */}
                {/* <Button icon={<ExportOutlined />} onClick={handleExport}>导出</Button> */}
              </div>
            </div>

            <Table
              columns={tableColumns} // 使用从外部获取的列定义
              dataSource={(walletAddresses || []) as WalletItem[]}
              rowKey="id"
              loading={loading} // 表格自身的加载状态
              locale={{ emptyText: '暂无钱包数据' }}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: totalWalletAddresses || 0,
                showSizeChanger: true,
                showQuickJumper: true,
                pageSizeOptions: ['10', '20', '50', '100'], // 确保这些值是字符串
                showTotal: (total) => `共 ${total} 个钱包`,
                onChange: (page, newPageSize) => {
                  setCurrentPage(page);
                  if (newPageSize) setPageSize(newPageSize);
                },
              }}
            />
          </Card>
        </>
      )}

      {/* 使用 WalletTagManagerModal 组件 */}
      {selectedWalletForTagging && (
        <WalletTagManagerModal
          visible={tagModalVisible}
          onClose={() => {
            setTagModalVisible(false);
            setSelectedWalletForTagging(null); // 清除选中的钱包
          }}
          onSaveTags={handleSaveTagsInList} // 使用更新后的回调函数
          wallet={selectedWalletForTagging}
          // initialAvailableTags 如果需要从外部配置，可以在这里传递
        />
      )}
    </div>
  );
};

export default WalletList;
