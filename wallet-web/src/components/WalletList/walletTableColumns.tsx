import React from 'react';
import { Space, Tag, Typography, Button, Dropdown, Image } from 'antd';
import { EllipsisOutlined, TagsOutlined } from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import { WalletAddressItem } from '../../mock/walletData'; // 保持原有路径或调整为共享类型路径
import ethIcon from '../../assets/images/eth.svg';
import usdtIcon from '../../assets/images/usdt.svg';
import trxIcon from '../../assets/images/tron.svg';
import NativeFilterSelect from '../common/NativeFilterSelect/NativeFilterSelect'; // 修正导入路径

const { Text } = Typography;

// 扩展钱包数据类型，与 WalletList.tsx 中的定义保持一致
export interface WalletItem extends WalletAddressItem {
  ethBalance?: string;
  erc20UsdtBalance?: string;
  trxBalance?: string;
  trc20UsdtBalance?: string;
}

// 定义 getWalletTableColumns 函数的参数类型
interface WalletTableColumnsProps {
  onCopyToClipboard: (text: string) => void;
  onOpenTagModal: (wallet: WalletItem) => void;
  // 如果有其他依赖项，例如激活/停用钱包的函数，也应在此处添加
}

export const getWalletTableColumns = ({
  onCopyToClipboard,
  onOpenTagModal,
}: WalletTableColumnsProps): ColumnsType<WalletItem> => [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    render: (text: string, record: WalletItem) => (
      <Space>
        <Text strong>{text}</Text>
        {record.tags &&
          record.tags.map((tag) => (
            <Tag
              key={tag}
              color={tag === '默认' ? 'blue' : tag === '常用' ? 'green' : tag === '商业' ? 'orange' : 'purple'}
            >
              {tag}
            </Tag>
          ))}
      </Space>
    ),
  },
  {
    title: 'ETH余额',
    dataIndex: 'ethBalance',
    key: 'ethBalance',
    render: (text: string) => (
      <Space>
        <Text strong>{text || '0'}</Text>
        <Image src={ethIcon} preview={false} width={16} />
      </Space>
    ),
    sorter: (a: WalletItem, b: WalletItem) => parseFloat(a.ethBalance || '0') - parseFloat(b.ethBalance || '0'),
  },
  {
    title: 'ERC20 USDT余额',
    dataIndex: 'erc20UsdtBalance',
    key: 'erc20UsdtBalance',
    render: (text: string) => (
      <Space>
        <Text strong>{text || '0'}</Text>
        <Image src={usdtIcon} preview={false} width={16} />
      </Space>
    ),
    sorter: (a: WalletItem, b: WalletItem) =>
      parseFloat(a.erc20UsdtBalance || '0') - parseFloat(b.erc20UsdtBalance || '0'),
  },
  {
    title: 'TRX余额',
    dataIndex: 'trxBalance',
    key: 'trxBalance',
    render: (text: string) => (
      <Space>
        <Text strong>{text || '0'}</Text>
        <Image src={trxIcon} preview={false} width={16} />
      </Space>
    ),
    sorter: (a: WalletItem, b: WalletItem) => parseFloat(a.trxBalance || '0') - parseFloat(b.trxBalance || '0'),
  },
  {
    title: 'TRC20 USDT余额',
    dataIndex: 'trc20UsdtBalance',
    key: 'trc20UsdtBalance',
    render: (text: string) => (
      <Space>
        <Text strong>{text || '0'}</Text>
        <Image src={usdtIcon} preview={false} width={16} />
      </Space>
    ),
    sorter: (a: WalletItem, b: WalletItem) =>
      parseFloat(a.trc20UsdtBalance || '0') - parseFloat(b.trc20UsdtBalance || '0'),
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    render: (text: string) => {
      let color = '';
      let label = '';

      switch (text) {
        case 'main':
          color = 'blue';
          label = '主钱包';
          break;
        case 'savings':
          color = 'green';
          label = '储蓄钱包';
          break;
        case 'investment':
          color = 'gold';
          label = '投资钱包';
          break;
        default:
          color = 'default';
          label = text;
      }

      return <Tag color={color}>{label}</Tag>;
    },
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      const typeOptions = [
        { text: '主钱包', value: 'main' },
        { text: '储蓄钱包', value: 'savings' },
        { text: '投资钱包', value: 'investment' },
      ];
      return (
        <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
          <NativeFilterSelect
            id="type-filter-select"
            placeholder="选择类型"
            options={typeOptions}
            value={selectedKeys[0] as string | number | undefined}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            style={{ marginBottom: 8, display: 'block', width: 188 }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => {
                confirm();
                close();
              }}
              size="small"
              style={{ width: 90 }}
            >
              搜索
            </Button>
            <Button
              onClick={() => {
                if (clearFilters) {
                  clearFilters();
                }
                setSelectedKeys([]); // 确保清除选中状态
                confirm(); // 确认清除
                close();
              }}
              size="small"
              style={{ width: 90 }}
            >
              重置
            </Button>
          </Space>
        </div>
      );
    },
    onFilter: (value: any, record: WalletItem) => record.type === value,
    // 如果只想在 filterDropdown 中处理筛选逻辑，可以移除 onFilter，并在 confirm 时手动触发外部的筛选更新。
    // 但保留 onFilter 可以让 antd 内部的筛选机制继续工作，只要 selectedKeys 被正确设置。
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (text: string) => (
      <Tag color={text === 'active' ? 'success' : 'error'}>{text === 'active' ? '活跃' : '非活跃'}</Tag>
    ),
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      const statusOptions = [
        { text: '活跃', value: 'active' },
        { text: '非活跃', value: 'inactive' },
      ];
      return (
        <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
          <NativeFilterSelect
            id="status-filter-select"
            placeholder="选择状态"
            options={statusOptions}
            value={selectedKeys[0] as string | number | undefined}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            style={{ marginBottom: 8, display: 'block', width: 188 }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => {
                confirm();
                close();
              }}
              size="small"
              style={{ width: 90 }}
            >
              搜索
            </Button>
            <Button
              onClick={() => {
                if (clearFilters) {
                  clearFilters();
                }
                setSelectedKeys([]);
                confirm();
                close();
              }}
              size="small"
              style={{ width: 90 }}
            >
              重置
            </Button>
          </Space>
        </div>
      );
    },
    onFilter: (value: any, record: WalletItem) => record.status === value,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: (text: string) => new Date(text).toLocaleString(),
    sorter: (a: WalletItem, b: WalletItem) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  },
  {
    title: '最后使用',
    dataIndex: 'lastUsed',
    key: 'lastUsed',
    render: (text: string) => new Date(text).toLocaleString(),
    sorter: (a: WalletItem, b: WalletItem) => new Date(a.lastUsed).getTime() - new Date(b.lastUsed).getTime(),
  },
  {
    title: '操作',
    key: 'action',
    render: (_: any, record: WalletItem) => (
      <Space>
        <Button type="text" icon={<TagsOutlined />} onClick={() => onOpenTagModal(record)} title="管理标签" />
        <Dropdown
          menu={{
            items: [
              { key: 'details', label: '查看详情' }, // TODO: 实现详情查看逻辑
              { key: 'edit', label: '编辑' }, // TODO: 实现编辑逻辑
              { key: 'transfer', label: '转账' }, // TODO: 实现转账逻辑
              { key: 'copy', label: '复制地址', onClick: () => onCopyToClipboard(record.address) },
              record.status === 'active'
                ? { key: 'deactivate', label: '停用' /* onClick: () => handleDeactivate(record.id) */ } // TODO: 实现停用逻辑
                : { key: 'activate', label: '激活' /* onClick: () => handleActivate(record.id) */ }, // TODO: 实现激活逻辑
            ],
          }}
        >
          <Button type="text" icon={<EllipsisOutlined />} />
        </Dropdown>
      </Space>
    ),
  },
];