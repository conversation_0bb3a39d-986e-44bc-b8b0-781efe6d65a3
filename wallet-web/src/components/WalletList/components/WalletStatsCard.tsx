import React from 'react';
import { Card, Row, Col, Statistic, Image } from 'antd';
import { WalletOutlined } from '@ant-design/icons';
import styles from '../WalletList.module.css'; // 假设样式可以共用或调整路径
import ethIcon from '../../../assets/images/eth.svg';
import usdtIcon from '../../../assets/images/usdt.svg';
import trxIcon from '../../../assets/images/tron.svg';

interface WalletStats {
  totalEthBalance: string;
  totalErc20UsdtBalance: string;
  totalTrxBalance: string;
  totalTrc20UsdtBalance: string;
  activeWallets: number;
  mainWallets: number;
  savingsWallets: number;
  investmentWallets: number;
}

interface WalletStatsCardProps {
  loading: boolean;
  statistics: WalletStats;
  totalWalletAddresses: number;
}

const WalletStatsCard: React.FC<WalletStatsCardProps> = ({ loading, statistics, totalWalletAddresses }) => {
  if (loading) {
    return (
      <Card className={styles.statsCard}>
        <div className={styles.loadingContainer}>
          <h3>加载统计数据中...</h3>
        </div>
      </Card>
    );
  }

  return (
    <Card className={styles.statsCard}>
      <Row gutter={24}>
        <Col span={4}>
          <Statistic title="钱包总数" value={totalWalletAddresses || 0} prefix={<WalletOutlined />} />
        </Col>
        <Col span={20}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="ETH总余额"
                value={statistics.totalEthBalance}
                precision={4}
                suffix={
                  <Image
                    src={ethIcon}
                    preview={false}
                    width={24}
                    style={{ verticalAlign: 'middle', marginLeft: 4 }}
                  />
                }
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="ERC20 USDT总余额"
                value={statistics.totalErc20UsdtBalance}
                precision={2}
                suffix={
                  <Image
                    src={usdtIcon}
                    preview={false}
                    width={24}
                    style={{ verticalAlign: 'middle', marginLeft: 4 }}
                  />
                }
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="TRX总余额"
                value={statistics.totalTrxBalance}
                precision={4}
                suffix={
                  <Image
                    src={trxIcon}
                    preview={false}
                    width={24}
                    style={{ verticalAlign: 'middle', marginLeft: 4 }}
                  />
                }
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="TRC20 USDT总余额"
                value={statistics.totalTrc20UsdtBalance}
                precision={2}
                suffix={
                  <Image
                    src={usdtIcon}
                    preview={false}
                    width={24}
                    style={{ verticalAlign: 'middle', marginLeft: 4 }}
                  />
                }
              />
            </Col>
          </Row>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginTop: 16 }}>
        <Col span={6}>
          <Statistic
            title="活跃钱包"
            value={statistics.activeWallets}
            suffix={`/ ${totalWalletAddresses || 0}`}
          />
        </Col>
        <Col span={6}>
          <Statistic title="主钱包" value={statistics.mainWallets} valueStyle={{ color: '#1890ff' }} />
        </Col>
        <Col span={6}>
          <Statistic title="储蓄钱包" value={statistics.savingsWallets} valueStyle={{ color: '#52c41a' }} />
        </Col>
        <Col span={6}>
          <Statistic title="投资钱包" value={statistics.investmentWallets} valueStyle={{ color: '#faad14' }} />
        </Col>
      </Row>
    </Card>
  );
};

export default WalletStatsCard;