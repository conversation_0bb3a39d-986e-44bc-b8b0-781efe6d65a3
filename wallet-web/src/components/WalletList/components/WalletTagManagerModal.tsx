import React, { useState, useEffect } from 'react';
import { Modal, Form, Checkbox, Row, Col, Input, Button, message, Space } from 'antd';
import { WalletItem } from '../walletTableColumns'; // 或者 WalletList.tsx 中的 WalletItem 定义

interface WalletTagManagerModalProps {
  visible: boolean;
  onClose: () => void;
  onSaveTags: (walletId: string, tags: string[]) => void; // 假设保存时需要钱包ID
  wallet: WalletItem | null;
  initialAvailableTags?: string[];
}

const WalletTagManagerModal: React.FC<WalletTagManagerModalProps> = ({
  visible,
  onClose,
  onSaveTags,
  wallet,
  initialAvailableTags = ['默认', '常用', '商业', '个人', '投资'],
}) => {
  const [form] = Form.useForm();
  const [availableTags, setAvailableTags] = useState<string[]>(initialAvailableTags);
  const [newTagInput, setNewTagInput] = useState('');

  useEffect(() => {
    if (wallet) {
      form.setFieldsValue({
        tags: wallet.tags || [],
      });
    } else {
      form.resetFields();
    }
  }, [wallet, form, visible]);

  const handleSave = () => {
    form.validateFields().then((values) => {
      if (wallet) {
        onSaveTags(wallet.id, values.tags); // 传递钱包ID和选中的标签
        message.success('标签已更新'); // 可以在父组件处理成功消息
        onClose(); // 关闭模态框
      }
    });
  };

  const handleAddNewTag = () => {
    if (newTagInput && !availableTags.includes(newTagInput)) {
      setAvailableTags([...availableTags, newTagInput]);
      // 自动选中新添加的标签
      const currentTags = form.getFieldValue('tags') || [];
      form.setFieldsValue({ tags: [...currentTags, newTagInput] });
      setNewTagInput(''); // 清空输入框
    } else if (availableTags.includes(newTagInput)) {
      message.warning('该标签已存在');
    }
  };

  return (
    <Modal
      title={`管理钱包 "${wallet?.name || ''}" 的标签`}
      visible={visible}
      onCancel={onClose}
      onOk={handleSave}
      okText="保存"
      cancelText="取消"
      destroyOnClose // 关闭时销毁内部组件状态
    >
      <Form form={form} layout="vertical">
        <Form.Item name="tags" label="选择标签">
          <Checkbox.Group>
            <Row>
              {availableTags.map((tag) => (
                <Col span={8} key={tag}>
                  <Checkbox value={tag}>{tag}</Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </Form.Item>
        <Form.Item label="添加新标签">
          <Space>
            <Input
              value={newTagInput}
              onChange={(e) => setNewTagInput(e.target.value)}
              placeholder="输入新标签名称"
              onPressEnter={handleAddNewTag}
            />
            <Button onClick={handleAddNewTag} type="dashed">
              添加
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WalletTagManagerModal;