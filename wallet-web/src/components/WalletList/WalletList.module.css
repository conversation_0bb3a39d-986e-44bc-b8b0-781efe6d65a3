.walletList {
    margin-bottom: 24px;
}

.statsCard {
    margin-bottom: 24px;
}

.tableCard {
    margin-bottom: 24px;
}

.tableHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.searchFilters {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.addressContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.address {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .tableHeader {
        flex-direction: column;
        align-items: flex-start;
    }

    .searchFilters {
        margin-bottom: 16px;
        width: 100%;
    }

    .actions {
        width: 100%;
    }
}