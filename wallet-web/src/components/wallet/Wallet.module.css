.walletPage {
    width: 100%;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.tabs {
    width: 100%;
}

.tabs :global(.ant-tabs-content) {
    padding-top: 16px;
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        align-items: flex-start;
    }

    .header h2 {
        margin-bottom: 16px;
    }
}

.emptyCard {
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
}

.addressCard {
    margin-top: 16px;
}

.addressContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.address {
    max-width: 80%;
    font-family: monospace;
}

.emptyAddress {
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
}

.mainCard {
    min-height: 500px;
}