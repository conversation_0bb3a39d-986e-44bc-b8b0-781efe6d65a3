import { useState, useEffect, useCallback, useMemo } from 'react';
import type { SorterResult, TablePaginationConfig } from 'antd/es/table/interface';
import type { FilterValue } from 'antd/es/table/interface';
import { getAddress } from '../../../services/api/address/address'; // Updated import
import type {
  GetWalletAddressListParams,
  GetWalletAddressListSortOrder,
  WalletApiApiWalletV1AddressInfo, // Restored type import
} from '../../../services/api/model';

const { getWalletAddressList } = getAddress(); // Moved to after imports

export type AddressItem = WalletApiApiWalletV1AddressInfo; // Updated type
export type SearchCondition = 'all' | 'hasBalance';

interface UseAddressBookLogicProps {
  visible: boolean;
  selectedNetwork?: string;
  initialPageSize?: number;
}

export const useAddressBookLogic = ({
  visible,
  selectedNetwork,
  initialPageSize = 10,
}: UseAddressBookLogicProps) => {
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [total, setTotal] = useState(0);
  const [sortedInfo, setSortedInfo] = useState<SorterResult<AddressItem>>({});
  const [searchCondition, setSearchCondition] = useState<SearchCondition>('all');
  const [walletAddresses, setWalletAddresses] = useState<AddressItem[]>([]);

  const mapSortOrder = useCallback((sortOrder?: string): GetWalletAddressListSortOrder | undefined => {
    if (!sortOrder) return undefined;
    return sortOrder === 'ascend' ? 'asc' : 'desc';
  }, []);

  const fetchAddresses = useCallback(
    async (
      page: number,
      size: number,
      search?: string,
      sortField?: string,
      sortOrder?: string,
      condition?: SearchCondition,
    ) => {
      setLoading(true);
      try {
        const params: GetWalletAddressListParams = {
          page,
          limit: size,
          address: search || undefined,
          sort_field: sortField || undefined,
          sort_order: mapSortOrder(sortOrder),
          has_balance: condition === 'hasBalance',
          type: selectedNetwork,
        };

        const response = await getWalletAddressList(params);

        if (response?.list) {
          setWalletAddresses(response.list);
          if (response.page) {
            setTotal(response.page.total || 0);
          }
        } else {
          setWalletAddresses([]);
          setTotal(0);
        }
      } catch (error) {
        console.error('获取地址列表失败:', error);
        setWalletAddresses([]);
        setTotal(0);
      } finally {
        setLoading(false);
      }
    },
    [selectedNetwork, mapSortOrder],
  );

  useEffect(() => {
    if (visible) {
      const sortField = sortedInfo.columnKey as string | undefined;
      const sortDirection = sortedInfo.order as string | undefined;
      fetchAddresses(currentPage, pageSize, searchText, sortField, sortDirection, searchCondition);
    }
  }, [visible, currentPage, pageSize, searchText, sortedInfo, searchCondition, fetchAddresses]);

  const handleSearch = useCallback(
    (value: string) => {
      setSearchText(value);
      setCurrentPage(1); // Reset to first page on new search
      // Fetching is handled by useEffect
    },
    [],
  );

  const handlePageChange = useCallback((page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);
    // Fetching is handled by useEffect
  }, []);

  const handleTableChange = useCallback(
    (
      pagination: TablePaginationConfig, // Not used directly for page changes here
      filters: Record<string, FilterValue | null>, // Not used
      sorter: SorterResult<AddressItem> | SorterResult<AddressItem>[],
    ) => {
      const sorterResult = Array.isArray(sorter) ? sorter[0] : sorter;
      setSortedInfo(sorterResult);
      setCurrentPage(1); // Reset to first page on sort change
      // Fetching is handled by useEffect
    },
    [],
  );

  const handleSearchConditionChange = useCallback((value: SearchCondition) => {
    setSearchCondition(value);
    setCurrentPage(1); // Reset to first page on condition change
    // Fetching is handled by useEffect
  }, []);

  const totalPages = useMemo(() => Math.ceil(total / pageSize), [total, pageSize]);

  return {
    searchText,
    loading,
    currentPage,
    pageSize,
    total,
    sortedInfo,
    searchCondition,
    walletAddresses,
    totalPages,
    handleSearch,
    handlePageChange,
    handleTableChange,
    handleSearchConditionChange,
    setSearchText, // Exposing for direct manipulation if needed by filter component
    setSearchCondition, // Exposing for direct manipulation if needed by filter component
  };
};