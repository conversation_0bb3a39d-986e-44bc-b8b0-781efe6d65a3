import { Modal, Table, Button, Tag, Typography, Image, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState, useMemo, useCallback } from 'react';
import ethIcon from '../../assets/images/eth.svg';
import trxIcon from '../../assets/images/tron.svg';
import usdtIcon from '../../assets/images/usdt.svg';
// DemogfApiWalletV1AddressInfo removed as AddressItem is imported from the hook
import { useAddressBookLogic, AddressItem } from './hooks/useAddressBookLogic';
import AddressFilters from './components/AddressFilters';
import AddressBookPaginationControls from './components/AddressBookPaginationControls';

const { Text } = Typography;

interface AddressBookModalProps {
  visible: boolean;
  onCancel: () => void;
  onSelect: (address: string) => void;
  addressType?: 'from' | 'to'; // addressType is not used in the current logic, consider removing if not needed
  selectedNetwork?: string;
}

const NETWORK_CONFIG = {
  ETH: { icon: ethIcon, color: 'blue', displayName: 'ETH' },
  TRX: { icon: trxIcon, color: 'green', displayName: 'TRX' },
  UNKNOWN: { icon: '', color: 'gray', displayName: 'Unknown' }, // Fallback
};

const getNetworkInfo = (type?: string) => {
  if (!type) return NETWORK_CONFIG.UNKNOWN;
  if (type.includes('ETH')) return NETWORK_CONFIG.ETH;
  if (type.includes('TRX')) return NETWORK_CONFIG.TRX;
  return NETWORK_CONFIG.UNKNOWN;
};

const AddressBookModal: React.FC<AddressBookModalProps> = ({
  visible,
  onCancel,
  onSelect,
  // addressType = 'from', // Removed as it's not used
  selectedNetwork,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]); // This useState is still needed for selectedRowKeys

  const {
    searchText,
    loading,
    currentPage,
    // pageSize, // pageSize is managed within the hook
    total,
    sortedInfo,
    searchCondition,
    walletAddresses,
    totalPages,
    handlePageChange,
    handleTableChange,
    handleSearchConditionChange,
    setSearchText, // from hook
    // setSearchCondition, // from hook - not directly used by AddressFilters, it uses onSearchConditionChange
  } = useAddressBookLogic({ visible, selectedNetwork });


  const columns = useMemo<ColumnsType<AddressItem>>(
    () => [
      {
        title: '地址',
        dataIndex: 'address',
        key: 'address',
        ellipsis: true,
        render: (text) => (
          <Text copyable style={{ width: '180px' }} ellipsis={{ tooltip: text }}>
            {text}
          </Text>
        ),
      },
      {
        title: '网络',
        dataIndex: 'type',
        key: 'type',
        render: (type) => {
          const networkInfo = getNetworkInfo(type);
          return (
            <Space>
              <Tag color={networkInfo.color}>{networkInfo.displayName}</Tag>
            </Space>
          );
        },
      },
      {
        title: '余额',
        dataIndex: 'chain_coin_balance',
        key: 'chain_coin_balance',
        sorter: true,
        sortOrder: sortedInfo.columnKey === 'chain_coin_balance' ? sortedInfo.order : undefined,
        render: (balance, record) => {
          const networkInfo = getNetworkInfo(record.type);
          return (
            <span>
              {balance || '0'}{' '}
              {networkInfo.icon && <Image src={networkInfo.icon} preview={false} width={16} />}
            </span>
          );
        },
      },
      {
        title: 'USDT余额',
        dataIndex: 'chain_usdt_balance',
        key: 'chain_usdt_balance',
        sorter: true,
        sortOrder: sortedInfo.columnKey === 'chain_usdt_balance' ? sortedInfo.order : undefined,
        render: (balance) => (
          <Space>
            <span>
              {balance || '0'} <Image src={usdtIcon} preview={false} width={16} />
            </span>
          </Space>
        ),
      },
    ],
    [sortedInfo], // sortedInfo from the hook
  );

  const handleConfirm = useCallback(() => {
    if (selectedRowKeys.length > 0) {
      const selectedAddresses = walletAddresses // walletAddresses from the hook
        .filter((item) => item.id !== undefined && selectedRowKeys.includes(item.id as React.Key))
        .map((item) => item.address)
        .filter((address): address is string => !!address) // Type guard
        .join('\n');

      if (selectedAddresses) {
        onSelect(selectedAddresses);
      }
    }
  }, [selectedRowKeys, walletAddresses, onSelect]);

  const rowSelection = useMemo(
    () => ({
      selectedRowKeys,
      onChange: (newSelectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(newSelectedRowKeys);
        // setSelectAll removed
      },
      type: 'checkbox' as const,
    }),
    [selectedRowKeys], // walletAddresses.length removed as setSelectAll is removed
  );

  return (
    <Modal
      title="钱包地址簿"
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={[
        <Button key="back" onClick={onCancel}>
          返回
        </Button>,
        <Button key="submit" type="primary" onClick={handleConfirm} disabled={selectedRowKeys.length === 0}>
          确认
        </Button>,
      ]}
    >
      <AddressFilters
        searchText={searchText}
        onSearchChange={setSearchText} // Directly use setSearchText from hook, handleSearch will be triggered by useEffect in hook
        searchCondition={searchCondition}
        onSearchConditionChange={handleSearchConditionChange} // Use handleSearchConditionChange from hook
      />

      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={walletAddresses}
        rowKey="id"
        pagination={false} // Using custom pagination
        scroll={{ y: 400 }}
        size="middle"
        loading={loading}
        onChange={handleTableChange} // handleTableChange from hook
      />

      <AddressBookPaginationControls
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange} // handlePageChange from hook
        totalRecords={total}
      />
    </Modal>
  );
};

export default AddressBookModal;
