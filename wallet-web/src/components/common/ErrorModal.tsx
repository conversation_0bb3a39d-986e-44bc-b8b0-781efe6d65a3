import React, { ReactNode } from 'react';
import { Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { CSSProperties } from 'react';

interface ErrorModalConfig {
  title?: string;
  content: ReactNode;
  icon?: ReactNode;
  width?: number;
  centered?: boolean;
  okText?: string;
  cancelText?: string;
  maskClosable?: boolean;
  keyboard?: boolean;
  mask?: boolean;
  zIndex?: number;
  className?: string;
  onOk?: () => void;
  onCancel?: () => void;
  showCancelButton?: boolean;
}

// 默认配置
const defaultConfig = {
  title: '警告!',
  icon: <ExclamationCircleOutlined />,
  width: 500,
  centered: true,
  okText: '我已了解',
  cancelText: '取消',
  maskClosable: false,
  keyboard: false,
  mask: true,
  zIndex: 1100,
  className: 'error-modal',
  showCancelButton: false,
};

// 样式配置
const modalStyles: CSSProperties = {
  fontSize: '16px',
  lineHeight: '1.6',
};

/**
 * 显示错误提示Modal
 * @param config Modal配置
 */
export const showErrorModal = (config: ErrorModalConfig): void => {
  // 合并默认配置和用户配置
  const finalConfig = { ...defaultConfig, ...config };

  // 如果需要显示取消按钮，使用confirm而不是error
  if (finalConfig.showCancelButton) {
    Modal.confirm({
      title: finalConfig.title,
      icon: finalConfig.icon,
      content: finalConfig.content,
      width: finalConfig.width,
      centered: finalConfig.centered,
      okText: finalConfig.okText,
      cancelText: finalConfig.cancelText,
      maskClosable: finalConfig.maskClosable,
      keyboard: finalConfig.keyboard,
      mask: finalConfig.mask,
      zIndex: finalConfig.zIndex,
      className: finalConfig.className,
      onOk: finalConfig.onOk,
      onCancel: finalConfig.onCancel,
    });
  } else {
    Modal.error({
      title: finalConfig.title,
      icon: finalConfig.icon,
      content: finalConfig.content,
      width: finalConfig.width,
      centered: finalConfig.centered,
      okText: finalConfig.okText,
      maskClosable: finalConfig.maskClosable,
      keyboard: finalConfig.keyboard,
      mask: finalConfig.mask,
      zIndex: finalConfig.zIndex,
      className: finalConfig.className,
      onOk: finalConfig.onOk,
    });
  }
};

/**
 * 显示安全警告Modal
 */
export const showSecurityWarningModal = (): void => {
  showErrorModal({
    title: '安全警告!',
    content: (
      <div style={modalStyles}>
        <br />
        当前系统使用了多重加密技术，确保您的资产安全
        <br />
        <br />
        助记词和密码必须记得一个，如果全部丢失将会永久性损失全部资产
      </div>
    ),
    className: 'security-warning-modal',
  });
};

/**
 * 显示网络请求警告Modal
 * @param onConfirm 确认回调函数
 * @param onCancel 取消回调函数
 */
export const showNetworkRequestModal = (onConfirm: () => void, onCancel?: () => void): void => {
  showErrorModal({
    title: '网络请求警告',
    content: (
      <div style={modalStyles}>
        <p>获取最新价格需要联网从币安获取价格数据。</p>
        <p>是否确认获取最新价格？</p>
      </div>
    ),
    okText: '确认获取',
    cancelText: '取消',
    onOk: onConfirm,
    onCancel: onCancel,
    showCancelButton: true,
    className: 'network-request-modal',
  });
};

export default {
  showErrorModal,
  showSecurityWarningModal,
  showNetworkRequestModal,
};
