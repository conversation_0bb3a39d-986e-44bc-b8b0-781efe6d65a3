import React from 'react';
import styles from './NativeFilterSelect.module.css';

interface Option {
  value: string | number;
  text: string;
}

interface NativeFilterSelectProps {
  options: Option[];
  value?: string | number | readonly string[]; // Allow undefined for placeholder
  onChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void; // Made onChange optional
  placeholder?: string;
  className?: string;
  style?: React.CSSProperties;
  id?: string; // For label association
}

const NativeFilterSelect: React.FC<NativeFilterSelectProps> = ({
  options,
  value,
  onChange, // Now optional
  placeholder,
  className,
  style,
  id,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    if (onChange) {
      onChange(event);
    }
  };

  return (
    <select
      id={id}
      value={value === undefined && placeholder ? '' : value} // Handle placeholder selection
      onChange={handleChange} // Use the new handler
      className={`${styles.nativeSelect} ${className || ''}`}
      style={style}
    >
      {placeholder && <option value="">{placeholder}</option>}
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.text}
        </option>
      ))}
    </select>
  );
};

export default NativeFilterSelect;
