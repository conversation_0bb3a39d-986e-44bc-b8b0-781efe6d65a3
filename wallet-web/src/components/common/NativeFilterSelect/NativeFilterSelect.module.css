.nativeSelect {
  /* Basic Reset & Sizing */
  appearance: none; /* Remove default arrow in some browsers */
  -webkit-appearance: none;
  -moz-appearance: none;
  box-sizing: border-box;
  display: inline-block;
  width: 100%; /* Default to full width, can be overridden */
  height: 32px; /* Match AntD default input height */
  padding: 4px 11px; /* Match AntD padding */
  padding-right: 30px; /* Space for custom arrow */
  font-size: 14px; /* Match AntD font size */
  line-height: 1.5715; /* Match AntD line height */
  color: rgba(0, 0, 0, 0.88); /* AntD text color */
  background-color: #ffffff; /* AntD background color */
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2212%22%20height%3D%2212%22%20viewBox%3D%220%200%2012%2012%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10.0596%203.94043L6.00001%207.99999L1.94043%203.94043%22%20stroke%3D%22%23BFBFBF%22%20stroke-width%3D%221.5%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%3C%2Fsvg%3E"); /* Custom arrow */
  background-repeat: no-repeat;
  background-position: right 10px center;
  border: 1px solid #d9d9d9; /* AntD border color */
  border-radius: 6px; /* AntD border radius */
  cursor: pointer;
  transition: all 0.2s; /* AntD transition */
}

.nativeSelect:hover {
  border-color: #4096ff; /* AntD hover border color */
}

.nativeSelect:focus {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1); /* AntD focus shadow */
  outline: 0;
}

.nativeSelect:disabled {
  color: rgba(0, 0, 0, 0.25);
  background-color: rgba(0, 0, 0, 0.04);
  border-color: #d9d9d9;
  cursor: not-allowed;
}

/* For placeholder option if used */
.nativeSelect option[value=""] {
  color: #bfbfbf; /* AntD placeholder color */
}