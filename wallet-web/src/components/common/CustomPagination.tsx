import React, { useMemo, useCallback } from 'react';
import { Button } from 'antd';
import type { CSSProperties } from 'react';

interface CustomPaginationProps {
  current: number;
  pageSize: number;
  total: number;
  onChange: (page: number, pageSize?: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  pageSizeOptions?: number[];
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: (total: number) => string;
}

const CustomPagination: React.FC<CustomPaginationProps> = ({
  current,
  pageSize,
  total,
  onChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showSizeChanger = true,
  showQuickJumper = false,
  showTotal = (total) => `共 ${total} 条记录`,
}) => {
  // 计算总页数
  const totalPages = Math.ceil(total / pageSize);

  // 使用 useMemo 优化页码数组生成
  const pageNumbers = useMemo(() => {
    const pages = [];
    const maxVisiblePages = 5; // 最多显示的页码数

    // 如果总页数小于等于最大可见页码数，直接显示所有页码
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 否则，显示当前页附近的页码
      let startPage = Math.max(1, current - Math.floor(maxVisiblePages / 2));
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      // 调整startPage以确保显示maxVisiblePages个页码
      if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }

      // 添加第一页
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }

      // 添加中间页码
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // 添加最后一页
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }

    return pages;
  }, [current, totalPages]);

  // 使用 useCallback 优化事件处理函数
  const handlePageChange = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        onChange(page, pageSize);
      }
    },
    [onChange, pageSize, totalPages],
  );

  const handlePageSizeChange = useCallback(
    (event: React.ChangeEvent<HTMLSelectElement>) => {
      const newPageSize = parseInt(event.target.value);
      if (onPageSizeChange) {
        onPageSizeChange(newPageSize);
      } else {
        onChange(1, newPageSize); // 切换每页记录数时回到第一页
      }
    },
    [onChange, onPageSizeChange],
  );

  const handleQuickJumperKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        const target = e.target as HTMLInputElement;
        const page = parseInt(target.value);
        if (!isNaN(page) && page >= 1 && page <= totalPages) {
          handlePageChange(page);
          target.value = '';
        }
      }
    },
    [handlePageChange, totalPages],
  );

  // 使用 useMemo 优化样式对象
  const styles = useMemo(
    () => ({
      container: {
        marginTop: 16,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      } as CSSProperties,
      pageSizeSelect: {
        padding: '4px 8px',
        borderRadius: '2px',
        border: '1px solid #d9d9d9',
      } as CSSProperties,
      quickJumperInput: {
        width: 50,
        margin: '0 8px',
        padding: '4px 8px',
        borderRadius: '2px',
        border: '1px solid #d9d9d9',
        textAlign: 'center' as const,
      } as CSSProperties,
    }),
    [],
  );

  return (
    <div style={styles.container}>
      <div>{showTotal && <span style={{ marginRight: 16 }}>{showTotal(total)}</span>}</div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {showSizeChanger && (
          <div style={{ marginRight: 16 }}>
            <span style={{ marginRight: 8 }}>每页</span>
            <select value={pageSize} onChange={handlePageSizeChange} style={styles.pageSizeSelect}>
              {pageSizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span style={{ marginLeft: 8 }}>条</span>
          </div>
        )}

        <Button disabled={current === 1} onClick={() => handlePageChange(current - 1)} style={{ margin: '0 8px' }}>
          上一页
        </Button>

        {pageNumbers.map((page, index) =>
          typeof page === 'number' ? (
            <Button
              key={index}
              type={current === page ? 'primary' : 'default'}
              onClick={() => handlePageChange(page)}
              style={{ margin: '0 4px' }}
            >
              {page}
            </Button>
          ) : (
            <span key={index} style={{ margin: '0 4px' }}>
              {page}
            </span>
          ),
        )}

        <Button
          disabled={current === totalPages}
          onClick={() => handlePageChange(current + 1)}
          style={{ margin: '0 8px' }}
        >
          下一页
        </Button>

        {showQuickJumper && (
          <div style={{ display: 'inline-flex', alignItems: 'center', marginLeft: 8 }}>
            <span>前往</span>
            <input
              type="number"
              min={1}
              max={totalPages}
              style={styles.quickJumperInput}
              onKeyDown={handleQuickJumperKeyDown}
            />
            <span>页</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomPagination;
