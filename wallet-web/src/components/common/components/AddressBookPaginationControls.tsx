import React from 'react';
import { Button } from 'antd';

interface AddressBookPaginationControlsProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalRecords: number;
}

const AddressBookPaginationControls: React.FC<AddressBookPaginationControlsProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  totalRecords,
}) => {
  const generatePageNumbers = (): (number | string)[] => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }

      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) pages.push('...');
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) pages.push('...');
        pages.push(totalPages);
      }
    }
    return pages;
  };

  if (totalPages === 0) {
    return (
      <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
        <span>共 0 条记录</span>
      </div>
    );
  }

  return (
    <div style={{ marginTop: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <div>
        <Button
          disabled={currentPage === 1}
          onClick={() => onPageChange(currentPage - 1)}
          style={{ margin: '0 8px' }}
        >
          上一页
        </Button>

        {generatePageNumbers().map((page, index) =>
          typeof page === 'number' ? (
            <Button
              key={index}
              type={currentPage === page ? 'primary' : 'default'}
              onClick={() => onPageChange(page)}
              style={{ margin: '0 4px' }}
            >
              {page}
            </Button>
          ) : (
            <span key={index} style={{ margin: '0 4px' }}>
              {page}
            </span>
          ),
        )}

        <Button
          disabled={currentPage === totalPages}
          onClick={() => onPageChange(currentPage + 1)}
          style={{ margin: '0 8px' }}
        >
          下一页
        </Button>
      </div>
      <span>共 {totalRecords} 条记录</span>
    </div>
  );
};

export default AddressBookPaginationControls;