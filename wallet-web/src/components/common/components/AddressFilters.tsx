import React from 'react';
import { Input, Radio, Row, Col } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import type { SearchCondition } from '../hooks/useAddressBookLogic';

interface AddressFiltersProps {
  searchText: string;
  onSearchChange: (text: string) => void;
  searchCondition: SearchCondition;
  onSearchConditionChange: (condition: SearchCondition) => void;
}

const AddressFilters: React.FC<AddressFiltersProps> = ({
  searchText,
  onSearchChange,
  searchCondition,
  onSearchConditionChange,
}) => {
  return (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={12}>
        <Input
          prefix={<SearchOutlined />}
          placeholder="搜索地址"
          allowClear
          value={searchText} // Control component with searchText prop
          onChange={(e) => onSearchChange(e.target.value)}
          style={{ width: '100%' }}
        />
      </Col>
      <Col span={12}>
        <Radio.Group
          value={searchCondition}
          onChange={(e) => onSearchConditionChange(e.target.value as SearchCondition)}
          style={{ marginLeft: 16 }}
        >
          <Radio.Button value="all">全部地址</Radio.Button>
          <Radio.Button value="hasBalance">有余额地址</Radio.Button>
        </Radio.Group>
      </Col>
    </Row>
  );
};

export default AddressFilters;