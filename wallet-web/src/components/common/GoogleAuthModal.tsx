import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Modal, Form, Input, Button, FormProps } from 'antd';
import { SafetyOutlined } from '@ant-design/icons';
import type { CSSProperties } from 'react';

export interface GoogleAuthModalProps {
  /**
   * 弹窗是否可见
   */
  visible: boolean;
  /**
   * 点击确认按钮时的回调，参数为谷歌验证码
   */
  onConfirm: (googleCode: string) => Promise<void>;
  /**
   * 点击取消按钮时的回调
   */
  onCancel: () => void;
  /**
   * 自定义标题，默认为"安全验证"
   */
  title?: React.ReactNode;
  /**
   * 自定义提示文本，默认为"请输入谷歌验证码完成设置"
   */
  promptText?: string;
  /**
   * 表单布局，默认为vertical
   */
  formLayout?: FormProps['layout'];
  /**
   * 确认按钮文字，默认为"确认"
   */
  confirmText?: string;
  /**
   * 取消按钮文字，默认为"取消"
   */
  cancelText?: string;
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 是否禁用表单，默认为false
   */
  disabled?: boolean;
}

/**
 * 谷歌验证码弹窗组件
 * 用于需要谷歌验证码验证的场景
 */
const GoogleAuthModal: React.FC<GoogleAuthModalProps> = ({
  visible,
  onConfirm,
  onCancel,
  title = <div style={{ textAlign: 'center', fontSize: '18px', fontWeight: 500 }}>安全验证</div>,
  promptText = '请输入谷歌验证码完成设置',
  formLayout = 'vertical',
  confirmText = '确认',
  cancelText = '取消',
  className = 'google-auth-modal',
  disabled = false,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 使用 useCallback 优化事件处理函数
  const handleCancel = useCallback(() => {
    if (!loading) {
      form.resetFields();
      onCancel();
    }
  }, [form, loading, onCancel]);

  const handleConfirm = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      await onConfirm(values.googleCode);

      // 成功后重置表单
      form.resetFields();
    } catch (error) {
      console.error('谷歌验证码验证失败:', error);
    } finally {
      setLoading(false);
    }
  }, [form, onConfirm]);

  useEffect(() => {
    if (visible) {
      form.resetFields(['googleCode']); // 只重置 googleCode 字段
    }
  }, [visible, form]);

  // 使用 useMemo 优化样式对象
  const styles = useMemo(
    () => ({
      modal: {
        borderRadius: '12px',
        overflow: 'hidden',
      } as CSSProperties,
      body: {
        padding: '24px 24px 12px',
      } as CSSProperties,
      iconContainer: {
        textAlign: 'center',
        marginBottom: '20px',
      } as CSSProperties,
      icon: {
        fontSize: '36px',
        color: '#1890ff',
        backgroundColor: '#e6f7ff',
        padding: '12px',
        borderRadius: '50%',
      } as CSSProperties,
      prompt: {
        marginTop: '12px',
        color: '#666',
        fontSize: '14px',
      } as CSSProperties,
      input: {
        width: '100%',
        height: '50px',
        borderRadius: '8px',
        fontSize: '16px',
        textAlign: 'center',
        letterSpacing: '8px',
      } as CSSProperties,
      buttonContainer: {
        display: 'flex',
        gap: '12px',
        marginTop: '24px',
      } as CSSProperties,
      button: {
        flex: 1,
        height: '44px',
        borderRadius: '8px',
      } as CSSProperties,
    }),
    [],
  );

  // 使用 useMemo 优化表单验证规则
  const formRules = useMemo(
    () => [
      { required: true, message: '请输入谷歌验证码' },
      { pattern: /^\d{6}$/, message: '验证码必须是6位数字' },
    ],
    [],
  );

  return (
    <Modal
      title={title}
      open={visible}
      footer={null}
      onCancel={handleCancel}
      centered
      width={380}
      bodyStyle={styles.body}
      style={styles.modal}
      className={className}
    >
      <div style={styles.iconContainer}>
        <SafetyOutlined style={styles.icon} />
        <p style={styles.prompt}>{promptText}</p>
      </div>

      <Form form={form} layout={formLayout} preserve={false}>
        <Form.Item name="googleCode" rules={formRules}>
          <Input
            prefix={<SafetyOutlined style={{ color: '#bfbfbf' }} />}
            placeholder="请输入6位验证码"
            maxLength={6}
            size="large"
            disabled={disabled}
            style={styles.input}
          />
        </Form.Item>

        <div style={styles.buttonContainer}>
          <Button onClick={handleCancel} style={styles.button} disabled={loading}>
            {cancelText}
          </Button>
          <Button type="primary" onClick={handleConfirm} loading={loading} style={styles.button}>
            {confirmText}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default GoogleAuthModal;
