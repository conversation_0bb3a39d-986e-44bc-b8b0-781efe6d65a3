import React from 'react';
import { Card, Statistic, Button } from 'antd';
import { WalletOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import styles from './WalletCard.module.css';

interface WalletCardProps {
  balance: string;
  currency: string;
  loading?: boolean;
}

export const WalletCard: React.FC<WalletCardProps> = ({ balance, currency, loading = false }) => {
  const navigate = useNavigate();

  return (
    <Card className={styles.walletCard}>
      <Statistic
        title="钱包余额"
        value={balance}
        precision={2}
        valueStyle={{ color: '#3f8600' }}
        prefix={<WalletOutlined />}
        suffix={currency}
        loading={loading}
      />
      <Button type="primary" className={styles.detailButton} onClick={() => navigate('/wallet')}>
        查看详情
      </Button>
    </Card>
  );
};

export default WalletCard;
