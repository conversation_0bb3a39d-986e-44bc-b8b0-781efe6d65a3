import React from 'react';
import { Drawer, Descriptions, Tag, Tooltip, Button, Space, message, Card, Typography } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { WalletApiApiWalletV1TokenFeeSupplement } from '../../../services/api/fee/types'; // Adjusted path
import styles from '../FeeManagement.module.css'; // Assuming you might want to use some styles
import { copyToClipboard } from '../../../utils/clipboardUtils';

const { Text } = Typography;

interface FeeDetailDrawerProps {
  visible: boolean;
  onClose: () => void;
  record: WalletApiApiWalletV1TokenFeeSupplement | null;
}

// Helper function to get status text and color (can be shared or moved to a utils file)
const getStatusText = (status?: string) => {
  switch (status) {
    case 'pending': return '待处理';
    case 'processing': return '处理中';
    case 'success': return '成功';
    case 'failed': return '失败';
    case 'partial_success': return '部分成功';
    default: return status || 'N/A';
  }
};

const getStatusColor = (status?: string) => {
  switch (status) {
    case 'pending': return 'orange';
    case 'processing': return 'blue';
    case 'success': return 'green';
    case 'failed': return 'red';
    case 'partial_success': return 'purple';
    default: return 'default';
  }
};

const getActivationStatusTextAndColor = (isActivating?: number) => {
  switch (isActivating) {
    case 0: return { text: '未激活', color: 'grey' };
    case 1: return { text: '激活中', color: 'blue' };
    case 2: return { text: '已激活', color: 'green' };
    default: return { text: '未知', color: 'default' };
  }
};

// TRX补充状态映射函数
const getTrxSupplementStatusConfig = (status: string) => {
  const statusConfig: Record<string, { color: string; text: string }> = {
    'pending': { color: 'orange', text: '待处理' },
    'processing': { color: 'blue', text: '处理中' },
    'success': { color: 'green', text: '成功' },
    'failed': { color: 'red', text: '失败' }
  };
  return statusConfig[status] || { color: 'default', text: '未知' };
};

// TRX补充状态标签组件
const TrxSupplementStatusTag: React.FC<{ status?: string }> = ({ status }) => {
  if (!status) return <span>-</span>;
  const config = getTrxSupplementStatusConfig(status);
  return <Tag color={config.color}>{config.text}</Tag>;
};

// TRX补充信息组件
const TrxSupplementInfo: React.FC<{ record: WalletApiApiWalletV1TokenFeeSupplement }> = ({ record }) => {
  // 只有TRON链的USDT才显示
  if (record.chain_type !== 'TRON' || record.token_symbol !== 'USDT') {
    return null;
  }

  return (
    <Card title="TRX补充信息" size="small" style={{ marginTop: 16 }}>
      <Descriptions column={2} size="small">
        <Descriptions.Item label="是否需要补充">
          {record.trx_supplement_needed ?
            <Tag color="orange">需要补充</Tag> :
            <Tag color="default">无需补充</Tag>
          }
        </Descriptions.Item>

        {record.trx_supplement_needed && (
          <>
            <Descriptions.Item label="补充状态">
              <TrxSupplementStatusTag status={record.trx_supplement_status} />
            </Descriptions.Item>

            <Descriptions.Item label="补充金额">
              {record.trx_supplement_amount ? `${record.trx_supplement_amount} TRX` : '-'}
            </Descriptions.Item>

            <Descriptions.Item label="补充前余额">
              {record.trx_balance_before ? `${record.trx_balance_before} TRX` : '-'}
            </Descriptions.Item>

            <Descriptions.Item label="补充后余额">
              {record.trx_balance_after ? `${record.trx_balance_after} TRX` : '-'}
            </Descriptions.Item>

            <Descriptions.Item label="补充交易哈希" span={2}>
              {record.trx_supplement_hash ? (
                <div>
                  <Text copyable={{ text: record.trx_supplement_hash }}>
                    {record.trx_supplement_hash.substring(0, 20)}...
                  </Text>
                  <Button
                    type="link"
                    size="small"
                    onClick={() => window.open(`https://tronscan.org/#/transaction/${record.trx_supplement_hash}`)}
                  >
                    查看交易
                  </Button>
                </div>
              ) : '-'}
            </Descriptions.Item>
          </>
        )}
      </Descriptions>
    </Card>
  );
};

const FeeDetailDrawer: React.FC<FeeDetailDrawerProps> = ({ visible, onClose, record }) => {
  if (!record) {
    return null;
  }

  const activationStatus = getActivationStatusTextAndColor(record.is_activating);

  return (
    <Drawer
      title="费用补充详情"
      placement="right"
      onClose={onClose}
      visible={visible}
      width={500}
      bodyStyle={{ paddingBottom: 80 }}
    >
      <Descriptions bordered column={1} size="small">
        <Descriptions.Item label="ID">{record.token_fee_supplement_id || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="计划订单ID">{record.withdraw_plan_id || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="用户归集ID">{record.user_withdraw_id || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="地址">
          <Space>
            <span>{record.address || 'N/A'}</span>
            {record.address && <Button type="text" icon={<CopyOutlined />} onClick={() => copyToClipboard(record.address || '')} size="small" />}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="链类型">{record.chain_type || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="代币符号">{record.token_symbol || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="费用类型">{record.fee_type || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="需要数量">{record.required_amount?.toFixed(18) || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="已补充数量">{record.provided_amount?.toFixed(18) || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="补充能量数量 (TRC20)">{record.energy_amount || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="状态">
          <Tag color={getStatusColor(record.status)}>{getStatusText(record.status)}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="矿工费交易哈希（USDT交易）">
          <Space>
            <span>{record.transaction_hash || 'N/A'}</span>
            {record.transaction_hash && <Button type="text" icon={<CopyOutlined />} onClick={() => copyToClipboard(record.transaction_hash || '')} size="small" />}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="错误信息">
          {record.error_message ? (
            <Tooltip title={record.error_message}>
              <span className={styles.errorMessageText}>{record.error_message}</span>
            </Tooltip>
          ) : 'N/A'}
        </Descriptions.Item>
        <Descriptions.Item label="关联任务ID">{record.related_task_id || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="重试次数">{record.retry_count ?? 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="创建时间">{record.created_at || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="更新时间">{record.updated_at || 'N/A'}</Descriptions.Item>
        <Descriptions.Item label="Energy ID">{record.energy_id || 'N/A'}</Descriptions.Item>
        {/* <Descriptions.Item label="激活状态">
            <Tag color={activationStatus.color}>{activationStatus.text}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="激活哈希">
          <Space>
            <span>{record.activate_hash || 'N/A'}</span>
            {record.activate_hash && <Button type="text" icon={<CopyOutlined />} onClick={() => copyToClipboard(record.activate_hash)} size="small" />}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="激活消耗TRX">{record.activate_amount || 'N/A'}</Descriptions.Item> */}
      </Descriptions>

      {/* TRX补充信息区块 */}
      <TrxSupplementInfo record={record} />
    </Drawer>
  );
};

export default FeeDetailDrawer;