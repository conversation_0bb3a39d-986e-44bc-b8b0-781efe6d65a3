import React from 'react';
import { Card, Row, Col, Statistic, Tooltip } from 'antd'; // Removed unused Progress
import {
  DollarOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  WalletOutlined
} from '@ant-design/icons';
import { WalletApiApiWalletV1FeeStatistics } from '../../../services/api/fee/types';
import styles from '../FeeManagement.module.css';

interface FeeStatisticsCardProps {
  statistics: WalletApiApiWalletV1FeeStatistics;
  loading: boolean;
}

const FeeStatisticsCard: React.FC<FeeStatisticsCardProps> = ({ statistics, loading }) => {
  // Removed unused completionRate calculation

  return (
    <Card
      className={`${styles.statsCard} hover-shadow`}
      loading={loading}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <WalletOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>费用统计</span>
        </div>
      }
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '24px',
        }
      }}
    >
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={8} md={4}>
          <Statistic
            title="总记录数"
            value={statistics.total_count}
            prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
            valueStyle={{ fontWeight: 600 }}
          />
        </Col>
        <Col xs={12} sm={8} md={4}>
          <Tooltip title="待处理的费用记录">
            <Statistic
              title="待处理"
              value={statistics.pending_count}
              valueStyle={{ color: '#faad14', fontWeight: 600 }}
              prefix={<ClockCircleOutlined />}
            />
          </Tooltip>
        </Col>
        <Col xs={12} sm={8} md={4}>
          <Tooltip title="处理中的费用记录">
            <Statistic
              title="处理中"
              value={statistics.processing_count}
              valueStyle={{ color: '#1890ff', fontWeight: 600 }}
              prefix={<SyncOutlined spin />}
            />
          </Tooltip>
        </Col>
        <Col xs={12} sm={8} md={4}>
          <Tooltip title="成功处理的费用记录">
            <Statistic
              title="成功"
              value={statistics.success_count}
              valueStyle={{ color: '#52c41a', fontWeight: 600 }}
              prefix={<CheckCircleOutlined />}
            />
          </Tooltip>
        </Col>
        <Col xs={12} sm={8} md={4}>
          <Tooltip title="处理失败的费用记录">
            <Statistic
              title="失败"
              value={statistics.failed_count}
              valueStyle={{ color: '#ff4d4f', fontWeight: 600 }}
              prefix={<CloseCircleOutlined />}
            />
          </Tooltip>
        </Col>
        <Col xs={12} sm={8} md={4}>
          <Tooltip title="部分成功的费用记录">
            <Statistic
              title="部分成功"
              value={statistics.partial_success_count}
              valueStyle={{ color: '#722ed1', fontWeight: 600 }}
              prefix={<WarningOutlined />}
            />
          </Tooltip>
        </Col>
      </Row>

      {/* <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        <Col xs={12} md={8}>
          <Statistic
            title="总需要费用数量"
            value={statistics.total_required_amount}
            precision={6}
            valueStyle={{ color: '#1890ff', fontWeight: 600 }}
          />
        </Col>
        <Col xs={12} md={8}>
          <Statistic
            title="总已补充费用数量"
            value={statistics.total_provided_amount}
            precision={6}
            valueStyle={{ color: '#52c41a', fontWeight: 600 }}
          />
        </Col>
        <Col xs={24} md={8}>
          <div>
            <div style={{ marginBottom: '8px' }}>补充完成率</div>
            <Progress
              percent={parseFloat(completionRate.toFixed(2))}
              status="active"
              strokeColor={{
                '0%': '#1890ff',
                '100%': '#52c41a',
              }}
            />
          </div>
        </Col>
      </Row> */}
    </Card>
  );
};

export default FeeStatisticsCard;
