import React from 'react';
import { Input, Button, Space } from 'antd';
import { SearchOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import NativeFilterSelect from '../../common/NativeFilterSelect/NativeFilterSelect';
import styles from '../FeeManagement.module.css';

interface FiltersState {
  address?: string;
  chainType?: string;
  tokenSymbol?: string;
  feeType?: string;
  status?: string;
}

interface FeeFiltersProps {
  searchText: string;
  filters: FiltersState;
  onSearchTextChange: (text: string) => void;
  onFilterChange: (key: string, value: string) => void;
  onSearch: () => void;
  onReset: () => void;
  onRefresh: () => void; // Added for refresh button
  loading?: boolean;
  refreshLoading?: boolean; // Added for refresh button loading state
}

const FeeFilters: React.FC<FeeFiltersProps> = ({
  searchText,
  filters,
  onSearchTextChange,
  onFilterChange,
  onSearch,
  onReset,
  onRefresh,
  loading,
  refreshLoading,
}) => {
  return (
    <div className={`${styles.filterContainer} fade-in`} style={{ display: 'flex', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>
      <Input
        placeholder="搜索地址"
        value={searchText}
        onChange={(e) => onSearchTextChange(e.target.value)}
        onPressEnter={onSearch}
        prefix={<SearchOutlined style={{ color: '#1890ff' }} />}
        className={`${styles.searchInput} hover-shadow transition-all`}
        style={{
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
        }}
      />

      <NativeFilterSelect
        placeholder="选择链类型"
        value={filters.chainType || ''}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onFilterChange('chainType', e.target.value)}
        options={[
          { text: 'ETH', value: 'ETH' },
          { text: 'TRON', value: 'TRON' }
        ]}
        style={{ width: 120 }}
        className="hover-shadow"
      />

      <NativeFilterSelect
        placeholder="选择代币"
        value={filters.tokenSymbol || ''}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onFilterChange('tokenSymbol', e.target.value)}
        options={[
          { text: 'ETH', value: 'ETH' },
          { text: 'TRX', value: 'TRX' },
          { text: 'USDT', value: 'USDT' }
        ]}
        style={{ width: 120 }}
        className="hover-shadow"
      />

      <NativeFilterSelect
        placeholder="选择费用类型"
        value={filters.feeType || ''}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onFilterChange('feeType', e.target.value)}
        options={[
          { text: 'Gas费用', value: 'gas_fee' },
          { text: 'Energy', value: 'energy' }
        ]}
        style={{ width: 120 }}
        className="hover-shadow"
      />

      <NativeFilterSelect
        placeholder="选择状态"
        value={filters.status || ''}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onFilterChange('status', e.target.value)}
        options={[
          { text: '待处理', value: 'pending' },
          { text: '处理中', value: 'processing' },
          { text: '成功', value: 'success' },
          { text: '失败', value: 'failed' },
          { text: '部分成功', value: 'partial_success' }
        ]}
        style={{ width: 120 }}
        className="hover-shadow"
      />

      <Space>
        <Button
          type="primary"
          icon={<FilterOutlined />}
          onClick={onSearch}
          className={`hover-shadow transition-all`}
          style={{
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)',
            height: '32px',
          }}
        >
          筛选
        </Button>

        <Button
          icon={<ReloadOutlined />}
          onClick={onReset}
          className="transition-all"
          style={{
            borderRadius: '8px',
            height: '32px',
          }}
        >
          重置
        </Button>
      </Space>
      <Button
        icon={<ReloadOutlined />}
        onClick={onRefresh}
        loading={refreshLoading}
        style={{
          borderRadius: '8px',
          height: '32px',
          marginLeft: 'auto', // Push refresh button to the far right
        }}
        className="transition-all"
      >
        刷新
      </Button>
    </div>
  );
};

export default FeeFilters;
