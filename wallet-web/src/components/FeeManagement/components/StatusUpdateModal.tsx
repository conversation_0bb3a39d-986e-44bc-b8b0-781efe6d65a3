import React, { useState, useCallback } from 'react';
import { Modal, Form, message } from 'antd'; // Removed Select and Input
import { WalletApiApiWalletV1TokenFeeSupplement } from '../../../services/api/fee/types';
import GoogleAuthWithPasswordModal from '../../common/GoogleAuthWithPasswordModal';
import NativeFilterSelect from '../../common/NativeFilterSelect/NativeFilterSelect'; // Added NativeFilterSelect

interface StatusUpdateModalProps {
  visible: boolean;
  onClose: () => void;
  record: WalletApiApiWalletV1TokenFeeSupplement | null;
  onUpdateStatus: (
    id: number,
    status: string,
    // transactionHash: string, // Removed
    // errorMessage: string, // Removed
    googleCode: string,
    password: string
  ) => Promise<boolean>;
}

const StatusUpdateModal: React.FC<StatusUpdateModalProps> = ({
  visible,
  onClose,
  record,
  onUpdateStatus
}) => {
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [googleModalVisible, setGoogleModalVisible] = useState(false);
  const [formValues, setFormValues] = useState<{
    status: string;
    // transactionHash: string; // Removed
    // errorMessage: string; // Removed
  }>({ status: '' });

  const statusOptions = [
    { value: 'pending', text: '待处理' },
    { value: 'processing', text: '处理中' },
    { value: 'success', text: '成功' },
    { value: 'failed', text: '失败' },
    { value: 'partial_success', text: '部分成功' },
  ];

  // 重置表单
  const resetForm = useCallback(() => {
    if (record) {
      form.setFieldsValue({
        status: record.status,
        // transactionHash: record.transaction_hash, // Removed
        // errorMessage: record.error_message, // Removed
      });
    }
  }, [form, record]);

  // 当Modal打开时重置表单
  React.useEffect(() => {
    if (visible && record) {
      resetForm();
    }
  }, [visible, record, resetForm]); // resetForm is now stable

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setFormValues(values);
      setGoogleModalVisible(true);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理谷歌验证码和密码提交
  const handleGoogleAuthSubmit = async (googleCode: string, password: string) => {
    if (!record || record.token_fee_supplement_id === undefined) {
      message.error('记录ID不存在，无法更新状态');
      return;
    }

    setConfirmLoading(true);
    try {
      const success = await onUpdateStatus(
        record.token_fee_supplement_id,
        formValues.status,
        // formValues.transactionHash, // Removed
        // formValues.errorMessage, // Removed
        googleCode,
        password
      );

      if (success) {
        message.success('状态更新成功');
        setGoogleModalVisible(false);
        onClose();
      }
    } catch (error) {
      console.error('更新状态失败:', error);
      message.error('更新状态失败，请重试');
    } finally {
      setConfirmLoading(false);
    }
  };

  return (
    <>
      <Modal
        title="更新费用记录状态"
        open={visible}
        onCancel={onClose}
        onOk={handleSubmit}
        confirmLoading={confirmLoading}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
            getValueFromEvent={(event: React.ChangeEvent<HTMLSelectElement>) => {
              // This function tells Form.Item how to get the value from the onChange event argument
              // that NativeFilterSelect will provide.
              return event.target.value;
            }}
            // The `trigger` prop defaults to `onChange`, which is what NativeFilterSelect uses.
            // Form.Item will provide `value` and `onChange` to NativeFilterSelect.
            // `value` will be the current form value for 'status'.
            // `onChange` will be a function that, when called by NativeFilterSelect with an event,
            // will use `getValueFromEvent` to extract the new value and update the form.
          >
            <NativeFilterSelect
              options={statusOptions}
              placeholder="请选择状态"
              // NativeFilterSelect expects an `onChange` prop that takes an event.
              // Form.Item, configured with `getValueFromEvent`, will provide an `onChange`
              // handler that expects an event, processes it with `getValueFromEvent`,
              // and then updates the form state. This aligns the two.
            />
          </Form.Item>
          {/* <Form.Item
            name="transactionHash"
            label="交易哈希"
          >
            <Input placeholder="请输入交易哈希" disabled />
          </Form.Item>
          <Form.Item
            name="errorMessage"
            label="错误信息"
          >
            <Input.TextArea rows={4} placeholder="请输入错误信息" disabled />
          </Form.Item> */}
        </Form>
      </Modal>

      <GoogleAuthWithPasswordModal
        visible={googleModalVisible}
        onCancel={() => setGoogleModalVisible(false)}
        onConfirm={handleGoogleAuthSubmit}
        title="验证身份"
      />
    </>
  );
};

export default StatusUpdateModal;
