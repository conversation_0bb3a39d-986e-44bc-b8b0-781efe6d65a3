.feeManagementPage {
  width: 100%;
  animation: fadeIn 0.5s ease-out;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.statsCard {
  margin-bottom: 24px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 82, 204, 0.08);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.statsCard:hover {
  box-shadow: 0 8px 16px rgba(0, 82, 204, 0.12);
  transform: translateY(-2px);
}

.tableCard {
  margin-top: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 82, 204, 0.08);
  border: none;
  overflow: hidden;
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  flex-wrap: wrap;
  padding: 8px 0;
}

.filterContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  max-width: 100%;
  width: 100%;
}

.searchInput {
  width: 220px;
}

.searchFilters {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  margin-bottom: 16px;
}

.actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.statusTag {
  min-width: 70px;
  text-align: center;
  border-radius: 4px;
  text-transform: capitalize;
}

.amountText {
  font-weight: 500;
  color: #1890ff;
}

/* 自定义表格样式 */
:global(.fee-table) :global(.ant-table) {
  border-radius: 12px;
  overflow: hidden;
}

:global(.fee-table) :global(.ant-table-thead > tr > th) {
  background-color: #f5f7fa;
  color: #262626;
  font-weight: 600;
  padding: 16px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:global(.fee-table) :global(.ant-table-tbody > tr > td) {
  padding: 16px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:global(.fee-table) :global(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background-color: #f0f5ff;
}

:global(.fee-table-row) {
  transition: all 0.3s ease;
}

:global(.fee-table-row:hover) {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 82, 204, 0.08);
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header h2 {
    margin-bottom: 16px;
  }

  .tableHeader {
    flex-direction: column;
    align-items: flex-start;
  }

  .searchFilters {
    margin-bottom: 16px;
    width: 100%;
  }

  .actions {
    width: 100%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
