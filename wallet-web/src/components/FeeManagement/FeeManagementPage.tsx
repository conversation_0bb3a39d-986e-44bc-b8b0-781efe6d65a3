import React, { useState } from 'react';
import {
  Typography,
  Card,
  Table,
  // Input, // Removed
  Button,
  Space,
  Tag,
  Tooltip,
  // Popconfirm, // Removed
  Row,
  Col,
  message
} from 'antd';
import {
  // SearchOutlined, // Removed
  // SyncOutlined, // Removed
  EditOutlined,
  EyeOutlined,
  CopyOutlined,
  WalletOutlined,
  // FilterOutlined, // Removed
  ReloadOutlined,
  DollarOutlined,
  QuestionCircleOutlined
  // ExportOutlined, // Removed as it's now used in FeeFilters
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import { WalletApiApiWalletV1TokenFeeSupplement } from '../../services/api/fee/types';
import { useFeeManagement } from './hooks/useFeeManagement';
import FeeStatisticsCard from './components/FeeStatisticsCard';
import StatusUpdateModal from './components/StatusUpdateModal';
import FeeFilters from './components/FeeFilters';
import FeeDetailDrawer from './components/FeeDetailDrawer'; // Import the new drawer
import styles from './FeeManagement.module.css';
import { copyToClipboard } from '../../utils/clipboardUtils';

const { Title } = Typography;
// Removed unused Option

const FeeManagementPage: React.FC = () => {
  const {
    feeRecords,
    statistics,
    loading,
    currentPage,
    pageSize,
    total,
    filters,
    sorter,
    setCurrentPage,
    setPageSize,
    setFilters,
    setSorter,
    refreshData,
    updateFeeStatus
  } = useFeeManagement();

  const [selectedRecord, setSelectedRecord] = useState<WalletApiApiWalletV1TokenFeeSupplement | null>(null);
  const [statusModalVisible, setStatusModalVisible] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [detailDrawerVisible, setDetailDrawerVisible] = useState<boolean>(false);
  const [selectedRecordForDetail, setSelectedRecordForDetail] = useState<WalletApiApiWalletV1TokenFeeSupplement | null>(null);

  // 处理表格变化
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);

    if (sorter.field && sorter.order) {
      setSorter({
        field: sorter.field,
        order: sorter.order === 'ascend' ? 'asc' : 'desc'
      });
    }
  };

  // 处理筛选变化
  const handleFilterChange = (key: string, value: string) => {
    setFilters({ ...filters, [key]: value });
    setCurrentPage(1); // 重置到第一页
  };

  // 重置筛选
  const handleResetFilters = () => {
    setFilters({
      address: '',
      chainType: '',
      tokenSymbol: '',
      feeType: '',
      status: ''
    });
    setCurrentPage(1);
  };

  // 打开状态更新模态框
  const openStatusModal = (record: WalletApiApiWalletV1TokenFeeSupplement) => {
    setSelectedRecord(record);
    setStatusModalVisible(true);
  };

  const openDetailDrawer = (record: WalletApiApiWalletV1TokenFeeSupplement) => {
    setSelectedRecordForDetail(record);
    setDetailDrawerVisible(true);
  };

  // 复制到剪贴板
  const handleCopyToClipboard = (text: string) => {
    copyToClipboard(text);
  };

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'processing':
        return 'blue';
      case 'success':
        return 'green';
      case 'failed':
        return 'red';
      case 'partial_success':
        return 'purple';
      default:
        return 'default';
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'processing':
        return '处理中';
      case 'success':
        return '成功';
      case 'failed':
        return '失败';
      case 'partial_success':
        return '部分成功';
      default:
        return status;
    }
  };

  // 获取激活状态显示文本和颜色
  const getActivationStatus = (isActivating?: number) => {
    switch (isActivating) {
      case 0:
        return { text: '未激活', color: 'grey' };
      case 1:
        return { text: '激活中', color: 'blue' };
      case 2:
        return { text: '已激活', color: 'green' };
      default:
        return { text: '未知', color: 'default' };
    }
  };

  // TRX补充状态映射函数
  const getTrxSupplementStatusConfig = (status: string) => {
    const statusConfig: Record<string, { color: string; text: string }> = {
      'pending': { color: 'orange', text: '待处理' },
      'processing': { color: 'blue', text: '处理中' },
      'success': { color: 'green', text: '成功' },
      'failed': { color: 'red', text: '失败' }
    };
    return statusConfig[status] || { color: 'default', text: '未知' };
  };

  // 表格列定义
  const columns: ColumnsType<WalletApiApiWalletV1TokenFeeSupplement> = [
    {
      title: 'ID',
      dataIndex: 'token_fee_supplement_id',
      key: 'token_fee_supplement_id',
      width: 80,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      render: (text) => (
        <Tooltip title={text}>
          <Space>
            <span>{text}</span>
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => handleCopyToClipboard(text)}
              size="small"
            />
          </Space>
        </Tooltip>
      ),
    },
    {
      title: '链类型',
      dataIndex: 'chain_type',
      key: 'chain_type',
      width: 100,
    },
    {
      title: '代币',
      dataIndex: 'token_symbol',
      key: 'token_symbol',
      width: 100,
    },
    {
      title: '费用类型',
      dataIndex: 'fee_type',
      key: 'fee_type',
      width: 120,
    },
    // {
    //   title: 'gas费',
    //   dataIndex: 'required_amount',
    //   key: 'required_amount',
    //   width: 120,
    //   render: (text) => Number(text).toFixed(18),
    // },
    {
      title: '补充数量',
      dataIndex: 'provided_amount',
      key: 'provided_amount',
      width: 160, // Adjusted width for retry count
      render: (text) => text !== undefined && text !== null ? Number(text).toFixed(18) : '-',
    },

    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} className={styles.statusTag}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '重试次数',
      dataIndex: 'retry_count',
      key: 'retry_count',
      width: 100,
      align: 'center',
    },
    // {
    //   title: '激活状态',
    //   dataIndex: 'is_activating',
    //   key: 'is_activating',
    //   width: 110,
    //   align: 'center',
    //   render: (isActivating?: number, record?: WalletApiApiWalletV1TokenFeeSupplement) => {
    //     const { text, color } = getActivationStatus(isActivating);
    //     return record?.token_symbol === 'USDT' && record?.chain_type === 'TRON'
    //       ? <Tag color={color}>{text}</Tag>
    //       : null;
    //   },
    // },
    // TRX补充状态列
    {
      title: 'TRX补充状态',
      dataIndex: 'trx_supplement_status',
      key: 'trx_supplement_status',
      width: 120,
      render: (status, record) => {
        // 只有TRON链的USDT才显示TRX补充状态
        if (record?.chain_type !== 'TRON' || record?.token_symbol !== 'USDT') {
          return '-';
        }

        // if (!record?.trx_supplement_needed) {
        //   return <Tag color="default">无需补充</Tag>;
        // }

        const config = getTrxSupplementStatusConfig(status);
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    // TRX补充金额列
    {
      title: 'TRX补充金额',
      dataIndex: 'trx_supplement_amount',
      key: 'trx_supplement_amount',
      width: 120,
      render: (amount, record) => {
        if (record?.chain_type !== 'TRON' || record?.token_symbol !== 'USDT' || !record?.trx_supplement_needed) {
          return '-';
        }
        return amount ? `${amount} TRX` : '-';
      }
    },
    // TRX余额变化列
    // {
    //   title: 'TRX余额变化',
    //   key: 'trx_balance_change',
    //   width: 150,
    //   render: (_, record) => {
    //     if (record?.chain_type !== 'TRON' || record?.token_symbol !== 'USDT' || !record?.trx_supplement_needed) {
    //       return '-';
    //     }

    //     const before = record?.trx_balance_before || 0;
    //     const after = record?.trx_balance_after || 0;

    //     if (record?.trx_supplement_status === 'success' && after > 0) {
    //       return (
    //         <div>
    //           <div style={{ fontSize: '12px', color: '#666' }}>
    //             {before} → {after} TRX
    //           </div>
    //           <div style={{ fontSize: '12px', color: '#52c41a' }}>
    //             +{(after - before).toFixed(6)}
    //           </div>
    //         </div>
    //       );
    //     }

    //     return before > 0 ? `${before} TRX` : '-';
    //   }
    // },
    // {
    //   title: '交易哈希',
    //   dataIndex: 'transaction_hash',
    //   key: 'transaction_hash',
    //   render: (text) =>
    //     text ? (
    //       <Tooltip title={text}>
    //         <Space>
    //           <span>{text}</span>
    //           <Button
    //             type="text"
    //             icon={<CopyOutlined />}
    //             onClick={() => copyToClipboard(text)}
    //             size="small"
    //           />
    //         </Space>
    //       </Tooltip>
    //     ) : (
    //       '-'
    //     ),
    // },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      width: 200,
      render: (text) =>
        text ? (
          <Tooltip title={text}>
            <span>{text.length > 30 ? `${text.substring(0, 30)}...` : text}</span>
          </Tooltip>
        ) : (
          '-'
        ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      sorter: true,
      sortOrder: sorter.field === 'created_at' ? (sorter.order === 'asc' ? 'ascend' : 'descend') : undefined,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 180,
      sorter: true,
      sortOrder: sorter.field === 'updated_at' ? (sorter.order === 'asc' ? 'ascend' : 'descend') : undefined,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => openDetailDrawer(record)}
            />
          </Tooltip>
          <Tooltip title="修改状态">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openStatusModal(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className={`${styles.feeManagementPage} wallet-init-step`}>
      {/* 页面标题卡片 */}
      <Card
        className="hover-shadow"
        style={{
          marginBottom: 24,
          borderRadius: '24px',
          boxShadow: '0 10px 20px rgba(0, 82, 204, 0.1)',
          border: 'none',
          overflow: 'hidden',
          background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
        }}
        bodyStyle={{ padding: '24px' }}
      >
        <Row>
          <Col xs={24} md={16}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div
                style={{
                  width: '56px',
                  height: '56px',
                  borderRadius: '16px',
                  background: 'rgba(24, 144, 255, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <DollarOutlined style={{ fontSize: '30px', color: '#1890ff' }} />
              </div>
              <div className="fade-up">
                <Title level={2} style={{ marginBottom: 4, fontSize: '28px', fontWeight: 600, color: '#0050b3' }}>
                  费用管理
                </Title>
                <Typography.Paragraph type="secondary" style={{ fontSize: '16px', marginBottom: 0 }}>
                  查看和管理您的费用补充记录和统计数据
                </Typography.Paragraph>
              </div>
            </div>
          </Col>
          <Col xs={24} md={8} style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px', alignItems: 'center' }}>
            <Tooltip title="刷新数据">
              <Button
                icon={<ReloadOutlined />}
                style={{
                  height: '40px',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
                }}
                className="hover-shadow transition-all"
                onClick={refreshData}
                loading={loading}
              >
                刷新
              </Button>
            </Tooltip>

            <Tooltip title="什么是费用管理？">
              <Button
                type="text"
                icon={<QuestionCircleOutlined style={{ fontSize: '18px', color: '#1890ff' }} />}
                shape="circle"
                className="hover-shadow transition-all"
                style={{
                  height: '40px',
                  width: '40px',
                }}
              />
            </Tooltip>
          </Col>
        </Row>
      </Card>

      {/* 统计信息卡片 */}
      <div className="fade-up" style={{ animationDelay: '0.1s' }}>
        <FeeStatisticsCard statistics={statistics} loading={loading} />
      </div>

      {/* 表格和筛选器 */}
      <Card
        className={`${styles.tableCard} hover-shadow fade-up`}
        style={{ animationDelay: '0.2s' }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <WalletOutlined style={{ color: '#1890ff' }} />
              <span style={{ fontSize: '16px', fontWeight: 600 }}>费用记录列表</span>
            </div>
            {/* Removed refresh and export buttons from here */}
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #f0f0f0',
            padding: '16px 24px',
          },
          body: {
            padding: '24px',
          }
        }}
      >
        <FeeFilters
          searchText={searchText}
          filters={filters}
          onSearchTextChange={setSearchText}
          onFilterChange={handleFilterChange}
          onSearch={refreshData}
          onReset={handleResetFilters}
          onRefresh={refreshData} // Pass refreshData to FeeFilters
          // onExport prop removed
          loading={loading}
          refreshLoading={loading} // Pass loading state for refresh button
        />

        <Table
          columns={columns}
          dataSource={feeRecords}
          rowKey="token_fee_supplement_id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
          className="fee-table"
          rowClassName={() => 'fee-table-row transition-all'}
        />
      </Card>

      {/* 状态更新模态框 */}
      <StatusUpdateModal
        visible={statusModalVisible}
        onClose={() => setStatusModalVisible(false)}
        record={selectedRecord}
        onUpdateStatus={updateFeeStatus}
      />

      {/* 详情抽屉 */}
      <FeeDetailDrawer
        visible={detailDrawerVisible}
        onClose={() => setDetailDrawerVisible(false)}
        record={selectedRecordForDetail}
      />

      {/* 背景动画元素 */}
      <div
        className="float"
        style={{
          position: 'fixed',
          top: '10%',
          right: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0) 70%)',
          animation: 'float 8s infinite ease-in-out',
          zIndex: 0,
        }}
      />
    </div>
  );
};

export default FeeManagementPage;
