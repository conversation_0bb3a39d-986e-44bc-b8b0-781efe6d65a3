import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import {
  getFee,
  WalletApiApiWalletV1TokenFeeSupplement,
  WalletApiApiWalletV1FeeStatistics
} from '../../../services/api/fee/types';

interface UseFeeManagementReturn {
  feeRecords: WalletApiApiWalletV1TokenFeeSupplement[];
  statistics: WalletApiApiWalletV1FeeStatistics;
  loading: boolean;
  currentPage: number;
  pageSize: number;
  total: number;
  filters: {
    address: string;
    chainType: string;
    tokenSymbol: string;
    feeType: string;
    status: string;
  };
  sorter: {
    field: string;
    order: string;
  };
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setFilters: (filters: any) => void;
  setSorter: (sorter: any) => void;
  refreshData: () => Promise<void>;
  updateFeeStatus: (
    id: number,
    status: string,
    // transactionHash: string, // Removed
    // errorMessage: string, // Removed
    googleCode: string,
    password: string
  ) => Promise<boolean>;
}

// 初始化统计信息
const initialStatistics: WalletApiApiWalletV1FeeStatistics = {
  total_count: 0,
  pending_count: 0,
  processing_count: 0,
  success_count: 0,
  failed_count: 0,
  partial_success_count: 0,
  total_required_amount: 0,
  total_provided_amount: 0
};

export const useFeeManagement = (): UseFeeManagementReturn => {
  const [feeRecords, setFeeRecords] = useState<WalletApiApiWalletV1TokenFeeSupplement[]>([]);
  const [statistics, setStatistics] = useState<WalletApiApiWalletV1FeeStatistics>(initialStatistics);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({
    address: '',
    chainType: '',
    tokenSymbol: '',
    feeType: '',
    status: ''
  });
  const [sorter, setSorter] = useState({
    field: 'created_at',
    order: 'desc'
  });

  // 获取费用记录列表
  const fetchFeeRecords = useCallback(async () => {
    setLoading(true);
    try {
      const feeApi = getFee();
      const response = await feeApi.getWalletTokenFeeSupplements({
        page: currentPage,
        limit: pageSize,
        address: filters.address || undefined,
        chain_type: filters.chainType || undefined,
        token_symbol: filters.tokenSymbol || undefined,
        fee_type: filters.feeType || undefined,
        status: filters.status || undefined,
        sort_field: sorter.field || undefined,
        sort_order: (sorter.order === 'ascend' ? 'asc' : sorter.order === 'descend' ? 'desc' : undefined) as any
      });

      setFeeRecords(response.list || []);
      setStatistics(response.stats || initialStatistics);
      setTotal(response.page?.total || 0);
    } catch (error) {
      console.error('获取费用记录失败:', error);
      message.error('获取费用记录失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, filters, sorter]);

  // 更新费用记录状态
  const updateFeeStatus = async (
    id: number,
    status: string,
    // transactionHash: string, // Removed
    // errorMessage: string, // Removed
    googleCode: string,
    password: string
  ): Promise<boolean> => {
    try {
      const feeApi = getFee();
      const response = await feeApi.postWalletTokenFeeSupplementStatus({
        token_fee_supplement_id: id,
        status,
        // transaction_hash: transactionHash, // Removed
        // error_message: errorMessage, // Removed
        google_code: googleCode,
        password
      });

      if (response.success) {
        await fetchFeeRecords();
        return true;
      }
      return false;
    } catch (error) {
      console.error('更新费用记录状态失败:', error);
      message.error('更新费用记录状态失败，请重试');
      return false;
    }
  };

  // 初始加载和依赖变化时刷新数据
  useEffect(() => {
    fetchFeeRecords();
  }, [fetchFeeRecords]);

  return {
    feeRecords,
    statistics,
    loading,
    currentPage,
    pageSize,
    total,
    filters,
    sorter,
    setCurrentPage,
    setPageSize,
    setFilters,
    setSorter,
    refreshData: fetchFeeRecords,
    updateFeeStatus
  };
};
