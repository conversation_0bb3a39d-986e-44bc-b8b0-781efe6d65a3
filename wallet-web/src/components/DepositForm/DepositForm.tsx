import React from 'react';
import { Form, Input, Button, InputNumber, Select, message } from 'antd';
import { WalletOutlined } from '@ant-design/icons';
import { useAppDispatch } from '../../hooks/useAppDispatch';
import { depositFunds } from '../../store/slices/walletSlice';
import { DepositRequest } from '../../types/wallet';
import styles from './DepositForm.module.css';

const { Option } = Select;

interface DepositFormProps {
  onSuccess?: () => void;
  loading?: boolean;
}

const DepositForm: React.FC<DepositFormProps> = ({ onSuccess, loading = false }) => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();

  const handleSubmit = async (values: DepositRequest) => {
    try {
      await dispatch(depositFunds(values)).unwrap();
      message.success('充值申请已提交，等待处理');
      form.resetFields();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      message.error(error.message || '充值失败，请稍后再试');
    }
  };

  return (
    <div className={styles.depositForm}>
      <h3 className={styles.formTitle}>充值</h3>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item name="amount" label="充值金额" rules={[{ required: true, message: '请输入充值金额' }]}>
          <InputNumber className={styles.amountInput} min={10} step={10} precision={2} placeholder="请输入充值金额" />
        </Form.Item>

        <Form.Item name="paymentMethod" label="支付方式" rules={[{ required: true, message: '请选择支付方式' }]}>
          <Select placeholder="请选择支付方式">
            <Option value="alipay">支付宝</Option>
            <Option value="wechat">微信支付</Option>
            <Option value="bank">银行卡</Option>
          </Select>
        </Form.Item>

        <Form.Item name="description" label="充值说明">
          <Input.TextArea placeholder="请输入充值说明（选填）" rows={3} />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<WalletOutlined />} loading={loading} block>
            确认充值
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default DepositForm;
