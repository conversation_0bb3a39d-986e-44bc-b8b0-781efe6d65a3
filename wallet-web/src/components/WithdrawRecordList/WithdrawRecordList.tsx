import React, { useState } from 'react';
import { openInExplorer as openGlobalExplorer } from '../../utils/explorerUtils'; // 导入全局函数并重命名
import { Table, Card, Button, message } from 'antd'; // Removed Drawer
import { ReloadOutlined, SwapOutlined } from '@ant-design/icons'; // Removed EyeOutlined
import CustomPagination from '../common/CustomPagination';
import WithdrawStatsCard from './components/WithdrawStatsCard';
import WithdrawFilters from './components/WithdrawFilters';
import { getWithdrawTableColumns } from './components/columnDefinitions';
import useWithdrawData, { FiltersState, PaginationState, WithdrawRecord } from './hooks/useWithdrawData';
import WithdrawDetailDrawer from './components/WithdrawDetailDrawer'; // Import the drawer
import styles from './WithdrawRecordList.module.css';

const WithdrawRecordList: React.FC = () => {
  const [detailDrawerVisible, setDetailDrawerVisible] = useState<boolean>(false);
  const [selectedRecordForDetail, setSelectedRecordForDetail] = useState<WithdrawRecord | null>(null);

  const initialPaginationState: PaginationState = {
    current: 1,
    pageSize: 10,
    total: 0,
  };

  const initialFiltersState: FiltersState = {};

  const {
    withdrawRecords,
    loading,
    pagination,
    searchText,
    filters,
    statistics,
    loadWithdrawRecords,
    handleFilterChange,
    handlePaginationChange,
    handleResetFilters,
    setSearchText,
  } = useWithdrawData(initialPaginationState, initialFiltersState);

  // 使用全局的 openGlobalExplorer 函数
  const handleOpenInExplorer = (type: string, hash: string, chain: string) => {
    // getWithdrawTableColumns 和 WithdrawDetailDrawer 期望 type 为 string
    // 全局的 openGlobalExplorer 期望 type 为 'tx' | 'address'
    if (type === 'tx' || type === 'address') {
      openGlobalExplorer(type, hash, chain);
    } else {
      message.warning(`不支持的区块浏览器链接类型: ${type}`);
    }
  };

  const handleViewDetails = (record: WithdrawRecord) => {
    setSelectedRecordForDetail(record);
    setDetailDrawerVisible(true);
  };

  const columns = getWithdrawTableColumns({
    openInExplorer: handleOpenInExplorer,
    onViewDetails: handleViewDetails,
  });

  return (
    <div className={`${styles.withdrawRecordList} fade-in`}>
      <WithdrawStatsCard statistics={statistics} />

      <Card
        className={`${styles.tableCard} hover-shadow`}
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
          border: 'none',
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <SwapOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontSize: '16px', fontWeight: 600 }}>归集记录列表</span>
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #f0f0f0',
            padding: '16px 24px',
          },
          body: {
            padding: '24px',
          }
        }}
      >
        <div className={styles.tableHeader}>
          <WithdrawFilters
            searchText={searchText}
            filters={filters}
            onSearchTextChange={setSearchText}
            onFilterChange={handleFilterChange}
            onDateRangeChange={(dateRange) => handleFilterChange({ dateRange })}
            onSearch={() => loadWithdrawRecords()}
            onReset={handleResetFilters}
          />
          <div className={styles.actions}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => loadWithdrawRecords()}
              loading={loading}
              style={{
                marginRight: 12,
                borderRadius: '8px',
                height: '32px',
              }}
              className="transition-all"
            >
              刷新
            </Button>
            {/* <Button
              icon={<ExportOutlined />}
              onClick={handleExport}
              loading={exportLoading}
              style={{
                borderRadius: '8px',
                height: '32px',
              }}
              className="transition-all"
            >
              导出
            </Button> */}
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={withdrawRecords}
          rowKey="user_withdraws_id"
          loading={{ spinning: loading, tip: '正在加载归集记录...' }}
          pagination={false}
          scroll={{ x: 1500 }}
          className="withdraw-table"
          style={{ marginTop: '16px' }}
          rowClassName={() => 'withdraw-table-row transition-all'}
        />

        <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
          <CustomPagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onChange={handlePaginationChange}
            showSizeChanger={true}
            showQuickJumper={true}
            showTotal={(total) => `共 ${total} 笔归集记录`}
          />
        </div>
      </Card>

      <WithdrawDetailDrawer
        visible={detailDrawerVisible}
        onClose={() => setDetailDrawerVisible(false)}
        record={selectedRecordForDetail}
        openInExplorer={handleOpenInExplorer}
      />
    </div>
  );
};

export default WithdrawRecordList;
