import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { getWithdraw } from '../../../services/api/withdraw/withdraw';
import type { WalletApiInternalModelEntityUserWithdraws } from '../../../services/api/model/walletApiInternalModelEntityUserWithdraws';
// import type { WalletApiApiWalletV1WithdrawRecord } from '../../../services/api/model/walletApiApiWalletV1WithdrawRecord'; // No longer directly used for main type
import type { GetWalletWithdrawRecordChain } from '../../../services/api/model/getWalletWithdrawRecordChain';
import type { GetWalletWithdrawRecordStatus } from '../../../services/api/model/getWalletWithdrawRecordStatus';
import type { GetWalletWithdrawRecordCoin } from '../../../services/api/model/getWalletWithdrawRecordCoin';
import type { GetWalletWithdrawRecordSortOrder } from '../../../services/api/model/getWalletWithdrawRecordSortOrder';

const MIN_LOADING_TIME_MS = 500;

// Extend WalletApiInternalModelEntityUserWithdraws and map fields
export interface WithdrawRecord {
  key?: string;
  user_withdraws_id?: number;
  token_fee_supplement_id?: number;
  name?: string; //币种名称
  chain?: string; // 链 (was chan in API)
  from_address?: string; // 付款地址 (was fromAddress in API)
  to_address?: string; // 归集地址 (was toAddress in API, and address in WalletApiApiWalletV1WithdrawRecord)
  amount?: string; // 申请归集金额 (API returns number, converted to string)
  handling_fee?: string; // 归集手续费 (API returns number, converted to string)
  actual_amount?: string; // 实际到账金额 (API returns number, converted to string)
  state?: number; // 状态
  tx_hash?: string; // 交易哈希
  error_message?: string; // 失败原因
  created_at?: string; // 创建时间
  checked_at?: string; // 审核时间
  processing_at?: string; // 处理时间
  completed_at?: string; // 完成时间
  updated_at?: string; // 更新时间
  retries?: number;
  energy_state?: number; // (was nergyState in API - assuming typo)
  gasfee_state?: number;
  eth_fee_mode?: number;
  eth_fee_max?: number;
  eth_gas_price?: number;
  eth_gas_limit?: number;
  trx_fee_max?: number;
  recharges_id?: number;
  gasfee_hash?: string;
  gasfee_amount?: string; // (API returns number, converted to string)
  energy_msg?: string;
}

export interface PaginationState {
  current: number;
  pageSize: number;
  total: number;
}

export interface FiltersState {
  address?: string;
  chain?: GetWalletWithdrawRecordChain;
  status?: GetWalletWithdrawRecordStatus;
  coin?: GetWalletWithdrawRecordCoin;
  dateRange?: string;
  sortField?: string;
  sortOrder?: GetWalletWithdrawRecordSortOrder;
}

export interface WithdrawStatistics {
  totalWithdrawCount: number;
  pendingCount: number;
  completedCount: number;
  rejectedCount: number;
  failedCount: number;
  totalAmount: string;
  totalFee: string;
}

const useWithdrawData = (
  initialPaginationState: PaginationState,
  initialFiltersState: FiltersState
) => {
  const [withdrawRecords, setWithdrawRecords] = useState<WithdrawRecord[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState<PaginationState>(initialPaginationState);
  const [searchText, setSearchText] = useState<string>('');
  const [filters, setFilters] = useState<FiltersState>(initialFiltersState);
  const [statistics, setStatistics] = useState<WithdrawStatistics>({
    totalWithdrawCount: 0,
    pendingCount: 0,
    completedCount: 0,
    rejectedCount: 0,
    failedCount: 0,
    totalAmount: '0',
    totalFee: '0',
  });

  // 加载归集记录统计数据
  const loadStatistics = useCallback(async () => {
    try {
      const { getWalletWithdrawRecordStatistic } = getWithdraw();
      const response = await getWalletWithdrawRecordStatistic();
      if (response) {
        setStatistics({
          totalWithdrawCount: response.total_withdraw_count || 0,
          pendingCount: response.pending_count || 0,
          completedCount: response.completed_count || 0,
          rejectedCount: response.rejected_count || 0,
          failedCount: response.failed_count || 0,
          totalAmount: response.total_usdt_amount || '0', // Using USDT amount as the main total
          totalFee: '0', // No fee information in the response, defaulting to '0'
        });
      }
    } catch (error) {
      console.error('加载归集统计数据失败:', error);
      message.error('加载归集统计数据失败');
    }
  }, []);

  // 加载归集记录
  const loadWithdrawRecords = useCallback(async () => {
    setLoading(true);
    const startTime = Date.now();
    try {
      const { getWalletWithdrawRecord } = getWithdraw();
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        address: searchText || filters.address,
        chain: filters.chain,
        status: filters.status,
        coin: filters.coin,
        date_range: filters.dateRange,
        sort_field: filters.sortField,
        sort_order: filters.sortOrder,
      };

      const response = await getWalletWithdrawRecord(params);

      if (response) {
        const records: WalletApiInternalModelEntityUserWithdraws[] = response.list || [];
        setWithdrawRecords(
          records.map((apiRecord) => {
            // Map API record (WalletApiInternalModelEntityUserWithdraws) to WithdrawRecord
            return {
              key: String((apiRecord as any).user_withdraws_id),
              user_withdraws_id: (apiRecord as any).user_withdraws_id,
              token_fee_supplement_id: (apiRecord as any).token_fee_supplement_id,
              name: (apiRecord as any).name,
              chain: (apiRecord as any).chan, // This mapping is correct as per interface comment
              from_address: (apiRecord as any).from_address,
              to_address: (apiRecord as any).to_address,
              amount: (apiRecord as any).amount !== undefined ? String((apiRecord as any).amount) : undefined,
              handling_fee: (apiRecord as any).handling_fee !== undefined ? String((apiRecord as any).handling_fee) : undefined,
              actual_amount: (apiRecord as any).actual_amount !== undefined ? String((apiRecord as any).actual_amount) : undefined,
              state: (apiRecord as any).state,
              tx_hash: (apiRecord as any).tx_hash,
              error_message: (apiRecord as any).error_message,
              created_at: (apiRecord as any).created_at,
              checked_at: (apiRecord as any).checked_at,
              processing_at: (apiRecord as any).processing_at,
              completed_at: (apiRecord as any).completed_at,
              updated_at: (apiRecord as any).updated_at,
              retries: (apiRecord as any).retries,
              energy_state: (apiRecord as any).nergy_state, // API sends nergy_state, WithdrawRecord interface expects energy_state
              gasfee_state: (apiRecord as any).gasfee_state,
              eth_fee_mode: (apiRecord as any).eth_fee_mode,
              eth_fee_max: (apiRecord as any).eth_fee_max,
              eth_gas_price: (apiRecord as any).eth_gas_price,
              eth_gas_limit: (apiRecord as any).eth_gas_limit,
              trx_fee_max: (apiRecord as any).trx_fee_max,
              recharges_id: (apiRecord as any).recharges_id,
              gasfee_hash: (apiRecord as any).gasfee_hash,
              gasfee_amount: (apiRecord as any).gasfee_amount !== undefined ? String((apiRecord as any).gasfee_amount) : undefined,
              energy_msg: (apiRecord as any).energy_msg,
            };
          })
        );

        // Update total without recreating the entire pagination object
        // This prevents the infinite loop caused by the dependency cycle
        if (response.page?.total !== pagination.total) {
          setPagination(prev => ({
            ...prev,
            total: response.page?.total || 0,
          }));
        }
      }
    } catch (error) {
      console.error('加载归集记录失败:', error);
      message.error('加载归集记录失败');
    } finally {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime < MIN_LOADING_TIME_MS) {
        setTimeout(() => setLoading(false), MIN_LOADING_TIME_MS - elapsedTime);
      } else {
        setLoading(false);
      }
    }
  }, [
    pagination, // Include the entire pagination object
    searchText,
    filters,
  ]);

  // 处理分页变化
  const handlePaginationChange = useCallback((page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }));
  }, []);

  // 处理筛选条件变化
  const handleFilterChange = useCallback((newFilters: Partial<FiltersState>) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      ...newFilters,
    }));
  }, []);

  // 重置筛选条件
  const handleResetFilters = useCallback(() => {
    setFilters(initialFiltersState);
    setSearchText('');
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  }, [initialFiltersState]);

  // 导出归集记录
  const handleExport = useCallback(async () => {
    setExportLoading(true);
    try {
      const { postWalletExportWithdrawRecord } = getWithdraw();
      const exportParams = {
        page: pagination.current,
        limit: pagination.pageSize,
        address: searchText || filters.address,
        chain: filters.chain,
        status: filters.status,
        coin: filters.coin,
        date_range: filters.dateRange,
      };

      const response = await postWalletExportWithdrawRecord(exportParams);

      if (response && response.success) {
        message.success('归集记录导出成功');
      } else {
        message.error('导出归集记录失败');
      }
    } catch (error) {
      console.error('导出归集记录失败:', error);
      message.error('导出归集记录失败');
    } finally {
      setExportLoading(false);
    }
  }, [searchText, filters, pagination]);

  // 在分页、筛选条件变化时加载数据
  useEffect(() => {
    loadWithdrawRecords();
  }, [loadWithdrawRecords]);

  // 在组件挂载时加载统计数据
  useEffect(() => {
    loadStatistics();
  }, [loadStatistics]);

  return {
    withdrawRecords,
    loading,
    pagination,
    searchText,
    filters,
    statistics,
    exportLoading,
    loadWithdrawRecords,
    handleFilterChange,
    handlePaginationChange,
    handleResetFilters,
    handleExport,
    setSearchText,
  };
};

export default useWithdrawData;
