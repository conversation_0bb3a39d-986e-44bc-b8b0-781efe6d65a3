.withdrawRecordList {
  width: 100%;
  animation: fadeIn 0.5s ease-out;
}

.tableCard {
  margin-top: 24px;
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  flex-wrap: wrap;
  padding: 8px 0;
}

.filterContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  max-width: 100%;
}

.searchInput {
  width: 220px;
}

.filterButton {
  margin-right: 8px;
}

.actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.statusTag {
  min-width: 70px;
  text-align: center;
  border-radius: 4px;
}

.amountText {
  font-weight: 500;
  color: #1890ff;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.statCard {
  margin-bottom: 16px;
}

.txHashLink {
  color: #1890ff;
  cursor: pointer;
  transition: all 0.3s;
}

.txHashLink:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.errorMessage {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.errorMessage:hover {
  white-space: normal;
  word-break: break-all;
}
