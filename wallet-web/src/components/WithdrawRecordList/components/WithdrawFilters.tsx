import React from 'react';
import { Input, <PERSON><PERSON>, DatePicker, Space } from 'antd';
import NativeFilterSelect from '../../common/NativeFilterSelect/NativeFilterSelect';
import { SearchOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import type { FiltersState } from '../hooks/useWithdrawData';
import { GetWalletWithdrawRecordChain } from '../../../services/api/model/getWalletWithdrawRecordChain';
import { GetWalletWithdrawRecordStatus } from '../../../services/api/model/getWalletWithdrawRecordStatus';
import { GetWalletWithdrawRecordCoin } from '../../../services/api/model/getWalletWithdrawRecordCoin';
import styles from '../WithdrawRecordList.module.css';

const { RangePicker } = DatePicker;

interface WithdrawFiltersProps {
  searchText: string;
  filters: FiltersState;
  onSearchTextChange: (text: string) => void;
  onFilterChange: (filters: Partial<FiltersState>) => void;
  onDateRangeChange: (dateRange: string) => void;
  onSearch: () => void;
  onReset: () => void;
}

const WithdrawFilters: React.FC<WithdrawFiltersProps> = ({
  searchText,
  filters,
  onSearchTextChange,
  onFilterChange,
  onDateRangeChange,
  onSearch,
  onReset,
}) => {
  // 处理日期范围变化
  const handleDateRangeChange = (dates: any, dateStrings: [string, string]) => {
    if (dates) {
      const dateRange = `${dateStrings[0]},${dateStrings[1]}`;
      onDateRangeChange(dateRange);
    } else {
      onDateRangeChange('');
    }
  };

  return (
    <div className={styles.filterContainer}>
      <Input
        placeholder="搜索地址"
        value={searchText}
        onChange={(e) => onSearchTextChange(e.target.value)}
        onPressEnter={onSearch}
        prefix={<SearchOutlined />}
        className={styles.searchInput}
        allowClear
      />

      <NativeFilterSelect
        placeholder="选择链"
        value={filters.chain}
        onChange={(event: React.ChangeEvent<HTMLSelectElement>) => onFilterChange({ chain: event.target.value as GetWalletWithdrawRecordChain | undefined })}
        options={Object.entries(GetWalletWithdrawRecordChain).map(([key, value]) => ({
          text: value,
          value: value,
        }))}
        style={{ width: 120 }}
      />

      <NativeFilterSelect
        placeholder="选择币种"
        value={filters.coin}
        onChange={(event: React.ChangeEvent<HTMLSelectElement>) => onFilterChange({ coin: event.target.value as GetWalletWithdrawRecordCoin | undefined })}
        options={Object.entries(GetWalletWithdrawRecordCoin).map(([key, value]) => ({
          text: value,
          value: value,
        }))}
        style={{ width: 120 }}
      />

      <NativeFilterSelect
        placeholder="选择状态"
        value={filters.status}
        onChange={(event: React.ChangeEvent<HTMLSelectElement>) => onFilterChange({ status: event.target.value as GetWalletWithdrawRecordStatus | undefined })}
        options={Object.entries(GetWalletWithdrawRecordStatus).map(([key, value]) => ({
          text:
            key === 'pending'
              ? '待审核'
              : key === 'processing'
              ? '处理中'
              : key === 'rejected'
              ? '已拒绝'
              : key === 'completed'
              ? '已完成'
              : key === 'failed'
              ? '失败'
              : value,
          value: value,
        }))}
        style={{ width: 120 }}
      />

      <RangePicker onChange={handleDateRangeChange} style={{ width: 240 }} />

      <Space>
        <Button
          type="primary"
          icon={<FilterOutlined />}
          onClick={onSearch}
          className={styles.filterButton}
        >
          筛选
        </Button>
        <Button icon={<ReloadOutlined />} onClick={onReset}>
          重置
        </Button>
      </Space>
    </div>
  );
};

export default WithdrawFilters;
