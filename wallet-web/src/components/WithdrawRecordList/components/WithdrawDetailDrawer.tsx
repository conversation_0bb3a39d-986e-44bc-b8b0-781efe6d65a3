import React from 'react';
import { Drawer, Descriptions, Typography, Tooltip, Button, Space } from 'antd'; // Removed Tag
import { CopyOutlined } from '@ant-design/icons';
import type { WithdrawRecord } from '../hooks/useWithdrawData';
import { formatDateTime, formatAmount } from '../../../utils/format'; // Changed formatDate to formatDateTime
import { getWithdrawStatusTag } from '../../../utils/statusUtils';
import { copyToClipboard } from '../../../utils/clipboardUtils';

const { Text, Paragraph } = Typography;

interface WithdrawDetailDrawerProps {
  visible: boolean;
  onClose: () => void;
  record: WithdrawRecord | null;
  openInExplorer: (type: string, hash: string, chain: string) => void;
}

// 辅助函数：渲染地址
const renderAddress = (address: string | undefined) => {
  if (!address) return '-';
  return (
    <Space>
      <Tooltip title={address}>
        <Text style={{ maxWidth: 250 }} ellipsis={{ tooltip: address }}>
          {address}
        </Text>
      </Tooltip>
      <Button icon={<CopyOutlined />} onClick={() => copyToClipboard(address || '')} size="small" type="text" />
    </Space>
  );
};


// 辅助函数：渲染哈希
const renderTxHash = (hash: string | undefined, chain: string | undefined, openInExplorerHandler: Function) => {
    if (!hash) return '-';
    return (
      <Space>
        <Tooltip title={`在区块链浏览器中查看: ${hash}`}>
          <Text
            style={{ maxWidth: 250, cursor: 'pointer', color: '#1890ff' }}
            ellipsis={{ tooltip: hash }}
            onClick={() => chain && openInExplorerHandler('tx', hash, chain)}
          >
            {hash}
          </Text>
        </Tooltip>
        <Button icon={<CopyOutlined />} onClick={() => copyToClipboard(hash || '')} size="small" type="text" />
      </Space>
    );
};

const WithdrawDetailDrawer: React.FC<WithdrawDetailDrawerProps> = ({ visible, onClose, record, openInExplorer }) => {
  if (!record) {
    return null;
  }

  return (
    <Drawer
      title={`归集详情 - ID: ${record.user_withdraws_id}`}
      placement="right"
      width={600}
      onClose={onClose}
      visible={visible}
    >
      <Descriptions bordered column={1} size="small">
        <Descriptions.Item label="记录ID">{record.user_withdraws_id}</Descriptions.Item>
        <Descriptions.Item label="币种">{record.name}</Descriptions.Item>
        <Descriptions.Item label="链">{record.chain}</Descriptions.Item>
        <Descriptions.Item label="付款地址">{renderAddress(record.from_address)}</Descriptions.Item>
        <Descriptions.Item label="归集地址">{renderAddress(record.to_address)}</Descriptions.Item>
        <Descriptions.Item label="归集金额">{formatAmount(record.amount ?? '0')}</Descriptions.Item>
        <Descriptions.Item label="手续费">{formatAmount(record.handling_fee ?? '0')}</Descriptions.Item>
        {/* <Descriptions.Item label="实际到账金额">{formatAmount(record.actual_amount ?? '0')}</Descriptions.Item> */}
        <Descriptions.Item label="状态">{getWithdrawStatusTag(record.state)}</Descriptions.Item>
        <Descriptions.Item label="交易哈希">
          {renderTxHash(record.tx_hash, record.chain, openInExplorer)}
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">{record.created_at ? formatDateTime(record.created_at) : '-'}</Descriptions.Item>
        {/* <Descriptions.Item label="审核时间">{record.checked_at ? formatDateTime(record.checked_at) : '-'}</Descriptions.Item> */}
        {/* <Descriptions.Item label="处理时间">{record.processing_at ? formatDateTime(record.processing_at) : '-'}</Descriptions.Item> */}
        <Descriptions.Item label="完成时间">{record.completed_at ? formatDateTime(record.completed_at) : '-'}</Descriptions.Item>
        <Descriptions.Item label="最后更新时间">{record.updated_at ? formatDateTime(record.updated_at) : '-'}</Descriptions.Item>
        <Descriptions.Item label="重试次数">{record.retries ?? '-'}</Descriptions.Item>

        {/* <Descriptions.Item label="Token费用补充ID">{record.token_fee_supplement_id ?? '-'}</Descriptions.Item> */}
        {/* <Descriptions.Item label="能量状态">{mapStateToText('energy', record.energy_state)}</Descriptions.Item> */}
        {/* <Descriptions.Item label="Gas费状态">{mapStateToText('gasfee', record.gasfee_state)}</Descriptions.Item> */}
        {/* <Descriptions.Item label="ETH费用模式">{mapStateToText('ethFeeMode', record.eth_fee_mode)}</Descriptions.Item> */}
        {/* <Descriptions.Item label="ETH最大费用">{formatAmount(record.eth_fee_max ?? 0)}</Descriptions.Item> */}
        {/* <Descriptions.Item label="ETH Gas价格">{record.eth_gas_price ?? '-'}</Descriptions.Item> */}
        {/* <Descriptions.Item label="ETH Gas限制">{record.eth_gas_limit ?? '-'}</Descriptions.Item> */}
        {/* <Descriptions.Item label="TRX最大费用">{formatAmount(record.trx_fee_max ?? 0)}</Descriptions.Item> */}
        <Descriptions.Item label="充值ID">{record.recharges_id ?? '-'}</Descriptions.Item>
        {/* <Descriptions.Item label="Gas费交易哈希">
            {renderTxHash(record.gasfee_hash, record.chain, openInExplorer)}
        </Descriptions.Item> */}
        <Descriptions.Item label="Gas费金额">{formatAmount(record.gasfee_amount ?? '0')}</Descriptions.Item>
        <Descriptions.Item label="能量信息">
          {record.energy_msg ? (
            <Paragraph copyable={{ tooltips: ['复制', '已复制'] }} style={{ maxWidth: '100%', margin: 0 }}>
              <Tooltip title={record.energy_msg}>
                <Text ellipsis>{record.energy_msg}</Text>
              </Tooltip>
            </Paragraph>
          ) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="错误信息">
          {record.error_message && record.error_message !== '{}' ? (
            <div>
              <Paragraph
                copyable={{ tooltips: ['复制', '已复制'] }}
                style={{ maxWidth: '100%', margin: 0 }}
              >
                {(() => {
                  try {
                    // 尝试解析JSON
                    const errorData = JSON.parse(record.error_message);

                    if (Array.isArray(errorData)) {
                      // 处理JSON数组格式
                      return (
                        <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                          {errorData.map((item, index) => (
                            <div key={index} style={{ marginBottom: '12px', padding: '8px', border: '1px solid #f0f0f0', borderRadius: '4px' }}>
                              {Object.entries(item).map(([key, value]) => (
                                <div key={key} style={{ marginBottom: '4px' }}>
                                  <Text strong>{key}: </Text>
                                  <Text type="danger">{String(value)}</Text>
                                </div>
                              ))}
                            </div>
                          ))}
                        </div>
                      );
                    } else {
                      // 处理JSON对象格式
                      return (
                        <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                          {Object.entries(errorData).map(([key, value]) => (
                            <div key={key} style={{ marginBottom: '8px' }}>
                              <Text strong>{key}: </Text>
                              <Text type="danger">{String(value)}</Text>
                            </div>
                          ))}
                        </div>
                      );
                    }
                  } catch (e) {
                    // 非JSON格式时，改进显示方式
                    return (
                      <Tooltip title={record.error_message}>
                        <div style={{ maxHeight: '200px', overflowY: 'auto', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                          <Text type="danger">{record.error_message}</Text>
                        </div>
                      </Tooltip>
                    );
                  }
                })()}
              </Paragraph>
            </div>
          ) : '-'}
        </Descriptions.Item>
      </Descriptions>
    </Drawer>
  );
};

export default WithdrawDetailDrawer;
