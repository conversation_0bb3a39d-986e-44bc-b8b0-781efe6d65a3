import React from 'react';
import { Typo<PERSON>, Tooltip, Button, Space } from 'antd'; // Removed Tag
import { EyeOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { WithdrawRecord } from '../hooks/useWithdrawData';
import styles from '../WithdrawRecordList.module.css';
import { formatAmount, formatDateTime } from '../../../utils/format';
import { getWithdrawStatusTag } from '../../../utils/statusUtils';

const { Text } = Typography;

interface ColumnProps {
  openInExplorer: (type: string, hash: string, chain: string) => void;
  onViewDetails: (record: WithdrawRecord) => void; // Callback to open details drawer
}

export const getWithdrawTableColumns = ({ openInExplorer, onViewDetails }: ColumnProps): ColumnsType<WithdrawRecord> => [
  {
    title: 'ID',
    dataIndex: 'user_withdraws_id',
    key: 'user_withdraws_id',
    width: 80,
  },
  {
    title: '币种',
    dataIndex: 'name',
    key: 'name',
    width: 100,
    fixed: 'left',
  },
  {
    title: '链',
    dataIndex: 'chain',
    key: 'chain',
    width: 100,
  },
  {
    title: '付款地址',
    dataIndex: 'from_address',
    key: 'from_address',
    width: 200,
    ellipsis: true,
    render: (address?: string) => (
      address ? <Tooltip title={address}><span>{address}</span></Tooltip> : '-'
    ),
  },
  {
    title: '归集地址',
    dataIndex: 'to_address',
    key: 'to_address',
    width: 200,
    ellipsis: true,
    render: (address?: string) => (
      address ? <Tooltip title={address}><span>{address}</span></Tooltip> : '-'
    ),
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    align: 'right',
    render: (value?: number | string | null) => (
      <Text className={styles.amountText}>{formatAmount(value, 6)}</Text>
    ),
  },
  {
    title: '手续费',
    dataIndex: 'handling_fee',
    key: 'handling_fee',
    width: 120,
    align: 'right',
    render: (value?: number | string | null) => (
      <Text className={styles.amountText}>{formatAmount(value, 8)}</Text>
    ),
  },
  //  {
  //   title: '实际到账',
  //   dataIndex: 'actual_amount',
  //   key: 'actual_amount',
  //   width: 120,
  //   align: 'right',
  //   render: (amount?: string) => (
  //     <Text strong style={{ color: '#52c41a' }}>{amount ? formatAmount(amount, 6) : '--'}</Text>
  //   ),
  // },
  //retries重试次数
  
  {
    title: '交易哈希',
    dataIndex: 'tx_hash',
    key: 'tx_hash',
    width: 180,
    ellipsis: true,
    render: (hash?: string, record?: WithdrawRecord) => {
      if (!hash || !record) return <Text type="secondary">-</Text>;
      
      return (
        <Tooltip title={`查看交易: ${hash}`}>
          <Text
            className={styles.txHashLink}
            onClick={() => record.chain && openInExplorer('tx', hash, record.chain)}
            style={{cursor: 'pointer'}}
          >
            {hash.length > 16 ? `${hash.substring(0, 6)}...${hash.substring(hash.length - 6)}` : hash}
          </Text>
        </Tooltip>
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
    width: 110,
    align: 'center',
    render: (state?: number) => getWithdrawStatusTag(state),
  },
  //重试次数
  {
    title: '重试次数',
    dataIndex: 'retries',
    key: 'retries',
    width: 100,
    align: 'center',
  },
  // {
  //   title: 'Gas费哈希',
  //   dataIndex: 'gasfee_hash',
  //   key: 'gasfee_hash',
  //   width: 180,
  //   ellipsis: true,
  //   render: (hash?: string, record?: WithdrawRecord) => {
  //     if (!hash || !record) return <Text type="secondary">-</Text>;
  //     return (
  //       <Tooltip title={`查看Gas费交易: ${hash}`}>
  //         <Text
  //           className={styles.txHashLink}
  //           onClick={() => record.chain && openInExplorer('tx', hash, record.chain)}
  //           style={{cursor: 'pointer'}}
  //         >
  //            {hash.length > 16 ? `${hash.substring(0, 6)}...${hash.substring(hash.length - 6)}` : hash}
  //         </Text>
  //       </Tooltip>
  //     );
  //   },
  // },
  {
    title: '失败原因',
    dataIndex: 'error_message',
    key: 'error_message',
    width: 200,
    ellipsis: true,
    render: (message?: string) => {
      if (!message || message === '{}') return <Text type="secondary">-</Text>;
      
      return (
        <Tooltip title={message}>
          <Text type="danger" className={styles.errorMessage} ellipsis>
            {message}
          </Text>
        </Tooltip>
      );
    },
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 170,
    render: (time?: string) => (time ? formatDateTime(time) : '-'),
  },
  {
    title: '完成时间',
    dataIndex: 'completed_at',
    key: 'completed_at',
    width: 170,
    render: (time?: string) => (time ? formatDateTime(time) : '-'),
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 100,
    align: 'center',
    render: (_, record: WithdrawRecord) => (
      <Space>
        <Tooltip title="查看详情">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => onViewDetails(record)}
          />
        </Tooltip>
      </Space>
    ),
  },
];
