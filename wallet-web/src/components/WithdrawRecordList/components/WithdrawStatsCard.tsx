import React from 'react';
import { Card, Row, Col, Statistic, Tooltip } from 'antd';
import {
  WalletOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PieChartOutlined,
} from '@ant-design/icons';
import type { WithdrawStatistics } from '../hooks/useWithdrawData';
import styles from '../WithdrawRecordList.module.css';

// Removed unused Text import

interface WithdrawStatsCardProps {
  statistics: WithdrawStatistics;
}

const WithdrawStatsCard: React.FC<WithdrawStatsCardProps> = ({ statistics }) => {
  return (
    <Card
      className={`${styles.statCard} hover-shadow`}
      style={{
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
        border: 'none',
        overflow: 'hidden',
      }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <PieChartOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>归集统计数据</span>
        </div>
      }
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '24px',
        }
      }}
    >
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Statistic
            title="总归集次数"
            value={statistics.totalWithdrawCount}
            prefix={<WalletOutlined style={{ color: '#1890ff' }} />}
          />
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Statistic
            title="待审核"
            value={statistics.pendingCount}
            prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
          />
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Statistic
            title="已完成"
            value={statistics.completedCount}
            prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          />
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Statistic
            title="已拒绝"
            value={statistics.rejectedCount}
            prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
          />
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Statistic
            title="失败"
            value={statistics.failedCount}
            prefix={<ExclamationCircleOutlined style={{ color: '#ff7a45' }} />}
          />
        </Col>
        {/* <Col xs={24} sm={12} md={8} lg={4}>
          <Tooltip title="归集总金额 / 手续费总额">
            <Statistic
              title="归集总额 / 手续费"
              value={`${statistics.totalAmount} / ${statistics.totalFee}`}
              prefix={<WalletOutlined style={{ color: '#1890ff' }} />}
            />
          </Tooltip>
        </Col> */}
      </Row>
    </Card>
  );
};

export default WithdrawStatsCard;
