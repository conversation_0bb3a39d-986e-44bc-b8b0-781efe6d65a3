import React from 'react';
import { List, Avatar, Tag, Typography, Empty } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { Transaction } from '../../services/walletService';
import styles from './TransactionList.module.css';

const { Text } = Typography;

interface TransactionListProps {
  transactions: Transaction[];
  loading?: boolean;
}

const TransactionList: React.FC<TransactionListProps> = ({ transactions, loading = false }) => {
  if (!transactions.length && !loading) {
    return <Empty description="暂无交易记录" />;
  }

  return (
    <List
      className={styles.transactionList}
      itemLayout="horizontal"
      loading={loading}
      dataSource={transactions}
      renderItem={(item) => {
        const isIncome = item.type === 'deposit';

        return (
          <List.Item className={styles.transactionItem}>
            <List.Item.Meta
              avatar={
                <Avatar
                  icon={isIncome ? <ArrowDownOutlined /> : <ArrowUpOutlined />}
                  className={isIncome ? styles.incomeAvatar : styles.expenseAvatar}
                />
              }
              title={item.description}
              description={new Date(item.timestamp).toLocaleString()}
            />
            <div className={styles.amountContainer}>
              <Text className={isIncome ? styles.incomeAmount : styles.expenseAmount} strong>
                {isIncome ? '+' : '-'} {item.amount} {item.currency}
              </Text>
              <Tag color={getStatusColor(item.status)}>{getStatusText(item.status)}</Tag>
            </div>
          </List.Item>
        );
      }}
    />
  );
};

// 获取交易状态对应的颜色
const getStatusColor = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'green';
    case 'pending':
      return 'orange';
    case 'failed':
      return 'red';
    default:
      return 'blue';
  }
};

// 获取交易状态对应的文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'completed':
      return '已完成';
    case 'pending':
      return '处理中';
    case 'failed':
      return '失败';
    default:
      return '未知';
  }
};

export default TransactionList;
