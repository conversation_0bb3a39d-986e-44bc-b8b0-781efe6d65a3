import React, { useMemo } from 'react';
import { Table, Card } from 'antd';
import { useAppSelector } from '../../hooks/useAppSelector'; // Kept for loading state from Redux if still used
import GoogleAuthWithPasswordModal from '../common/GoogleAuthWithPasswordModal';
import styles from './AddressList.module.css';
import { useAddressListData } from './hooks/useAddressListData';
import { useAddressModals } from './hooks/useAddressModals';
import AddressStatsCard from './components/AddressStatsCard';
import AddressTableToolbar from './components/AddressTableToolbar';
import { getAddressTableColumns } from './components/AddressTableColumns';
import ExportAddressModal from './components/ExportAddressModal';
import GenerateAddressModal from './components/GenerateAddressModal';
import { LinkOutlined } from '@ant-design/icons';

const AddressList: React.FC = () => {
  const {
    currentPage,
    pageSize,
    searchText,
    setSearchText,
    filters,
    setFilters,
    sorter,
    addresses,
    totalAddresses,
    statisticData,
    loading: dataLoading, // Renamed to avoid conflict if useAppSelector also has 'loading'
    refreshAllData,
    refreshBalance,
    bindAddress,
    handleSearch,
    handleReset,
    handleTableChange,
  } = useAddressListData();

  // If global loading state is still needed from Redux, otherwise dataLoading from hook is primary
  const { loading: globalLoading } = useAppSelector((state) => state.wallet);
  const isLoading = dataLoading || globalLoading;


  const {
    exportModalVisible,
    setExportModalVisible,
    exportType,
    setExportType,
    exportAuthModalVisible,
    setExportAuthModalVisible,
    generateModalVisible,
    setGenerateModalVisible,
    generateForm,
    authModalVisible,
    setAuthModalVisible,
    taskId,
    handleOpenGenerateModal,
    handleCountConfirm,
    handleAuthConfirm,
    handleExport,
    executeExport,
    handleExportAuthConfirm,
  } = useAddressModals({ refreshAllData });


  const columns = useMemo(
    () => getAddressTableColumns(sorter, refreshBalance, bindAddress),
    [sorter, refreshBalance, bindAddress],
  );

  const paginationConfig = useMemo(
    () => ({
      current: currentPage,
      pageSize: pageSize,
      total: totalAddresses,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 个地址`,
    }),
    [currentPage, pageSize, totalAddresses],
  );

  return (
    <div className={`${styles.addressList} fade-in`}>
      <AddressStatsCard statisticData={statisticData} />

      <Card
        className={`${styles.tableCard} hover-shadow`}
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
          border: 'none',
          overflow: 'hidden',
          marginTop: '24px',
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <LinkOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontSize: '16px', fontWeight: 600 }}>地址列表</span>
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #f0f0f0',
            padding: '16px 24px',
          },
          body: {
            padding: '24px',
          }
        }}
      >
        <AddressTableToolbar
          searchText={searchText}
          setSearchText={setSearchText}
          filters={filters}
          setFilters={setFilters}
          loading={isLoading}
          handleSearch={handleSearch}
          handleReset={handleReset}
          handleOpenGenerateModal={handleOpenGenerateModal}
          refreshAllData={refreshAllData}
          handleExport={handleExport}
        />

        <Table
          columns={columns}
          dataSource={addresses}
          rowKey={(record) => String(record.id)}
          loading={{ spinning: isLoading, tip: '正在加载地址数据...' }}
          onChange={handleTableChange}
          pagination={paginationConfig}
          className="address-table"
          style={{ marginTop: '16px' }}
          rowClassName={() => 'address-table-row transition-all'}
        />
      </Card>

      <ExportAddressModal
        visible={exportModalVisible}
        onOk={executeExport}
        onCancel={() => setExportModalVisible(false)}
        exportType={exportType}
        setExportType={setExportType}
      />

      <GenerateAddressModal
        visible={generateModalVisible}
        onOk={handleCountConfirm}
        onCancel={() => setGenerateModalVisible(false)}
        form={generateForm}
        taskId={taskId}
      />

      <GoogleAuthWithPasswordModal
        visible={authModalVisible}
        onConfirm={handleAuthConfirm}
        onCancel={() => setAuthModalVisible(false)}
        title="生成地址验证"
        promptText="请完成安全验证以生成新地址"
        confirmText="确认生成"
      />

      <GoogleAuthWithPasswordModal
        visible={exportAuthModalVisible}
        onConfirm={handleExportAuthConfirm}
        onCancel={() => setExportAuthModalVisible(false)}
        title="导出地址验证"
        promptText="请完成安全验证以导出地址"
        confirmText="确认导出"
      />
    </div>
  );
};

export default AddressList;
