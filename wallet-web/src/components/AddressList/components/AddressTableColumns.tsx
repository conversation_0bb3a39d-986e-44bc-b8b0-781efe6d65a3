// src/components/AddressList/components/AddressTableColumns.tsx
import { Space, Button, Typography, Tag, Dropdown, Image, message } from 'antd';
import { CopyOutlined, EllipsisOutlined, QrcodeOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { AddressItem, SorterState } from '../types';
import ethIcon from '../../../assets/images/eth.svg';
import usdtIcon from '../../../assets/images/usdt.svg';
import trxIcon from '../../../assets/images/tron.svg';
import { showAddressQRCode } from './AddressQRCodeModal';
import { copyToClipboard } from '../../../utils/clipboardUtils';

const { Text } = Typography;

export const getAddressTableColumns = (
  sorter: SorterState,
  refreshBalanceFunc: (address: string) => void,
  bindAddressFunc?: (address: string, type: string) => void,
): ColumnsType<AddressItem> => [
  {
    title: '地址',
    dataIndex: 'address',
    // onCellClick: (record: AddressItem) => ({

    // }),
    key: 'address',
    render: (text: string | undefined, record: AddressItem) => (
      <Space>
        <Text >
          {text || ''}
        </Text>
        <Button
          type="text"
          icon={<QrcodeOutlined />}
          onClick={() => {
            showAddressQRCode({
              address: text,
              networkType: record.type,
            });
          }}
          size="small"
        />
        <Text type="secondary" style={{ fontSize: 12 }}>
          <Button type="text" icon={<CopyOutlined />} onClick={() => copyToClipboard(text || '')} size="small" />
        </Text>
      </Space>
    ),
  },
  {
    title: '名称',
    dataIndex: 'label',
    key: 'label',
    render: (text: string, record: AddressItem) => (
      <Space>
        <Text strong>{text || `地址 ${record.id}`}</Text>
        {record.tags?.map((tag) => (
          <Tag
            key={tag}
            color={tag === '默认' ? 'blue' : tag === '常用' ? 'green' : tag === '商业' ? 'orange' : 'purple'}
          >
            {tag}
          </Tag>
        ))}
      </Space>
    ),
  },
  {
    title: '所属钱包',
    dataIndex: 'walletName',
    key: 'walletName',
    render: (text: string) => <Text>{text}</Text>,
  },
  // {
  //   title: '绑定标签',
  //   dataIndex: 'label',
  //   key: 'label', // Consider if a different key is needed if 'label' is used for name
  //   render: (text: string) => {
  //     if (text) {
  //       return <Text>{text}</Text>;
  //     } else {
  //       return <Text style={{ color: 'red' }}>未绑定</Text>;
  //     }
  //   },
  // },
  {
    title: '网络',
    dataIndex: 'network',
    key: 'network',
    render: (text: string) => {
      let color = '';
      switch (text) {
        case 'ETH':
          color = 'blue';
          break;
        case 'TRON':
          color = 'red';
          break;
        default:
          color = 'default';
      }
      return <Tag color={color}>{text}</Tag>;
    },
  },
  {
    title: '链余额',
    dataIndex: 'chain_coin_balance',
    key: 'chain_coin_balance',
    render: (text: string, record: AddressItem) => (
      <Space>
        <Text strong>{text || '0'}</Text>
        <Image
          src={record.type === 'ETH' ? ethIcon : trxIcon}
          preview={false}
          width={16}
          style={{ verticalAlign: 'middle' }}
        />
      </Space>
    ),
    sorter: true, // Enable sorting for this column
    sortOrder: sorter.field === 'chain_coin_balance' ? sorter.order : undefined,
  },
  {
    title: 'USDT余额',
    dataIndex: 'chain_usdt_balance',
    key: 'chain_usdt_balance',
    render: (text: string) => (
      <Space>
        <Text strong>{text || '0'}</Text>
        <Image src={usdtIcon} preview={false} width={16} style={{ verticalAlign: 'middle' }} />
      </Space>
    ),
    sorter: true, // Enable sorting for this column
    sortOrder: sorter.field === 'chain_usdt_balance' ? sorter.order : undefined,
  },
  {
    title: '是否已绑定',
    dataIndex: 'bind_status',
    key: 'bind_status',
    render: (bindStatus: number) => <span>{bindStatus === 1 ? '✅' : '❌'}</span>,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: number) => (
      <Tag color={status === 2 ? 'success' : 'error'}>{status === 2 ? '活跃' : '非活跃'}</Tag>
    ),
  },
  {
    title: '绑定时间',
    dataIndex: 'bind_at',
    key: 'bind_at',
    render: (text: string) => {
      if (text) {
        try {
          return new Date(text).toLocaleString();
        } catch (e) { return '-'; }
      } else {
        return '-';
      }
    },
  },
  {
    title: '刷新余额时间',
    dataIndex: 'last_query_at',
    key: 'last_query_at',
    render: (text: string) => {
      if (text) {
         try {
          return new Date(text).toLocaleString();
        } catch (e) { return '-'; }
      } else {
        return '-';
      }
    },
  },
  {
    title: '创建时间',
    dataIndex: 'create_at',
    key: 'create_at',
    render: (text: string) => {
        try {
          return new Date(text).toLocaleString();
        } catch (e) { return '-'; }
    },
    sorter: true,
    sortOrder: sorter.field === 'create_at' ? sorter.order : undefined,
  },
  {
    title: '最后更新时间',
    dataIndex: 'update_at',
    key: 'update_at',
    render: (text: string) => {
        if (text) {
            try {
                return new Date(text).toLocaleString();
            } catch (e) { return '-'; }
        }
        return '-';
    },
  },
  {
    title: '操作',
    key: 'action',
    render: (_: any, record: AddressItem) => (
      <Space>
        <Dropdown
          menu={{
            items: [
              {
                key: 'explorer',
                label: '在区块浏览器中查看',
                onClick: () => {
                  // Basic example, ideally use a utility for explorer links
                  const explorerUrl = record.type === 'ETH'
                    ? `https://etherscan.io/address/${record.address}`
                    : record.type === 'TRON'
                    ? `https://tronscan.org/#/address/${record.address}`
                    : '';
                  if (explorerUrl) {
                    window.open(explorerUrl, '_blank');
                  } else {
                    message.warning('不支持的网络类型');
                  }
                }
              },
              { key: 'refresh', label: '刷新余额', onClick: () => refreshBalanceFunc(record.address || '') },
              record.bind_status === 0 && bindAddressFunc
                ? {
                    key: 'bind',
                    label: '标记为已绑定',
                    onClick: () => bindAddressFunc(record.address || '', record.type || ''),
                  }
                : null,
              // record.bind_status === 0
              //   ? {
              //       key: 'add_to_withdraw_plan',
              //       label: '添加到归集计划',
              //       onClick: () => {
              //         addAddressToWithdrawPlan(record.address || '', record.type || '');
              //         // 如果绑定函数存在，也调用它来更新绑定状态
              //         if (bindAddressFunc) {
              //           bindAddressFunc(record.address || '', record.type || '');
              //         }
              //       },
              //     }
              //   : null,
            ],
          }}
        >
          <Button type="text" icon={<EllipsisOutlined />} />
        </Dropdown>
      </Space>
    ),
  },
];
