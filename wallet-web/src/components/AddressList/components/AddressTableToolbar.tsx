// src/components/AddressList/components/AddressTableToolbar.tsx
import React from 'react';
import { Input, Button } from 'antd';
import { SearchOutlined, FilterOutlined, PlusOutlined, ReloadOutlined, ExportOutlined } from '@ant-design/icons';
import NativeFilterSelect from '../../common/NativeFilterSelect/NativeFilterSelect'; // 新增导入
import styles from '../AddressList.module.css'; // 假设样式可以复用
import type { FilterState } from '../types';

const networkOptions = [
  { value: 'ETH', text: 'ETH' },
  { value: 'TRON', text: 'TRON' },
];

interface AddressTableToolbarProps {
  searchText: string;
  setSearchText: (text: string) => void;
  filters: FilterState;
  setFilters: (filters: FilterState) => void;
  loading: boolean;
  handleSearch: () => void;
  handleReset: () => void;
  handleOpenGenerateModal: () => void;
  refreshAllData: () => void;
  handleExport: () => void;
}

const AddressTableToolbar: React.FC<AddressTableToolbarProps> = ({
  searchText,
  setSearchText,
  filters,
  setFilters,
  loading,
  handleSearch,
  handleReset,
  handleOpenGenerateModal,
  refreshAllData,
  handleExport,
}) => {
  return (
    <div className={`${styles.tableHeader} fade-in`}>
      <div className={styles.searchFilters}>
        <Input
          placeholder="搜索地址或名称"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onPressEnter={handleSearch}
          prefix={<SearchOutlined style={{ color: '#1890ff' }} />}
          style={{
            width: 250,
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
          }}
          className="hover-shadow transition-all"
        />
        <div style={{ display: 'inline-block', width: 120, marginLeft: 12, marginRight: 12 }}>
          <NativeFilterSelect
            options={networkOptions}
            value={filters.network || ''}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
              const newNetwork = e.target.value || undefined;
              setFilters({ ...filters, network: newNetwork });
            }}
            placeholder="全部网络" // NativeFilterSelect 使用 placeholder prop
            className="hover-shadow" // 可以传递 className
            style={{ // 可以传递 style
              width: '100%',
              // padding 和 border 由 NativeFilterSelect 内部样式处理，如果需要覆盖则保留
              // borderRadius: '8px', // 通常由组件内部或其 CSS 模块处理
              height: '32px', // 确保高度一致
              // boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)', // 通常由组件内部或其 CSS 模块处理
              // transition: 'all 0.3s ease', // 通常由组件内部或其 CSS 模块处理
            }}
          />
        </div>
        <Button
          type="primary"
          onClick={handleSearch}
          icon={<FilterOutlined />}
          style={{
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)',
            height: '32px',
          }}
          className="hover-shadow transition-all"
        >
          筛选
        </Button>
        <Button
          onClick={handleReset}
          style={{
            borderRadius: '8px',
            height: '32px',
          }}
          className="transition-all"
        >
          重置
        </Button>
      </div>
      <div className={styles.actions}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          style={{
            marginRight: 12,
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)',
            height: '32px',
          }}
          onClick={handleOpenGenerateModal}
          className="hover-shadow transition-all"
        >
          生成地址
        </Button>
        <Button
          icon={<ReloadOutlined />}
          onClick={refreshAllData}
          loading={loading}
          style={{
            marginRight: 12,
            borderRadius: '8px',
            height: '32px',
          }}
          className="transition-all"
        >
          刷新
        </Button>
        <Button
          icon={<ExportOutlined />}
          onClick={handleExport}
          style={{
            borderRadius: '8px',
            height: '32px',
          }}
          className="transition-all"
        >
          导出
        </Button>
      </div>
    </div>
  );
};

export default AddressTableToolbar;