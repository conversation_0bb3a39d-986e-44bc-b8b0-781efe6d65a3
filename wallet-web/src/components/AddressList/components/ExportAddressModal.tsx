// src/components/AddressList/components/ExportAddressModal.tsx
import React from 'react';
import { Modal, Radio, Space, Typography } from 'antd';
import type { ExportType } from '../types';

const { Text } = Typography;

interface ExportAddressModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  exportType: ExportType;
  setExportType: (type: ExportType) => void;
}

const ExportAddressModal: React.FC<ExportAddressModalProps> = ({
  visible,
  onOk,
  onCancel,
  exportType,
  setExportType,
}) => {
  return (
    <Modal
      title="导出地址列表"
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>
        <p>请选择要导出的数据范围：</p>
        <Radio.Group value={exportType} onChange={(e) => setExportType(e.target.value as ExportType)}>
          <Space direction="vertical">
            <Radio value="all">所有地址</Radio>
            <Radio value="unbound">
              <Space>
                <span>未绑定地址</span>
                {exportType === 'unbound' && <Text type="warning">导出此项会标记为已绑定</Text>}
              </Space>
            </Radio>
          </Space>
        </Radio.Group>
      </div>
    </Modal>
  );
};

export default ExportAddressModal;