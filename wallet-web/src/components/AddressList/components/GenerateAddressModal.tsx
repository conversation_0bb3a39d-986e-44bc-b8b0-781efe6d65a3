// src/components/AddressList/components/GenerateAddressModal.tsx
import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, InputNumber, Progress, Typography, Result, message } from 'antd';
import { CheckCircleFilled, ExclamationCircleFilled } from '@ant-design/icons';
import type { FormInstance } from 'antd/es/form';
import { getAddress } from '../../../services/api/address/address';

const { Text, Title, Paragraph } = Typography;
const { getWalletAddressTaskProgress } = getAddress();

interface GenerateAddressModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  form: FormInstance;
  taskId?: string; // 新增：任务ID，从GoogleAuthWithPasswordModal验证成功后获取
}

const GenerateAddressModal: React.FC<GenerateAddressModalProps> = ({
  visible,
  onOk,
  onCancel,
  form,
  taskId
}) => {
  // 状态管理
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isFailed, setIsFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [processedRows, setProcessedRows] = useState(0);
  const [totalRows, setTotalRows] = useState(0);

  // 轮询间隔（毫秒）
  const POLL_INTERVAL = 1000;

  // 使用ref来存储轮询定时器
  const pollTimerRef = useRef<ReturnType<typeof setTimeout>>();

  // 当任务ID变化时，开始轮询任务进度
  useEffect(() => {
    if (taskId) {
      setIsGenerating(true);
      startPollingTaskProgress(taskId);
    }
  }, [taskId]);

  // 重置状态
  useEffect(() => {
    if (!visible) {
      setIsGenerating(false);
      setProgress(0);
      setIsCompleted(false);
      setIsFailed(false);
      setErrorMessage('');
      setProcessedRows(0);
      setTotalRows(0);

      // 清除轮询定时器
      if (pollTimerRef.current) {
        window.clearTimeout(pollTimerRef.current);
        pollTimerRef.current = undefined;
      }
    }
  }, [visible]);

  // 轮询任务进度
  const startPollingTaskProgress = (taskId: string) => {
    const pollTaskProgress = async () => {
      try {
        const response = await getWalletAddressTaskProgress({ task_id: taskId });
        const data = response;

        // 更新进度信息
        if (data.progress !== undefined) {
          setProgress(data.progress);
        }

        if (data.processed_rows !== undefined) {
          setProcessedRows(data.processed_rows);
        }

        if (data.total_rows !== undefined) {
          setTotalRows(data.total_rows);
        }

        // 根据任务状态更新UI
        if (data.status === 'completed') {
          setIsCompleted(true);
          setIsGenerating(false);
          // 停止轮询
          if (pollTimerRef.current) {
            window.clearTimeout(pollTimerRef.current);
            pollTimerRef.current = undefined;
          }
        } else if (data.status === 'failed') {
          setIsFailed(true);
          setIsGenerating(false);
          setErrorMessage(data.error_message || '生成地址失败');
          // 停止轮询
          if (pollTimerRef.current) {
            window.clearTimeout(pollTimerRef.current);
            pollTimerRef.current = undefined;
          }
        } else if (data.status === 'processing' || data.status === 'pending') {
          // 继续轮询
          pollTimerRef.current = setTimeout(pollTaskProgress, POLL_INTERVAL);
        }
      } catch (error: any) {
        console.error('获取任务进度失败:', error);
        message.error('获取任务进度失败: ' + (error.message || '未知错误'));

        // 如果API调用失败，我们仍然继续轮询，但增加轮询间隔
        pollTimerRef.current = setTimeout(pollTaskProgress, POLL_INTERVAL * 2);
      }
    };

    // 开始第一次轮询
    pollTaskProgress();
  };

  // 处理提交 - 现在只是关闭模态框，实际提交在父组件中处理
  const handleSubmit = () => {
    onOk();
  };

  // 处理完成后的关闭 - 直接调用onOk，它会在useAddressModals中处理关闭逻辑
  const handleComplete = () => {
    onOk();
  };

  // 处理失败后的关闭 - 直接调用onOk，它会在useAddressModals中处理关闭逻辑
  const handleFailed = () => {
    onOk();
  };

  // 渲染不同状态的内容
  const renderContent = () => {
    if (isCompleted) {
      return (
        <Result
          icon={<CheckCircleFilled style={{ color: '#52c41a', fontSize: 72 }} />}
          title={<Title level={4}>地址生成成功！</Title>}
          subTitle={<Paragraph style={{ fontSize: 16, color: '#666' }}>您的地址已成功生成，可以在地址列表中查看</Paragraph>}
        />
      );
    }

    if (isFailed) {
      return (
        <Result
          icon={<ExclamationCircleFilled style={{ color: '#ff4d4f', fontSize: 72 }} />}
          title={<Title level={4}>地址生成失败</Title>}
          subTitle={
            <Paragraph style={{ fontSize: 16, color: '#666' }}>
              {errorMessage || '生成地址过程中发生错误，请稍后重试'}
            </Paragraph>
          }
        />
      );
    }

    if (isGenerating) {
      return (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Title level={4} style={{ marginBottom: 24 }}>
            正在生成地址
          </Title>
          <Progress
            percent={progress}
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            style={{ marginBottom: 16 }}
          />
          {totalRows > 0 && (
            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">
                已处理: {processedRows} / {totalRows}
              </Text>
            </div>
          )}
          <Text type="secondary">请耐心等待，地址生成过程需要一些时间...</Text>
        </div>
      );
    }

    return (
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          name="count"
          label="生成数量"
          initialValue={10000}
          rules={[
            { required: true, message: '请输入生成数量' },
            { type: 'number', min: 1, max: 10000, message: '生成数量必须在1-10000之间' },
          ]}
        >
          <InputNumber min={1} max={10000} style={{ width: '100%' }} />
        </Form.Item>
      </Form>
    );
  };

  return (
    <Modal
      title="生成地址"
      open={visible}
      onOk={
        isCompleted
          ? handleComplete
          : isFailed
            ? handleFailed
            : isGenerating
              ? undefined
              : handleSubmit
      }
      onCancel={isGenerating ? undefined : onCancel}
      okText={
        isCompleted
          ? '完成'
          : isFailed
            ? '关闭'
            : isGenerating
              ? '生成中...'
              : '确认'
      }
      cancelText="取消"
      okButtonProps={{
        disabled: isGenerating,
        style: isCompleted
          ? { backgroundColor: '#52c41a', borderColor: '#52c41a' }
          : isFailed
            ? { backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }
            : undefined,
      }}
      cancelButtonProps={{
        style: { display: isGenerating || isCompleted || isFailed ? 'none' : 'inline-block' },
      }}
      maskClosable={!isGenerating}
      closable={!isGenerating}
    >
      {renderContent()}
    </Modal>
  );
};

export default GenerateAddressModal;
