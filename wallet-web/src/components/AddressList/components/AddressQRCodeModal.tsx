// src/components/AddressList/components/AddressQRCodeModal.tsx
import React from 'react';
import ReactDOM from 'react-dom';
import { Modal, Typography, Space, Button, Card } from 'antd';
import { CopyOutlined, QrcodeOutlined } from '@ant-design/icons';
import QRCode from 'react-qr-code';
import { copyToClipboard } from '../../../utils/clipboardUtils';

const { Title, Text, Paragraph } = Typography;

interface AddressQRCodeModalProps {
  visible: boolean;
  onClose: () => void;
  address: string | undefined;
  networkType?: string;
}

// 函数组件，用于渲染弹窗
const AddressQRCodeModal: React.FC<AddressQRCodeModalProps> = ({
  visible,
  onClose,
  address,
  networkType,
}) => {
  const handleCopyAddress = () => {
    if (address) {
      copyToClipboard(address);
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <QrcodeOutlined style={{ color: '#1890ff' }} />
          <span>地址二维码</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="copy" icon={<CopyOutlined />} onClick={handleCopyAddress}>
          复制地址
        </Button>,
        <Button key="close" type="primary" onClick={onClose}>
          关闭
        </Button>,
      ]}
      width={400}
      centered
      destroyOnClose
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        {address ? (
          <>
            <Card
              style={{
                marginBottom: 20,
                border: '1px solid #f0f0f0',
                borderRadius: '12px',
                padding: '16px',
              }}
              bodyStyle={{ padding: 0 }}
            >
              <QRCode
                value={address}
                size={200}
                style={{
                  maxWidth: '100%',
                  width: '200px',
                  height: '200px',
                  background: 'white',
                  borderRadius: '8px',
                }}
                level="M"
                bgColor="#FFFFFF"
                fgColor="#000000"
              />
            </Card>

            <Space direction="vertical" style={{ width: '100%' }}>
              {networkType && (
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  网络类型: {networkType.toUpperCase()}
                </Text>
              )}

              <Title level={5} style={{ margin: '8px 0' }}>
                钱包地址
              </Title>

              <Paragraph
                copyable={{
                  text: address,
                  onCopy: () => copyToClipboard(address),
                }}
                style={{
                  backgroundColor: '#f5f5f5',
                  padding: '12px',
                  borderRadius: '6px',
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  wordBreak: 'break-all',
                  margin: 0,
                }}
              >
                {address}
              </Paragraph>

              <Text type="secondary" style={{ fontSize: '12px', marginTop: '8px' }}>
                请确保收款地址的网络类型正确，否则可能导致资产丢失
              </Text>
            </Space>
          </>
        ) : (
          <div style={{ padding: '40px 0' }}>
            <Text type="secondary">无效的地址</Text>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default AddressQRCodeModal;

// 工具函数，用于显示地址二维码
export const showAddressQRCode = (props: Omit<AddressQRCodeModalProps, 'visible' | 'onClose'>) => {
  // 创建一个div作为Modal的容器
  const container = document.createElement('div');
  document.body.appendChild(container);

  // 创建状态管理系统
  let currentVisible = true;

  // 关闭Modal并清理DOM
  const close = () => {
    if (container.parentNode) {
      // 先把Modal关闭，然后延迟删除DOM节点
      currentVisible = false;
      render();

      setTimeout(() => {
        if (container.parentNode) {
          ReactDOM.unmountComponentAtNode(container);
          container.parentNode.removeChild(container);
        }
      }, 200); // 等待Modal关闭动画完成
    }
  };

  // 内部渲染函数
  function render() {
    ReactDOM.render(
      <AddressQRCodeModal
        visible={currentVisible}
        onClose={close}
        address={props.address}
        networkType={props.networkType}
      />,
      container
    );
  }

  // 初始渲染
  render();

  // 返回关闭函数，以便外部控制
  return close;
};
