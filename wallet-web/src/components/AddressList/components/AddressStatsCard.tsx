// src/components/AddressList/components/AddressStatsCard.tsx
import React from 'react';
import { Card, Row, Col, Statistic, Image, Typography } from 'antd';
import { LinkOutlined, PieChartOutlined } from '@ant-design/icons';
import type { WalletApiApiWalletV1GetAddressStatisticRes } from '../../../services/api/model';
import ethIcon from '../../../assets/images/eth.svg';
import usdtIcon from '../../../assets/images/usdt.svg';
import trxIcon from '../../../assets/images/tron.svg';
import styles from '../AddressList.module.css'; // 假设样式可以复用

const { Text } = Typography; // Removed unused Title

interface AddressStatsCardProps {
  statisticData: WalletApiApiWalletV1GetAddressStatisticRes;
}

const AddressStatsCard: React.FC<AddressStatsCardProps> = ({ statisticData }) => {
  return (
    <Card
      className={`${styles.statsCard} fade-in hover-shadow`}
      style={{
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 82, 204, 0.08)',
        border: 'none',
        overflow: 'hidden',
      }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <PieChartOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>地址资产统计</span>
        </div>
      }
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '16px 24px',
        },
        body: {
          padding: '24px',
        }
      }}
    >
      <Row gutter={[24, 24]}>
        <Col span={4}>
          <Statistic
            title={
              <Text strong style={{ fontSize: '16px', color: '#262626' }}>地址总数</Text>
            }
            value={statisticData.total_size || 0}
            prefix={
              <div style={{
                background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                width: 32,
                height: 32,
                borderRadius: '8px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 8
              }}>
                <LinkOutlined style={{ color: '#1890ff', fontSize: 18 }} />
              </div>
            }
            valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#1890ff' }}
          />
        </Col>
        <Col span={20}>
          <Row gutter={24}>
            <Col span={6}>
              <Statistic
                title={
                  <Text strong style={{ fontSize: '16px', color: '#262626' }}>ETH总余额</Text>
                }
                value={statisticData.eth_total_balance || '0'}
                precision={4}
                valueStyle={{ fontSize: '20px', fontWeight: 600 }}
                suffix={
                  <Image
                    src={ethIcon}
                    preview={false}
                    width={28}
                    style={{ verticalAlign: 'middle', marginLeft: 8 }}
                  />
                }
              />
            </Col>
            <Col span={6}>
              <Statistic
                title={
                  <Text strong style={{ fontSize: '16px', color: '#262626' }}>ERC20 USDT总余额</Text>
                }
                value={statisticData.erc20_usdt_total_balance || '0'}
                precision={2}
                valueStyle={{ fontSize: '20px', fontWeight: 600 }}
                suffix={
                  <Image
                    src={usdtIcon}
                    preview={false}
                    width={28}
                    style={{ verticalAlign: 'middle', marginLeft: 8 }}
                  />
                }
              />
            </Col>
            <Col span={6}>
              <Statistic
                title={
                  <Text strong style={{ fontSize: '16px', color: '#262626' }}>TRX总余额</Text>
                }
                value={statisticData.trx_total_balance || '0'}
                precision={4}
                valueStyle={{ fontSize: '20px', fontWeight: 600 }}
                suffix={
                  <Image
                    src={trxIcon}
                    preview={false}
                    width={28}
                    style={{ verticalAlign: 'middle', marginLeft: 8 }}
                  />
                }
              />
            </Col>
            <Col span={6}>
              <Statistic
                title={
                  <Text strong style={{ fontSize: '16px', color: '#262626' }}>TRC20 USDT总余额</Text>
                }
                value={statisticData.trc20_usdt_total_balance || '0'}
                precision={2}
                valueStyle={{ fontSize: '20px', fontWeight: 600 }}
                suffix={
                  <Image
                    src={usdtIcon}
                    preview={false}
                    width={28}
                    style={{ verticalAlign: 'middle', marginLeft: 8 }}
                  />
                }
              />
            </Col>
          </Row>
        </Col>
      </Row>
      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        <Col span={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>活跃地址</Text>
              }
              value={statisticData.active_address || 0}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#52c41a' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <LinkOutlined style={{ color: '#52c41a', fontSize: 18 }} />
                </div>
              }
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>ETH网络地址</Text>
              }
              value={statisticData.eth_address_count || 0}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#1890ff' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <Image
                    src={ethIcon}
                    preview={false}
                    width={18}
                    style={{ verticalAlign: 'middle' }}
                  />
                </div>
              }
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card
            className="hover-shadow transition-all"
            style={{
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              overflow: 'hidden',
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Statistic
              title={
                <Text strong style={{ fontSize: '16px', color: '#262626' }}>TRX网络地址</Text>
              }
              value={statisticData.trx_address_count || 0}
              valueStyle={{ fontSize: '24px', fontWeight: 600, color: '#ff4d4f' }}
              prefix={
                <div style={{
                  background: 'linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)',
                  width: 32,
                  height: 32,
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8
                }}>
                  <Image
                    src={trxIcon}
                    preview={false}
                    width={18}
                    style={{ verticalAlign: 'middle' }}
                  />
                </div>
              }
            />
          </Card>
        </Col>
      </Row>
    </Card>
  );
};

export default AddressStatsCard;