// src/components/AddressList/types.ts
import type { WalletApiApiWalletV1AddressInfo } from '../../services/api/model'; // Updated type

export interface AddressItem extends Omit<WalletApiApiWalletV1AddressInfo, 'status' | 'id' | 'wallet_id'> { // Updated type
  network?: string;
  ethBalance?: string;
  erc20UsdtBalance?: string;
  trxBalance?: string;
  trc20UsdtBalance?: string;
  status: number;
  tags?: string[];
  walletName?: string;
  type?: string;
  chain_coin_balance?: string;
  chain_usdt_balance?: string;
  bind_status?: number;
  last_query_at?: string;
  bind_at?: string;
  id: number;
  wallet_id: number;
  label?: string;
  create_at: string;
  update_at?: string;
}

export interface FilterState {
  network?: string;
  status?: string;
  tags?: string[];
}

export interface SorterState {
  field?: string;
  order?: 'ascend' | 'descend';
}

export type ExportType = 'all' | 'unbound';