// src/components/AddressList/hooks/useAddressListData.ts
import { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { message } from 'antd';
import { useAppDispatch } from '../../../hooks/useAppDispatch';
import {
  WalletApiApiWalletV1AddressInfo,
  GetWalletAddressListParams,
  WalletApiApiWalletV1GetAddressStatisticRes,
} from '../../../services/api/model';
import { getAddress } from '../../../services/api/address/address';
import { fetchWalletList } from '../../../store/slices/walletSlice';
import type { AddressItem, FilterState, SorterState } from '../types';

const { getWalletAddressList, getWalletAddressStatistic, postWalletRefreshAddress, postWalletBindAddress } = getAddress();

const MIN_LOADING_TIME_MS = 500;

export function useAddressListData() {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const navigate = useNavigate();

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState<FilterState>({});
  const [sorter, setSorter] = useState<SorterState>({});
  const [addresses, setAddresses] = useState<AddressItem[]>([]);
  const [totalAddresses, setTotalAddresses] = useState(0);
  const [statisticData, setStatisticData] = useState<WalletApiApiWalletV1GetAddressStatisticRes>({});
  const [loading, setLoading] = useState(false); // 添加 loading 状态


  const loadWalletList = useCallback(async () => {
    try {
      await dispatch(fetchWalletList()).unwrap();
    } catch (error) {
      console.error('获取钱包列表失败:', error);
      // message.error('获取钱包列表失败'); // 考虑是否在 hook 中直接显示 message
    }
  }, [dispatch]);

  const loadStatistics = useCallback(async () => {
    setLoading(true);
    const startTime = Date.now();
    try {
      const response = await getWalletAddressStatistic();
      setStatisticData(response);
    } catch (error) {
      console.error('获取地址统计数据失败:', error);
      message.error('获取地址统计数据失败');
    } finally {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime < MIN_LOADING_TIME_MS) {
        setTimeout(() => setLoading(false), MIN_LOADING_TIME_MS - elapsedTime);
      } else {
        setLoading(false);
      }
    }
  }, []);

  const loadAddresses = useCallback(
    async (customSorter?: SorterState) => {
      setLoading(true);
      const startTime = Date.now();
      try {
        const pageNumber = Number(currentPage);
        const params: GetWalletAddressListParams = {
          page: pageNumber,
          limit: pageSize,
          type: filters.network,
          address: searchText,
        };

        const currentSorter = customSorter || sorter;
        if (currentSorter.field && currentSorter.order) {
          (params as any).sort_field = currentSorter.field;
          (params as any).sort_order = currentSorter.order === 'ascend' ? 'asc' : 'desc';
        }

        const response = await getWalletAddressList(params);
        const addressList = response.list || [];
        const pageInfo = response.page;

        const formattedAddresses: AddressItem[] = addressList
          .filter(
            (addr: WalletApiApiWalletV1AddressInfo): addr is WalletApiApiWalletV1AddressInfo & { id: number; wallet_id: number; create_at: string } => {
              return addr.id !== undefined && addr.wallet_id !== undefined && addr.create_at !== undefined;
            },
          )
          .map((addr: WalletApiApiWalletV1AddressInfo & { id: number; wallet_id: number; create_at: string }) => ({
            ...addr,
            network: addr.type?.toUpperCase(),
            ethBalance: addr.type?.toLowerCase() === 'eth' ? addr.chain_coin_balance : '0',
            erc20UsdtBalance: addr.type?.toLowerCase() === 'eth' ? addr.chain_usdt_balance : '0',
            trxBalance: addr.type?.toLowerCase() === 'trx' ? addr.chain_coin_balance : '0',
            trc20UsdtBalance: addr.type?.toLowerCase() === 'trx' ? addr.chain_usdt_balance : '0',
            tags: addr.label ? addr.label.split(',') : [],
            walletName: `钱包 ${addr.wallet_id}`,
            status: addr.status || 0,
            id: addr.id,
            wallet_id: addr.wallet_id,
            create_at: addr.create_at,
          }));

        setAddresses(formattedAddresses);
        setTotalAddresses(pageInfo?.total || 0);
      } catch (error) {
        console.error('获取地址列表失败:', error);
        message.error('获取地址列表失败');
      } finally {
        const elapsedTime = Date.now() - startTime;
        if (elapsedTime < MIN_LOADING_TIME_MS) {
          setTimeout(() => setLoading(false), MIN_LOADING_TIME_MS - elapsedTime);
        } else {
          setLoading(false);
        }
      }
    },
    [currentPage, pageSize, filters.network, searchText, sorter],
  );

  const refreshAllData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([loadAddresses(), loadStatistics()]);
    } catch (error) {
      console.error('刷新数据失败:', error);
      message.error('刷新数据失败');
    } finally {
      setLoading(false);
    }
  }, [loadAddresses, loadStatistics]);

  const refreshBalance = useCallback(
    async (address: string) => {
      if (!address) {
        message.error('请输入地址');
        return;
      }
      setLoading(true);
      const startTime = Date.now();
      try {
        await postWalletRefreshAddress({ address });
        message.success('刷新余额中...');
        // 延迟刷新，等待后端处理
        setTimeout(() => {
            refreshAllData();
        }, 1000); // 延迟1秒，可以根据实际情况调整
      } catch (error: any) {
        console.error('刷新余额失败:', error);
        message.error(error?.message || '刷新余额失败');
      } finally {
        const elapsedTime = Date.now() - startTime;
        if (elapsedTime < MIN_LOADING_TIME_MS) {
          setTimeout(() => setLoading(false), MIN_LOADING_TIME_MS - elapsedTime);
        } else {
          setLoading(false);
        }
      }
    },
    [refreshAllData],
  );

  const bindAddress = useCallback(
    async (address: string, type: string) => {
      if (!address || !type) {
        message.error('地址和类型不能为空');
        return;
      }
      setLoading(true);
      const startTime = Date.now();
      try {
        await postWalletBindAddress({ address, type });
        message.success('地址已成功标记为已绑定');
        // 刷新数据
        refreshAllData();
      } catch (error: any) {
        console.error('绑定地址失败:', error);
        message.error(error?.message || '绑定地址失败');
      } finally {
        const elapsedTime = Date.now() - startTime;
        if (elapsedTime < MIN_LOADING_TIME_MS) {
          setTimeout(() => setLoading(false), MIN_LOADING_TIME_MS - elapsedTime);
        } else {
          setLoading(false);
        }
      }
    },
    [refreshAllData],
  );


  const updateUrlParams = useCallback(
    (page: number, newPageSize: number, currentSearchText?: string, network?: string) => {
      const params = new URLSearchParams();
      params.set('page', String(page));
      params.set('limit', String(newPageSize));
      if (currentSearchText) params.set('address', currentSearchText);
      if (network) params.set('type', network);
      navigate(`${location.pathname}?${params.toString()}`, { replace: true });
    },
    [location.pathname, navigate],
  );

  const handleSearch = useCallback(() => {
    setCurrentPage(1);
    updateUrlParams(1, pageSize, searchText, filters.network);
    // loadAddresses 将在 currentPage 或 searchText 变化时通过 useEffect 触发
  }, [pageSize, searchText, filters.network, updateUrlParams]);


  const handleReset = useCallback(() => {
    setSearchText('');
    setFilters({});
    setSorter({});
    setCurrentPage(1);
    updateUrlParams(1, pageSize);
     // loadAddresses 将在 currentPage, searchText, filters 变化时通过 useEffect 触发
  }, [pageSize, updateUrlParams]);


  const handleTableChange = useCallback(
    (pagination: any, tableFilters: any, newSorter: any) => {
      const newCurrentPage = Number(pagination.current) || 1;
      const newPageSize = Number(pagination.pageSize) || pageSize;

      setCurrentPage(newCurrentPage);
      setPageSize(newPageSize);
      updateUrlParams(newCurrentPage, newPageSize, searchText, filters.network);

      if (newSorter && 'field' in newSorter && newSorter.order) {
        setSorter({
          field: newSorter.field as string,
          order: newSorter.order as 'ascend' | 'descend',
        });
      } else {
        setSorter({});
      }
    },
    [pageSize, searchText, filters.network, updateUrlParams],
  );

  // Effect to load data when pagination, sorter, or filters change
  useEffect(() => {
    loadAddresses();
  }, [currentPage, pageSize, sorter, filters, searchText, loadAddresses]);


  // Effect to initialize from URL params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const pageParam = params.get('page');
    const limitParam = params.get('limit');
    const addressParam = params.get('address');
    const typeParam = params.get('type');

    if (pageParam) setCurrentPage(Number(pageParam));
    if (limitParam) setPageSize(Number(limitParam));
    if (addressParam) setSearchText(addressParam);
    if (typeParam) setFilters((prev) => ({ ...prev, network: typeParam }));
  }, [location.search]);

  // Initial data load
  useEffect(() => {
    loadWalletList();
    loadStatistics();
    // loadAddresses is called by the dependency change in the previous useEffect
  }, [loadWalletList, loadStatistics]);

  return {
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    searchText,
    setSearchText,
    filters,
    setFilters,
    sorter,
    setSorter,
    addresses,
    totalAddresses,
    statisticData,
    loading,
    loadAddresses, // Expose if direct call is needed, though useEffect handles most cases
    loadStatistics,
    refreshAllData,
    refreshBalance,
    bindAddress,
    handleSearch,
    handleReset,
    handleTableChange,
    updateUrlParams, // Expose if needed by other parts
  };
}