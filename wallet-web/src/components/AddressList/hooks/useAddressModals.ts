// src/components/AddressList/hooks/useAddressModals.ts
import { useState, useCallback } from 'react';
import { Form, message } from 'antd';
import { getAddress } from '../../../services/api/address/address';
import type { WalletApiApiWalletV1ExportAddressReq } from '../../../services/api/model';
import type { ExportType } from '../types';

const { postWalletBatchCreateAddress, postWalletExportAddress } = getAddress();

interface UseAddressModalsProps {
  refreshAllData: () => Promise<void>;
}

export function useAddressModals({ refreshAllData }: UseAddressModalsProps) {
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportType, setExportType] = useState<ExportType>('all');
  const [exportAuthModalVisible, setExportAuthModalVisible] = useState(false);

  const [generateModalVisible, setGenerateModalVisible] = useState(false);
  const [generateForm] = Form.useForm();
  const [authModalVisible, setAuthModalVisible] = useState(false);
  const [addressCount, setAddressCount] = useState<number>(0);
  const [taskId, setTaskId] = useState<string | undefined>(undefined);


  // 打开生成地址弹窗
  const handleOpenGenerateModal = useCallback(() => {
    // 重置表单和状态
    generateForm.resetFields();
    setTaskId(undefined); // 重置任务ID
    setGenerateModalVisible(true);
  }, [generateForm]);

  // 处理数量确认，显示验证弹窗
  const handleCountConfirm = useCallback(() => {
    // 如果有任务ID，说明是进度条完成后的关闭操作，直接关闭弹窗
    if (taskId) {
      setGenerateModalVisible(false);
      setTaskId(undefined); // 重置任务ID
      return;
    }

    // 否则是正常的数量确认，显示验证弹窗
    generateForm.validateFields().then((values) => {
      setAddressCount(values.count);
      setGenerateModalVisible(false);
      setAuthModalVisible(true);
    });
  }, [generateForm, taskId]);

  const handleAuthConfirm = useCallback(
    async (googleCode: string, password?: string) => { // password can be optional if not always required
      if (!password) {
        message.error('请输入密码'); // Or handle as needed
        throw new Error('密码是必需的');
      }
      const params = {
        count: addressCount,
        password: password,
        google_code: googleCode,
      };

      try {
        const response = await postWalletBatchCreateAddress(params);
        setAuthModalVisible(false);

        // 获取任务ID并显示进度条
        if (response.task_id) {
          setTaskId(response.task_id);
          setGenerateModalVisible(true);
          message.success('地址生成任务已提交，正在处理中...');
        } else {
          message.success('地址生成请求已提交');
          await refreshAllData();
        }
      } catch (error: any) {
        message.error(error?.message || '生成地址失败');
        throw error;
      }
    },
    [addressCount, refreshAllData],
  );

  const handleExport = useCallback(() => {
    setExportModalVisible(true);
  }, []);

  const executeExport = useCallback(() => {
    setExportModalVisible(false);
    setExportAuthModalVisible(true);
  }, []);

  const handleExportAuthConfirm = useCallback(
    async (googleCode: string, password?: string) => {
      if (!password) {
        message.error('请输入密码');
        throw new Error('密码是必需的');
      }
      try {
        const params: WalletApiApiWalletV1ExportAddressReq = {
          type: exportType === 'all' ? 'all' : 'unbound',
          password,
          google_code: googleCode,
        };

        await postWalletExportAddress(params);
        setExportAuthModalVisible(false);
        message.success('导出请求已提交，稍后将收到导出结果');

        if (exportType === 'unbound') {
          await refreshAllData();
        }
      } catch (error: any) {
        message.error(error?.message || '导出地址失败');
        throw error;
      }
    },
    [exportType, refreshAllData],
  );

  return {
    exportModalVisible,
    setExportModalVisible,
    exportType,
    setExportType,
    exportAuthModalVisible,
    setExportAuthModalVisible,
    generateModalVisible,
    setGenerateModalVisible,
    generateForm,
    authModalVisible,
    setAuthModalVisible,
    taskId,
    setTaskId,
    handleOpenGenerateModal,
    handleCountConfirm,
    handleAuthConfirm,
    handleExport,
    executeExport,
    handleExportAuthConfirm,
  };
}