.addressList {
    margin-bottom: 24px;
    animation: fadeIn 0.5s ease-out;
}

.statsCard {
    margin-bottom: 24px;
}

.tableCard {
    margin-bottom: 24px;
}

.tableHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 0;
}

.searchFilters {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.addressContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.address {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: 'SFMono-Regular', <PERSON>sol<PERSON>, 'Liberation Mono', Menlo, Courier, monospace;
    background-color: #f5f7fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
}

/* 自定义表格样式 */
:global(.address-table) :global(.ant-table) {
    border-radius: 12px;
    overflow: hidden;
}

:global(.address-table) :global(.ant-table-thead > tr > th) {
    background-color: #f5f7fa;
    color: #262626;
    font-weight: 600;
    padding: 16px 16px;
    border-bottom: 1px solid #f0f0f0;
}

:global(.address-table) :global(.ant-table-tbody > tr > td) {
    padding: 16px 16px;
    border-bottom: 1px solid #f0f0f0;
}

:global(.address-table) :global(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #f0f5ff;
}

:global(.address-table) :global(.ant-table-pagination.ant-pagination) {
    margin: 16px 0;
    padding: 16px 16px;
}

:global(.address-table) :global(.ant-pagination-item-active) {
    border-color: #1890ff;
    font-weight: 600;
}

:global(.address-table) :global(.ant-pagination-item:hover) {
    border-color: #1890ff;
}

:global(.address-table) :global(.ant-pagination-item-active a) {
    color: #1890ff;
}

:global(.address-table-row) {
    transition: all 0.3s ease;
}

:global(.address-table-row:hover) {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 82, 204, 0.08);
}

@media (max-width: 768px) {
    .tableHeader {
        flex-direction: column;
        align-items: flex-start;
    }

    .searchFilters {
        margin-bottom: 16px;
        width: 100%;
    }

    .actions {
        width: 100%;
    }
}