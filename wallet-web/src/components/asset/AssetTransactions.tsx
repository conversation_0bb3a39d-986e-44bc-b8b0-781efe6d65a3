import React from 'react';
import { Card, Table, Tag, Typography, Space } from 'antd';
import { Transaction } from '../../services/walletService';

const { Text } = Typography;

interface AssetTransactionsProps {
  transactions: Transaction[];
  loading: boolean;
}

const AssetTransactions: React.FC<AssetTransactionsProps> = ({ transactions, loading }) => {
  // 格式化时间
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 获取交易类型标签
  const getTransactionTypeTag = (type: string) => {
    switch (type) {
      case 'deposit':
        return <Tag color="green">充值</Tag>;
      case 'withdrawal':
        return <Tag color="red">归集</Tag>;
      case 'transfer':
        return <Tag color="blue">转账</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  // 获取交易状态标签
  const getTransactionStatusTag = (status: string) => {
    switch (status) {
      case 'completed':
        return <Tag color="success">已完成</Tag>;
      case 'pending':
        return <Tag color="processing">处理中</Tag>;
      case 'failed':
        return <Tag color="error">失败</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text: string) => formatDate(text),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (text: string) => getTransactionTypeTag(text),
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text: string, record: Transaction) => (
        <Text strong>
          {record.type === 'withdrawal' ? '-' : record.type === 'deposit' ? '+' : ''}
          {text} {record.currency}
        </Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => getTransactionStatusTag(text),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '地址',
      key: 'address',
      render: (text: string, record: Transaction) => (
        <Space size="middle">
          {record.toAddress && (
            <Text ellipsis style={{ maxWidth: 150, fontFamily: 'monospace' }}>
              {record.toAddress.substring(0, 8)}...{record.toAddress.substring(record.toAddress.length - 8)}
            </Text>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Card title="交易历史" loading={loading}>
      <Table
        dataSource={transactions}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 5 }}
        loading={loading}
        locale={{ emptyText: '暂无交易记录' }}
      />
    </Card>
  );
};

export default AssetTransactions;
