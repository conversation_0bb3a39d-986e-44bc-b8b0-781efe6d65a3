import React, { useState } from 'react';
import { Card, Tabs, Form, Input, Button, Typography, message, Result, QRCode, Space, Divider } from 'antd';
import { SendOutlined, QrcodeOutlined, CopyOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { AssetDetail } from '../../mock/assetData';

const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;

interface AssetActionsProps {
  assetDetail: AssetDetail;
  loading: boolean;
  receiveAddress: string;
  onSend: (toAddress: string, amount: string, memo?: string) => void;
  onCopyAddress: (address: string) => void;
  sendSuccess: boolean;
}

const AssetActions: React.FC<AssetActionsProps> = ({
  assetDetail,
  loading,
  receiveAddress,
  onSend,
  onCopyAddress,
  sendSuccess,
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('send');

  // 处理发送表单提交
  const handleSendSubmit = (values: any) => {
    const { toAddress, amount, memo } = values;

    // 简单验证
    if (parseFloat(amount) <= 0) {
      message.error('金额必须大于0');
      return;
    }

    if (parseFloat(amount) > parseFloat(assetDetail.balance)) {
      message.error('余额不足');
      return;
    }

    onSend(toAddress, amount, memo);
  };

  // 重置表单
  const resetForm = () => {
    form.resetFields();
    setActiveTab('send');
  };

  return (
    <Card>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <SendOutlined />
              发送
            </span>
          }
          key="send"
        >
          {sendSuccess ? (
            <Result
              status="success"
              title="发送成功！"
              subTitle="您的交易已提交到区块链网络，请等待确认。"
              extra={[
                <Button type="primary" key="back" onClick={resetForm}>
                  继续发送
                </Button>,
              ]}
            />
          ) : (
            <Form form={form} layout="vertical" onFinish={handleSendSubmit} disabled={loading}>
              <Form.Item name="toAddress" label="接收地址" rules={[{ required: true, message: '请输入接收地址' }]}>
                <Input placeholder="请输入接收地址" />
              </Form.Item>

              <Form.Item name="amount" label="金额" rules={[{ required: true, message: '请输入金额' }]}>
                <Input
                  type="number"
                  step="0.000001"
                  placeholder="请输入金额"
                  suffix={assetDetail.name}
                  addonAfter={
                    <Button
                      type="link"
                      size="small"
                      onClick={() => form.setFieldsValue({ amount: assetDetail.balance })}
                    >
                      全部
                    </Button>
                  }
                />
              </Form.Item>

              <Form.Item name="memo" label="备注（可选）">
                <Input.TextArea placeholder="请输入备注信息" rows={3} />
              </Form.Item>

              <Form.Item>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text type="secondary">网络手续费: {assetDetail.networkFee}</Text>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    发送
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          )}
        </TabPane>

        <TabPane
          tab={
            <span>
              <QrcodeOutlined />
              接收
            </span>
          }
          key="receive"
        >
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Title level={4}>扫描二维码接收 {assetDetail.name}</Title>

            <div style={{ margin: '20px 0' }}>
              {receiveAddress ? (
                <QRCode value={receiveAddress} size={200} bordered={false} />
              ) : (
                <div
                  style={{
                    width: 200,
                    height: 200,
                    margin: '0 auto',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    background: '#f5f5f5',
                  }}
                >
                  {loading ? '加载中...' : '无法生成二维码'}
                </div>
              )}
            </div>

            <Paragraph>
              <Text strong>您的{assetDetail.name}接收地址:</Text>
            </Paragraph>

            <Paragraph>
              <div
                style={{
                  background: '#f5f5f5',
                  padding: '10px',
                  borderRadius: '4px',
                  wordBreak: 'break-all',
                  textAlign: 'center',
                  fontFamily: 'monospace',
                }}
              >
                {receiveAddress || '加载中...'}
              </div>
            </Paragraph>

            <Button
              type="primary"
              icon={<CopyOutlined />}
              onClick={() => onCopyAddress(receiveAddress)}
              disabled={!receiveAddress}
            >
              复制地址
            </Button>

            <Divider />

            <Paragraph type="secondary">
              <Space>
                <CheckCircleOutlined />
                <Text>只发送 {assetDetail.name} 到此地址，发送其他币种可能导致资产丢失</Text>
              </Space>
            </Paragraph>
          </div>
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default AssetActions;
