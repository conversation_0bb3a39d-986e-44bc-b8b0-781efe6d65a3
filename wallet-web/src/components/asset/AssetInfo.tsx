import React from 'react';
import { <PERSON>, Statistic, Row, Col, Typography, Tag, But<PERSON>, Divider, Space } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, CopyOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { AssetDetail } from '../../mock/assetData';

const { Title, Text } = Typography;

interface AssetInfoProps {
  assetDetail: AssetDetail;
  loading: boolean;
  onCopyAddress: (address: string) => void;
}

const AssetInfo: React.FC<AssetInfoProps> = ({ assetDetail, loading, onCopyAddress }) => {
  const isPositiveChange = assetDetail.change24h.startsWith('+');

  return (
    <Card loading={loading}>
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={3} style={{ margin: 0 }}>
              {assetDetail.name}
              <Tag color="blue" style={{ marginLeft: 8 }}>
                {assetDetail.chain}
              </Tag>
            </Title>
            <Text type="secondary">当前价格: {assetDetail.price} CNY</Text>
          </div>
        </Col>

        <Col xs={24} md={12}>
          <Statistic
            title="余额"
            value={assetDetail.balance}
            precision={assetDetail.chain === 'ETH' ? 6 : 2}
            suffix={assetDetail.name}
          />
          <Text type="secondary" style={{ display: 'block', marginTop: 8 }}>
            ≈ {assetDetail.value} {assetDetail.currency}
          </Text>
        </Col>

        <Col xs={24} md={12}>
          <Statistic
            title="24小时变化"
            value={assetDetail.change24h.replace(/[+-]/, '')}
            precision={2}
            valueStyle={{ color: isPositiveChange ? '#3f8600' : '#cf1322' }}
            prefix={isPositiveChange ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            suffix="%"
          />
        </Col>

        <Col span={24}>
          <Divider />
          <Title level={5}>钱包地址</Title>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Text ellipsis style={{ maxWidth: '80%', fontFamily: 'monospace' }}>
              {assetDetail.address}
            </Text>
            <Button type="text" icon={<CopyOutlined />} onClick={() => onCopyAddress(assetDetail.address)} />
          </div>
        </Col>

        <Col span={24}>
          <Divider />
          <Space>
            <InfoCircleOutlined />
            <Text type="secondary">网络手续费: {assetDetail.networkFee}</Text>
          </Space>
        </Col>
      </Row>
    </Card>
  );
};

export default AssetInfo;
