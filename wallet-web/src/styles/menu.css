/* 自定义菜单样式 */
.main-menu.ant-menu-dark .ant-menu-item {
  transition: all 0.3s ease;
  margin: 8px 0;
}

.main-menu.ant-menu-dark .ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: translateX(4px);
}

.main-menu.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.5);
}

.main-menu.ant-menu-dark .ant-menu-item-selected:hover {
  transform: translateX(4px);
}

.main-menu.ant-menu-dark .ant-menu-item a {
  color: rgba(255, 255, 255, 0.85);
  transition: all 0.3s ease;
}

.main-menu.ant-menu-dark .ant-menu-item-selected a {
  color: #ffffff;
}

.main-menu.ant-menu-dark .ant-menu-item:hover a {
  color: #ffffff;
}

/* 菜单图标样式 */
.main-menu.ant-menu-dark .ant-menu-item .anticon {
  transition: all 0.3s ease;
}

.main-menu.ant-menu-dark .ant-menu-item:hover .anticon {
  transform: scale(1.1);
}

.main-menu.ant-menu-dark .ant-menu-item-selected .anticon {
  color: #ffffff;
}
