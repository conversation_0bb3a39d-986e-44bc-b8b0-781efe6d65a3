/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.85;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.float {
  animation: float 4s ease-in-out infinite;
}

/* Transition classes */
.transition-all {
  transition: all 0.3s ease;
}

/* Hover effects */
.hover-scale:hover {
  transform: scale(1.02);
}

.hover-shadow:hover {
  box-shadow: 0 15px 50px rgba(0, 82, 204, 0.15);
}

/* Chart animations */
@keyframes chartFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.chart-animation {
  animation: chartFadeIn 0.5s ease-out;
}
