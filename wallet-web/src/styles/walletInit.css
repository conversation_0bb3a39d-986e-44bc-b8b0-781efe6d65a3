/* Wallet Initialization Animations and Styles */

/* Slide transitions for steps */
.wallet-init-step {
  animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade up animation for elements */
.fade-up {
  animation: fadeUp 0.5s ease-out;
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered fade in for list items */
.stagger-fade-in > * {
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

.stagger-fade-in > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-fade-in > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-fade-in > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-fade-in > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-fade-in > *:nth-child(5) { animation-delay: 0.5s; }

/* Hover effects for interactive elements */
.interactive-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 82, 204, 0.12);
}

.interactive-card:hover .indicator {
  opacity: 1;
}

.interactive-card:hover .float {
  animation-duration: 2s;
}

/* Mnemonic word styles */
.mnemonic-word {
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
}

.mnemonic-word:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.15);
}

.mnemonic-word.selected {
  background: #e6f7ff;
  border-color: #1890ff;
}

/* Verification word container */
.verification-words-container {
  min-height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.verification-words-container.active {
  border-color: #1890ff;
  background: #f0f7ff;
}

.verification-words-container.complete {
  border-color: #52c41a;
  background: #f6ffed;
}

/* Progress steps */
.wallet-init-progress {
  margin-bottom: 32px;
}

.wallet-init-progress .ant-steps-item-icon {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.wallet-init-progress .ant-steps-item-finish .ant-steps-item-icon {
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* Password strength indicator */
.password-strength-indicator {
  height: 4px;
  border-radius: 2px;
  margin-top: 8px;
  transition: all 0.3s ease;
}

.password-strength-weak {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7875 100%);
  width: 25%;
}

.password-strength-medium {
  background: linear-gradient(90deg, #faad14 0%, #ffc53d 100%);
  width: 50%;
}

.password-strength-strong {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  width: 75%;
}

.password-strength-very-strong {
  background: linear-gradient(90deg, #13c2c2 0%, #36cfc9 100%);
  width: 100%;
}

/* Tooltip animations */
.tooltip-bounce {
  animation: tooltipBounce 2s infinite;
}

@keyframes tooltipBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

/* Confetti animation for success */
.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #1890ff;
  opacity: 0;
}

.confetti-animation {
  position: relative;
}

.confetti-animation .confetti:nth-child(1) {
  animation: confetti-drop-1 1s forwards;
  background-color: #1890ff;
  left: 10%;
  top: -20px;
}

.confetti-animation .confetti:nth-child(2) {
  animation: confetti-drop-2 1.2s forwards;
  background-color: #52c41a;
  left: 30%;
  top: -20px;
}

.confetti-animation .confetti:nth-child(3) {
  animation: confetti-drop-3 0.8s forwards;
  background-color: #faad14;
  left: 50%;
  top: -20px;
}

.confetti-animation .confetti:nth-child(4) {
  animation: confetti-drop-4 1.5s forwards;
  background-color: #722ed1;
  left: 70%;
  top: -20px;
}

.confetti-animation .confetti:nth-child(5) {
  animation: confetti-drop-5 0.9s forwards;
  background-color: #eb2f96;
  left: 90%;
  top: -20px;
}

@keyframes confetti-drop-1 {
  0% { transform: translateY(0) rotate(0); opacity: 1; }
  100% { transform: translateY(100px) rotate(180deg); opacity: 0; }
}

@keyframes confetti-drop-2 {
  0% { transform: translateY(0) rotate(0); opacity: 1; }
  100% { transform: translateY(120px) rotate(-90deg); opacity: 0; }
}

@keyframes confetti-drop-3 {
  0% { transform: translateY(0) rotate(0); opacity: 1; }
  100% { transform: translateY(80px) rotate(90deg); opacity: 0; }
}

@keyframes confetti-drop-4 {
  0% { transform: translateY(0) rotate(0); opacity: 1; }
  100% { transform: translateY(110px) rotate(-180deg); opacity: 0; }
}

@keyframes confetti-drop-5 {
  0% { transform: translateY(0) rotate(0); opacity: 1; }
  100% { transform: translateY(90px) rotate(270deg); opacity: 0; }
}

/* Background gradient animation */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
