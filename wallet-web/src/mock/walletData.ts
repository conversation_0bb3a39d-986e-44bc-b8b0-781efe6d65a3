import {
    mockWalletInitialized,
    mockWalletAddresses,
    type WalletAddressItem // 导入类型
} from './data/wallet.data';

export type { WalletAddressItem }; // 重新导出类型

// 检查钱包是否已初始化
export const mockCheckWalletInitialized = (userId: string): Promise<boolean> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(mockWalletInitialized[userId] || false);
        }, 500);
    });
};

// 创建新钱包
export const mockCreateWallet = (userId: string, password: string): Promise<{ address: string }> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            // 模拟成功创建钱包
            resolve({
                address: '0x' + Math.random().toString(16).substring(2, 42)
            });
        }, 1000);
    });
};

// 模拟导入钱包（通过助记词）
export const mockImportWalletByMnemonic = (userId: string, mnemonic: string, password: string): Promise<{ address: string }> => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            // 验证助记词格式
            const words = mnemonic.trim().split(/\s+/);
            if (words.length !== 12 && words.length !== 24) {
                reject(new Error('助记词必须是12个或24个单词'));
                return;
            }

            // 模拟成功导入钱包
            resolve({
                address: '0x' + Math.random().toString(16).substring(2, 42)
            });
        }, 1000);
    });
};

// 导出钱包私钥
export const mockExportWallet = (userId: string, password: string): Promise<{ privateKey: string }> => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            // 检查钱包是否已初始化
            if (!mockWalletInitialized[userId]) {
                reject(new Error('钱包未初始化'));
                return;
            }

            // 生成模拟私钥（实际应用中会有更复杂的逻辑）
            const privateKey = `${Math.random().toString(16).substring(2, 66)}${Math.random().toString(16).substring(2, 66)}`;
            resolve({ privateKey });
        }, 1000);
    });
};

// 获取钱包地址列表（带分页）
export const mockGetWalletAddresses = (
    page = 1,
    pageSize = 10,
    filters?: {
        type?: string;
        status?: string;
        search?: string;
        tags?: string[];
    }
): Promise<{ addresses: WalletAddressItem[], total: number }> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            let filteredAddresses = [...mockWalletAddresses];

            // 应用过滤条件
            if (filters) {
                if (filters.type) {
                    filteredAddresses = filteredAddresses.filter(addr => addr.type === filters.type);
                }

                if (filters.status) {
                    filteredAddresses = filteredAddresses.filter(addr => addr.status === filters.status);
                }

                if (filters.search) {
                    const searchLower = filters.search.toLowerCase();
                    filteredAddresses = filteredAddresses.filter(addr =>
                        addr.address.toLowerCase().includes(searchLower) ||
                        addr.name.toLowerCase().includes(searchLower)
                    );
                }

                if (filters.tags && filters.tags.length > 0) {
                    filteredAddresses = filteredAddresses.filter(addr =>
                        addr.tags && filters.tags?.some(tag => addr.tags?.includes(tag))
                    );
                }
            }

            // 计算分页
            const startIndex = (page - 1) * pageSize;
            const paginatedAddresses = filteredAddresses.slice(startIndex, startIndex + pageSize);

            resolve({
                addresses: paginatedAddresses,
                total: filteredAddresses.length
            });
        }, 500);
    });
};

// 模拟获取钱包列表 (mockWallets 来自 data 文件)
// 注意：mockGetWalletList 已经在 data 文件中定义为 mockWallets，这里可以移除或调整
// 为了保持服务模拟的一致性，可以保留一个包装函数
import { mockWallets } from './data/wallet.data';
export const mockGetWallets = (): Promise<any[]> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(mockWallets);
        }, 500);
    });
};


// 模拟批量生成地址
export const mockGenerateAddresses = (
    walletId: string,
    network: string,
    count: number
): Promise<{ success: boolean; count: number }> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                success: true,
                count
            });
        }, 1500);
    });
};

// Mock generateGoogleAuthCode 函数，返回模拟的谷歌验证码数据
export const mockGenerateGoogleAuthCode = (): Promise<{ secret: string, qr_code: string }> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            // 生成随机密钥
            const secret = Array(16).fill(0).map(() =>
                "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567"[Math.floor(Math.random() * 32)]
            ).join('');

            // 生成otpauth URL
            const otpauthUrl = `otpauth://totp/XPay:<EMAIL>?secret=${secret}&issuer=XPay&algorithm=SHA1&digits=6&period=30`;

            resolve({
                secret,
                qr_code: otpauthUrl
            });
        }, 500);
    });
};