import { UserInfo } from '../services/authService';

// Mock 用户数据
export const mockUsers = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123',
    email: '<EMAIL>',
    walletAddress: '******************************************',
    createdAt: '2023-01-01T00:00:00.000Z',
  },
  {
    id: '2',
    username: 'user',
    password: 'user123',
    email: '<EMAIL>',
    walletAddress: '******************************************',
    createdAt: '2023-02-01T00:00:00.000Z',
  },
];

// Mock 登录函数
export const mockLogin = (username: string, password: string): Promise<{ token: string; user: UserInfo }> => {
  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      const user = mockUsers.find((u) => u.username === username && u.password === password);

      if (user) {
        // 登录成功
        const { password, ...userInfo } = user;
        // 使用更简单的 token 格式
        const token = `user-${user.id}`;

        resolve({
          token,
          user: userInfo as UserInfo,
        });
      } else {
        // 登录失败
        reject({ message: '用户名或密码错误' });
      }
    }, 800); // 模拟 800ms 延迟
  });
};

// Mock 注册函数
export const mockRegister = (
  username: string,
  password: string,
  email: string,
): Promise<{ token: string; user: UserInfo }> => {
  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      // 检查用户名是否已存在
      const existingUser = mockUsers.find((u) => u.username === username);

      if (existingUser) {
        reject({ message: '用户名已存在' });
        return;
      }

      // 检查邮箱是否已存在
      const existingEmail = mockUsers.find((u) => u.email === email);

      if (existingEmail) {
        reject({ message: '邮箱已被注册' });
        return;
      }

      // 创建新用户
      const newUser = {
        id: `${mockUsers.length + 1}`,
        username,
        password,
        email,
        walletAddress: `0x${Math.random().toString(16).substring(2, 42)}`,
        createdAt: new Date().toISOString(),
      };

      // 添加到用户列表（实际应用中应该持久化存储）
      mockUsers.push(newUser);

      // 返回用户信息和 token
      const { password: _, ...userInfo } = newUser;
      // 使用更简单的 token 格式
      const token = `user-${newUser.id}`;

      resolve({
        token,
        user: userInfo as UserInfo,
      });
    }, 1000); // 模拟 1000ms 延迟
  });
};

// Mock 获取当前用户信息
export const mockGetCurrentUser = (token: string): Promise<UserInfo> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // 如果没有 token，返回默认用户（admin）
        if (!token) {
          const defaultUser = mockUsers[0];
          const { password, ...userInfo } = defaultUser;
          resolve(userInfo as UserInfo);
          return;
        }

        // 尝试从 token 中提取用户 ID
        let userId = '1'; // 默认使用 ID 为 1 的用户（admin）

        // 处理新的 token 格式: user-{id}
        if (token.startsWith('user-')) {
          userId = token.split('-')[1];
        }
        // 处理旧的 token 格式: mock-jwt-token-{id}-{timestamp}
        else if (token.startsWith('mock-jwt-token-')) {
          const parts = token.split('-');
          if (parts.length >= 3) {
            userId = parts[2];
          }
        }

        // 查找用户
        const user = mockUsers.find((u) => u.id === userId) || mockUsers[0];

        // 返回用户信息（不包含密码）
        const { password, ...userInfo } = user;
        resolve(userInfo as UserInfo);
      } catch (error) {
        // 如果发生任何错误，返回默认用户
        const defaultUser = mockUsers[0];
        const { password, ...userInfo } = defaultUser;
        resolve(userInfo as UserInfo);
      }
    }, 500);
  });
};
