import { NetworkFee } from '../services/networkService';

// 网络费用模拟数据
export const mockNetworkFees: Record<string, NetworkFee> = {
    'Ethereum': {
        slow: { value: 0.00000195, usd: 0.02, time: '~35分钟', gwei: 1.15 },
        average: { value: 0.00000412, usd: 0.06, time: '~6分钟', gwei: 1.45 },
        fast: { value: 0.00000875, usd: 0.14, time: '~1分钟', gwei: 3.15 },
    },
    'BSC': {
        slow: { value: 0.00000095, usd: 0.01, time: '~15分钟', gwei: 0.85 },
        average: { value: 0.00000212, usd: 0.03, time: '~3分钟', gwei: 1.25 },
        fast: { value: 0.00000475, usd: 0.08, time: '~30秒', gwei: 2.35 },
    },
    'Polygon': {
        slow: { value: 0.00000055, usd: 0.005, time: '~10分钟', gwei: 0.65 },
        average: { value: 0.00000132, usd: 0.02, time: '~2分钟', gwei: 1.15 },
        fast: { value: 0.00000295, usd: 0.045, time: '~20秒', gwei: 1.85 },
    },
    'Arbitrum': {
        slow: { value: 0.00000125, usd: 0.015, time: '~8分钟', gwei: 0.95 },
        average: { value: 0.00000232, usd: 0.035, time: '~2分钟', gwei: 1.35 },
        fast: { value: 0.00000495, usd: 0.075, time: '~30秒', gwei: 2.25 },
    },
    'Optimism': {
        slow: { value: 0.00000145, usd: 0.018, time: '~10分钟', gwei: 1.05 },
        average: { value: 0.00000252, usd: 0.038, time: '~3分钟', gwei: 1.40 },
        fast: { value: 0.00000525, usd: 0.082, time: '~45秒', gwei: 2.40 },
    }
}; 