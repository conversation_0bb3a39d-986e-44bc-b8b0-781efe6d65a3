import { WalletBalance, Transaction } from '../../services/walletService';

// Mock 钱包余额数据
export const mockWalletBalance: WalletBalance = {
    balance: '10000.00',
    currency: 'CNY'
};

// Mock 钱包地址
export const mockWalletAddress: string = '******************************************';

// Mock 交易记录数据
export const mockTransactions: Transaction[] = [
    {
        id: '1',
        amount: '1000.00',
        currency: 'CNY',
        type: 'deposit',
        status: 'completed',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1天前
        description: '充值',
        walletId: '123'
    },
    {
        id: '2',
        amount: '500.00',
        currency: 'CNY',
        type: 'withdrawal',
        status: 'completed',
        timestamp: new Date(Date.now() - 172800100).toISOString(), // 2天前
        description: '归集',
        walletId: '123'
    },
    {
        id: '3',
        amount: '200.00',
        currency: 'CNY',
        type: 'transfer',
        status: 'pending',
        timestamp: new Date(Date.now() - 259200000).toISOString(), // 3天前
        description: '转账给张三',
        toAddress: '******************************************',
        walletId: '123'
    },
    {
        id: '4',
        amount: '300.00',
        currency: 'CNY',
        type: 'deposit',
        status: 'completed',
        timestamp: new Date(Date.now() - 345600000).toISOString(), // 4天前
        description: '充值',
        walletId: '123'
    },
    {
        id: '5',
        amount: '150.00',
        currency: 'CNY',
        type: 'transfer',
        status: 'failed',
        timestamp: new Date(Date.now() - 432000000).toISOString(), // 5天前
        description: '转账给李四',
        toAddress: '******************************************',
        walletId: '123'
    }
];

// Mock 钱包初始化状态
export const mockWalletInitialized: { [userId: string]: boolean } = {
    '1': true,  // admin用户已初始化钱包
    '2': false  // user用户未初始化钱包
};

// Mock 钱包地址列表类型定义
export interface WalletAddressItem {
    id: string;
    address: string;
    name: string;
    balance: string;
    currency: string;
    createdAt: string;
    lastUsed: string;
    type: 'main' | 'savings' | 'investment';
    status: 'active' | 'inactive';
    tags?: string[];
}

// 生成 Mock 钱包地址列表的函数
export const generateMockWalletAddresses = (count: number): WalletAddressItem[] => {
    return Array(count).fill(null).map((_, index) => {
        const id = (index + 1).toString();
        const isMain = index === 0;
        const types: ('main' | 'savings' | 'investment')[] = ['main', 'savings', 'investment'];
        // Ensure 'main' is only for the first item, others are 'savings' or 'investment'
        const type = isMain ? 'main' : types[Math.floor(Math.random() * 2) + 1];
        const tags = [];

        if (isMain) tags.push('默认');
        if (index % 5 === 0 && !isMain) tags.push('常用'); // Avoid '常用' for '主钱包' if it's also index 0
        if (index % 7 === 0) tags.push('商业');
        if (index % 9 === 0) tags.push('个人');

        return {
            id,
            address: `0x${Math.random().toString(16).substring(2, 42)}`,
            name: isMain ? '主钱包' : `钱包 ${id}`,
            balance: (Math.random() * 10000).toFixed(2),
            currency: 'CNY',
            createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
            lastUsed: new Date(Date.now() - Math.random() * 1000000000).toISOString(),
            type,
            status: Math.random() > 0.1 ? 'active' : 'inactive',
            tags: tags.length > 0 ? tags : undefined
        };
    });
};

// Mock 钱包地址列表数据 (生成50条)
export const mockWalletAddresses: WalletAddressItem[] = generateMockWalletAddresses(50);

// 模拟钱包列表数据
export const mockWallets = [
    {
        id: 'wallet-1',
        name: '主钱包',
        description: '默认钱包',
        type: 'main',
        status: 'active',
        createdAt: new Date(Date.now() - 10000000000).toISOString()
    },
    {
        id: 'wallet-2',
        name: '投资钱包',
        description: '用于长期投资',
        type: 'investment',
        status: 'active',
        createdAt: new Date(Date.now() - 8001000000).toISOString()
    },
    {
        id: 'wallet-3',
        name: '交易钱包',
        description: '日常交易',
        type: 'savings',
        status: 'active',
        createdAt: new Date(Date.now() - 6000000000).toISOString()
    },
    {
        id: 'wallet-4',
        name: '储蓄钱包',
        description: '稳定收益',
        type: 'savings',
        status: 'active',
        createdAt: new Date(Date.now() - 4000000000).toISOString()
    },
    {
        id: 'wallet-5',
        name: '商业钱包',
        description: '业务归集',
        type: 'investment',
        status: 'active',
        createdAt: new Date(Date.now() - 2000000000).toISOString()
    }
];