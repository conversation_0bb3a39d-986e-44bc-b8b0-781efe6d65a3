import { Transaction } from '../services/walletService';

export interface AssetDetail {
    chain: string;
    name: string;
    balance: string;
    value: string;
    currency: string;
    address: string;
    price: string;
    change24h: string;
    marketCap: string;
    volume24h: string;
    description: string;
    networkFee: string;
}

// 模拟获取资产详情的函数
export const mockGetAssetDetail = (chain: string): Promise<AssetDetail> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            const assetDetails: Record<string, AssetDetail> = {
                eth: {
                    chain: 'ETH',
                    name: 'Ethereum',
                    balance: '1.5',
                    value: '3076.13',
                    currency: 'CNY',
                    address: '******************************************',
                    price: '2050.75',
                    change24h: '+2.5%',
                    marketCap: '246,000,000,000',
                    volume24h: '12,500,000,000',
                    description: 'Ethereum是一个开源的、去中心化的区块链平台，支持智能合约功能。',
                    networkFee: '0.001 ETH'
                },
                tron: {
                    chain: 'TRON',
                    name: 'TRON',
                    balance: '5000',
                    value: '425.00',
                    currency: 'CNY',
                    address: 'TRX1234567890abcdef1234567890abcdef12345678',
                    price: '0.085',
                    change24h: '+1.2%',
                    marketCap: '7,500,000,000',
                    volume24h: '1,200,000,000',
                    description: 'TRON是一个去中心化的内容娱乐协议，基于区块链和分布式存储技术。',
                    networkFee: '5 TRX'
                },
                erc20: {
                    chain: 'ERC20',
                    name: 'USDT',
                    balance: '2000',
                    value: '2000.00',
                    currency: 'CNY',
                    address: '******************************************',
                    price: '1.00',
                    change24h: '0%',
                    marketCap: '83,000,000,000',
                    volume24h: '56,000,000,000',
                    description: 'USDT是一种与美元挂钩的稳定币，运行在以太坊网络上。',
                    networkFee: '5 USDT'
                },
                trc20: {
                    chain: 'TRC20',
                    name: 'USDT',
                    balance: '3000',
                    value: '3000.00',
                    currency: 'CNY',
                    address: 'TRX1234567890abcdef1234567890abcdef12345678',
                    price: '1.00',
                    change24h: '0%',
                    marketCap: '83,000,000,000',
                    volume24h: '56,000,000,000',
                    description: 'USDT是一种与美元挂钩的稳定币，这是运行在TRON网络上的版本。',
                    networkFee: '1 USDT'
                }
            };

            const detail = assetDetails[chain.toLowerCase()] || {
                chain: '未知',
                name: '未知资产',
                balance: '0',
                value: '0',
                currency: 'CNY',
                address: '未知地址',
                price: '0',
                change24h: '0%',
                marketCap: '0',
                volume24h: '0',
                description: '未找到该资产的详细信息。',
                networkFee: '未知'
            };

            resolve(detail);
        }, 800);
    });
};

// 模拟获取资产交易历史的函数
export const mockGetAssetTransactions = (chain: string): Promise<Transaction[]> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            const transactions: Record<string, Transaction[]> = {
                eth: [
                    {
                        id: 'eth1',
                        amount: '0.5',
                        currency: 'ETH',
                        type: 'deposit',
                        status: 'completed',
                        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1天前
                        description: 'ETH充值',
                        walletId: '123'
                    },
                    {
                        id: 'eth2',
                        amount: '0.2',
                        currency: 'ETH',
                        type: 'withdrawal',
                        status: 'completed',
                        timestamp: new Date(Date.now() - 172800100).toISOString(), // 2天前
                        description: 'ETH归集',
                        walletId: '123'
                    },
                    {
                        id: 'eth3',
                        amount: '0.1',
                        currency: 'ETH',
                        type: 'transfer',
                        status: 'pending',
                        timestamp: new Date(Date.now() - 259200000).toISOString(), // 3天前
                        description: 'ETH转账',
                        toAddress: '******************************************',
                        walletId: '123'
                    }
                ],
                tron: [
                    {
                        id: 'trx1',
                        amount: '1000',
                        currency: 'TRX',
                        type: 'deposit',
                        status: 'completed',
                        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1天前
                        description: 'TRX充值',
                        walletId: '123'
                    },
                    {
                        id: 'trx2',
                        amount: '500',
                        currency: 'TRX',
                        type: 'withdrawal',
                        status: 'completed',
                        timestamp: new Date(Date.now() - 172800100).toISOString(), // 2天前
                        description: 'TRX归集',
                        walletId: '123'
                    }
                ],
                erc20: [
                    {
                        id: 'usdt1',
                        amount: '500',
                        currency: 'USDT',
                        type: 'deposit',
                        status: 'completed',
                        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1天前
                        description: 'USDT充值',
                        walletId: '123'
                    },
                    {
                        id: 'usdt2',
                        amount: '200',
                        currency: 'USDT',
                        type: 'transfer',
                        status: 'completed',
                        timestamp: new Date(Date.now() - 172800100).toISOString(), // 2天前
                        description: 'USDT转账',
                        toAddress: '******************************************',
                        walletId: '123'
                    }
                ],
                trc20: [
                    {
                        id: 'usdt-trx1',
                        amount: '1000',
                        currency: 'USDT',
                        type: 'deposit',
                        status: 'completed',
                        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1天前
                        description: 'USDT充值',
                        walletId: '123'
                    },
                    {
                        id: 'usdt-trx2',
                        amount: '300',
                        currency: 'USDT',
                        type: 'withdrawal',
                        status: 'failed',
                        timestamp: new Date(Date.now() - 172800100).toISOString(), // 2天前
                        description: 'USDT归集失败',
                        walletId: '123'
                    }
                ]
            };

            resolve(transactions[chain.toLowerCase()] || []);
        }, 800);
    });
}; 