import React, { useState, useEffect } from 'react';
import { Card, Spin, Steps } from 'antd';
import { useNavigate } from 'react-router-dom';
// import { useAppDispatch } from '../hooks/useAppDispatch'; // dispatch is not used
import logo from '../assets/images/logo.svg';
import {
  SafetyOutlined,
  KeyOutlined,
  CheckCircleOutlined,
  LockOutlined,
  QrcodeOutlined,
  SmileOutlined
} from '@ant-design/icons';
// generateWalletMnemonic and generateGoogleAuthCode are now in the hook

// 导入新的组件
import WalletOptions from '../components/walletInit/WalletOptions';
import Step1SecurityWarnings from '../components/walletInit/Step1SecurityWarnings';
import Step2MnemonicDisplay from '../components/walletInit/Step2MnemonicDisplay';
import Step3MnemonicVerify from '../components/walletInit/Step3MnemonicVerify';
import Step4PasswordForm from '../components/walletInit/Step4PasswordForm';
import Step5GoogleAuth from '../components/walletInit/Step5GoogleAuth';
import Step6Success from '../components/walletInit/Step6Success';
import ImportWalletManager from '../components/walletInit/ImportWalletManager';
import { useCreateWalletFlow, warningsList, CreateStep } from '../hooks/useCreateWalletFlow';

const WalletInit: React.FC = () => {
  const navigate = useNavigate();
  // const dispatch = useAppDispatch(); // dispatch is not used
  const [componentLoading, setComponentLoading] = useState(false); // Renamed from 'loading' to avoid conflict with hook's loading
  const [activeOption, setActiveOption] = useState<'none' | 'create' | 'import'>('none');
  const [importedWalletAddress, setImportedWalletAddress] = useState<string>('');

  const handleNavigateToLogin = () => {
    navigate('/login');
  };

  const createWalletFlow = useCreateWalletFlow({
    onSuccessNavigation: handleNavigateToLogin
  });

  // Effect to handle overall loading state based on hook's loading
  useEffect(() => {
    setComponentLoading(createWalletFlow.loading);
  }, [createWalletFlow.loading]);


  const handleStartCreateWalletOption = async () => {
    setActiveOption('create');
    // The actual mnemonic generation is now inside the hook,
    // called when the warning step is confirmed or when the create flow starts.
    // We might need to trigger it explicitly if the hook doesn't do it on init.
    // For now, let's assume the hook handles its own initialization.
    // If the hook needs an explicit start:
    await createWalletFlow.handleStartCreateWalletProcess();
  };

  // 通过助记词导入钱包 - 现在由 useImportWalletFlow hook 处理


  // 获取当前步骤索引
  const getStepIndex = (step: CreateStep): number => {
    const stepOrder: CreateStep[] = ['warning', 'mnemonic', 'verify', 'password', 'google2fa', 'success'];
    return stepOrder.indexOf(step);
  };

  // 获取步骤图标
  const getStepIcon = (step: CreateStep) => {
    switch (step) {
      case 'warning': return <SafetyOutlined />;
      case 'mnemonic': return <KeyOutlined />;
      case 'verify': return <CheckCircleOutlined />;
      case 'password': return <LockOutlined />;
      case 'google2fa': return <QrcodeOutlined />;
      case 'success': return <SmileOutlined />;
      default: return null;
    }
  };

  // 渲染进度条
  const renderProgressSteps = () => {
    // const { token } = theme.useToken(); // token is not used
    const currentStep = getStepIndex(createWalletFlow.createStep);

    if (activeOption !== 'create' || createWalletFlow.createStep === 'success') {
      return null;
    }

    return (
      <Steps
        className="wallet-init-progress fade-in"
        current={currentStep}
        items={[
          { title: '安全提示', icon: getStepIcon('warning') },
          { title: '备份助记词', icon: getStepIcon('mnemonic') },
          { title: '验证助记词', icon: getStepIcon('verify') },
          { title: '设置密码', icon: getStepIcon('password') },
          { title: '谷歌验证', icon: getStepIcon('google2fa') },
        ]}
        style={{
          marginBottom: 32,
          padding: '0 12px'
        }}
        progressDot
        size="small"
      />
    );
  };

  // 渲染创建钱包流程
  const renderCreateWalletFlow = () => {
    switch (createWalletFlow.createStep) {
      case 'warning':
        return (
          <div className="wallet-init-step">
            <Step1SecurityWarnings
              warnings={warningsList} // Use warningsList from the hook
              checkedWarnings={createWalletFlow.checkedWarnings}
              onWarningCheck={createWalletFlow.handleWarningCheck}
              onNext={createWalletFlow.handleShowMnemonic}
              onBack={() => {
                setActiveOption('none');
                createWalletFlow.resetCreateFlow();
              }}
              onReset={createWalletFlow.handleResetWarnings}
            />
          </div>
        );
      case 'mnemonic':
        return (
          <div className="wallet-init-step">
            <Step2MnemonicDisplay
              mnemonic={createWalletFlow.mnemonic}
              onBack={() => createWalletFlow.setCreateStep('warning')}
              onNext={createWalletFlow.handleVerifyMnemonicStep}
            />
          </div>
        );
      case 'verify':
        return (
          <div className="wallet-init-step">
            <Step3MnemonicVerify
              shuffledWords={createWalletFlow.shuffledWords}
              selectedWords={createWalletFlow.selectedWords}
              onSelectWord={createWalletFlow.handleSelectWord}
              onRemoveWord={createWalletFlow.handleRemoveWord}
              onBack={() => createWalletFlow.setCreateStep('mnemonic')}
              onConfirm={createWalletFlow.handleConfirmMnemonic}
              expectedWordCount={createWalletFlow.mnemonic.split(' ').length}
              errorMessage={createWalletFlow.verifyError}
            />
          </div>
        );
      case 'password':
        return (
          <div className="wallet-init-step">
            <Step4PasswordForm
              onFinish={createWalletFlow.handlePasswordFinish}
              onBack={() => createWalletFlow.setCreateStep('verify')}
              title="设置钱包密码"
              loading={createWalletFlow.loadingGoogleAuth}
            />
          </div>
        );
      case 'google2fa':
        return (
          <div className="wallet-init-step">
            <Step5GoogleAuth
              googleAuthData={createWalletFlow.googleAuthData}
              onFinish={createWalletFlow.handleGoogleAuthFinish}
              onBack={() => createWalletFlow.setCreateStep('password')}
              loading={createWalletFlow.loading} // Main loading for the final step
              onCopySecretKey={createWalletFlow.handleCopySecretKey}
            />
          </div>
        );
      case 'success':
        // The onSuccessNavigation is now part of the hook's props
        return (
          <div className="wallet-init-step confetti-animation">
            {/* Confetti elements */}
            <div className="confetti"></div>
            <div className="confetti"></div>
            <div className="confetti"></div>
            <div className="confetti"></div>
            <div className="confetti"></div>
            <Step6Success onFinish={createWalletFlow.onSuccessNavigation} />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 50%, #e6fffb 100%)',
        padding: '16px',
        transition: 'all 0.3s ease',
        backgroundSize: '400% 400%',
        animation: 'gradient 15s ease infinite',
      }}
    >
      <Card
        className="fade-in hover-shadow"
        style={{
          width: '100%',
          maxWidth: 750,
          boxShadow: '0 20px 40px rgba(0, 82, 204, 0.15)',
          borderRadius: 24,
          border: 'none',
          overflow: 'hidden',
          transition: 'all 0.5s ease',
          transform: 'translateY(0)',
        }}
        bodyStyle={{
          padding: activeOption === 'none' ? '50px 40px' : '30px 40px',
          transition: 'padding 0.3s ease'
        }}
      >
        <Spin spinning={componentLoading || createWalletFlow.loading} size="large" tip={activeOption === 'create' ? "处理中..." : undefined}>
          {activeOption === 'none' && (
            <WalletOptions
              onCreateWallet={handleStartCreateWalletOption}
              onImportWallet={() => setActiveOption('import')}
              logo={logo}
            />
          )}

          {activeOption === 'create' && (
            <>
              {renderProgressSteps()}
              {renderCreateWalletFlow()}
            </>
          )}

          {activeOption === 'import' && (
            <div className="wallet-init-step">
              <ImportWalletManager
                onFinishImport={() => {}} // Now handled by the hook
                onBack={() => {
                  setActiveOption('none');
                  setImportedWalletAddress(''); // Reset imported address if going back
                }}
                loading={componentLoading} // Import uses componentLoading
                walletAddress={importedWalletAddress}
              />
            </div>
          )}
        </Spin>
      </Card>

      {/* Background animated elements */}
      <div style={{
        position: 'fixed',
        top: '10%',
        right: '5%',
        width: '200px',
        height: '200px',
        borderRadius: '50%',
        background: 'radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0) 70%)',
        animation: 'float 8s infinite ease-in-out',
        zIndex: 0
      }}></div>

      <div style={{
        position: 'fixed',
        bottom: '15%',
        left: '10%',
        width: '150px',
        height: '150px',
        borderRadius: '50%',
        background: 'radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, rgba(82, 196, 26, 0) 70%)',
        animation: 'float 6s infinite ease-in-out reverse',
        zIndex: 0,
        animationDelay: '2s'
      }}></div>
    </div>
  );
};

export default WalletInit;
