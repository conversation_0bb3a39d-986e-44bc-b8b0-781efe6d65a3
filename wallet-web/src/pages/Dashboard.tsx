import React, { useEffect, useState } from 'react';
import { Spin, Row, Col, Typography } from 'antd';
import { WalletOutlined, LineChartOutlined, DollarOutlined } from '@ant-design/icons';
// import { useNavigate } from 'react-router-dom';
import { mockGetCurrentUser, mockUsers } from '../mock/authData';
import { UserInfo } from '../services/authService';
import { AssetsList, AssetDistribution, RecentTransactions, DashboardHeader } from '../components/dashboard';
import { getWallet } from '../services/api/wallet/wallet';
import type { WalletApiApiWalletV1DashboardStatisticRes } from '../services/api/model/walletApiApiWalletV1DashboardStatisticRes';

const { Text } = Typography;

// 默认用户信息，避免因 token 问题导致的错误
const defaultUser: UserInfo = {
  id: mockUsers[0].id,
  username: mockUsers[0].username,
  email: mockUsers[0].email,
  walletAddress: mockUsers[0].walletAddress,
  createdAt: mockUsers[0].createdAt,
};

interface Asset {
  chain: string;
  name: string;
  balance: string;
  value: string;
  currency: string;
  change?: string;
  icon?: string;
}

const Dashboard: React.FC = () => {
  // const navigate = useNavigate();
  const [user, setUser] = useState<UserInfo>(defaultUser);
  const [loading, setLoading] = useState(true);
  const [pageReady, setPageReady] = useState(false);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [dashboardStats, setDashboardStats] = useState<WalletApiApiWalletV1DashboardStatisticRes>({});
  const [hideBalances, setHideBalances] = useState(false);

  // 计算总资产价值
  const totalAssetValue = dashboardStats.total_balance || '0';

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    setPageReady(false);

    // 记录开始加载的时间
    const startTime = Date.now();
    const minLoadingTime = 800; // 最短加载时间为800毫秒

    try {
      // 获取用户信息
      const token = localStorage.getItem('accessToken');
      if (token) {
        try {
          const userData = await mockGetCurrentUser(token);
          setUser(userData);
        } catch (err: any) {
          console.log(err.message);
        }
      }

      // 获取仪表盘统计数据
      const statsResponse = await getWallet().getWalletDashboardStatistic();
      if (statsResponse) {
        // 处理API响应，确保数值转换为字符串
        const processedStats: Partial<WalletApiApiWalletV1DashboardStatisticRes> = {};
        Object.entries(statsResponse).forEach(([key, value]) => {
          if (typeof value === 'number') {
            processedStats[key as keyof WalletApiApiWalletV1DashboardStatisticRes] = String(value) as any;
          } else {
            processedStats[key as keyof WalletApiApiWalletV1DashboardStatisticRes] = value as any;
          }
        });

        setDashboardStats(processedStats as WalletApiApiWalletV1DashboardStatisticRes);

        // 构建资产列表
        const newAssets: Asset[] = [];

        if (statsResponse.eth_total_balance) {
          newAssets.push({
            chain: 'ETH',
            name: '以太坊',
            balance: String(statsResponse.eth_total_balance),
            value: String(statsResponse.eth_total_balance),
            currency: 'ETH',
          });
        }

        if (statsResponse.trx_total_balance) {
          newAssets.push({
            chain: 'TRON',
            name: '波场',
            balance: String(statsResponse.trx_total_balance),
            value: String(statsResponse.trx_total_balance),
            currency: 'TRX',
          });
        }

        if (statsResponse.erc20_usdt_total_balance) {
          newAssets.push({
            chain: 'ERC20',
            name: 'USDT',
            balance: String(statsResponse.erc20_usdt_total_balance),
            value: String(statsResponse.erc20_usdt_total_balance),
            currency: 'USDT',
          });
        }

        if (statsResponse.trc20_usdt_total_balance) {
          newAssets.push({
            chain: 'TRC20',
            name: 'USDT',
            balance: String(statsResponse.trc20_usdt_total_balance),
            value: String(statsResponse.trc20_usdt_total_balance),
            currency: 'USDT',
          });
        }

        setAssets(newAssets);
      }
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
    } finally {
      // 计算已经过去的时间
      const elapsedTime = Date.now() - startTime;
      // 如果加载时间小于最短加载时间，则等待剩余时间
      if (elapsedTime < minLoadingTime) {
        setTimeout(() => {
          setLoading(false);
          window.setTimeout(() => {
            setPageReady(true);
          }, 100);
        }, minLoadingTime - elapsedTime);
      } else {
        // 如果已经超过最短加载时间，直接结束加载状态
        setLoading(false);
        window.setTimeout(() => {
          setPageReady(true);
        }, 100);
      }
    }
  };

  // const copyToClipboard = (text: string) => {
  //   navigator.clipboard.writeText(text);
  // };

  const toggleHideBalances = () => {
    setHideBalances(!hideBalances);
  };

  // 生成资产分布数据
  const assetDistribution = assets.map((asset) => {
    // 避免除以0的情况
    const totalValue = parseFloat(totalAssetValue) || 1;
    const assetValue = parseFloat(asset.value) || 0;
    const percentage = (assetValue / totalValue) * 100;

    return {
      name: `${asset.chain} ${asset.name}`,
      value: assetValue,
      percent: percentage.toFixed(1),
    };
  });

  if (!pageReady) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '80vh',
          gap: '24px',
          background: 'linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 50%, #e6fffb 100%)',
          backgroundSize: '400% 400%',
          animation: 'gradient 15s ease infinite',
        }}
      >
        <div
          className="pulse"
          style={{
            width: '100px',
            height: '100px',
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            boxShadow: '0 10px 30px rgba(24, 144, 255, 0.2)',
          }}
        >
          <WalletOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
        </div>
        <div className="fade-up" style={{ textAlign: 'center' }}>
          <Text style={{ fontSize: '20px', fontWeight: 600, color: '#1890ff', display: 'block', marginBottom: '8px' }}>
            正在加载您的资产数据
          </Text>
          <Text type="secondary" style={{ fontSize: '14px' }}>
            请稍候，我们正在为您准备最新的资产信息
          </Text>
        </div>
        <Spin size="large" tip="加载中..." />

        {/* 背景动画元素 */}
        <div
          className="float"
          style={{
            position: 'absolute',
            top: '15%',
            right: '10%',
            width: '200px',
            height: '200px',
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0) 70%)',
            zIndex: 0,
            animationDuration: '8s'
          }}
        />

        <div
          className="float"
          style={{
            position: 'absolute',
            bottom: '20%',
            left: '15%',
            width: '150px',
            height: '150px',
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, rgba(82, 196, 26, 0) 70%)',
            zIndex: 0,
            animationDuration: '6s',
            animationDelay: '1s'
          }}
        />
      </div>
    );
  }

  return (
    <div className="wallet-init-step" style={{ animation: 'slideIn 0.5s ease-out' }}>
      <Spin tip="加载中..." spinning={loading} size="large">
        {/* 仪表盘头部 */}
        <DashboardHeader
          username={user?.username || localStorage.getItem('username') || '用户'}
          hideBalances={hideBalances}
          toggleHideBalances={toggleHideBalances}
          onRefresh={loadDashboardData}
          loading={loading}
        />

        {/* 资产概览卡片 */}
        {/* <div className="fade-up" style={{ marginBottom: 24, animationDelay: '0.1s' }}>
          <Row gutter={[24, 24]}>
            <Col xs={24} md={8}>
              <div
                className="interactive-card"
                style={{
                  background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                  borderRadius: 16,
                  padding: '24px',
                  height: '100%',
                  boxShadow: '0 8px 20px rgba(24, 144, 255, 0.15)',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <div style={{ position: 'absolute', top: 0, right: 0, opacity: 0.1 }}>
                  <WalletOutlined style={{ fontSize: 80, color: '#1890ff' }} />
                </div>
                <Text style={{ fontSize: 14, color: '#0050b3', marginBottom: 8, display: 'block' }}>总资产</Text>
                <div style={{ marginBottom: 16 }}>
                  <Text style={{
                    fontSize: 28,
                    fontWeight: 'bold',
                    color: '#0050b3',
                    display: 'block'
                  }}>
                    {hideBalances ? '******' : totalAssetValue} USDT
                  </Text>
                </div>
                <Text style={{ fontSize: 12, color: '#0050b3' }}>
                  {assets.length} 种资产 · {new Date().toLocaleDateString()}
                </Text>
              </div>
            </Col>

            <Col xs={24} md={8}>
              <div
                className="interactive-card"
                style={{
                  background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                  borderRadius: 16,
                  padding: '24px',
                  height: '100%',
                  boxShadow: '0 8px 20px rgba(82, 196, 26, 0.15)',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <div style={{ position: 'absolute', top: 0, right: 0, opacity: 0.1 }}>
                  <DollarOutlined style={{ fontSize: 80, color: '#52c41a' }} />
                </div>
                <Text style={{ fontSize: 14, color: '#237804', marginBottom: 8, display: 'block' }}>ETH资产</Text>
                <div style={{ marginBottom: 16 }}>
                  <Text style={{
                    fontSize: 28,
                    fontWeight: 'bold',
                    color: '#237804',
                    display: 'block'
                  }}>
                    {hideBalances ? '******' : dashboardStats.eth_total_balance || '0'} ETH
                  </Text>
                </div>
                <Text style={{ fontSize: 12, color: '#237804' }}>
                  以太坊主网 · {(dashboardStats as any).eth_address_count || '0'} 个地址
                </Text>
              </div>
            </Col>

            <Col xs={24} md={8}>
              <div
                className="interactive-card"
                style={{
                  background: 'linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%)',
                  borderRadius: 16,
                  padding: '24px',
                  height: '100%',
                  boxShadow: '0 8px 20px rgba(250, 173, 20, 0.15)',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <div style={{ position: 'absolute', top: 0, right: 0, opacity: 0.1 }}>
                  <LineChartOutlined style={{ fontSize: 80, color: '#fa8c16' }} />
                </div>
                <Text style={{ fontSize: 14, color: '#ad4e00', marginBottom: 8, display: 'block' }}>TRON资产</Text>
                <div style={{ marginBottom: 16 }}>
                  <Text style={{
                    fontSize: 28,
                    fontWeight: 'bold',
                    color: '#ad4e00',
                    display: 'block'
                  }}>
                    {hideBalances ? '******' : dashboardStats.trx_total_balance || '0'} TRX
                  </Text>
                </div>
                <Text style={{ fontSize: 12, color: '#ad4e00' }}>
                  波场主网 · {(dashboardStats as any).trx_address_count || '0'} 个地址
                </Text>
              </div>
            </Col>
          </Row>
        </div> */}

        {/* 资产内容 */}
        <div className="fade-up" style={{ marginBottom: 24, animationDelay: '0.2s' }}>
          <Row gutter={[24, 24]}>
            {/* 多链资产列表 */}
            <Col xs={24} lg={16}>
              <AssetsList assets={assets} hideBalances={hideBalances} />
            </Col>

            {/* 资产分布 */}
            <Col xs={24} lg={8}>
              <AssetDistribution distribution={assetDistribution} />
            </Col>
          </Row>
        </div>

        {/* 最近交易 */}
        <div className="fade-up" style={{ animationDelay: '0.3s' }}>
          <RecentTransactions hideBalances={hideBalances} />
        </div>
      </Spin>
    </div>
  );
};

export default Dashboard;
