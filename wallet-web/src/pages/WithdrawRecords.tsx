import React, { useState, useCallback } from 'react';
import { Typography, Card, Row, Col, Button, Tooltip } from 'antd';
import {
  ReloadOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import WithdrawRecordList from '../components/WithdrawRecordList/WithdrawRecordList';
import styles from '../components/wallet/Wallet.module.css'; // 复用Wallet页面的样式

const { Title, Paragraph } = Typography; // Removed Text

const WithdrawRecords: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const handleRefresh = useCallback(() => {
    setLoading(true);

    // 记录开始加载的时间
    const startTime = Date.now();
    const minLoadingTime = 800; // 最短加载时间为800毫秒

    // 模拟刷新操作
    setTimeout(() => {
      // 计算已经过去的时间
      const elapsedTime = Date.now() - startTime;

      // 如果加载时间小于最短加载时间，则等待剩余时间后再刷新页面
      if (elapsedTime < minLoadingTime) {
        setTimeout(() => {
          window.location.reload();
        }, minLoadingTime - elapsedTime);
      } else {
        // 如果已经超过最短加载时间，直接刷新页面
        window.location.reload();
      }
    }, 0);
  }, []);
  return (
    <div className={`${styles.walletPage} wallet-init-step`}>
      {/* 页面标题卡片 */}
      <Card
        className="hover-shadow"
        style={{
          marginBottom: 24,
          borderRadius: '24px',
          boxShadow: '0 10px 20px rgba(0, 82, 204, 0.1)',
          border: 'none',
          overflow: 'hidden',
          background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
        }}
      >
        <Row align="middle" justify="space-between" gutter={[16, 16]}>
          <Col xs={24} md={16}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div
                className="pulse"
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: '16px',
                  background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  boxShadow: '0 8px 16px rgba(24, 144, 255, 0.2)',
                }}
              >
                <HistoryOutlined style={{ fontSize: '30px', color: '#1890ff' }} />
              </div>
              <div className="fade-up">
                <Title level={2} style={{ marginBottom: 4, fontSize: '28px', fontWeight: 600, color: '#0050b3' }}>
                  归集记录
                </Title>
                <Paragraph type="secondary" style={{ fontSize: '16px', marginBottom: 0 }}>
                  查看和管理您的资产归集历史记录和统计数据
                </Paragraph>
              </div>
            </div>
          </Col>
          <Col xs={24} md={8} style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px', alignItems: 'center' }}>
            <Tooltip title="刷新">
              <Button
                icon={<ReloadOutlined />}
                style={{
                  height: '40px',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
                }}
                className="hover-shadow transition-all"
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
            </Tooltip>
          </Col>
        </Row>
      </Card>

      <div className={`${styles.content} fade-up`} style={{ animationDelay: '0.1s' }}>
        <WithdrawRecordList />
      </div>

      {/* 背景动画元素 */}
      <div
        className="float"
        style={{
          position: 'fixed',
          top: '10%',
          right: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0) 70%)',
          animation: 'float 8s infinite ease-in-out',
          zIndex: 0
        }}
      />

      <div
        className="float"
        style={{
          position: 'fixed',
          bottom: '15%',
          left: '10%',
          width: '150px',
          height: '150px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, rgba(82, 196, 26, 0) 70%)',
          animation: 'float 6s infinite ease-in-out reverse',
          zIndex: 0,
          animationDelay: '2s'
        }}
      />
    </div>
  );
};

export default WithdrawRecords;
