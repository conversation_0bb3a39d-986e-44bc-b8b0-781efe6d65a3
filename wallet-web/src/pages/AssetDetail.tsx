import { ArrowLeftOutlined } from '@ant-design/icons';
import { Row, Col, Button, Typography, message, Spin } from 'antd';
import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch } from '../hooks/useAppDispatch';
import { useAppSelector } from '../hooks/useAppSelector';
import {
  fetchAssetDetail,
  fetchAssetTransactions,
  sendAssetAction,
  fetchReceiveAddress,
  clearAssetState,
} from '../store/slices/assetSlice';
import AssetInfo from '../components/asset/AssetInfo';
import AssetTransactions from '../components/asset/AssetTransactions';
import AssetActions from '../components/asset/AssetActions';
import { RootState } from '../store';
import { copyToClipboard } from '../utils/clipboardUtils';

const { Title } = Typography;

const AssetDetail: React.FC = () => {
  const { chain } = useParams<{ chain: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const { assetDetail, transactions, receiveAddress, loading, error, sendSuccess } = useAppSelector(
    (state: RootState) => state.asset,
  );

  // 加载资产详情和交易历史
  useEffect(() => {
    if (chain) {
      dispatch(fetchAssetDetail(chain));
      dispatch(fetchAssetTransactions(chain));
      dispatch(fetchReceiveAddress(chain));
    }

    // 组件卸载时清除状态
    return () => {
      dispatch(clearAssetState());
    };
  }, [dispatch, chain]);

  // 处理复制地址
  const handleCopyAddress = (address: string) => {
    copyToClipboard(address);
  };

  // 处理发送资产
  const handleSendAsset = (toAddress: string, amount: string, memo?: string) => {
    if (chain) {
      dispatch(sendAssetAction({ chain, toAddress, amount, memo }));
    }
  };

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  // 显示错误信息
  useEffect(() => {
    if (error) {
      message.error(error);
    }
  }, [error]);

  // 如果没有资产详情，显示加载中
  if (!assetDetail && loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <Spin size="large" tip="加载资产详情中..." />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', alignItems: 'center' }}>
        <Button type="text" icon={<ArrowLeftOutlined />} onClick={handleGoBack} style={{ marginRight: '16px' }} />
        <Title level={2} style={{ margin: 0 }}>
          {assetDetail?.name} 详情
        </Title>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          {assetDetail && <AssetInfo assetDetail={assetDetail} loading={loading} onCopyAddress={handleCopyAddress} />}

          <div style={{ marginTop: '24px' }}>
            <AssetTransactions transactions={transactions} loading={loading} />
          </div>
        </Col>

        <Col xs={24} lg={8}>
          {assetDetail && (
            <AssetActions
              assetDetail={assetDetail}
              loading={loading}
              receiveAddress={receiveAddress}
              onSend={handleSendAsset}
              onCopyAddress={handleCopyAddress}
              sendSuccess={sendSuccess}
            />
          )}
        </Col>
      </Row>
    </div>
  );
};

export default AssetDetail;
