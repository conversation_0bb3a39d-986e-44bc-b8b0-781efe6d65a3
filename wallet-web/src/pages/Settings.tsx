import React, { useState, useEffect, useCallback } from 'react';
import { Tabs, Card, Typography, Space, Grid, Row, Col, Tooltip, Button } from 'antd'; // Removed unused Badge
import {
  // Removed unused LockOutlined
  SettingOutlined,
  ToolOutlined,
  SafetyOutlined,
  ReloadOutlined,
  // Removed unused QuestionCircleOutlined
  DollarOutlined,
  ClusterOutlined,
  WalletOutlined
} from '@ant-design/icons';
import SecuritySettings from '../components/settings/SecuritySettings';
import BasicSettings from '../components/settings/BasicSettings';
import StrategyCollectionSettings from '../components/settings/StrategyCollectionSettings';
import FeeSettings from '../components/settings/FeeSettings';
import DepositSettings from '../components/settings/DepositSettings';
import '../components/settings/Settings.css';

const { TabPane } = Tabs;
const { Title, Paragraph } = Typography; // Removed unused Text
const { useBreakpoint } = Grid;

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('basic');
  const screens = useBreakpoint();
  const [tabPosition, setTabPosition] = useState<'left' | 'top'>('left');
  const [basicKey, setBasicKey] = useState(0);
  const [strategyKey, setStrategyKey] = useState(0);
  const [feeKey, setFeeKey] = useState(0);
  const [depositKey, setDepositKey] = useState(0);
  const [loading, setLoading] = useState(false);

  const handleRefresh = useCallback(() => {
    setLoading(true);

    // 记录开始加载的时间
    const startTime = Date.now();
    const minLoadingTime = 800; // 最短加载时间为800毫秒

    // 模拟刷新操作
    setTimeout(() => {
      // 计算已经过去的时间
      const elapsedTime = Date.now() - startTime;

      // 如果加载时间小于最短加载时间，则等待剩余时间后再刷新页面
      if (elapsedTime < minLoadingTime) {
        setTimeout(() => {
          window.location.reload();
        }, minLoadingTime - elapsedTime);
      } else {
        // 如果已经超过最短加载时间，直接刷新页面
        window.location.reload();
      }
    }, 0);
  }, []);

  useEffect(() => {
    // 在小屏幕上将标签位置设置为顶部
    setTabPosition(screens.md ? 'left' : 'top');
  }, [screens.md]);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    if (key === 'basic') {
      setBasicKey((prevKey) => prevKey + 1);
    } else if (key === 'strategy') {
      setStrategyKey((prevKey) => prevKey + 1);
    } else if (key === 'fee') {
      setFeeKey((prevKey) => prevKey + 1);
    } else if (key === 'deposit') {
      setDepositKey((prevKey) => prevKey + 1);
    }
  };

  return (
    <div className="settings-page wallet-init-step">
      {/* 页面标题卡片 */}
      <Card
        className="hover-shadow"
        style={{
          marginBottom: 24,
          borderRadius: '24px',
          boxShadow: '0 10px 20px rgba(0, 82, 204, 0.1)',
          border: 'none',
          overflow: 'hidden',
          background: 'linear-gradient(135deg, #e6fffb 0%, #b5f5ec 100%)',
        }}
      >
        <Row align="middle" justify="space-between" gutter={[16, 16]}>
          <Col xs={24} md={16}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div
                className="pulse"
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: '16px',
                  background: 'linear-gradient(135deg, #e6fffb 0%, #b5f5ec 100%)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  boxShadow: '0 8px 16px rgba(19, 194, 194, 0.2)',
                }}
              >
                <ToolOutlined style={{ fontSize: '30px', color: '#13c2c2' }} />
              </div>
              <div className="fade-up">
                <Title level={2} style={{ marginBottom: 4, fontSize: '28px', fontWeight: 600, color: '#006d75' }}>
                  系统设置
                </Title>
                <Paragraph type="secondary" style={{ fontSize: '16px', marginBottom: 0 }}>
                  管理您的安全、通知和归集设置
                </Paragraph>
              </div>
            </div>
          </Col>
          <Col xs={24} md={8} style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px', alignItems: 'center' }}>
            <Tooltip title="刷新">
              <Button
                icon={<ReloadOutlined />}
                style={{
                  height: '40px',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
                }}
                className="hover-shadow transition-all"
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
            </Tooltip>
          </Col>
        </Row>
      </Card>

      <Card
        className="settings-container hover-shadow fade-up"
        style={{
          borderRadius: '24px',
          boxShadow: '0 10px 20px rgba(0, 82, 204, 0.1)',
          border: 'none',
          overflow: 'hidden',
          animationDelay: '0.1s',
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          tabPosition={tabPosition}
          className="settings-tabs"
          size={screens.md ? 'large' : 'middle'}
          type="card"
        >
          <TabPane
            tab={
              <Space size={screens.md ? 12 : 8}>
                <SafetyOutlined style={{ fontSize: '18px', color: activeTab === 'security' ? '#1890ff' : undefined }} />
                <span style={{ fontSize: '16px', fontWeight: activeTab === 'security' ? 600 : 500 }}>安全设置</span>
              </Space>
            }
            key="security"
          >
            <div className="fade-in" style={{ padding: '20px 0' }}>
              <SecuritySettings />
            </div>
          </TabPane>
          <TabPane
            tab={
              <Space size={screens.md ? 12 : 8}>
                <SettingOutlined style={{ fontSize: '18px', color: activeTab === 'basic' ? '#1890ff' : undefined }} />
                <span style={{ fontSize: '16px', fontWeight: activeTab === 'basic' ? 600 : 500 }}>基本设置</span>
              </Space>
            }
            key="basic"
          >
            <div className="fade-in" style={{ padding: '20px 0' }}>
              <BasicSettings key={basicKey} />
            </div>
          </TabPane>
          <TabPane
            tab={
              <Space size={screens.md ? 12 : 8}>
                <ClusterOutlined style={{ fontSize: '18px', color: activeTab === 'strategy' ? '#1890ff' : undefined }} />
                <span style={{ fontSize: '16px', fontWeight: activeTab === 'strategy' ? 600 : 500 }}>策略归集</span>
              </Space>
            }
            key="strategy"
          >
            <div className="fade-in" style={{ padding: '20px 0' }}>
              <StrategyCollectionSettings key={strategyKey} />
            </div>
          </TabPane>
          <TabPane
            tab={
              <Space size={screens.md ? 12 : 8}>
                <DollarOutlined style={{ fontSize: '18px', color: activeTab === 'fee' ? '#1890ff' : undefined }} />
                <span style={{ fontSize: '16px', fontWeight: activeTab === 'fee' ? 600 : 500 }}>手续费设置</span>
              </Space>
            }
            key="fee"
          >
            <div className="fade-in" style={{ padding: '20px 0' }}>
              <FeeSettings key={feeKey} />
            </div>
          </TabPane>
          <TabPane
            tab={
              <Space size={screens.md ? 12 : 8}>
                <WalletOutlined style={{ fontSize: '18px', color: activeTab === 'deposit' ? '#1890ff' : undefined }} />
                <span style={{ fontSize: '16px', fontWeight: activeTab === 'deposit' ? 600 : 500 }}>充值设置</span>
              </Space>
            }
            key="deposit"
          >
            <div className="fade-in" style={{ padding: '20px 0' }}>
              <DepositSettings key={depositKey} />
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 背景动画元素 */}
      <div
        className="float"
        style={{
          position: 'fixed',
          top: '10%',
          right: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(19, 194, 194, 0.1) 0%, rgba(19, 194, 194, 0) 70%)',
          animation: 'float 8s infinite ease-in-out',
          zIndex: 0
        }}
      />

      <div
        className="float"
        style={{
          position: 'fixed',
          bottom: '15%',
          left: '10%',
          width: '150px',
          height: '150px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(19, 194, 194, 0.1) 0%, rgba(19, 194, 194, 0) 70%)',
          animation: 'float 6s infinite ease-in-out reverse',
          zIndex: 0,
          animationDelay: '2s'
        }}
      />
    </div>
  );
};

export default Settings;
