import React, { useState, useEffect, useCallback } from 'react';
import { Typography, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import WalletList from '../components/WalletList/WalletList';
import styles from '../components/wallet/Wallet.module.css';
import { useAppDispatch } from '../hooks/useAppDispatch';
import { fetchWalletAddresses } from '../store/slices/walletSlice';

const { Title } = Typography;

const Wallet: React.FC = () => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);

  const handleRefresh = useCallback(() => {
    setLoading(true);
    dispatch(
      fetchWalletAddresses({
        page: 1,
        pageSize: 10,
        filters: {},
      }),
    ).finally(() => setLoading(false));
  }, [dispatch]);

  // 在组件挂载时获取钱包地址列表
  useEffect(() => {
    handleRefresh();
  }, [handleRefresh]);

  return (
    <div className={styles.walletPage}>
      <div className={styles.header}>
        <Title level={2}>我的钱包</Title>
        <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
          刷新
        </Button>
      </div>

      <WalletList />
    </div>
  );
};

export default Wallet;
