import React, { useEffect, useState, useCallback } from 'react';
import { Form, Input, Button, Typography, message, Modal, Spin, Card, Tooltip } from 'antd';
import {
  LockOutlined,
  ArrowRightOutlined,
  SafetyOutlined,
  KeyOutlined,
  SecurityScanOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import logo from '../assets/images/logo.svg';
import { checkWalletStatus } from '../store/slices/walletSlice';
import { useAppDispatch } from '../hooks/useAppDispatch';
import { authenticateWallet } from '../services/walletService';

const { Title, Text, Paragraph } = Typography;

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [form] = Form.useForm();
  const [googleForm] = Form.useForm();
  const [passwordEntered, setPasswordEntered] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [googleModalVisible, setGoogleModalVisible] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [showWelcomeAnimation, setShowWelcomeAnimation] = useState(true);
  const [unlockAttempt, setUnlockAttempt] = useState(0);
  const [googleCodeFocused, setGoogleCodeFocused] = useState(false);
  const dispatch = useAppDispatch();

  // 欢迎动画效果
  useEffect(() => {
    // 显示欢迎动画，2秒后隐藏
    const timer = setTimeout(() => {
      setShowWelcomeAnimation(false);
    }, 2000);

    return () => window.clearTimeout(timer);
  }, []);

  useEffect(() => {
    // 显示错误信息
    if (error) {
      message.error(error);
      setError(null);
      // 增加解锁尝试次数
      setUnlockAttempt(prev => prev + 1);
    }
  }, [error]);

  // 优化谷歌验证码弹窗表单重置逻辑，避免不必要的重渲染
  const resetGoogleForm = useCallback(() => {
    googleForm.resetFields(['googleCode']);
  }, [googleForm]);

  useEffect(() => {
    if (googleModalVisible) {
      // 当弹窗显示时，清空 googleCode 字段，使用 setTimeout 确保在动画开始前重置
      const timer = setTimeout(() => {
        resetGoogleForm();
      }, 50);
      return () => window.clearTimeout(timer);
    }
  }, [googleModalVisible, resetGoogleForm]);

  // 评估密码强度
  const evaluatePasswordStrength = (password: string): number => {
    if (!password) return 0;

    let strength = 0;
    // 长度检查
    if (password.length >= 8) strength += 1;
    if (password.length >= 12) strength += 1;

    // 复杂性检查
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    // 归一化到0-4的范围
    return Math.min(4, Math.floor(strength / 1.5));
  };

  // 获取密码强度类名
  const getPasswordStrengthClass = (): string => {
    switch (passwordStrength) {
      case 1: return 'password-strength-weak';
      case 2: return 'password-strength-medium';
      case 3: return 'password-strength-strong';
      case 4: return 'password-strength-very-strong';
      default: return '';
    }
  };

  // 获取密码强度文本
  const getPasswordStrengthText = (): string => {
    switch (passwordStrength) {
      case 1: return '弱';
      case 2: return '中';
      case 3: return '强';
      case 4: return '非常强';
      default: return '';
    }
  };

  // 监听密码输入变化
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPasswordEntered(!!value);
    setPasswordStrength(evaluatePasswordStrength(value));
  };

  // 处理下一步按钮点击
  const handleNextStep = () => {
    form
      .validateFields()
      .then((values) => {
        // 保存当前密码并显示谷歌验证码对话框
        setCurrentPassword(values.password);
        setGoogleModalVisible(true);
      })
      .catch((errorInfo) => {
        console.log('表单验证失败:', errorInfo);
      });
  };

  // 处理解锁逻辑 (使用真实API) - 优化状态更新顺序，避免闪烁
  const handleUnlock = useCallback(async (password: string, googleCode: string) => {
    setLoading(true);
    try {
      // 调用钱包认证API
      const response = await authenticateWallet(password, googleCode); // response is WalletApiApiWalletV1AuthRes

      // 检查API响应结构并保存token
      // axiosInstance 拦截器已处理响应，response 直接是 { accessToken, refreshToken, ... } 结构
      if (response && response.accessToken && response.refreshToken) {
        localStorage.setItem('accessToken', response.accessToken);
        localStorage.setItem('refreshToken', response.refreshToken);
        console.log('钱包认证成功，accessToken 和 refreshToken 已保存');
        // 钱包状态将由 token 决定，不再需要前端 dispatch

        // 保存用户名，用于显示在顶部栏 (如果API响应中有的话)
        if ((response as any).username) { // 尝试直接从 response 获取 username
             localStorage.setItem('username', (response as any).username);
        } else {
            localStorage.setItem('username', '用户' + Math.floor(Math.random() * 1000));
        }
      } else {
        console.warn('服务器返回的token结构不符合预期或token为空', response);
        // 暂时保留旧的mock token逻辑，以便在API不符合预期时仍能测试流程
        localStorage.setItem('accessToken', 'mock-accessToken-' + Date.now());
        localStorage.setItem('refreshToken', 'mock-refreshToken-' + Date.now());
        localStorage.setItem('username', '用户' + Math.floor(Math.random() * 1000));
      }

      // 检查钱包初始化状态
      const initStatusResult = await dispatch(checkWalletStatus()).unwrap();

      // 先关闭弹窗和加载状态，再进行导航，避免状态变化导致的闪烁
      setLoading(false);
      setGoogleModalVisible(false);

      // 延迟执行导航和消息提示，确保弹窗已完全关闭
      setTimeout(() => {
        if (!initStatusResult.is_initialized) {
          // 钱包未初始化，跳转到初始化页面
          message.info('请初始化您的钱包');
          navigate('/wallet-init');
        } else {
          // 钱包已初始化，并且我们刚刚通过 authenticateWallet 成功解锁
          message.success('解锁成功');
          navigate('/dashboard'); // 直接跳转到 dashboard
        }
      }, 300); // 给弹窗足够的时间完全关闭

    } catch (err: any) {
      // 显示错误消息
      setError(err.message || '解锁失败');
      setLoading(false);
      setGoogleModalVisible(false);
    }
  }, [dispatch, navigate, setLoading, setError, setGoogleModalVisible]);

  // 提交谷歌验证码 - 优化表单验证和错误处理
  const handleGoogleAuthSubmit = useCallback(() => {
    googleForm
      .validateFields()
      .then((values) => {
        // 调用解锁函数
        handleUnlock(currentPassword, values.googleCode);
      })
      .catch((errorInfo) => {
        console.log('谷歌验证码表单验证失败:', errorInfo);
        // 不要在验证失败时设置loading或改变modal状态，保持当前状态
      });
  }, [googleForm, currentPassword, handleUnlock]);

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 50%, #e6fffb 100%)',
        backgroundSize: '400% 400%',
        animation: 'gradient 15s ease infinite',
        transition: 'all 0.3s ease',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* 背景动画元素 */}
      <div
        className="float"
        style={{
          position: 'absolute',
          top: '10%',
          right: '15%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0) 70%)',
          zIndex: 0,
          animationDuration: '8s'
        }}
      />

      <div
        className="float"
        style={{
          position: 'absolute',
          bottom: '15%',
          left: '10%',
          width: '150px',
          height: '150px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, rgba(82, 196, 26, 0) 70%)',
          zIndex: 0,
          animationDuration: '6s',
          animationDelay: '1s'
        }}
      />

      <Card
        className={`fade-in hover-shadow ${showWelcomeAnimation ? 'pulse' : ''}`}
        style={{
          width: 420,
          padding: '24px',
          boxShadow: '0 20px 40px rgba(0, 82, 204, 0.15)',
          borderRadius: '24px',
          border: 'none',
          overflow: 'hidden',
          transition: 'all 0.5s ease',
          transform: 'translateY(0)',
          position: 'relative',
          zIndex: 1,
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 36 }} className="fade-up">
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 24,
              position: 'relative',
            }}
          >
            <div
              className={showWelcomeAnimation ? "pulse" : ""}
              style={{
                position: 'absolute',
                width: '160px',
                height: '160px',
                background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
                borderRadius: '50%',
                opacity: 0.7,
                transition: 'all 0.5s ease',
              }}
            />
            <img
              src={logo}
              alt="XPay Logo"
              className="float"
              style={{
                width: 140,
                height: 140,
                position: 'relative',
                zIndex: 1,
                filter: 'drop-shadow(0 4px 12px rgba(24, 144, 255, 0.2))',
                transition: 'all 0.5s ease',
              }}
              onError={(e) => {
                // 如果图片加载失败，显示文字
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>
          <Title level={2} className="fade-up" style={{ marginBottom: 12, color: '#262626', fontWeight: 600 }}>
            欢迎回来
          </Title>
          <Paragraph className="fade-up" style={{ color: '#595959', fontSize: '16px', marginBottom: 24 }}>
            请输入您的钱包密码进行解锁
          </Paragraph>

          {/* 安全提示图标 */}
          <div className="stagger-fade-in" style={{ display: 'flex', justifyContent: 'center', gap: '24px', marginBottom: '24px' }}>
            <Tooltip title="密码安全加密存储">
              <div style={{ textAlign: 'center' }}>
                <SecurityScanOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                <Text style={{ display: 'block', marginTop: '4px', fontSize: '12px', color: '#8c8c8c' }}>安全加密</Text>
              </div>
            </Tooltip>
            <Tooltip title="双重验证保障">
              <div style={{ textAlign: 'center' }}>
                <SafetyOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                <Text style={{ display: 'block', marginTop: '4px', fontSize: '12px', color: '#8c8c8c' }}>双重验证</Text>
              </div>
            </Tooltip>
            <Tooltip title="完全控制您的资产">
              <div style={{ textAlign: 'center' }}>
                <KeyOutlined style={{ fontSize: '24px', color: '#fa8c16' }} />
                <Text style={{ display: 'block', marginTop: '4px', fontSize: '12px', color: '#8c8c8c' }}>完全控制</Text>
              </div>
            </Tooltip>
          </div>
        </div>

        <Form form={form} name="unlock" initialValues={{ remember: true }} size="large" className="fade-up">
          <Form.Item name="password" rules={[{ required: true, message: '请输入密码!' }]}>
            <Input.Password
              prefix={<LockOutlined style={{ color: '#1890ff', fontSize: '18px' }} />}
              placeholder="输入密码"
              onChange={handlePasswordChange}
              style={{
                height: '56px',
                borderRadius: '12px',
                boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
                transition: 'all 0.3s ease',
                fontSize: '16px',
              }}
              suffix={loading ? <Spin size="small" /> : null}
              autoFocus
            />
          </Form.Item>

          {/* 密码强度指示器 */}
          {passwordEntered && (
            <div style={{ marginBottom: 16 }} className="fade-in">
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>密码强度</Text>
                <Text type={passwordStrength > 2 ? "success" : passwordStrength > 0 ? "warning" : "secondary"} style={{ fontSize: 12 }}>
                  {getPasswordStrengthText()}
                </Text>
              </div>
              <div className={`password-strength-indicator ${getPasswordStrengthClass()}`}></div>
            </div>
          )}

          <Form.Item style={{ marginTop: passwordEntered ? 28 : 0 }}>
            <Tooltip
              title={!passwordEntered ? "请输入密码" : "点击进入下一步"}
              placement="right"
              open={!passwordEntered && unlockAttempt > 0}
            >
              <Button
                type="primary"
                onClick={handleNextStep}
                className={`hover-shadow ${passwordEntered ? 'fade-in' : ''}`}
                style={{
                  height: '56px',
                  borderRadius: '12px',
                  fontSize: '16px',
                  fontWeight: 500,
                  boxShadow: passwordEntered ? '0 4px 12px rgba(24, 144, 255, 0.25)' : 'none',
                  transition: 'all 0.3s ease',
                  opacity: passwordEntered ? 1 : 0.6,
                }}
                block
                icon={loading ? null : <ArrowRightOutlined />}
                disabled={!passwordEntered || loading}
              >
                {loading ? "验证中..." : "下一步"}
              </Button>
            </Tooltip>
          </Form.Item>

          {/* 忘记密码提示 */}
          {unlockAttempt > 1 && (
            <div className="fade-in" style={{ textAlign: 'center', marginTop: 16 }}>
              <Text type="secondary" style={{ fontSize: 13 }}>
                忘记密码？请使用助记词重新导入钱包
              </Text>
            </div>
          )}
        </Form>

        {/* 版权信息 */}
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            © {new Date().getFullYear()} XPay 钱包 · 安全可靠的数字资产管理
          </Text>
        </div>
      </Card>

      {/* Google验证码对话框 - 移除可能导致闪烁的动画类 */}
      <Modal
        title={<div style={{ textAlign: 'center', fontSize: '20px', fontWeight: 600 }}>安全验证</div>}
        open={googleModalVisible}
        onCancel={() => !loading && setGoogleModalVisible(false)}
        width={420}
        centered
        styles={{
          body: { padding: '28px 24px 16px' },
          mask: { backdropFilter: 'blur(4px)' },
          content: {
            borderRadius: '24px',
            overflow: 'hidden',
            boxShadow: '0 20px 40px rgba(0, 82, 204, 0.15)'
          },
          // 添加过渡效果，使打开和关闭更平滑
          wrapper: {
            transition: 'all 0.3s cubic-bezier(0.23, 1, 0.32, 1)'
          }
        }}
        footer={null}
        className="google-auth-modal"
        destroyOnClose={false} // 保持DOM结构，避免重建导致的闪烁
        maskClosable={false} // 防止意外点击关闭
      >
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <div
            style={{
              display: 'inline-flex',
              justifyContent: 'center',
              alignItems: 'center',
              width: '90px',
              height: '90px',
              background: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
              borderRadius: '50%',
              boxShadow: '0 8px 20px rgba(24, 144, 255, 0.25)',
              marginBottom: '20px',
              transition: 'all 0.3s ease',
            }}
          >
            <SafetyOutlined
              style={{
                fontSize: '45px',
                color: '#1890ff',
              }}
            />
          </div>
          <Title level={4} style={{ marginBottom: '8px' }}>双重验证</Title>
          <Paragraph style={{
            color: '#595959',
            fontSize: '15px',
            lineHeight: '1.5',
            maxWidth: '320px',
            margin: '0 auto 8px'
          }}>
            请输入您的谷歌验证器中显示的6位验证码
          </Paragraph>

          {/* 验证码输入提示 */}
          <div className={`tooltip-bounce ${googleCodeFocused ? 'fade-in' : ''}`} style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '8px',
            marginTop: '8px',
            opacity: googleCodeFocused ? 1 : 0,
            transition: 'opacity 0.3s ease'
          }}>
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
            <Text style={{ color: '#1890ff', fontSize: '13px' }}>验证码每30秒更新一次</Text>
          </div>
        </div>

        <Form
          form={googleForm}
          layout="vertical"
          name="googleAuth"
          preserve={false} // 不保留表单状态，避免状态冲突
        >
          <Form.Item
            name="googleCode"
            rules={[
              { required: true, message: '请输入谷歌验证码' },
              { pattern: /^\d{6}$/, message: '验证码必须是6位数字' },
            ]}
          >
            <Input
              prefix={<SafetyOutlined style={{ color: '#1890ff' }} />}
              placeholder="请输入6位验证码"
              maxLength={6}
              size="large"
              style={{
                width: '100%',
                height: '60px',
                borderRadius: '12px',
                fontSize: '24px',
                textAlign: 'center',
                letterSpacing: '12px',
                boxShadow: '0 2px 8px rgba(0, 82, 204, 0.08)',
                transition: 'all 0.3s ease',
              }}
              autoFocus
              onFocus={() => setGoogleCodeFocused(true)}
              onBlur={() => setGoogleCodeFocused(false)}
            />
          </Form.Item>

          <div style={{ display: 'flex', gap: '16px', marginTop: '28px' }}>
            <Button
              onClick={() => {
                if (!loading) {
                  // 先重置表单，再关闭弹窗，避免状态变化导致的闪烁
                  resetGoogleForm();
                  setGoogleModalVisible(false);
                }
              }}
              disabled={loading}
              style={{
                flex: 1,
                height: '50px',
                borderRadius: '12px',
                fontSize: '16px',
                boxShadow: 'none',
                transition: 'all 0.3s ease',
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={handleGoogleAuthSubmit}
              style={{
                flex: 1,
                height: '50px',
                borderRadius: '12px',
                fontSize: '16px',
                fontWeight: 500,
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 12px rgba(24, 144, 255, 0.25)'
              }}
            >
              {loading ? "验证中..." : "验证并解锁"}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default Login;
