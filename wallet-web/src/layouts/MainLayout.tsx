import React, { useEffect, useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space, Spin } from 'antd'; // Removed message
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { getAuth } from '../services/api/auth/auth'; // Added getAuth
import {
  DashboardOutlined,
  // SwapOutlined,
  // HistoryOutlined,
  SettingOutlined,
  LogoutOutlined,
  LinkOutlined,
  WalletOutlined,
  DollarOutlined,
  // RetweetOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
  ScheduleOutlined,
} from '@ant-design/icons';
import { useAppDispatch } from '../hooks/useAppDispatch';
import { useAppSelector } from '../hooks/useAppSelector';
import { logout } from '../store/slices/authSlice';
import { resetWallet } from '../store/slices/walletSlice';
const { Header, Sider, Content } = Layout;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);
  const [layoutReady, setLayoutReady] = useState(false);

  useEffect(() => {
    // 添加一个短暂的延迟，确保认证状态已经稳定
    const timer = window.setTimeout(() => {
      if (!localStorage.getItem('accessToken')) {
        navigate('/login');
      } else {
        setLayoutReady(true);
        // 如果没有用户信息但有token，使用localStorage中的用户名
        // if (localStorage.getItem('token')) {
        //   const storedUsername = localStorage.getItem('username') || '';
        //   setUsername(storedUsername);
        // } else if (user) {
        //   setUsername(user.username);
        // }
      }
    }, 100);

    return () => window.clearTimeout(timer);
  }, [isAuthenticated, navigate, user]);

  const handleLogout = async () => { // Modified to async
    const refreshToken = localStorage.getItem('refreshToken');

    // Optional: Add a loading state to prevent multiple clicks
    // const [isLoggingOut, setIsLoggingOut] = useState(false);
    // if (isLoggingOut) return;
    // setIsLoggingOut(true);

    if (refreshToken) {
      try {
        const authApi = getAuth();
        await authApi.postWalletLogout({ refreshToken });
        // message.success('已从服务器会话中退出'); // Optional user notification
      } catch (error) {
        console.error('服务端登出失败:', error);
        // message.error('服务端登出失败，但将继续本地操作'); // Optional user notification
        // Even if backend logout fails, continue with frontend logout to lock the UI.
      }
    } else {
      console.warn('登出时未找到 refreshToken');
      // If no refreshToken, proceed directly to local logout
    }

    // Ensure local logout logic always executes
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken'); // Added removal of refreshToken
    dispatch(logout());
    dispatch(resetWallet());
    navigate('/login');

    // setIsLoggingOut(false); // Clear loading state
  };

  // 如果布局还没准备好，显示加载状态
  if (!layoutReady) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        width={260}
        theme="dark"
        breakpoint="lg"
        collapsedWidth="0"
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          background: 'linear-gradient(180deg, #001529 0%, #002140 100%)',
          boxShadow: '2px 0 8px rgba(0,21,41,0.15)',
        }}
      >
        <div
          style={{
            height: 80,
            padding: '20px 24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
            gap: '12px',
            borderBottom: '1px solid rgba(255,255,255,0.1)',
            marginBottom: '8px',
          }}
        >
          <div
            className="pulse"
            style={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              boxShadow: '0 2px 8px rgba(24, 144, 255, 0.5)',
            }}
          >
            <WalletOutlined style={{ color: 'white', fontSize: '22px' }} />
          </div>
          <h1 style={{
            color: 'white',
            margin: 0,
            fontSize: '20px',
            fontWeight: 600,
            letterSpacing: '0.5px',
          }}>XPay钱包</h1>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          defaultSelectedKeys={['/dashboard']}
          style={{
            background: 'transparent',
            borderRight: 'none',
            padding: '12px 8px',
          }}
          className="main-menu"
          items={[
            {
              key: '/dashboard',
              icon: <DashboardOutlined style={{ fontSize: '18px' }} />,
              label: <Link to="/dashboard" style={{ fontSize: '15px', fontWeight: 500 }}>仪表盘</Link>,
              style: {
                marginBottom: '8px',
                borderRadius: '8px',
                height: '48px',
                lineHeight: '48px',
              }
            },
            // { key: "/wallet", icon: <WalletOutlined />, label: <Link to="/wallet">我的钱包</Link> },
            {
              key: '/addresses',
              icon: <LinkOutlined style={{ fontSize: '18px' }} />,
              label: <Link to="/addresses" style={{ fontSize: '15px', fontWeight: 500 }}>地址管理</Link>,
              style: {
                marginBottom: '8px',
                borderRadius: '8px',
                height: '48px',
                lineHeight: '48px',
              }
            },
            // {
            //   key: '/transactions',
            //   icon: <HistoryOutlined style={{ fontSize: '18px' }} />,
            //   label: <Link to="/transactions" style={{ fontSize: '15px', fontWeight: 500 }}>交易记录</Link>,
            //   style: {
            //     marginBottom: '8px',
            //     borderRadius: '8px',
            //     height: '48px',
            //     lineHeight: '48px',
            //   }
            // },
            {
              key: '/recharge',
              icon: <VerticalAlignBottomOutlined style={{ fontSize: '18px' }} />,
              label: <Link to="/recharge" style={{ fontSize: '15px', fontWeight: 500 }}>充值记录</Link>,
              style: {
                marginBottom: '8px',
                borderRadius: '8px',
                height: '48px',
                lineHeight: '48px',
              }
            },
           
            {
              key: '/withdraw-plan',
              icon: <ScheduleOutlined style={{ fontSize: '18px' }} />,
              label: <Link to="/withdraw-plan" style={{ fontSize: '15px', fontWeight: 500 }}>归集计划</Link>,
              style: {
                marginBottom: '8px',
                borderRadius: '8px',
                height: '48px',
                lineHeight: '48px',
              }
            },
            {
              key: '/fee-management',
              icon: <DollarOutlined style={{ fontSize: '18px' }} />,
              label: <Link to="/fee-management" style={{ fontSize: '15px', fontWeight: 500 }}>费用管理</Link>,
              style: {
                marginBottom: '8px',
                borderRadius: '8px',
                height: '48px',
                lineHeight: '48px',
              }
            },

            {
              key: '/withdraw',
              icon: <VerticalAlignTopOutlined style={{ fontSize: '18px' }} />,
              label: <Link to="/withdraw" style={{ fontSize: '15px', fontWeight: 500 }}>归集记录</Link>,
              style: {
                marginBottom: '8px',
                borderRadius: '8px',
                height: '48px',
                lineHeight: '48px',
              }
            },
            // {
            //   key: '/transfer',
            //   icon: <SwapOutlined style={{ fontSize: '18px' }} />,
            //   label: <Link to="/transfer" style={{ fontSize: '15px', fontWeight: 500 }}>归集记录</Link>,
            //   style: {
            //     marginBottom: '8px',
            //     borderRadius: '8px',
            //     height: '48px',
            //     lineHeight: '48px',
            //   }
            // },
            // { key: '/collection', icon: <RetweetOutlined />, label: <Link to="/collection">归集操作</Link> },
            {
              key: '/settings',
              icon: <SettingOutlined style={{ fontSize: '18px' }} />,
              label: <Link to="/settings" style={{ fontSize: '15px', fontWeight: 500 }}>设置</Link>,
              style: {
                marginBottom: '8px',
                borderRadius: '8px',
                height: '48px',
                lineHeight: '48px',
              }
            },
          ]}
        />
      </Sider>
      <Layout style={{ marginLeft: 260 }}>
        <Header
          className="fade-in"
          style={{
            background: 'white',
            padding: '0 24px',
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            position: 'sticky',
            top: 0,
            zIndex: 1,
            width: '100%',
            boxShadow: '0 2px 8px rgba(0, 21, 41, 0.08)',
            height: '64px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <Space>
            {(user || localStorage.getItem('accessToken')) && (
              <Dropdown
                menu={{
                  items: [
                    { type: 'divider' },
                    { key: 'logout', icon: <LogoutOutlined />, label: '锁定钱包', onClick: handleLogout },
                  ],
                }}
                placement="bottomRight"
              >
                <Button
                  type="text"
                  className="hover-shadow transition-all"
                  style={{
                    height: 64,
                    display: 'flex',
                    alignItems: 'center',
                    padding: '0 12px',
                    borderRadius: '8px',
                  }}
                >
                  <Space>
                    <Avatar
                      icon={<LogoutOutlined />}
                      style={{
                        backgroundColor: '#1890ff',
                        boxShadow: '0 2px 8px rgba(24, 144, 255, 0.2)',
                      }}
                    />
                    <span style={{ marginLeft: '8px', fontWeight: 500 }}>
                      {localStorage.getItem('username') || '用户'}
                    </span>
                  </Space>
                </Button>
              </Dropdown>
            )}
          </Space>
        </Header>
        <Content
          className="fade-in"
          style={{
            margin: '24px 24px',
            padding: 0,
            background: 'transparent',
            minHeight: 280,
            overflow: 'initial',
            borderRadius: '12px',
          }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
