import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { configService } from './config';

// 扩展 AxiosInstance 类型，使其返回值为 response.data 而不是整个 response
interface ApiInstance extends AxiosInstance {
    get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
    delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
    patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
}

// 创建 axios 实例
const instance = axios.create({
    baseURL: configService.getApiUrl(), // Use runtime configuration
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
}) as ApiInstance;

// Initialize configuration and update baseURL when loaded
configService.loadConfig().then((config) => {
    instance.defaults.baseURL = config.API_URL;
    console.log('Axios baseURL updated to:', config.API_URL);
}).catch((error) => {
    console.warn('Failed to load configuration for axios, using fallback URL:', error);
});

// 请求拦截器
instance.interceptors.request.use(
    (config) => {
        // 确保使用最新的 API URL
        const currentApiUrl = configService.getApiUrl();
        if (currentApiUrl && config.baseURL !== currentApiUrl) {
            config.baseURL = currentApiUrl;
        }

        // 从 localStorage 获取 token
        const accessToken = localStorage.getItem('accessToken');
        if (accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 响应拦截器
instance.interceptors.response.use(
    (response: AxiosResponse) => {
        // console.log('response', response.data);

        // 检查Content-Type和Content-Disposition头，判断是否是文件下载
        const contentType = response.headers['content-type'];
        const contentDisposition = response.headers['content-disposition'];

        // 如果是文件下载类型（csv、excel、二进制流等）
        if (
            (contentType && (
                contentType.includes('text/csv') ||
                contentType.includes('application/octet-stream') ||
                contentType.includes('application/vnd.ms-excel') ||
                contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            )) ||
            (contentDisposition && contentDisposition.includes('attachment'))
        ) {
            // 从Content-Disposition中提取文件名
            let filename = 'download';
            if (contentDisposition) {
                const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                const matches = filenameRegex.exec(contentDisposition);
                if (matches != null && matches[1]) {
                    filename = matches[1].replace(/['"]/g, '');
                }
            }

            // 提取接口名称 (从URL路径中获取最后一部分作为接口名)
            const urlPath = response.config.url || '';
            const apiName = urlPath.split('/').filter(Boolean).pop() || 'api';

            // 获取当前日期时间，格式化为YYYYMMDD_HHmmss
            const now = new Date();
            const dateStr = now.getFullYear() +
                ('0' + (now.getMonth() + 1)).slice(-2) +
                ('0' + now.getDate()).slice(-2);
            const timeStr = ('0' + now.getHours()).slice(-2) +
                ('0' + now.getMinutes()).slice(-2) +
                ('0' + now.getSeconds()).slice(-2);
            const dateTimeStr = `${dateStr}_${timeStr}`;

            // 如果没有指定文件名，根据内容类型、接口名称和时间生成文件名
            if (filename === 'download') {
                filename = `${apiName}_${dateTimeStr}`;
                if (contentType && contentType.includes('csv')) {
                    filename += '.csv';
                } else if (contentType && contentType.includes('excel')) {
                    filename += '.xlsx';
                }
            } else {
                // 如果有提供文件名，也在其中添加接口和时间信息
                const fileExt = filename.includes('.') ? filename.substring(filename.lastIndexOf('.')) : '';
                const fileBaseName = filename.includes('.') ? filename.substring(0, filename.lastIndexOf('.')) : filename;
                filename = `${fileBaseName}_${apiName}_${dateTimeStr}${fileExt}`;
            }

            // 创建blob并下载
            const blob = new Blob([response.data], { type: contentType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', filename);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            // 返回空对象或适当的响应，阻止后续处理
            return {};
        }

        // 处理正常JSON响应
        const data = response.data;
        if (data.code === 0) {
            return data.data;
        } else {
            if (data.code === 61 && window.location.pathname !== '/login') {
                console.log('Access token expired (code 61), logging out and redirecting to /login. Response data:', data);
                localStorage.removeItem('accessToken');
                // Consider dispatching a logout action here if using Redux/ Zustand etc.
                // import { store } from '../store'; // Example import
                // import { logout } from '../store/slices/authSlice'; // Example import
                // store.dispatch(logout()); // Example dispatch
                window.location.href = '/login';
                // It's important to reject the promise so the caller can handle the error if needed.
                return Promise.reject(data);
            }

            return Promise.reject(data);
        }
    },
    (error) => {
        if (error.response) {
            // 处理错误响应
            const { status, data } = error.response;

            // 未授权，清除 token 并重定向到登录页
            if (status === 401) {
                localStorage.removeItem('accessToken');
                window.location.href = '/login';
            }


            // 处理其他错误状态码
            console.error(`请求错误 ${status}:`, data);
        } else if (error.request) {
            // 请求已发出但未收到响应
            console.error('网络错误，未收到响应:', error.request);
        } else {
            // 设置请求时发生错误
            console.error('请求配置错误:', error.message);
        }

        return Promise.reject(error);
    }
);

// 将 axiosInstance 作为函数导出，以符合 orval 的要求
export const axiosInstance = <T>(config: AxiosRequestConfig): Promise<T> => {
    return instance(config);
};

// 同时导出实例本身，以便在其他地方直接使用
export { instance };