import api from './api';

export interface LoginRequest {
    username: string;
    password: string;
}

export interface RegisterRequest {
    username: string;
    password: string;
    email: string;
}

export interface UserInfo {
    id: string;
    username: string;
    email: string;
    nickname?: string;
    avatar?: string;
    walletAddress?: string;
    createdAt: string;
}

// 用户登录
export const login = (data: LoginRequest) => {
    return api.post<{ token: string, user: UserInfo }>('/auth/login', data);
};

// 用户注册
export const register = (data: RegisterRequest) => {
    return api.post<{ token: string, user: UserInfo }>('/auth/register', data);
};

// 获取当前用户信息
export const getCurrentUser = () => {
    return api.get<UserInfo>('/auth/me');
};

// 退出登录
export const logout = () => {
    localStorage.removeItem('token');
    return Promise.resolve();
};

// 检查用户是否已登录
export const isAuthenticated = () => {
    return !!localStorage.getItem('token');
}; 