import api from './api';
import { UserInfo } from './authService';

export interface ProfileUpdateRequest {
    email?: string;
    nickname?: string;
    avatar?: string;
}

export interface PasswordUpdateRequest {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
}

export interface NotificationSettings {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    transactionAlerts: boolean;
    securityAlerts: boolean;
    marketingAlerts: boolean;
}

// 更新用户个人资料
export const updateUserProfile = (data: ProfileUpdateRequest) => {
    return api.put<UserInfo>('/users/profile', data);
};

// 更新用户密码
export const updateUserPassword = (data: PasswordUpdateRequest) => {
    return api.put<{ success: boolean }>('/users/password', data);
};

// 获取用户通知设置
export const getUserNotificationSettings = () => {
    return api.get<NotificationSettings>('/users/notifications/settings');
};

// 更新用户通知设置
export const updateUserNotificationSettings = (data: NotificationSettings) => {
    return api.put<NotificationSettings>('/users/notifications/settings', data);
};

// 启用两步验证
export const enableTwoFactorAuth = () => {
    return api.post<{ qrCode: string, secret: string }>('/users/2fa/enable');
};

// 禁用两步验证
export const disableTwoFactorAuth = (code: string) => {
    return api.post<{ success: boolean }>('/users/2fa/disable', { code });
};

// 验证两步验证码
export const verifyTwoFactorAuthCode = (code: string) => {
    return api.post<{ success: boolean }>('/users/2fa/verify', { code });
}; 