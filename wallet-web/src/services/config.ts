/**
 * Configuration service for loading environment variables at runtime
 * This service loads configuration from the .env file in the public directory
 */

interface AppConfig {
  API_URL: string;
}

class ConfigService {
  private config: AppConfig | null = null;
  private loading = false;
  private loadPromise: Promise<AppConfig> | null = null;

  /**
   * Load configuration from .env file
   */
  async loadConfig(): Promise<AppConfig> {
    // If already loaded, return cached config
    if (this.config) {
      return this.config;
    }

    // If already loading, return the existing promise
    if (this.loading && this.loadPromise) {
      return this.loadPromise;
    }

    // Start loading
    this.loading = true;
    this.loadPromise = this.fetchConfig();

    try {
      this.config = await this.loadPromise;
      return this.config;
    } finally {
      this.loading = false;
    }
  }

  /**
   * Fetch configuration from .env file
   */
  private async fetchConfig(): Promise<AppConfig> {
    try {
      // Try to fetch .env file from public directory
      const response = await fetch('/.env');
      
      if (!response.ok) {
        throw new Error(`Failed to load .env file: ${response.status}`);
      }

      const envText = await response.text();
      const config = this.parseEnvFile(envText);

      console.log('Configuration loaded successfully:', config);
      return config;
    } catch (error) {
      console.warn('Failed to load .env file, using fallback configuration:', error);
      
      // Fallback to build-time environment variables or defaults
      return this.getFallbackConfig();
    }
  }

  /**
   * Parse .env file content
   */
  private parseEnvFile(envText: string): AppConfig {
    const config: Partial<AppConfig> = {};
    
    const lines = envText.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Skip empty lines and comments
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue;
      }
      
      // Parse key=value pairs
      const equalIndex = trimmedLine.indexOf('=');
      if (equalIndex === -1) {
        continue;
      }
      
      const key = trimmedLine.substring(0, equalIndex).trim();
      const value = trimmedLine.substring(equalIndex + 1).trim();
      
      // Remove quotes if present
      const cleanValue = value.replace(/^["']|["']$/g, '');
      
      if (key === 'API_URL') {
        config.API_URL = cleanValue;
      }
    }

    return this.validateConfig(config);
  }

  /**
   * Validate and provide defaults for configuration
   */
  private validateConfig(config: Partial<AppConfig>): AppConfig {
    return {
      API_URL: config.API_URL || this.getFallbackApiUrl(),
    };
  }

  /**
   * Get fallback configuration when .env file is not available
   */
  private getFallbackConfig(): AppConfig {
    return {
      API_URL: this.getFallbackApiUrl(),
    };
  }

  /**
   * Get fallback API URL from various sources
   */
  private getFallbackApiUrl(): string {
    // Try build-time environment variable first
    if (process.env.REACT_APP_API_URL) {
      return process.env.REACT_APP_API_URL;
    }
    throw new Error('Failed to load API URL');
  }

  /**
   * Get current configuration (synchronous)
   * Returns null if configuration hasn't been loaded yet
   */
  getConfig(): AppConfig | null {
    return this.config;
  }

  /**
   * Get API URL (synchronous)
   * Returns fallback URL if configuration hasn't been loaded yet
   */
  getApiUrl(): string {
    return this.config?.API_URL || this.getFallbackApiUrl();
  }

  /**
   * Reset configuration (useful for testing)
   */
  reset(): void {
    this.config = null;
    this.loading = false;
    this.loadPromise = null;
  }
}

// Export singleton instance
export const configService = new ConfigService();

// Export types
export type { AppConfig };
