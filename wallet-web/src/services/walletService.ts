import api from './api';
import { TransferRequest, DepositRequest, WithdrawRequest } from '../types/wallet';
import { getAuth } from './api/auth/auth';
import { getWallet } from './api/wallet/wallet';
import { getSetting } from './api/setting/setting';
import { WalletApiApiWalletV1WalletSettingReq } from './api/model';
// import type { // Removed GetWalletStatusResult import
//     GetWalletStatusResult
// } from './api/wallet/wallet';

export interface WalletBalance {
    balance: string;
    currency: string;
}

export interface Transaction {
    id: string;
    amount: string;
    currency: string;
    type: 'deposit' | 'withdrawal' | 'transfer';
    status: 'pending' | 'completed' | 'failed';
    timestamp: string;
    description?: string;
    toAddress?: string;
    fromAddress?: string;
    walletId: string;
}

export interface WalletAddressItem {
    id: string;
    address: string;
    name: string;
    balance: string;
    currency: string;
    createdAt: string;
    lastUsed: string;
    type: 'main' | 'savings' | 'investment';
    status: 'active' | 'inactive';
    tags?: string[];
}

// 获取钱包余额
export const getWalletBalance = (): Promise<WalletBalance> => {
    return api.get<WalletBalance>('/wallet/balance');
};

// 获取交易历史
export const getTransactionHistory = (page = 1, limit = 10): Promise<{ transactions: Transaction[], total: number }> => {
    return api.get<{ transactions: Transaction[], total: number }>('/wallet/transactions', {
        params: { page, limit }
    });
};

// 发起转账
export const createTransfer = (toAddress: string, amount: string, currency: string): Promise<{ transactionId: string }> => {
    return api.post<{ transactionId: string }>('/wallet/transfer', {
        toAddress,
        amount,
        currency
    });
};

// 获取钱包地址
export const getWalletAddress = (): Promise<{ address: string }> => {
    return api.get<{ address: string }>('/wallet/address');
};

// // 检查钱包是否已初始化 (REMOVED as per plan)
// export const checkWalletInitialized = (): Promise<boolean> => {
//     return getWallet().getWalletStatus() // getWalletStatus is in wallet.ts
//         .then((result: GetWalletStatusResult) => {
//             return result.is_initialized === true;
//         });
// };

// // 获取钱包状态 (REMOVED as per plan)
// export const getWalletStatus = (): Promise<GetWalletStatusResult> => {
//     return getWallet().getWalletStatus(); // getWalletStatus is in wallet.ts
// };

// 获取钱包信息
export const getWalletInfo = (): Promise<any> => {
    return getWallet().getWalletInfo();
};

// 修改钱包密码
export const changeWalletPassword = (oldPassword: string, newPassword: string, googleCode: string): Promise<any> => {
    return getAuth().postWalletChangePassword({ // postWalletChangePassword is in auth.ts
        old_password: oldPassword,
        new_password: newPassword,
        repeat_password: newPassword,
        google_code: googleCode
    });
};

// // 设置谷歌验证码开关
// export const setGoogleCodeSwitch = (enable: boolean): Promise<any> => {
//     return getWalletAfter().postWalletSetGoogleCodeSwitch({
//         switch: enable ? 1 : 0
//     });
// };

// 绑定谷歌验证码
// export const bindGoogleCode = (secret: string, code: string, oldGoogleCode?: string): Promise<any> => {
//     const params: any = {
//         secret,
//         code
//     };

//     // 如果提供了旧的谷歌验证码，则添加到请求参数中
//     if (oldGoogleCode) {
//         params.old_google_code = oldGoogleCode;
//     }

// return getWalletAfter().postWalletBindGoogleCode(params);
// };

// // 关闭谷歌验证码
// export const closeGoogleCode = (googleCode: string): Promise<any> => {
//     return getWalletAfter().postWalletCloseGoogleCode({
//         google_code: googleCode
//     });
// };

// 钱包认证(解锁)
export const authenticateWallet = (password: string, googleCode: string): Promise<any> => {
    return getAuth().postWalletAuth({ // postWalletAuth is in auth.ts
        password,
        google_code: googleCode
    });
};

// 生成钱包助记词
export const generateWalletMnemonic = (): Promise<string> => {
    return getWallet().getWalletGenerate() // getWalletGenerate is in wallet.ts
        .then((result: any) => {
            return result.mnemonic || '';
        });
};

// 生成谷歌验证码
export const generateGoogleAuthCode = (): Promise<{ secret: string, qrCode: string }> => {
    // 使用真实API
    return getAuth().getWalletGenerateGoogleCode() // getWalletGenerateGoogleCode is in auth.ts
        .then((result: any) => {
            console.log('API返回的谷歌验证码数据:', result);
            // API返回的qr_code是URL格式字符串而非Base64图片，直接作为二维码内容
            return {
                secret: result.secret || '',
                qrCode: result.qr_code || ''
            };
        });
};

// 重新绑定谷歌验证码
export const rebindGoogleCode = (secret: string, code: string, oldGoogleCode?: string): Promise<any> => {
    const params: any = {
        secret,
        code
    };

    // 如果提供了旧的谷歌验证码，则添加到请求参数中
    if (oldGoogleCode) {
        params.old_google_code = oldGoogleCode;
    }

    return getAuth().postWalletRebindGoogleCode(params); // postWalletRebindGoogleCode is in auth.ts
};

// 创建新钱包
export const createWallet = (mnemonic: string, password: string, googleCode?: string, secret?: string): Promise<{ address: string }> => {
    // 构建请求参数，如果有Google 2FA参数则包含它们
    const requestData: any = { mnemonic, password };

    if (googleCode && secret) {
        requestData.google_code = googleCode;
        requestData.secret = secret;
    }

    // 使用wallet-before中的postWalletCreate API
    return getWallet().postWalletCreate(requestData) // postWalletCreate is in wallet.ts
        .then((result: any) => {
            // 处理结果，确保返回正确的格式
            return {
                address: result.address || '',
                success: result.success
            };
        });
};

// 导入钱包
export const importWallet = (mnemonic: string, password: string, googleCode?: string, secret?: string): Promise<{ address: string }> => {
    // 构建请求参数，如果有Google 2FA参数则包含它们
    const requestData: any = {
        mnemonic,
        password,
        is_import: true // 添加标志表明这是导入操作
    };

    if (googleCode && secret) {
        requestData.google_code = googleCode;
        requestData.secret = secret;
    }

    // 使用wallet-before中的API
    return getWallet().postWalletCreate(requestData) // postWalletCreate is in wallet.ts
        .then((result: any) => {
            // 处理结果，确保返回正确的格式
            return {
                address: result.address || '',
                success: result.success
            };
        });
};

// 转账功能
export const transferFunds = (transferData: TransferRequest): Promise<{ transactionId: string }> => {
    return api.post<{ transactionId: string }>('/wallet/transfer', transferData);
};

// 充值功能
export const depositFunds = (depositData: DepositRequest): Promise<{ transactionId: string }> => {
    return api.post<{ transactionId: string }>('/wallet/deposit', depositData);
};

// 提款功能
export const withdrawFunds = (withdrawData: WithdrawRequest): Promise<{ transactionId: string }> => {
    return api.post<{ transactionId: string }>('/wallet/withdraw', withdrawData);
};

// 导出钱包私钥
export const exportWalletPrivateKey = (password: string): Promise<{ privateKey: string }> => {
    return api.post<{ privateKey: string }>('/wallet/export', { password });
};

// 更新钱包设置（统一接口）
export const updateWalletSettings = (
    settingsData: Partial<WalletApiApiWalletV1WalletSettingReq>
): Promise<any> => {
    const settingApi = getSetting();
    // 确保必填字段存在
    if (!settingsData.google_code || !settingsData.password) {
        throw new Error('Google验证码和密码是必填项');
    }
    // 将部分参数转换为完整参数
    const fullSettingsData = settingsData as WalletApiApiWalletV1WalletSettingReq;
    return settingApi.postWalletSetting(fullSettingsData);
};

// 获取钱包地址列表
export const getWalletAddressList = (
    page = 1,
    pageSize = 10,
    filters?: {
        type?: string;
        status?: string;
        search?: string;
        tags?: string[];
    }
): Promise<{ addresses: WalletAddressItem[], total: number }> => {
    return api.get<{ addresses: WalletAddressItem[], total: number }>('/wallet/addresses', {
        params: {
            page,
            pageSize,
            ...filters
        }
    });
};

// 获取钱包列表
export interface Wallet {
    id: string;
    name: string;
    description?: string;
    type: 'main' | 'savings' | 'investment';
    status: 'active' | 'inactive';
    createdAt: string;
}

export const getWalletList = (): Promise<Wallet[]> => {
    return api.get<Wallet[]>('/wallet/list');
};

// 批量生成地址
export interface GenerateAddressesParams {
    walletId: string;
    network: string;
    count: number;
}

export const generateAddresses = (params: GenerateAddressesParams): Promise<{ success: boolean; count: number }> => {
    return api.post<{ success: boolean; count: number }>('/wallet/addresses/generate', params);
};