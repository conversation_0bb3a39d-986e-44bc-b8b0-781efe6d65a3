import { AxiosInstance, AxiosRequestConfig } from 'axios';

// 扩展 AxiosInstance 类型，使其返回值为 response.data 而不是整个 response
interface ApiInstance extends AxiosInstance {
    get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
    delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
}

// 创建一个 mock API 实例
const mockApi = {
    defaults: {} as any,
    interceptors: {
        request: {
            use: () => 0,
            eject: () => { },
            clear: () => { }
        },
        response: {
            use: () => 0,
            eject: () => { },
            clear: () => { }
        }
    },
    getUri: (config?: AxiosRequestConfig) => '',
    request: <T = any>(config: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock request to ${config.url}`);
        return Promise.resolve({} as T);
    },
    get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock GET request to ${url}`);
        return Promise.resolve({} as T);
    },
    delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock DELETE request to ${url}`);
        return Promise.resolve({} as T);
    },
    head: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock HEAD request to ${url}`);
        return Promise.resolve({} as T);
    },
    options: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock OPTIONS request to ${url}`);
        return Promise.resolve({} as T);
    },
    post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock POST request to ${url}`, data);
        return Promise.resolve({} as T);
    },
    put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock PUT request to ${url}`, data);
        return Promise.resolve({} as T);
    },
    patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock PATCH request to ${url}`, data);
        return Promise.resolve({} as T);
    },
    postForm: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock POST FORM request to ${url}`, data);
        return Promise.resolve({} as T);
    },
    putForm: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock PUT FORM request to ${url}`, data);
        return Promise.resolve({} as T);
    },
    patchForm: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
        console.log(`Mock PATCH FORM request to ${url}`, data);
        return Promise.resolve({} as T);
    }
};

// 使用类型断言将 mockApi 转换为 ApiInstance
const api = mockApi as unknown as ApiInstance;

// 注释掉原来的 axios 实例和拦截器
/*
// 创建axios实例
const api = axios.create({
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080/api',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
}) as ApiInstance;

// 请求拦截器
api.interceptors.request.use(
    (config) => {
        // 从localStorage获取token
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    (response) => {
        // 直接返回响应数据，而不是整个响应对象
        return response.data;
    },
    (error) => {
        if (error.response) {
            // 处理错误响应
            const { status } = error.response;
            if (status === 401) {
                // 未授权，清除token并重定向到登录页
                localStorage.removeItem('token');
                window.location.href = '/login';
            }
        }
        return Promise.reject(error);
    }
);
*/

export default api; 