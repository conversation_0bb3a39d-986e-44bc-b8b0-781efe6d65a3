/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  GetWalletCollectRecordStatisticParams,
  GetWalletCollectTaskListParams,
  GetWalletExportTaskAddressRecordParams,
  GetWalletExportTaskRecordParams,
  GetWalletTaskAddressRecordParams,
  GetWalletTaskAddressStatisticParams,
  WalletApiApiWalletV1CollectRecordStatisticRes,
  WalletApiApiWalletV1CollectTaskListRes,
  WalletApiApiWalletV1ExportTaskAddressRecordRes,
  WalletApiApiWalletV1ExportTaskRecordRes,
  WalletApiApiWalletV1GetCollectAddressRes,
  WalletApiApiWalletV1GetFeeAddressRes,
  WalletApiApiWalletV1SubmitCollectTaskReq,
  WalletApiApiWalletV1SubmitCollectTaskRes,
  WalletApiApiWalletV1TaskAddressRecordRes,
  WalletApiApiWalletV1TaskAddressStatisticRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getCollect = () => {
/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 归集任务统计信息
 */
const getWalletCollectRecordStatistic = (
    params: GetWalletCollectRecordStatisticParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1CollectRecordStatisticRes>(
      {url: `/wallet/collect-record-statistic`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 归集任务列表
 */
const getWalletCollectTaskList = (
    params: GetWalletCollectTaskListParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1CollectTaskListRes>(
      {url: `/wallet/collect-task-list`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 导出任务地址记录
 */
const getWalletExportTaskAddressRecord = (
    params: GetWalletExportTaskAddressRecordParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ExportTaskAddressRecordRes>(
      {url: `/wallet/export-task-address-record`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 导出任务记录
 */
const getWalletExportTaskRecord = (
    params: GetWalletExportTaskRecordParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ExportTaskRecordRes>(
      {url: `/wallet/export-task-record`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 获取归集地址
 */
const getWalletGetCollectAddress = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetCollectAddressRes>(
      {url: `/wallet/get-collect-address`, method: 'GET'
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 获取矿工费地址
 */
const getWalletGetFeeAddress = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetFeeAddressRes>(
      {url: `/wallet/get-fee-address`, method: 'GET'
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 提交归集任务
 */
const postWalletSubmitCollectTask = (
    walletApiApiWalletV1SubmitCollectTaskReq: WalletApiApiWalletV1SubmitCollectTaskReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1SubmitCollectTaskRes>(
      {url: `/wallet/submit-collect-task`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1SubmitCollectTaskReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 任务地址记录
 */
const getWalletTaskAddressRecord = (
    params: GetWalletTaskAddressRecordParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1TaskAddressRecordRes>(
      {url: `/wallet/task-address-record`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 任务地址统计信息
 */
const getWalletTaskAddressStatistic = (
    params?: GetWalletTaskAddressStatisticParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1TaskAddressStatisticRes>(
      {url: `/wallet/task-address-statistic`, method: 'GET',
        params
    },
      );
    }
  return {getWalletCollectRecordStatistic,getWalletCollectTaskList,getWalletExportTaskAddressRecord,getWalletExportTaskRecord,getWalletGetCollectAddress,getWalletGetFeeAddress,postWalletSubmitCollectTask,getWalletTaskAddressRecord,getWalletTaskAddressStatistic}};
export type GetWalletCollectRecordStatisticResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['getWalletCollectRecordStatistic']>>>
export type GetWalletCollectTaskListResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['getWalletCollectTaskList']>>>
export type GetWalletExportTaskAddressRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['getWalletExportTaskAddressRecord']>>>
export type GetWalletExportTaskRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['getWalletExportTaskRecord']>>>
export type GetWalletGetCollectAddressResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['getWalletGetCollectAddress']>>>
export type GetWalletGetFeeAddressResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['getWalletGetFeeAddress']>>>
export type PostWalletSubmitCollectTaskResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['postWalletSubmitCollectTask']>>>
export type GetWalletTaskAddressRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['getWalletTaskAddressRecord']>>>
export type GetWalletTaskAddressStatisticResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getCollect>['getWalletTaskAddressStatistic']>>>
