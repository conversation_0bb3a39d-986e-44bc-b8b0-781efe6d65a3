/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  WalletApiApiWalletV1GetPublicWalletInitStatusRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getPublic = () => {
/**
 * @summary 获取钱包初始化状态(公共)
 */
const getWalletInitStatus = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetPublicWalletInitStatusRes>(
      {url: `/wallet/init-status`, method: 'GET'
    },
      );
    }
  return {getWalletInitStatus}};
export type GetWalletInitStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getPublic>['getWalletInitStatus']>>>
