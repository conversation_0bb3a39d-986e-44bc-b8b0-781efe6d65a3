/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  GetNodeGetTokenBalanceParams,
  WalletApiApiWalletV1GetGastrackerRes,
  WalletApiApiWalletV1GetTokenBalanceRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getNode = () => {
/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 获取gastracker
 */
const getNodeGetGastracker = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetGastrackerRes>(
      {url: `/node/get-gastracker`, method: 'GET'
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 查询代币和 usdt余额
 */
const getNodeGetTokenBalance = (
    params?: GetNodeGetTokenBalanceParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetTokenBalanceRes>(
      {url: `/node/get-token-balance`, method: 'GET',
        params
    },
      );
    }
  return {getNodeGetGastracker,getNodeGetTokenBalance}};
export type GetNodeGetGastrackerResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getNode>['getNodeGetGastracker']>>>
export type GetNodeGetTokenBalanceResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getNode>['getNodeGetTokenBalance']>>>
