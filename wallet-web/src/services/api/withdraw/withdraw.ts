/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  GetWalletWithdrawRecordParams,
  WalletApiApiWalletV1CreateWithdrawReq,
  WalletApiApiWalletV1CreateWithdrawRes,
  WalletApiApiWalletV1ExportWithdrawRecordReq,
  WalletApiApiWalletV1ExportWithdrawRecordRes,
  WalletApiApiWalletV1GetWithdrawRecordRes,
  WalletApiApiWalletV1GetWithdrawRecordStatisticRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getWithdraw = () => {
/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 创建提现记录
 */
const postWalletCreateWithdraw = (
    walletApiApiWalletV1CreateWithdrawReq: WalletApiApiWalletV1CreateWithdrawReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1CreateWithdrawRes>(
      {url: `/wallet/create-withdraw`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1CreateWithdrawReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 导出提现记录
 */
const postWalletExportWithdrawRecord = (
    walletApiApiWalletV1ExportWithdrawRecordReq: WalletApiApiWalletV1ExportWithdrawRecordReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ExportWithdrawRecordRes>(
      {url: `/wallet/export-withdraw-record`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1ExportWithdrawRecordReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 提现记录
 */
const getWalletWithdrawRecord = (
    params: GetWalletWithdrawRecordParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetWithdrawRecordRes>(
      {url: `/wallet/withdraw-record`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 提现记录统计
 */
const getWalletWithdrawRecordStatistic = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetWithdrawRecordStatisticRes>(
      {url: `/wallet/withdraw-record-statistic`, method: 'GET'
    },
      );
    }
  return {postWalletCreateWithdraw,postWalletExportWithdrawRecord,getWalletWithdrawRecord,getWalletWithdrawRecordStatistic}};
export type PostWalletCreateWithdrawResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdraw>['postWalletCreateWithdraw']>>>
export type PostWalletExportWithdrawRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdraw>['postWalletExportWithdrawRecord']>>>
export type GetWalletWithdrawRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdraw>['getWalletWithdrawRecord']>>>
export type GetWalletWithdrawRecordStatisticResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdraw>['getWalletWithdrawRecordStatistic']>>>
