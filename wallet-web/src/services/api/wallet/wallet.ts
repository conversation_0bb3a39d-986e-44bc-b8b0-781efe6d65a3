/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  WalletApiApiWalletV1CheckStatusRes,
  WalletApiApiWalletV1CreateWalletReq,
  WalletApiApiWalletV1CreateWalletRes,
  WalletApiApiWalletV1DashboardStatisticRes,
  WalletApiApiWalletV1GenerateWalletRes,
  WalletApiApiWalletV1GetWalletInfoRes,
  WalletApiApiWalletV1ResetWalletReq,
  WalletApiApiWalletV1ResetWalletRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getWallet = () => {
/**
 * 使用助记词创建钱包 需要输入密码
 * @summary 创建钱包
 */
const postWalletCreate = (
    walletApiApiWalletV1CreateWalletReq: WalletApiApiWalletV1CreateWalletReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1CreateWalletRes>(
      {url: `/wallet/create`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1CreateWalletReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 仪表盘统计
 */
const getWalletDashboardStatistic = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1DashboardStatisticRes>(
      {url: `/wallet/dashboard-statistic`, method: 'GET'
    },
      );
    }
  /**
 * @summary 生成钱包助记词
 */
const getWalletGenerate = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GenerateWalletRes>(
      {url: `/wallet/generate`, method: 'GET'
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 获取钱包信息
 */
const getWalletInfo = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetWalletInfoRes>(
      {url: `/wallet/info`, method: 'GET'
    },
      );
    }
  /**
 * @summary 重置钱包
 */
const postWalletReset = (
    walletApiApiWalletV1ResetWalletReq: WalletApiApiWalletV1ResetWalletReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ResetWalletRes>(
      {url: `/wallet/reset`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1ResetWalletReq
    },
      );
    }
  /**
 * @summary 检测钱包状态
 */
const getWalletStatus = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1CheckStatusRes>(
      {url: `/wallet/status`, method: 'GET'
    },
      );
    }
  return {postWalletCreate,getWalletDashboardStatistic,getWalletGenerate,getWalletInfo,postWalletReset,getWalletStatus}};
export type PostWalletCreateResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWallet>['postWalletCreate']>>>
export type GetWalletDashboardStatisticResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWallet>['getWalletDashboardStatistic']>>>
export type GetWalletGenerateResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWallet>['getWalletGenerate']>>>
export type GetWalletInfoResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWallet>['getWalletInfo']>>>
export type PostWalletResetResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWallet>['postWalletReset']>>>
export type GetWalletStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWallet>['getWalletStatus']>>>
