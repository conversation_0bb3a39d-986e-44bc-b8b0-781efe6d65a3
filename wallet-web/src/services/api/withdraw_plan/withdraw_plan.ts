/**
 * Generated manually for withdraw plan API
 */
import type {
  WalletApiApiWalletV1AddAddressToWithdrawPlanReq,
  WalletApiApiWalletV1AddAddressToWithdrawPlanRes,
  WalletApiApiWalletV1GetWithdrawPlanListReq,
  WalletApiApiWalletV1GetWithdrawPlanListRes
} from '../model';

import { axiosInstance } from '../../axiosInstance';

export const getWithdrawPlan = () => {
  /**
   * 此接口需要在Authorization头部提供Bearer Token进行认证
   * @summary 添加地址到归集计划
   */
  const postWalletAddAddressToWithdrawPlan = (
    walletApiApiWalletV1AddAddressToWithdrawPlanReq: WalletApiApiWalletV1AddAddressToWithdrawPlanReq,
  ) => {
    return axiosInstance<WalletApiApiWalletV1AddAddressToWithdrawPlanRes>(
      {
        url: `/wallet/add-address-to-withdraw-plan`,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        data: walletApiApiWalletV1AddAddressToWithdrawPlanReq
      },
    );
  }

  /**
   * 此接口需要在Authorization头部提供Bearer Token进行认证
   * @summary 获取归集计划列表
   */
  const getWalletWithdrawPlanList = (
    params: WalletApiApiWalletV1GetWithdrawPlanListReq,
  ) => {
    return axiosInstance<WalletApiApiWalletV1GetWithdrawPlanListRes>(
      {
        url: `/wallet/withdraw-plan-list`,
        method: 'GET',
        params
      },
    );
  }

  return { postWalletAddAddressToWithdrawPlan, getWalletWithdrawPlanList };
};

export type PostWalletAddAddressToWithdrawPlanResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawPlan>['postWalletAddAddressToWithdrawPlan']>>>
export type GetWalletWithdrawPlanListResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getWithdrawPlan>['getWalletWithdrawPlanList']>>>
