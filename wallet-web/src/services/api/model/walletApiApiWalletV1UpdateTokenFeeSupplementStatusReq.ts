/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1UpdateTokenFeeSupplementStatusReq {
  /** 代币费用补充记录ID */
  token_fee_supplement_id: number;
  /** 状态 (pending, processing, success, failed, partial_success) */
  status: string;
  /** 补充费用的交易哈希 */
  transaction_hash?: string;
  /** 错误信息 */
  error_message?: string;
  /** 谷歌验证码 */
  google_code: string;
  /** 密码 */
  password: string;
}
