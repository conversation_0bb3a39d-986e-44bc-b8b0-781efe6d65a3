/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1TaskAddressRecordReqSortOrder } from './walletApiApiWalletV1TaskAddressRecordReqSortOrder';
import type { WalletApiApiWalletV1TaskAddressRecordReqStatus } from './walletApiApiWalletV1TaskAddressRecordReqStatus';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1TaskAddressRecordReq {
  /** 归集任务ID */
  task_id: number;
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1TaskAddressRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1TaskAddressRecordReqStatus;
}
