/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1GetTransactionRecordReqType } from './walletApiApiWalletV1GetTransactionRecordReqType';
import type { WalletApiApiWalletV1GetTransactionRecordReqChain } from './walletApiApiWalletV1GetTransactionRecordReqChain';
import type { WalletApiApiWalletV1GetTransactionRecordReqSortOrder } from './walletApiApiWalletV1GetTransactionRecordReqSortOrder';
import type { WalletApiApiWalletV1GetTransactionRecordReqStatus } from './walletApiApiWalletV1GetTransactionRecordReqStatus';
import type { WalletApiApiWalletV1GetTransactionRecordReqCoin } from './walletApiApiWalletV1GetTransactionRecordReqCoin';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1GetTransactionRecordReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 类型 */
  type?: WalletApiApiWalletV1GetTransactionRecordReqType;
  /** 链 */
  chain?: WalletApiApiWalletV1GetTransactionRecordReqChain;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1GetTransactionRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1GetTransactionRecordReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1GetTransactionRecordReqCoin;
  /** 日期范围 */
  date_range?: string;
}
