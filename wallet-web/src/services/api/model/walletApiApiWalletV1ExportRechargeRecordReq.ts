/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1ExportRechargeRecordReqChain } from './walletApiApiWalletV1ExportRechargeRecordReqChain';
import type { WalletApiApiWalletV1ExportRechargeRecordReqSortOrder } from './walletApiApiWalletV1ExportRechargeRecordReqSortOrder';
import type { WalletApiApiWalletV1ExportRechargeRecordReqStatus } from './walletApiApiWalletV1ExportRechargeRecordReqStatus';
import type { WalletApiApiWalletV1ExportRechargeRecordReqCoin } from './walletApiApiWalletV1ExportRechargeRecordReqCoin';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1ExportRechargeRecordReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 链 */
  chain?: WalletApiApiWalletV1ExportRechargeRecordReqChain;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1ExportRechargeRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1ExportRechargeRecordReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1ExportRechargeRecordReqCoin;
  /** 日期范围 */
  date_range?: string;
}
