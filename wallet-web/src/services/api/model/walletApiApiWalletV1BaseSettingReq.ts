/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1BaseSettingReq {
  id?: number;
  /** BIP39 Mnemonic phrase (should be encrypted) */
  mnemonic?: string;
  /** 密码 */
  password: string;
  /** Hashed password for secure storage */
  passwordHash?: string;
  /** Default collection address (maybe deprecated by chain-specific ones) */
  collectAddress?: string;
  /** 谷歌验证码 */
  google_code: string;
  /** Private key for mining/staking (handle with extreme care, encrypt) */
  minerPrivateKey?: string;
  /** Address associated with the miner private key */
  minerAddress?: string;
  /** Enable/disable strategic collection */
  strategyCollectSwitch?: number;
  /** Threshold for ETH collection */
  ethCollectThreshold?: string;
  /** Threshold for TRX collection */
  trxCollectThreshold?: string;
  /** Threshold for USDT collection */
  usdtCollectThreshold?: string;
  /** Enable/disable scheduled collection via cron */
  cronCollectSwitch?: number;
  /** Cron schedule time (e.g., 0 2 * * *) */
  cronCollectTime?: string;
  createAt?: string;
  updateAt?: string;
  /** Timestamp of the last successful unlock */
  lastUnlockAt?: string;
  /** Count of consecutive unlock failures */
  unlockErrorCount?: number;
  /** Enable/disable Google Authenticator requirement */
  googleCodeSwitch?: number;
  /** Specific collection address for TRX chain */
  trxCollectAddress?: string;
  /** Specific collection address for ETH chain */
  ethCollectAddress?: string;
  /** Private key for paying TRX fees (handle with care, encrypt) */
  trxFeePrivateKey?: string;
  /** Private key for paying ETH fees (handle with care, encrypt) */
  ethFeePrivateKey?: string;
  /** Address holding funds for TRX fees */
  trxFeeAddress?: string;
  /** Address holding funds for TRX fees */
  trxActivateAmount?: number;
  /** Address holding funds for ETH fees */
  ethFeeAddress?: string;
  /** eth 矿工费模式 1 自动 2手动 */
  ethFeeMode?: number;
  /** eth固定矿工费发送金额 */
  ethFeeAmount?: number;
  /** trx 固定矿工费发送金额 */
  trxFeeAmount?: number;
  /** trx 固定矿工费发送金额 */
  trc20TriggerFeeAmount?: number;
  /** eth 最大矿工费 */
  ethFeeMax?: number;
  /** eth 最大矿工费 */
  erc20FeeMax?: number;
  ethGasPrice?: number;
  ethGasLimit?: number;
  erc20GasPrice?: number;
  erc20GasLimit?: number;
  /** eth 矿工费模式 1 自动 2手动 */
  trxFeeMode?: number;
  /** trx 交易最多支付手续费 */
  trxFeeMax?: number;
  /** 归集保持账户内最低trx 余额 防止交易失败 */
  trxKeepAmount?: number;
  /** eth 归集的时候 预留金额 */
  ethKeepAmount?: number;
  /** 转账usdt最低需要的能量 手动模式设置 */
  trc20MinRequiredEnergy?: number;
  /** 最大允许消耗的 trx 能量费金额 此处用于购买trx 能量的费用设置 */
  trc20MaxEnergyFee?: number;
  /** 最低带宽 */
  trc20MinRequiredBandwidth?: number;
  mnemonicSalt?: Blob;
  mnemonicIterations?: number;
  googleSecretSalt?: Blob;
  googleSecretIterations?: number;
  /** eth最小充值金额 */
  ethMinTakeAmount?: number;
  /** trx最小充值金额 */
  trxMinTakeAmount?: number;
  /** usdt 最小充值金额 */
  usdtMinTakeAmount?: number;
}
