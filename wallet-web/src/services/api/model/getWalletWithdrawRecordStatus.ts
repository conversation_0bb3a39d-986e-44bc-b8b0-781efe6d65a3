/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export type GetWalletWithdrawRecordStatus = typeof GetWalletWithdrawRecordStatus[keyof typeof GetWalletWithdrawRecordStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetWalletWithdrawRecordStatus = {
  pending: 'pending',
  processing: 'processing',
  rejected: 'rejected',
  completed: 'completed',
  failed: 'failed',
} as const;
