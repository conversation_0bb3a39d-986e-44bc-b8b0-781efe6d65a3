/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1GetTokenFeeSupplementsReqSortOrder } from './walletApiApiWalletV1GetTokenFeeSupplementsReqSortOrder';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1GetTokenFeeSupplementsReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 链类型 (例如: ETH, TRON) */
  chain_type?: string;
  /** 代币符号 (例如: ETH, TRX, USDT) */
  token_symbol?: string;
  /** 费用类型 (例如: gas_fee, energy) */
  fee_type?: string;
  /** 状态 (pending, processing, success, failed, partial_success) */
  status?: string;
  /** TRX补充状态 (pending, processing, success, failed) */
  trx_supplement_status?: string;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1GetTokenFeeSupplementsReqSortOrder;
}
