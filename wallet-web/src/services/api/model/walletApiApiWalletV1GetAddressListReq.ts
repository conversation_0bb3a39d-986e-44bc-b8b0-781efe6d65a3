/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1GetAddressListReqSortOrder } from './walletApiApiWalletV1GetAddressListReqSortOrder';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1GetAddressListReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 类型 */
  type?: string;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1GetAddressListReqSortOrder;
  /** 是否查询有余额的地址 */
  has_balance?: boolean;
}
