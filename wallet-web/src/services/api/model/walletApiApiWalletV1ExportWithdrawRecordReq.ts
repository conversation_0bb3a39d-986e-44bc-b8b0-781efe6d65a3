/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1ExportWithdrawRecordReqChain } from './walletApiApiWalletV1ExportWithdrawRecordReqChain';
import type { WalletApiApiWalletV1ExportWithdrawRecordReqSortOrder } from './walletApiApiWalletV1ExportWithdrawRecordReqSortOrder';
import type { WalletApiApiWalletV1ExportWithdrawRecordReqStatus } from './walletApiApiWalletV1ExportWithdrawRecordReqStatus';
import type { WalletApiApiWalletV1ExportWithdrawRecordReqCoin } from './walletApiApiWalletV1ExportWithdrawRecordReqCoin';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1ExportWithdrawRecordReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 提币源地址 */
  from_address?: string;
  /** 链 */
  chain?: WalletApiApiWalletV1ExportWithdrawRecordReqChain;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1ExportWithdrawRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1ExportWithdrawRecordReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1ExportWithdrawRecordReqCoin;
  /** 日期范围 */
  date_range?: string;
}
