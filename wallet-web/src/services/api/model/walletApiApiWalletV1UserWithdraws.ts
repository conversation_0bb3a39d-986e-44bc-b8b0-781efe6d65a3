/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiApiWalletV1UserWithdraws {
  /** 主键ID */
  user_withdraws_id?: number;
  token_fee_supplement_id?: number;
  /** 币种ID */
  name?: string;
  /** 链 */
  chan?: string;
  /** 提币目标地址 */
  from_address?: string;
  /** 提币目标地址 */
  to_address?: string;
  /** 申请提现金额 */
  amount?: number;
  /** 提现手续费 */
  handling_fee?: number;
  /** 实际到账金额 */
  actual_amount?: number;
  /** 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed) */
  state?: number;
  /** 链上交易哈希/ID */
  tx_hash?: string;
  /** 失败或错误信息 */
  error_message?: string;
  /** 创建时间 */
  created_at?: string;
  /** 审核时间 (审核通过或拒绝的时间) */
  checked_at?: string;
  /** 开始处理时间 (进入“处理中”状态的时间) */
  processing_at?: string;
  /** 完成时间 (变为“已完成”或“失败”状态的时间) */
  completed_at?: string;
  /** 最后更新时间 */
  updated_at?: string;
  retries?: number;
  /** 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了 */
  nergy_state?: number;
  /** gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了 */
  gasfee_state?: number;
  /** eth 矿工费模式 1 自动 2手动 */
  eth_fee_mode?: number;
  /** eth 最大矿工费 */
  eth_fee_max?: number;
  eth_gas_price?: number;
  eth_gas_limit?: number;
  trx_fee_max?: number;
  /** 充值订单id */
  recharges_id?: number;
  /** gas费hash */
  gasfee_hash?: string;
  /** gas费金额 */
  gasfee_amount?: number;
  energy_msg?: string;
}
