/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletCollectRecordStatisticSortOrder } from './getWalletCollectRecordStatisticSortOrder';
import type { GetWalletCollectRecordStatisticStatus } from './getWalletCollectRecordStatisticStatus';
import type { GetWalletCollectRecordStatisticCoin } from './getWalletCollectRecordStatisticCoin';
import type { GetWalletCollectRecordStatisticTaskType } from './getWalletCollectRecordStatisticTaskType';
import type { GetWalletCollectRecordStatisticExecuteType } from './getWalletCollectRecordStatisticExecuteType';

export type GetWalletCollectRecordStatisticParams = {
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 类型
 */
type?: string;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletCollectRecordStatisticSortOrder;
/**
 * 状态
 */
status?: GetWalletCollectRecordStatisticStatus;
/**
 * 币种
 */
coin?: GetWalletCollectRecordStatisticCoin;
/**
 * 日期范围
 */
date_range?: string;
/**
 * 任务类型
 */
task_type?: GetWalletCollectRecordStatisticTaskType;
/**
 * 执行类型
 */
execute_type?: GetWalletCollectRecordStatisticExecuteType;
};
