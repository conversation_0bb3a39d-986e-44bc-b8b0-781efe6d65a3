/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletRechargeRecordChain } from './getWalletRechargeRecordChain';
import type { GetWalletRechargeRecordSortOrder } from './getWalletRechargeRecordSortOrder';
import type { GetWalletRechargeRecordStatus } from './getWalletRechargeRecordStatus';
import type { GetWalletRechargeRecordCoin } from './getWalletRechargeRecordCoin';

export type GetWalletRechargeRecordParams = {
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 链
 */
chain?: GetWalletRechargeRecordChain;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletRechargeRecordSortOrder;
/**
 * 状态
 */
status?: GetWalletRechargeRecordStatus;
/**
 * 币种
 */
coin?: GetWalletRechargeRecordCoin;
/**
 * 日期范围
 */
date_range?: string;
};
