/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1GetRechargeRecordReqChain } from './walletApiApiWalletV1GetRechargeRecordReqChain';
import type { WalletApiApiWalletV1GetRechargeRecordReqSortOrder } from './walletApiApiWalletV1GetRechargeRecordReqSortOrder';
import type { WalletApiApiWalletV1GetRechargeRecordReqStatus } from './walletApiApiWalletV1GetRechargeRecordReqStatus';
import type { WalletApiApiWalletV1GetRechargeRecordReqCoin } from './walletApiApiWalletV1GetRechargeRecordReqCoin';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1GetRechargeRecordReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 链 */
  chain?: WalletApiApiWalletV1GetRechargeRecordReqChain;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1GetRechargeRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1GetRechargeRecordReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1GetRechargeRecordReqCoin;
  /** 日期范围 */
  date_range?: string;
}
