/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1CollectTaskListReqSortOrder } from './walletApiApiWalletV1CollectTaskListReqSortOrder';
import type { WalletApiApiWalletV1CollectTaskListReqStatus } from './walletApiApiWalletV1CollectTaskListReqStatus';
import type { WalletApiApiWalletV1CollectTaskListReqCoin } from './walletApiApiWalletV1CollectTaskListReqCoin';
import type { WalletApiApiWalletV1CollectTaskListReqTaskType } from './walletApiApiWalletV1CollectTaskListReqTaskType';
import type { WalletApiApiWalletV1CollectTaskListReqExecuteType } from './walletApiApiWalletV1CollectTaskListReqExecuteType';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1CollectTaskListReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 类型 */
  type?: string;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1CollectTaskListReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1CollectTaskListReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1CollectTaskListReqCoin;
  /** 日期范围 */
  date_range?: string;
  /** 任务类型 */
  task_type?: WalletApiApiWalletV1CollectTaskListReqTaskType;
  /** 执行类型 */
  execute_type?: WalletApiApiWalletV1CollectTaskListReqExecuteType;
}
