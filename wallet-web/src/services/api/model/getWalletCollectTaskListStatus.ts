/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export type GetWalletCollectTaskListStatus = typeof GetWalletCollectTaskListStatus[keyof typeof GetWalletCollectTaskListStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetWalletCollectTaskListStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
