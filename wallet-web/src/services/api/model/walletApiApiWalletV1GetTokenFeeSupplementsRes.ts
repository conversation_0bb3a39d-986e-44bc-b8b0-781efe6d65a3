/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1Page } from './walletApiApiWalletV1Page';
import type { WalletApiApiWalletV1TokenFeeSupplement } from './walletApiApiWalletV1TokenFeeSupplement';
import type { WalletApiApiWalletV1FeeStatistics } from './walletApiApiWalletV1FeeStatistics';

export interface WalletApiApiWalletV1GetTokenFeeSupplementsRes {
  /** 分页信息 */
  page?: WalletApiApiWalletV1Page;
  /** 代币费用补充记录列表 */
  list?: WalletApiApiWalletV1TokenFeeSupplement[];
  /** 费用统计信息 */
  stats?: WalletApiApiWalletV1FeeStatistics;
}
