/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiApiWalletV1TaskList {
  /** ID */
  id?: number;
  /** 网络类型，支持ETH或TRON */
  network_type?: string;
  /** 币种类型，支持ETH、TRX或USDT */
  token_type?: string;
  /** 任务类型，支持many2one(多对一归集)或one2many(一对多转账) */
  task_type?: string;
  /** 执行类型，支持manual(手动)或auto(自动) */
  execute_type?: string;
  /** 任务名称 */
  task_name?: string;
  /** 归集任务ID */
  task_id?: number;
  /** 转账金额 */
  amount?: string;
  /** 是否转出全部金额 */
  is_all_amount?: boolean;
  /** 总金额 */
  total_amount?: string;
  /** 总手续费 */
  total_fee?: string;
  /** 任务状态 */
  task_status?: string;
  /** 创建时间 */
  create_at?: string;
  /** 更新时间 */
  update_at?: string;
  /** 矿工费限制 */
  gas_limit?: string;
  /** 矿工费价格 */
  gas_price?: string;
  /** 转出地址 */
  from_address?: string;
  /** 转入地址 */
  to_address?: string;
  /** 成功的任务地址数量 */
  success_address_count?: number;
  /** 失败的任务地址数量 */
  failed_address_count?: number;
  /** 待执行的任务地址数量 */
  pending_address_count?: number;
  /** 取消的任务地址数量 */
  canceled_address_count?: number;
}
