/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 状态
 */
export type WalletApiApiWalletV1ExportTaskAddressRecordReqStatus = typeof WalletApiApiWalletV1ExportTaskAddressRecordReqStatus[keyof typeof WalletApiApiWalletV1ExportTaskAddressRecordReqStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WalletApiApiWalletV1ExportTaskAddressRecordReqStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
