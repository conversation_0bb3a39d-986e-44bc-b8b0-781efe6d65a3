/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 状态
 */
export type WalletApiApiWalletV1TaskAddressRecordReqStatus = typeof WalletApiApiWalletV1TaskAddressRecordReqStatus[keyof typeof WalletApiApiWalletV1TaskAddressRecordReqStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WalletApiApiWalletV1TaskAddressRecordReqStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
