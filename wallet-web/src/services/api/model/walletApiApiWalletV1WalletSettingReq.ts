/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1WalletSettingReqEthFeeMode } from './walletApiApiWalletV1WalletSettingReqEthFeeMode';
import type { WalletApiApiWalletV1WalletSettingReqTrxFeeMode } from './walletApiApiWalletV1WalletSettingReqTrxFeeMode';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1WalletSettingReq {
  /** 谷歌验证码 */
  google_code: string;
  /** 密码 */
  password: string;
  /** 策略归集开关 */
  strategy_collect_switch?: string;
  /** ETH归集阈值 */
  eth_collect_threshold?: string;
  /** TRX归集阈值 */
  trx_collect_threshold?: string;
  /** USDT归集阈值 */
  usdt_collect_threshold?: string;
  /** 定时归集开关 */
  cron_collect_switch?: string;
  /** 定时归集时间 */
  cron_collect_time?: string;
  /** TRX链特定归集地址 */
  trx_collect_address?: string;
  /** ETH链特定归集地址 */
  eth_collect_address?: string;
  /** TRX费用支付私钥(请谨慎处理,加密) */
  trx_fee_private_key?: string;
  /** ETH费用支付私钥(请谨慎处理,加密) */
  eth_fee_private_key?: string;
  /** TRX费用资金持有地址 */
  trx_fee_address?: string;
  /** ETH费用资金持有地址 */
  eth_fee_address?: string;
  /** TRX激活账户金额 */
  trx_activate_amount?: string;
  /** ETH矿工费模式 1自动 2手动 */
  eth_fee_mode?: WalletApiApiWalletV1WalletSettingReqEthFeeMode;
  /** TRX矿工费模式 1自动 2手动 */
  trx_fee_mode?: WalletApiApiWalletV1WalletSettingReqTrxFeeMode;
  /** ETH固定矿工费发送金额 */
  eth_fee_amount?: string;
  /** TRX固定矿工费发送金额 */
  trx_fee_amount?: string;
  /** ETH最大矿工费 */
  eth_fee_max?: string;
  /** ERC20最大矿工费 */
  erc20_fee_max?: string;
  /** TRX交易最多支付手续费 */
  trx_fee_max?: string;
  /** ETH Gas价格 */
  eth_gas_price?: string;
  /** ETH Gas限制 */
  eth_gas_limit?: string;
  /** ERC20 Gas价格 */
  erc20_gas_price?: string;
  /** ERC20 Gas限制 */
  erc20_gas_limit?: string;
  /** 归集保持账户内最低TRX余额,防止交易失败 */
  trx_keep_amount?: string;
  /** ETH归集时预留金额 */
  eth_keep_amount?: string;
  /** 转账USDT最低需要的能量,手动模式设置 */
  trc20_min_required_energy?: string;
  /** 最大允许消耗的TRX能量费金额,用于购买TRX能量的费用设置 */
  trc20_max_energy_fee?: string;
  /** 最低带宽 */
  trc20_min_required_bandwidth?: string;
  /** eth最小取款金额 */
  eth_min_take_amount?: string;
  /** trx最小取款金额 */
  trx_min_take_amount?: string;
  /** usdt最小取款金额 */
  usdt_min_take_amount?: string;
  /** TRC20触发手续费 */
  trc20_trigger_fee_amount?: string;
}
