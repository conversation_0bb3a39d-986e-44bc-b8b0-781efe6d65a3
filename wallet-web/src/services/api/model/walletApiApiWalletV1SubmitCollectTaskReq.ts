/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1SubmitCollectTaskReq {
  /** 链类型，支持ETH或TRON */
  type: string;
  /** 币种，支持ETH、TRX或USDT */
  coin: string;
  /** 交易类型，one2many(一对多转账)或many2one(多对一归集) */
  trans_type: string;
  /** 转出地址，归集使用 多对一 归集资金时候 */
  from_address: string[];
  /** 转入地址，收矿工费使用 一对多发送矿工费时 */
  to_address: string[];
  /** 是否转出全部金额 如果为true，则不填写amount */
  is_all_amount: boolean;
  /** 转账金额，仅在is_all_amount为false时必填 */
  amount?: string;
  /** 矿工费限制，可选参数，默认使用系统估算值 eth 网络专用 */
  gas_limit?: string;
  /** 矿工费价格，可选参数，默认使用系统估算值 eth 网络专用 */
  gas_price?: string;
  /** 手续费限制，可选参数，默认使用系统估算值 tron 网络专用 */
  fee_limit?: string;
}
