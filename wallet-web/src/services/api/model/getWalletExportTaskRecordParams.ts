/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletExportTaskRecordSortOrder } from './getWalletExportTaskRecordSortOrder';
import type { GetWalletExportTaskRecordStatus } from './getWalletExportTaskRecordStatus';
import type { GetWalletExportTaskRecordCoin } from './getWalletExportTaskRecordCoin';
import type { GetWalletExportTaskRecordTaskType } from './getWalletExportTaskRecordTaskType';
import type { GetWalletExportTaskRecordExecuteType } from './getWalletExportTaskRecordExecuteType';

export type GetWalletExportTaskRecordParams = {
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 类型
 */
type?: string;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletExportTaskRecordSortOrder;
/**
 * 状态
 */
status?: GetWalletExportTaskRecordStatus;
/**
 * 币种
 */
coin?: GetWalletExportTaskRecordCoin;
/**
 * 日期范围
 */
date_range?: string;
/**
 * 任务类型
 */
task_type?: GetWalletExportTaskRecordTaskType;
/**
 * 执行类型
 */
execute_type?: GetWalletExportTaskRecordExecuteType;
};
