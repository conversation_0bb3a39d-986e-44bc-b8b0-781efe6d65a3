/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 状态
 */
export type WalletApiApiWalletV1CollectRecordStatisticReqStatus = typeof WalletApiApiWalletV1CollectRecordStatisticReqStatus[keyof typeof WalletApiApiWalletV1CollectRecordStatisticReqStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WalletApiApiWalletV1CollectRecordStatisticReqStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
