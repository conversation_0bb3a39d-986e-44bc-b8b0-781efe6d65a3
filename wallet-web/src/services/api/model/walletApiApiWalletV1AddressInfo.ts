/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiApiWalletV1AddressInfo {
  /** id */
  id?: number;
  /** 钱包id */
  wallet_id?: number;
  /** 地址 */
  address?: string;
  /** 类型 */
  type?: string;
  /** 标签 */
  label?: string;
  /** 绑定状态 */
  bind_status?: number;
  /** 绑定时间 */
  bind_at?: string;
  /** 最近查询时间 */
  last_query_at?: string;
  /** 状态 */
  status?: number;
  /** 链币余额 */
  chain_coin_balance?: string;
  /** 链usdt余额 */
  chain_usdt_balance?: string;
  /** 创建时间 */
  create_at?: string;
  /** 更新时间 */
  update_at?: string;
}
