/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export type GetWalletTransactionRecordStatus = typeof GetWalletTransactionRecordStatus[keyof typeof GetWalletTransactionRecordStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetWalletTransactionRecordStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
