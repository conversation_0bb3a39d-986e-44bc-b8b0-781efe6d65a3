/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiInternalModelEntityTokenFeeSupplements {
  /** 主键ID */
  tokenFeeSupplementId?: number;
  /** 计划订单id */
  withdrawPlanId?: number;
  /** 计划订单id */
  userWithdrawId?: number;
  /** 需要补充费用的地址 */
  address?: string;
  /** 链类型 (例如: ERC20, TRC20) */
  chainType?: string;
  /** 代币符号 (例如: USDT) */
  tokenSymbol?: string;
  /** 费用类型 (例如: gas_fee, energy) */
  feeType?: string;
  /** 需要的费用数量 (原生代币单位) */
  requiredAmount?: number;
  /** 已补充的费用数量 (原生代币单位) */
  providedAmount?: number;
  /** 补充能量数量 trc20 专用 */
  energyAmount?: number;
  /** 补充能量数量 trc20 专用 */
  energyFee?: number;
  /** 状态 (pending, processing, success, failed, partial_success) */
  status?: string;
  /** 补充费用的交易哈希 */
  transactionHash?: string;
  /** 错误信息 */
  errorMessage?: string;
  /** 关联的归集任务ID */
  relatedTaskId?: string;
  /** 重试次数 */
  retryCount?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  energyId?: string;
  /** 是否激活 0 未激活 1 激活中 2 已激活 */
  isActivating?: number;
  /** 激活hash */
  activateHash?: string;
  /** 激活消耗trx */
  activateAmount?: number;
}
