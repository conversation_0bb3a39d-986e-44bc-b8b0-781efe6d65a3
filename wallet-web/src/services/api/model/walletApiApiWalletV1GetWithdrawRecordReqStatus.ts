/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 状态
 */
export type WalletApiApiWalletV1GetWithdrawRecordReqStatus = typeof WalletApiApiWalletV1GetWithdrawRecordReqStatus[keyof typeof WalletApiApiWalletV1GetWithdrawRecordReqStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WalletApiApiWalletV1GetWithdrawRecordReqStatus = {
  pending: 'pending',
  processing: 'processing',
  rejected: 'rejected',
  completed: 'completed',
  failed: 'failed',
} as const;
