/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1ExportTaskAddressRecordReqSortOrder } from './walletApiApiWalletV1ExportTaskAddressRecordReqSortOrder';
import type { WalletApiApiWalletV1ExportTaskAddressRecordReqStatus } from './walletApiApiWalletV1ExportTaskAddressRecordReqStatus';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1ExportTaskAddressRecordReq {
  /** 归集任务ID */
  task_id: number;
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1ExportTaskAddressRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1ExportTaskAddressRecordReqStatus;
}
