/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiInternalUtilityCryptoTronCost {
  net_fee_cost?: number;
  fee?: number;
  energy_fee_cost?: number;
  net_usage?: number;
  multi_sign_fee?: number;
  net_fee?: number;
  energy_penalty_total?: number;
  energy_usage?: number;
  energy_fee?: number;
  energy_usage_total?: number;
  memoFee?: number;
  origin_energy_usage?: number;
  account_create_fee?: number;
}
