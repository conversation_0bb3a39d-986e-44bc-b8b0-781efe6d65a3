/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1CollectRecordStatisticReqSortOrder } from './walletApiApiWalletV1CollectRecordStatisticReqSortOrder';
import type { WalletApiApiWalletV1CollectRecordStatisticReqStatus } from './walletApiApiWalletV1CollectRecordStatisticReqStatus';
import type { WalletApiApiWalletV1CollectRecordStatisticReqCoin } from './walletApiApiWalletV1CollectRecordStatisticReqCoin';
import type { WalletApiApiWalletV1CollectRecordStatisticReqTaskType } from './walletApiApiWalletV1CollectRecordStatisticReqTaskType';
import type { WalletApiApiWalletV1CollectRecordStatisticReqExecuteType } from './walletApiApiWalletV1CollectRecordStatisticReqExecuteType';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1CollectRecordStatisticReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 类型 */
  type?: string;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1CollectRecordStatisticReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1CollectRecordStatisticReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1CollectRecordStatisticReqCoin;
  /** 日期范围 */
  date_range?: string;
  /** 任务类型 */
  task_type?: WalletApiApiWalletV1CollectRecordStatisticReqTaskType;
  /** 执行类型 */
  execute_type?: WalletApiApiWalletV1CollectRecordStatisticReqExecuteType;
}
