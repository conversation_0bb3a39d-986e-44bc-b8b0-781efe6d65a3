/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 状态
 */
export type WalletApiApiWalletV1CollectTaskListReqStatus = typeof WalletApiApiWalletV1CollectTaskListReqStatus[keyof typeof WalletApiApiWalletV1CollectTaskListReqStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WalletApiApiWalletV1CollectTaskListReqStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
