/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiApiWalletV1RechargeRecord {
  /** 充值ID */
  recharges_id?: number;
  /** 币种ID */
  token_id?: number;
  /** 币种名称 */
  name?: string;
  /** 链 */
  chain?: string;
  /** 代币合约地址 */
  token_contract_address?: string;
  /** 来源地址 */
  from_address?: string;
  /** 目标地址 */
  to_address?: string;
  /** 交易哈希 */
  tx_hash?: string;
  /** 失败原因 */
  error?: string;
  /** 充值数量 */
  amount?: string;
  /** 状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed) */
  state?: number;
  /** 确认数 */
  confirmations?: number;
  /** 创建时间 */
  created_at?: string;
  /** 完成时间 */
  completed_at?: string;
  /** 更新时间 */
  updated_at?: string;
  /** 是否提现 */
  is_withdraw?: number;
  /** 提现ID */
  withdraw_id?: number;
}
