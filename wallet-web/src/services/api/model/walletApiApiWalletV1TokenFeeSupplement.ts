/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiApiWalletV1TokenFeeSupplement {
  /** 主键ID */
  token_fee_supplement_id?: number;
  /** 计划订单id */
  withdraw_plan_id?: number;
  /** 计划订单id */
  user_withdraw_id?: number;
  /** 需要补充费用的地址 */
  address?: string;
  /** 链类型 (例如: ERC20, TRC20) */
  chain_type?: string;
  /** 代币符号 (例如: USDT) */
  token_symbol?: string;
  /** 费用类型 (例如: gas_fee, energy) */
  fee_type?: string;
  /** 需要的费用数量 (原生代币单位) */
  required_amount?: number;
  /** 已补充的费用数量 (原生代币单位) */
  provided_amount?: number;
  /** 补充能量数量 trc20 专用 */
  energy_amount?: number;
  /** 补充能量数量 trc20 专用 */
  energy_fee?: number;
  /** 状态 (pending, processing, success, failed, partial_success) */
  status?: string;
  /** 补充费用的交易哈希 */
  transaction_hash?: string;
  /** 错误信息 */
  error_message?: string;
  /** 关联的归集任务ID */
  related_task_id?: string;
  /** 重试次数 */
  retry_count?: number;
  /** 创建时间 */
  created_at?: string;
  /** 更新时间 */
  updated_at?: string;
  energy_id?: string;
  /** 是否激活 0 未激活 1 激活中 2 已激活 */
  is_activating?: number;
  /** 激活hash */
  activate_hash?: string;
  /** 激活消耗trx */
  activate_amount?: number;
  /** TRX补充是否需要 0-不需要 1-需要 */
  trx_supplement_needed?: number;
  /** TRX补充状态 pending-待处理 processing-处理中 success-成功 failed-失败 */
  trx_supplement_status?: string;
  /** TRX补充交易哈希 */
  trx_supplement_hash?: string;
  /** TRX补充数量 */
  trx_supplement_amount?: number;
  /** 补充前TRX余额 */
  trx_balance_before?: number;
  /** 补充后TRX余额 */
  trx_balance_after?: number;
}
