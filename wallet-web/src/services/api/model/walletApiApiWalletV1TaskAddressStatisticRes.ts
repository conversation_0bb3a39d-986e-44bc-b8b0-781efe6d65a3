/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiApiWalletV1TaskAddressStatisticRes {
  /** 任务总金额 */
  total_amount?: string;
  /** 手续费 */
  total_fee?: string;
  /** 网络类型 */
  network_type?: string;
  /** 代币类型 */
  coin_type?: string;
  /** 转账总数 */
  transfer_count?: number;
  /** 已完成数量 */
  completed_count?: number;
  /** 处理中数量 */
  processing_count?: number;
  /** 失败数量 */
  failed_count?: number;
  /** 待执行数量 */
  pending_count?: number;
  /** 取消数量 */
  canceled_count?: number;
}
