/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export type GetWalletExportTaskRecordStatus = typeof GetWalletExportTaskRecordStatus[keyof typeof GetWalletExportTaskRecordStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetWalletExportTaskRecordStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
