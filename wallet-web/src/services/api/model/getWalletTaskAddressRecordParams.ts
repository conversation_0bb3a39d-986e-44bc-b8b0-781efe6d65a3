/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletTaskAddressRecordSortOrder } from './getWalletTaskAddressRecordSortOrder';
import type { GetWalletTaskAddressRecordStatus } from './getWalletTaskAddressRecordStatus';

export type GetWalletTaskAddressRecordParams = {
/**
 * 归集任务ID
 */
task_id: number;
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletTaskAddressRecordSortOrder;
/**
 * 状态
 */
status?: GetWalletTaskAddressRecordStatus;
};
