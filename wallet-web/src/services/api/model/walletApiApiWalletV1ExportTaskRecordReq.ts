/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1ExportTaskRecordReqSortOrder } from './walletApiApiWalletV1ExportTaskRecordReqSortOrder';
import type { WalletApiApiWalletV1ExportTaskRecordReqStatus } from './walletApiApiWalletV1ExportTaskRecordReqStatus';
import type { WalletApiApiWalletV1ExportTaskRecordReqCoin } from './walletApiApiWalletV1ExportTaskRecordReqCoin';
import type { WalletApiApiWalletV1ExportTaskRecordReqTaskType } from './walletApiApiWalletV1ExportTaskRecordReqTaskType';
import type { WalletApiApiWalletV1ExportTaskRecordReqExecuteType } from './walletApiApiWalletV1ExportTaskRecordReqExecuteType';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1ExportTaskRecordReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 类型 */
  type?: string;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1ExportTaskRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1ExportTaskRecordReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1ExportTaskRecordReqCoin;
  /** 日期范围 */
  date_range?: string;
  /** 任务类型 */
  task_type?: WalletApiApiWalletV1ExportTaskRecordReqTaskType;
  /** 执行类型 */
  execute_type?: WalletApiApiWalletV1ExportTaskRecordReqExecuteType;
}
