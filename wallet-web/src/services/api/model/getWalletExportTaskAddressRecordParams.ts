/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletExportTaskAddressRecordSortOrder } from './getWalletExportTaskAddressRecordSortOrder';
import type { GetWalletExportTaskAddressRecordStatus } from './getWalletExportTaskAddressRecordStatus';

export type GetWalletExportTaskAddressRecordParams = {
/**
 * 归集任务ID
 */
task_id: number;
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletExportTaskAddressRecordSortOrder;
/**
 * 状态
 */
status?: GetWalletExportTaskAddressRecordStatus;
};
