/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1ExportTransactionRecordReqType } from './walletApiApiWalletV1ExportTransactionRecordReqType';
import type { WalletApiApiWalletV1ExportTransactionRecordReqChain } from './walletApiApiWalletV1ExportTransactionRecordReqChain';
import type { WalletApiApiWalletV1ExportTransactionRecordReqSortOrder } from './walletApiApiWalletV1ExportTransactionRecordReqSortOrder';
import type { WalletApiApiWalletV1ExportTransactionRecordReqStatus } from './walletApiApiWalletV1ExportTransactionRecordReqStatus';
import type { WalletApiApiWalletV1ExportTransactionRecordReqCoin } from './walletApiApiWalletV1ExportTransactionRecordReqCoin';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1ExportTransactionRecordReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 类型 */
  type?: WalletApiApiWalletV1ExportTransactionRecordReqType;
  /** 链 */
  chain?: WalletApiApiWalletV1ExportTransactionRecordReqChain;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1ExportTransactionRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1ExportTransactionRecordReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1ExportTransactionRecordReqCoin;
  /** 日期范围 */
  date_range?: string;
}
