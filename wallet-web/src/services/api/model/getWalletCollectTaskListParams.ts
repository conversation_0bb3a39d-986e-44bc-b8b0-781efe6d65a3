/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletCollectTaskListSortOrder } from './getWalletCollectTaskListSortOrder';
import type { GetWalletCollectTaskListStatus } from './getWalletCollectTaskListStatus';
import type { GetWalletCollectTaskListCoin } from './getWalletCollectTaskListCoin';
import type { GetWalletCollectTaskListTaskType } from './getWalletCollectTaskListTaskType';
import type { GetWalletCollectTaskListExecuteType } from './getWalletCollectTaskListExecuteType';

export type GetWalletCollectTaskListParams = {
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 类型
 */
type?: string;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletCollectTaskListSortOrder;
/**
 * 状态
 */
status?: GetWalletCollectTaskListStatus;
/**
 * 币种
 */
coin?: GetWalletCollectTaskListCoin;
/**
 * 日期范围
 */
date_range?: string;
/**
 * 任务类型
 */
task_type?: GetWalletCollectTaskListTaskType;
/**
 * 执行类型
 */
execute_type?: GetWalletCollectTaskListExecuteType;
};
