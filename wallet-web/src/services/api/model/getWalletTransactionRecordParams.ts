/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletTransactionRecordType } from './getWalletTransactionRecordType';
import type { GetWalletTransactionRecordChain } from './getWalletTransactionRecordChain';
import type { GetWalletTransactionRecordSortOrder } from './getWalletTransactionRecordSortOrder';
import type { GetWalletTransactionRecordStatus } from './getWalletTransactionRecordStatus';
import type { GetWalletTransactionRecordCoin } from './getWalletTransactionRecordCoin';

export type GetWalletTransactionRecordParams = {
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 类型
 */
type?: GetWalletTransactionRecordType;
/**
 * 链
 */
chain?: GetWalletTransactionRecordChain;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletTransactionRecordSortOrder;
/**
 * 状态
 */
status?: GetWalletTransactionRecordStatus;
/**
 * 币种
 */
coin?: GetWalletTransactionRecordCoin;
/**
 * 日期范围
 */
date_range?: string;
};
