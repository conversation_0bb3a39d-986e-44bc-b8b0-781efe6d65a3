/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletWithdrawRecordChain } from './getWalletWithdrawRecordChain';
import type { GetWalletWithdrawRecordSortOrder } from './getWalletWithdrawRecordSortOrder';
import type { GetWalletWithdrawRecordStatus } from './getWalletWithdrawRecordStatus';
import type { GetWalletWithdrawRecordCoin } from './getWalletWithdrawRecordCoin';

export type GetWalletWithdrawRecordParams = {
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 提币源地址
 */
from_address?: string;
/**
 * 链
 */
chain?: GetWalletWithdrawRecordChain;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletWithdrawRecordSortOrder;
/**
 * 状态
 */
status?: GetWalletWithdrawRecordStatus;
/**
 * 币种
 */
coin?: GetWalletWithdrawRecordCoin;
/**
 * 日期范围
 */
date_range?: string;
};
