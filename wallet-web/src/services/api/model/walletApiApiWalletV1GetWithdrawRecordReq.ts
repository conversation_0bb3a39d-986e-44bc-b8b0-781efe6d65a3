/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1GetWithdrawRecordReqChain } from './walletApiApiWalletV1GetWithdrawRecordReqChain';
import type { WalletApiApiWalletV1GetWithdrawRecordReqSortOrder } from './walletApiApiWalletV1GetWithdrawRecordReqSortOrder';
import type { WalletApiApiWalletV1GetWithdrawRecordReqStatus } from './walletApiApiWalletV1GetWithdrawRecordReqStatus';
import type { WalletApiApiWalletV1GetWithdrawRecordReqCoin } from './walletApiApiWalletV1GetWithdrawRecordReqCoin';

/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 */
export interface WalletApiApiWalletV1GetWithdrawRecordReq {
  /** 页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 地址 */
  address?: string;
  /** 提币源地址 */
  from_address?: string;
  /** 链 */
  chain?: WalletApiApiWalletV1GetWithdrawRecordReqChain;
  /** 排序字段 */
  sort_field?: string;
  /** 排序方向 */
  sort_order?: WalletApiApiWalletV1GetWithdrawRecordReqSortOrder;
  /** 状态 */
  status?: WalletApiApiWalletV1GetWithdrawRecordReqStatus;
  /** 币种 */
  coin?: WalletApiApiWalletV1GetWithdrawRecordReqCoin;
  /** 日期范围 */
  date_range?: string;
}
