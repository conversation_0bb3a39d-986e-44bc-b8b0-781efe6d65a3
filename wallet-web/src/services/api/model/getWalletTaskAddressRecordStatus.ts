/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export type GetWalletTaskAddressRecordStatus = typeof GetWalletTaskAddressRecordStatus[keyof typeof GetWalletTaskAddressRecordStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetWalletTaskAddressRecordStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
