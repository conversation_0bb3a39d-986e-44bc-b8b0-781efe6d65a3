/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { GetWalletAddressListSortOrder } from './getWalletAddressListSortOrder';

export type GetWalletAddressListParams = {
/**
 * 页码
 */
page: number;
/**
 * 每页数量
 */
limit: number;
/**
 * 地址
 */
address?: string;
/**
 * 类型
 */
type?: string;
/**
 * 排序字段
 */
sort_field?: string;
/**
 * 排序方向
 */
sort_order?: GetWalletAddressListSortOrder;
/**
 * 是否查询有余额的地址
 */
has_balance?: boolean;
};
