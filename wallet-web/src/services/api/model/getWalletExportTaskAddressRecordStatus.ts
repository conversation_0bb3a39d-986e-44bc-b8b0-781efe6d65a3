/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export type GetWalletExportTaskAddressRecordStatus = typeof GetWalletExportTaskAddressRecordStatus[keyof typeof GetWalletExportTaskAddressRecordStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetWalletExportTaskAddressRecordStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
