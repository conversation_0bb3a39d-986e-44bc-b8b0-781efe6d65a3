/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1TaskList } from './walletApiApiWalletV1TaskList';

export interface WalletApiApiWalletV1TaskAddressRecord {
  /** ID */
  id?: number;
  /** 归集任务ID */
  task_id?: number;
  /** 转出地址 */
  sender_address?: string;
  /** 转入地址 */
  receiver_address?: string;
  /** 转账金额 */
  amount?: string;
  /** 手续费 */
  fee?: string;
  /** 网络类型 */
  network?: string;
  /** 状态 completed,processing,failed,pending,canceled */
  status?: string;
  /** 失败原因 */
  fail_reason?: string;
  /** 交易哈希 */
  transaction_hash?: string;
  /** 创建时间 */
  create_at?: string;
  /** 更新时间 */
  update_at?: string;
  /** 任务信息 */
  task_info?: WalletApiApiWalletV1TaskList;
}
