/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

/**
 * 状态
 */
export type WalletApiApiWalletV1GetTransactionRecordReqStatus = typeof WalletApiApiWalletV1GetTransactionRecordReqStatus[keyof typeof WalletApiApiWalletV1GetTransactionRecordReqStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WalletApiApiWalletV1GetTransactionRecordReqStatus = {
  completed: 'completed',
  processing: 'processing',
  failed: 'failed',
  pending: 'pending',
  canceled: 'canceled',
} as const;
