/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiInternalModelEntityUserWithdraws {
  /** 主键ID */
  userWithdrawsId?: number;
  tokenFeeSupplementId?: number;
  /** 币种ID */
  name?: string;
  /** 链 */
  chan?: string;
  /** 提币目标地址 */
  fromAddress?: string;
  /** 提币目标地址 */
  toAddress?: string;
  /** 申请归集金额 */
  amount?: number;
  /** 归集手续费 */
  handlingFee?: number;
  /** 实际到账金额 */
  actualAmount?: number;
  /** 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed) */
  state?: number;
  /** 链上交易哈希/ID */
  txHash?: string;
  /** 失败或错误信息 */
  errorMessage?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 审核时间 (审核通过或拒绝的时间) */
  checkedAt?: string;
  /** 开始处理时间 (进入“处理中”状态的时间) */
  processingAt?: string;
  /** 完成时间 (变为“已完成”或“失败”状态的时间) */
  completedAt?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  retries?: number;
  /** 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了 */
  nergyState?: number;
  /** gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了 */
  gasfeeState?: number;
  /** eth 矿工费模式 1 自动 2手动 */
  ethFeeMode?: number;
  /** eth 最大矿工费 */
  ethFeeMax?: number;
  ethGasPrice?: number;
  ethGasLimit?: number;
  trxFeeMax?: number;
  /** 充值订单id */
  rechargesId?: number;
  /** gas费hash */
  gasfeeHash?: string;
  /** gas费金额 */
  gasfeeAmount?: number;
  energyMsg?: string;
}
