/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type { WalletApiApiWalletV1Page } from './walletApiApiWalletV1Page';
import type { WalletApiApiWalletV1TaskAddressRecord } from './walletApiApiWalletV1TaskAddressRecord';

export interface WalletApiApiWalletV1TaskAddressRecordRes {
  /** 分页信息 */
  page?: WalletApiApiWalletV1Page;
  /** 归集任务列表 */
  list?: WalletApiApiWalletV1TaskAddressRecord[];
}
