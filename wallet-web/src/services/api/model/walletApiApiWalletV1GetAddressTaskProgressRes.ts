/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */

export interface WalletApiApiWalletV1GetAddressTaskProgressRes {
  /** 任务ID */
  task_id?: string;
  /** 任务状态：pending(待处理), processing(处理中), completed(已完成), failed(失败) */
  status?: string;
  /** 进度百分比，0-100 */
  progress?: number;
  /** 已处理数量 */
  processed_rows?: number;
  /** 总数量 */
  total_rows?: number;
  /** 错误信息，仅在status为failed时有值 */
  error_message?: string;
}
