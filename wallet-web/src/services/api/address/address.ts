/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  GetWalletAddressListParams,
  GetWalletAddressTaskProgressParams,
  GetWalletTransactionRecordParams,
  WalletApiApiWalletV1BatchCreateAddressReq,
  WalletApiApiWalletV1BatchCreateAddressRes,
  WalletApiApiWalletV1BindAddressReq,
  WalletApiApiWalletV1BindAddressRes,
  WalletApiApiWalletV1ExportAddressReq,
  WalletApiApiWalletV1ExportAddressRes,
  WalletApiApiWalletV1ExportTransactionRecordReq,
  WalletApiApiWalletV1ExportTransactionRecordRes,
  WalletApiApiWalletV1GetAddressListRes,
  WalletApiApiWalletV1GetAddressStatisticRes,
  WalletApiApiWalletV1GetAddressTaskProgressRes,
  WalletApiApiWalletV1GetTransactionRecordRes,
  WalletApiApiWalletV1GetTransactionRecordStatisticRes,
  WalletApiApiWalletV1RefreshAddressReq,
  WalletApiApiWalletV1RefreshAddressRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getAddress = () => {
/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 获取地址列表
 */
const getWalletAddressList = (
    params: GetWalletAddressListParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetAddressListRes>(
      {url: `/wallet/address-list`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 获取地址页面的统计信息
 */
const getWalletAddressStatistic = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetAddressStatisticRes>(
      {url: `/wallet/address-statistic`, method: 'GET'
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 查询地址创建任务进度
 */
const getWalletAddressTaskProgress = (
    params: GetWalletAddressTaskProgressParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetAddressTaskProgressRes>(
      {url: `/wallet/address-task-progress`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 批量创建地址
 */
const postWalletBatchCreateAddress = (
    walletApiApiWalletV1BatchCreateAddressReq: WalletApiApiWalletV1BatchCreateAddressReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1BatchCreateAddressRes>(
      {url: `/wallet/batch-create-address`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1BatchCreateAddressReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 绑定地址
 */
const postWalletBindAddress = (
    walletApiApiWalletV1BindAddressReq: WalletApiApiWalletV1BindAddressReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1BindAddressRes>(
      {url: `/wallet/bind-address`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1BindAddressReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 导出地址
 */
const postWalletExportAddress = (
    walletApiApiWalletV1ExportAddressReq: WalletApiApiWalletV1ExportAddressReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ExportAddressRes>(
      {url: `/wallet/export-address`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1ExportAddressReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 导出交易记录
 */
const postWalletExportTransactionRecord = (
    walletApiApiWalletV1ExportTransactionRecordReq: WalletApiApiWalletV1ExportTransactionRecordReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ExportTransactionRecordRes>(
      {url: `/wallet/export-transaction-record`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1ExportTransactionRecordReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 刷新地址余额
 */
const postWalletRefreshAddress = (
    walletApiApiWalletV1RefreshAddressReq: WalletApiApiWalletV1RefreshAddressReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1RefreshAddressRes>(
      {url: `/wallet/refresh-address`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1RefreshAddressReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 交易记录
 */
const getWalletTransactionRecord = (
    params: GetWalletTransactionRecordParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetTransactionRecordRes>(
      {url: `/wallet/transaction-record`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 交易记录统计
 */
const getWalletTransactionRecordStatistic = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetTransactionRecordStatisticRes>(
      {url: `/wallet/transaction-record-statistic`, method: 'GET'
    },
      );
    }
  return {getWalletAddressList,getWalletAddressStatistic,getWalletAddressTaskProgress,postWalletBatchCreateAddress,postWalletBindAddress,postWalletExportAddress,postWalletExportTransactionRecord,postWalletRefreshAddress,getWalletTransactionRecord,getWalletTransactionRecordStatistic}};
export type GetWalletAddressListResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['getWalletAddressList']>>>
export type GetWalletAddressStatisticResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['getWalletAddressStatistic']>>>
export type GetWalletAddressTaskProgressResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['getWalletAddressTaskProgress']>>>
export type PostWalletBatchCreateAddressResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['postWalletBatchCreateAddress']>>>
export type PostWalletBindAddressResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['postWalletBindAddress']>>>
export type PostWalletExportAddressResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['postWalletExportAddress']>>>
export type PostWalletExportTransactionRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['postWalletExportTransactionRecord']>>>
export type PostWalletRefreshAddressResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['postWalletRefreshAddress']>>>
export type GetWalletTransactionRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['getWalletTransactionRecord']>>>
export type GetWalletTransactionRecordStatisticResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAddress>['getWalletTransactionRecordStatistic']>>>
