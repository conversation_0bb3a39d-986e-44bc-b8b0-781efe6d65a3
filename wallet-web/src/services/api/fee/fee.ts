/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  GetWalletTokenFeeSupplementsParams,
  WalletApiApiWalletV1GetFeeStatisticsRes,
  WalletApiApiWalletV1GetTokenFeeSupplementsRes,
  WalletApiApiWalletV1UpdateTokenFeeSupplementStatusReq,
  WalletApiApiWalletV1UpdateTokenFeeSupplementStatusRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getFee = () => {
/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 获取费用统计信息
 */
const getWalletFeeStatistics = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetFeeStatisticsRes>(
      {url: `/wallet/fee-statistics`, method: 'GET'
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 更新代币费用补充记录状态
 */
const postWalletTokenFeeSupplementStatus = (
    walletApiApiWalletV1UpdateTokenFeeSupplementStatusReq: WalletApiApiWalletV1UpdateTokenFeeSupplementStatusReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1UpdateTokenFeeSupplementStatusRes>(
      {url: `/wallet/token-fee-supplement-status`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1UpdateTokenFeeSupplementStatusReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 获取代币费用补充记录列表
 */
const getWalletTokenFeeSupplements = (
    params: GetWalletTokenFeeSupplementsParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetTokenFeeSupplementsRes>(
      {url: `/wallet/token-fee-supplements`, method: 'GET',
        params
    },
      );
    }
  return {getWalletFeeStatistics,postWalletTokenFeeSupplementStatus,getWalletTokenFeeSupplements}};
export type GetWalletFeeStatisticsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getFee>['getWalletFeeStatistics']>>>
export type PostWalletTokenFeeSupplementStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getFee>['postWalletTokenFeeSupplementStatus']>>>
export type GetWalletTokenFeeSupplementsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getFee>['getWalletTokenFeeSupplements']>>>
