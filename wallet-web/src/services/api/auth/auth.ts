/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  WalletApiApiWalletV1AuthReq,
  WalletApiApiWalletV1AuthRes,
  WalletApiApiWalletV1ChangePasswordReq,
  WalletApiApiWalletV1ChangePasswordRes,
  WalletApiApiWalletV1GenerateGoogleCodeRes,
  WalletApiApiWalletV1LogoutReq,
  WalletApiApiWalletV1LogoutRes,
  WalletApiApiWalletV1ReBindGoogleCodeReq,
  WalletApiApiWalletV1ReBindGoogleCodeRes,
  WalletApiApiWalletV1RefreshTokenReq,
  WalletApiApiWalletV1RefreshTokenRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getAuth = () => {
/**
 * @summary 钱包认证
 */
const postWalletAuth = (
    walletApiApiWalletV1AuthReq: WalletApiApiWalletV1AuthReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1AuthRes>(
      {url: `/wallet/auth`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1AuthReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 修改密码
 */
const postWalletChangePassword = (
    walletApiApiWalletV1ChangePasswordReq: WalletApiApiWalletV1ChangePasswordReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ChangePasswordRes>(
      {url: `/wallet/change-password`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1ChangePasswordReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 生成google验证码密钥以及二维码
 */
const getWalletGenerateGoogleCode = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GenerateGoogleCodeRes>(
      {url: `/wallet/generate-google-code`, method: 'GET'
    },
      );
    }
  /**
 * @summary Logout
 */
const postWalletLogout = (
    walletApiApiWalletV1LogoutReq: WalletApiApiWalletV1LogoutReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1LogoutRes>(
      {url: `/wallet/logout`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1LogoutReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 绑定google验证码
 */
const postWalletRebindGoogleCode = (
    walletApiApiWalletV1ReBindGoogleCodeReq: WalletApiApiWalletV1ReBindGoogleCodeReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ReBindGoogleCodeRes>(
      {url: `/wallet/rebind-google-code`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1ReBindGoogleCodeReq
    },
      );
    }
  /**
 * @summary Refresh token
 */
const postWalletRefresh = (
    walletApiApiWalletV1RefreshTokenReq: WalletApiApiWalletV1RefreshTokenReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1RefreshTokenRes>(
      {url: `/wallet/refresh`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1RefreshTokenReq
    },
      );
    }
  return {postWalletAuth,postWalletChangePassword,getWalletGenerateGoogleCode,postWalletLogout,postWalletRebindGoogleCode,postWalletRefresh}};
export type PostWalletAuthResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAuth>['postWalletAuth']>>>
export type PostWalletChangePasswordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAuth>['postWalletChangePassword']>>>
export type GetWalletGenerateGoogleCodeResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAuth>['getWalletGenerateGoogleCode']>>>
export type PostWalletLogoutResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAuth>['postWalletLogout']>>>
export type PostWalletRebindGoogleCodeResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAuth>['postWalletRebindGoogleCode']>>>
export type PostWalletRefreshResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getAuth>['postWalletRefresh']>>>
