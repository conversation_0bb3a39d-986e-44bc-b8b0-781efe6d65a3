/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  WalletApiApiWalletV1BaseSettingReq,
  WalletApiApiWalletV1BaseSettingRes,
  WalletApiApiWalletV1WalletSettingReq,
  WalletApiApiWalletV1WalletSettingRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getSetting = () => {
/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 基本设置(已废弃,请使用/wallet/setting)
 */
const postWalletBaseSetting = (
    walletApiApiWalletV1BaseSettingReq: WalletApiApiWalletV1BaseSettingReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1BaseSettingRes>(
      {url: `/wallet/base-setting`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1BaseSettingReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 钱包设置
 */
const postWalletSetting = (
    walletApiApiWalletV1WalletSettingReq: WalletApiApiWalletV1WalletSettingReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1WalletSettingRes>(
      {url: `/wallet/setting`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1WalletSettingReq
    },
      );
    }
  return {postWalletBaseSetting,postWalletSetting}};
export type PostWalletBaseSettingResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSetting>['postWalletBaseSetting']>>>
export type PostWalletSettingResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSetting>['postWalletSetting']>>>
