/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 */
import type {
  GetWalletRechargeRecordParams,
  WalletApiApiWalletV1ExportRechargeRecordReq,
  WalletApiApiWalletV1ExportRechargeRecordRes,
  WalletApiApiWalletV1GetRechargeRecordRes,
  WalletApiApiWalletV1GetRechargeRecordStatisticRes
} from '.././model';

import { axiosInstance } from '../../axiosInstance';



  export const getRecharge = () => {
/**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 导出充值记录
 */
const postWalletExportRechargeRecord = (
    walletApiApiWalletV1ExportRechargeRecordReq: WalletApiApiWalletV1ExportRechargeRecordReq,
 ) => {
      return axiosInstance<WalletApiApiWalletV1ExportRechargeRecordRes>(
      {url: `/wallet/export-recharge-record`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: walletApiApiWalletV1ExportRechargeRecordReq
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 充值记录
 */
const getWalletRechargeRecord = (
    params: GetWalletRechargeRecordParams,
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetRechargeRecordRes>(
      {url: `/wallet/recharge-record`, method: 'GET',
        params
    },
      );
    }
  /**
 * 此接口需要在Authorization头部提供Bearer Token进行认证
 * @summary 充值记录统计
 */
const getWalletRechargeRecordStatistic = (
    
 ) => {
      return axiosInstance<WalletApiApiWalletV1GetRechargeRecordStatisticRes>(
      {url: `/wallet/recharge-record-statistic`, method: 'GET'
    },
      );
    }
  return {postWalletExportRechargeRecord,getWalletRechargeRecord,getWalletRechargeRecordStatistic}};
export type PostWalletExportRechargeRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getRecharge>['postWalletExportRechargeRecord']>>>
export type GetWalletRechargeRecordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getRecharge>['getWalletRechargeRecord']>>>
export type GetWalletRechargeRecordStatisticResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getRecharge>['getWalletRechargeRecordStatistic']>>>
