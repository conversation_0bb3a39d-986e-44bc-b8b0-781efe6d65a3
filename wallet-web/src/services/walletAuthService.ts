// import { setCountdown } from '../store/slices/authSlice'; // Removed unused import
import { getAuth } from './api/auth/auth';
import { WalletApiApiWalletV1AuthReq } from './api/model';

// 钱包认证服务
export const walletAuthService = {
    // 钱包认证
    async auth(authRequest: WalletApiApiWalletV1AuthReq) {
        const walletApi = getAuth();
        const response = await walletApi.postWalletAuth(authRequest); // response type is WalletApiApiWalletV1AuthRes

        // 如果认证成功且返回了 accessToken
        if (response.accessToken) { // Use accessToken from type definition
            // 保存 token
            localStorage.setItem('token', response.accessToken);
            // countdown is not in WalletApiApiWalletV1AuthRes, removing related logic
            // store.dispatch(setCountdown(response.countdown));
            
            // 钱包状态将由 token 决定，不再需要前端 dispatch
        }
        // Consider else block for handling cases where accessToken might be missing after successful auth

        return response;
    }
};