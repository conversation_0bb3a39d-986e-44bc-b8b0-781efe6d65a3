// import api from './api'; // Commented out as it's currently unused
import { Transaction } from './walletService';
import { mockGetAssetDetail, mockGetAssetTransactions, AssetDetail } from '../mock/assetData';

// 获取资产详情
export const getAssetDetail = (chain: string): Promise<AssetDetail> => {
    // 在实际环境中，这里会调用API
    // return api.get<AssetDetail>(`/assets/${chain}`);

    // 使用mock数据
    return mockGetAssetDetail(chain);
};

// 获取资产交易历史
export const getAssetTransactions = (chain: string, page = 1, limit = 10): Promise<Transaction[]> => {
    // 在实际环境中，这里会调用API
    // return api.get<Transaction[]>(`/assets/${chain}/transactions`, {
    //   params: { page, limit }
    // });

    // 使用mock数据
    return mockGetAssetTransactions(chain);
};

// 发送资产
export const sendAsset = (
    chain: string,
    toAddress: string,
    amount: string,
    memo?: string
): Promise<{ transactionId: string }> => {
    // 在实际环境中，这里会调用API
    // return api.post<{ transactionId: string }>(`/assets/${chain}/send`, {
    //   toAddress,
    //   amount,
    //   memo
    // });

    // 使用mock数据
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                transactionId: `tx-${Date.now()}-${Math.floor(Math.random() * 1000)}`
            });
        }, 1000);
    });
};

// 接收资产（生成接收地址）
export const receiveAsset = (chain: string): Promise<{ address: string }> => {
    // 在实际环境中，这里会调用API
    // return api.get<{ address: string }>(`/assets/${chain}/receive`);

    // 使用mock数据
    return new Promise((resolve) => {
        setTimeout(() => {
            const addresses: Record<string, string> = {
                eth: '******************************************',
                tron: 'TRX1234567890abcdef1234567890abcdef12345678',
                erc20: '******************************************',
                trc20: 'TRX1234567890abcdef1234567890abcdef12345678'
            };

            resolve({
                address: addresses[chain.toLowerCase()] || '未知地址'
            });
        }, 800);
    });
};
