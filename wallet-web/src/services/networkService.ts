import api from './api';
import { mockNetworkFees } from '../mock/networkData';

export interface NetworkFee {
    slow: { value: number, usd: number, time: string, gwei: number },
    average: { value: number, usd: number, time: string, gwei: number },
    fast: { value: number, usd: number, time: string, gwei: number },
    custom?: { value: number, usd: number, time: string, gwei: number },
}

// 获取网络费用
export const getNetworkFees = (network: string) => {
    return api.get<NetworkFee>('/network-fees', { params: { network } });
};

// 创建模拟数据 - 开发阶段使用
export const getMockNetworkFees = (network: string): Promise<NetworkFee> => {
    // 使用导入的mock数据
    // 默认以太坊网络费用
    const defaultFees: NetworkFee = {
        slow: { value: 0.00000215, usd: 0.03, time: '~30分钟', gwei: 1.25 },
        average: { value: 0.00000431, usd: 0.07, time: '~5分钟', gwei: 1.52 },
        fast: { value: 0.00000897, usd: 0.15, time: '~1分钟', gwei: 3.28 },
    };

    // 等待1秒模拟API延迟
    return new Promise(resolve => {
        setTimeout(() => {
            resolve(mockNetworkFees[network] || defaultFees);
        }, 1000);
    });
}; 