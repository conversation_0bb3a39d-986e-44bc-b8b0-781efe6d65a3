export const ETH_LIKE_NETWORKS = ['eth', 'bsc', 'polygon', 'arbitrum', 'optimism'];
export const TRON_LIKE_NETWORKS = ['trx', 'tron'];

export function isEthLikeNetwork(network?: string): boolean {
  if (!network) return false;
  const lowerNetwork = network.toLowerCase();
  return ETH_LIKE_NETWORKS.some(n => lowerNetwork.includes(n));
}

export function isTronLikeNetwork(network?: string): boolean {
  if (!network) return false;
  const lowerNetwork = network.toLowerCase();
  return TRON_LIKE_NETWORKS.some(n => lowerNetwork.includes(n));
}