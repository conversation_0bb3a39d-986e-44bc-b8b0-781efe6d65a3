// 使用 clipboard.js 的版本示例
// 安装: npm install clipboard
// 使用: import { copyToClipboard } from './clipboardUtils.clipboard-js';

import ClipboardJS from 'clipboard';
import { message } from 'antd';

/**
 * 使用 clipboard.js 复制文本到剪贴板
 * @param text 要复制的文本
 * @param options 配置选项
 */
export const copyToClipboard = (
  text: string,
  options: {
    showSuccessMessage?: boolean;
    showErrorMessage?: boolean;
    successMessage?: string;
    errorMessage?: string;
  } = {}
): Promise<boolean> => {
  const {
    showSuccessMessage = true,
    showErrorMessage = true,
    successMessage = '已复制到剪贴板',
    errorMessage = '复制失败，请手动复制'
  } = options;

  return new Promise((resolve) => {
    // 创建临时元素
    const tempElement = document.createElement('button');
    tempElement.style.position = 'fixed';
    tempElement.style.left = '-999999px';
    tempElement.style.opacity = '0';
    tempElement.style.pointerEvents = 'none';
    
    // 初始化 clipboard.js
    const clipboard = new ClipboardJS(tempElement, {
      text: () => text
    });

    // 成功回调
    clipboard.on('success', (e) => {
      if (showSuccessMessage) {
        message.success(successMessage);
      }
      cleanup();
      resolve(true);
    });

    // 失败回调
    clipboard.on('error', (e) => {
      console.error('Clipboard.js failed:', e);
      if (showErrorMessage) {
        message.error(errorMessage);
      }
      cleanup();
      resolve(false);
    });

    // 清理函数
    const cleanup = () => {
      clipboard.destroy();
      if (document.body.contains(tempElement)) {
        document.body.removeChild(tempElement);
      }
    };

    // 添加到 DOM 并触发点击
    document.body.appendChild(tempElement);
    tempElement.click();
  });
};

/**
 * 创建一个绑定到 DOM 元素的复制功能
 * @param selector CSS 选择器
 * @param getText 获取要复制文本的函数
 */
export const bindCopyToElement = (
  selector: string,
  getText: (element: Element) => string,
  options: {
    onSuccess?: (e: ClipboardJS.Event) => void;
    onError?: (e: ClipboardJS.Event) => void;
  } = {}
): ClipboardJS => {
  const clipboard = new ClipboardJS(selector, {
    text: getText
  });

  clipboard.on('success', (e) => {
    message.success('已复制到剪贴板');
    options.onSuccess?.(e);
  });

  clipboard.on('error', (e) => {
    message.error('复制失败，请手动复制');
    options.onError?.(e);
  });

  return clipboard;
};

/**
 * React Hook 版本
 */
export const useCopyToClipboard = () => {
  const copy = (text: string, options?: Parameters<typeof copyToClipboard>[1]) => {
    return copyToClipboard(text, options);
  };

  return { copy };
};
