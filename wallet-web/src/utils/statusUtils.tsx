import React from 'react';
import { Tag } from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  QuestionCircleOutlined, // Added for unknown status
  StopOutlined, // Added for rejected status
  SyncOutlined, // Added for processing status
} from '@ant-design/icons';

// 获取状态文本
export const getStatusText = (status: string | number): string => {
  switch (status) {
    case 'success':
    case 4: // Completed
      return '成功';
    case 'failed':
    case 5: // Failed
      return '失败';
    case 'pending':
    case 1: // Pending
      return '待审核';
    case 2: // Processing
      return '处理中';
    case 3: // Rejected
      return '已拒绝';
    default:
      return '未知';
  }
};

// 获取状态颜色
export const getStatusColor = (status: string | number): string => {
  switch (status) {
    case 'success':
    case 4: // Completed
      return 'success';
    case 'failed':
    case 5: // Failed
      return 'error';
    case 'pending':
    case 1: // Pending
      return 'warning';
    case 2: // Processing
      return 'processing';
    case 3: // Rejected
      return 'error'; // Or a specific color for rejected like 'magenta' or 'volcano'
    default:
      return 'default';
  }
};

// 获取状态图标
export const getStatusIcon = (status: string | number): React.ReactNode => {
  switch (status) {
    case 'success':
    case 4: // Completed
      return <CheckCircleOutlined />;
    case 'failed':
    case 5: // Failed
      return <CloseCircleOutlined />;
    case 'pending':
    case 1: // Pending
      return <ClockCircleOutlined />;
    case 2: // Processing
      return <SyncOutlined spin />;
    case 3: // Rejected
      return <StopOutlined />;
    default:
      return <QuestionCircleOutlined />;
  }
};

// 获取归集状态标签
export const getWithdrawStatusTag = (state: number | undefined): React.ReactNode => {
  if (state === undefined) {
    return <Tag icon={<QuestionCircleOutlined />} color="default">未知</Tag>;
  }

  let color = '';
  let text = '';
  let icon: React.ReactNode = null;

  switch (state) {
    case 1: // 待审核
      color = 'warning';
      text = '待审核';
      icon = <ClockCircleOutlined />;
      break;
    case 2: // 处理中
      color = 'processing';
      text = '处理中';
      icon = <SyncOutlined spin />;
      break;
    case 3: // 已拒绝
      color = 'error';
      text = '已拒绝';
      icon = <StopOutlined />;
      break;
    case 4: // 已完成
      color = 'success';
      text = '已完成';
      icon = <CheckCircleOutlined />;
      break;
    case 5: // 失败
      color = 'error';
      text = '失败';
      icon = <CloseCircleOutlined />;
      break;
    default:
      color = 'default';
      text = `未知 (${state})`;
      icon = <QuestionCircleOutlined />;
  }

  return (
    <Tag color={color} icon={icon}>
      {text}
    </Tag>
  );
};