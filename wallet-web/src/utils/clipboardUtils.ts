// @ts-ignore - clipboard.js 类型定义
import ClipboardJS from 'clipboard';
import { message } from 'antd';

/**
 * 使用 clipboard.js 复制文本到剪贴板
 * @param text 要复制的文本
 * @param options 配置选项
 */
export const copyToClipboard = (
  text: string,
  options: {
    showSuccessMessage?: boolean;
    showErrorMessage?: boolean;
    successMessage?: string;
    errorMessage?: string;
  } = {}
): Promise<boolean> => {
  const {
    showSuccessMessage = true,
    showErrorMessage = true,
    successMessage = '已复制到剪贴板',
    errorMessage = '复制失败，请手动复制'
  } = options;

  return new Promise((resolve) => {
    // 创建临时元素
    const tempElement = document.createElement('button');
    tempElement.style.position = 'fixed';
    tempElement.style.left = '-999999px';
    tempElement.style.top = '-999999px';
    tempElement.style.opacity = '0';
    tempElement.style.pointerEvents = 'none';
    tempElement.setAttribute('aria-hidden', 'true');
    tempElement.setAttribute('tabindex', '-1');

    // 初始化 clipboard.js
    const clipboard = new ClipboardJS(tempElement, {
      text: () => text
    });

    // 成功回调
    clipboard.on('success', (e: any) => {
      if (showSuccessMessage) {
        message.success(successMessage);
      }
      cleanup();
      resolve(true);
      e.clearSelection(); // 清除选择状态
    });

    // 失败回调
    clipboard.on('error', (e: any) => {
      console.error('Clipboard.js failed:', e);
      if (showErrorMessage) {
        message.error(errorMessage);
      }
      cleanup();
      resolve(false);
    });

    // 清理函数
    const cleanup = () => {
      clipboard.destroy();
      if (document.body.contains(tempElement)) {
        document.body.removeChild(tempElement);
      }
    };

    // 添加到 DOM 并触发点击
    document.body.appendChild(tempElement);
    tempElement.click();
  });
};

/**
 * 检查剪贴板功能是否可用
 */
export const isClipboardSupported = (): boolean => {
  // clipboard.js 会自动检测并选择最佳的复制方法
  // 支持现代浏览器的 Clipboard API 和旧浏览器的 execCommand
  return ClipboardJS.isSupported();
};

/**
 * 创建一个可复制的按钮组件的辅助函数
 * @param text 要复制的文本
 * @param callback 复制完成后的回调
 */
export const createCopyHandler = (
  text: string,
  callback?: (success: boolean) => void
) => {
  return async () => {
    const success = await copyToClipboard(text);
    callback?.(success);
  };
};

/**
 * 绑定复制功能到现有的 DOM 元素
 * @param selector CSS 选择器或 DOM 元素
 * @param getText 获取要复制文本的函数
 * @param options 配置选项
 */
export const bindCopyToElement = (
  selector: string | Element,
  getText: (element: Element) => string,
  options: {
    onSuccess?: (e: any) => void;
    onError?: (e: any) => void;
    showSuccessMessage?: boolean;
    showErrorMessage?: boolean;
    successMessage?: string;
    errorMessage?: string;
  } = {}
): ClipboardJS => {
  const {
    onSuccess,
    onError,
    showSuccessMessage = true,
    showErrorMessage = true,
    successMessage = '已复制到剪贴板',
    errorMessage = '复制失败，请手动复制'
  } = options;

  const clipboard = new ClipboardJS(selector, {
    text: getText
  });

  clipboard.on('success', (e: any) => {
    if (showSuccessMessage) {
      message.success(successMessage);
    }
    onSuccess?.(e);
    e.clearSelection();
  });

  clipboard.on('error', (e: any) => {
    console.error('Clipboard.js failed:', e);
    if (showErrorMessage) {
      message.error(errorMessage);
    }
    onError?.(e);
  });

  return clipboard;
};

/**
 * React Hook 版本的复制功能
 */
export const useCopyToClipboard = () => {
  const copy = (text: string, options?: Parameters<typeof copyToClipboard>[1]) => {
    return copyToClipboard(text, options);
  };

  return { copy, isSupported: isClipboardSupported() };
};