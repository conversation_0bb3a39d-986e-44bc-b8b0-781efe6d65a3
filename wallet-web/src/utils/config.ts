import { configService } from '../services/config';

/**
 * Utility functions for configuration management
 */

/**
 * Get the current API URL
 * This is a convenience function that can be used anywhere in the app
 */
export const getApiUrl = (): string => {
  return configService.getApiUrl();
};

/**
 * Check if the configuration has been loaded
 */
export const isConfigLoaded = (): boolean => {
  return configService.getConfig() !== null;
};

/**
 * Wait for configuration to be loaded
 * Useful when you need to ensure config is loaded before proceeding
 */
export const waitForConfig = async (timeout = 5000): Promise<void> => {
  const startTime = Date.now();
  
  while (!isConfigLoaded() && (Date.now() - startTime) < timeout) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  if (!isConfigLoaded()) {
    throw new Error('Configuration loading timeout');
  }
};

/**
 * Reload configuration
 * Useful for development or when configuration might have changed
 */
export const reloadConfig = async (): Promise<void> => {
  configService.reset();
  await configService.loadConfig();
};

/**
 * Get configuration with fallback
 * Returns the configuration or a default value if not loaded
 */
export const getConfigWithFallback = <T>(
  getter: (config: any) => T,
  fallback: T
): T => {
  const config = configService.getConfig();
  if (!config) {
    return fallback;
  }
  
  try {
    return getter(config);
  } catch {
    return fallback;
  }
};

/**
 * Environment detection utilities
 */
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

/**
 * Debug configuration
 * Logs current configuration to console (only in development)
 */
export const debugConfig = (): void => {
  if (isDevelopment()) {
    const config = configService.getConfig();
    console.group('🔧 Configuration Debug');
    console.log('Loaded:', isConfigLoaded());
    console.log('API URL:', getApiUrl());
    console.log('Full config:', config);
    console.groupEnd();
  }
};
