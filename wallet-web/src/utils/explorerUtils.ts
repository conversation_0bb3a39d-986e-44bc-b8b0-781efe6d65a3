// src/utils/explorerUtils.ts
export const openInExplorer = (type: 'tx' | 'address', value: string, network: string) => {
  if (!value) return; // 如果值为空，不执行操作

  let url = '';

  // 根据网络类型选择不同的区块浏览器
  switch (network) {
    case 'ETH':
      url = `https://etherscan.io/${type === 'tx' ? 'tx' : 'address'}/${value}`;
      break;
    case 'TRON':
      url = `https://tronscan.org/#/${type === 'tx' ? 'transaction' : 'address'}/${value}`;
      break;
    default:
      // 默认使用以太坊浏览器
      url = `https://etherscan.io/${type === 'tx' ? 'tx' : 'address'}/${value}`;
  }

  window.open(url, '_blank');
};