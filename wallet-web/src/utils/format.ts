import { Decimal } from 'decimal.js';

// 格式化金额，保留指定小数位数
export const formatAmount = (amount: string | number | null | undefined, decimals = 2): string => {
  if (amount === null || amount === undefined || amount === '') {
    return '--'; // 或者根据需求返回 '0.00' 或其他默认值
  }
  try {
    const num = new Decimal(amount);
    return num.toFixed(decimals);
  } catch (error) {
    console.error('Error formatting amount with Decimal.js:', error);
    // 对于无法解析的非数字字符串，可以返回原始值或特定错误提示
    // 或者尝试回退到 parseFloat，但这可能会有精度问题
    const fallbackNum = typeof amount === 'string' ? parseFloat(amount) : Number(amount);
    if (!isNaN(fallbackNum)) {
      return fallbackNum.toFixed(decimals);
    }
    return String(amount); // 或者 '--'
  }
};

// 格式化日期时间
export const formatDateTime = (timestamp: string | number | Date): string => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 格式化钱包地址，显示前几位和后几位
export const formatAddress = (address: string, prefixLength = 6, suffixLength = 4): string => {
  if (!address) return '';
  if (address.length <= prefixLength + suffixLength) return address;

  const prefix = address.substring(0, prefixLength);
  const suffix = address.substring(address.length - suffixLength);

  return `${prefix}...${suffix}`;
};

// 格式化交易类型
export const formatTransactionType = (type: string): string => {
  const typeMap: Record<string, string> = {
    deposit: '充值',
    withdrawal: '归集',
    transfer: '转账',
  };

  return typeMap[type] || type;
};

// 格式化交易状态
export const formatTransactionStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '处理中',
    completed: '已完成',
    failed: '失败',
  };

  return statusMap[status] || status;
};

// 格式化时间戳或日期字符串为本地化日期时间字符串
export const formatUnixTimestamp = (timestamp?: string | number, defaultValue = '-'): string => {
  if (timestamp === undefined || timestamp === null || timestamp === '' || timestamp === 0 || timestamp === '0') {
    return defaultValue;
  }
  try {
    let date: Date;
    if (typeof timestamp === 'string') {
      // 尝试直接解析字符串，可能是 ISO 格式或毫秒级时间戳字符串
      if (!isNaN(Number(timestamp))) { // Check if it's a numeric string
        const num = Number(timestamp);
        // Heuristic: if it's a 10-digit number, assume seconds. Otherwise, assume milliseconds.
        // This is a common case for Unix timestamps.
        // For more robust parsing, a library like date-fns or moment.js is recommended.
        date = new Date(String(num).length === 10 ? num * 1000 : num);
      } else {
        date = new Date(timestamp); // Try parsing as ISO string etc.
      }
    } else {
      // If it's a number, apply similar heuristic for seconds/milliseconds
      date = new Date(String(timestamp).length === 10 ? timestamp * 1000 : timestamp);
    }

    if (isNaN(date.getTime())) { // Check if date is valid
      return defaultValue;
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return defaultValue;
  }
};
