// 定义导出的数据行类型，可以根据实际需要进行调整
interface ExportDataRow {
  [key: string]: string | number | boolean | undefined | null;
}

/**
 * 将数据导出为 CSV 文件并触发下载
 * @param data 要导出的数据数组
 * @param filename 下载的文件名 (不含 .csv 后缀)
 * @param headersMap 可选的对象，用于将数据对象的键映射到 CSV 文件头。例如：{ id: 'ID', name: '名称' }
 */
export const exportDataToCsv = (data: ExportDataRow[], filename: string, headersMap?: Record<string, string>): void => {
  if (!data || data.length === 0) {
    console.warn('No data to export.');
    // 可以选择性地显示一个消息给用户，例如使用 antd 的 message 组件
    // import { message } from 'antd';
    // message.warn('没有可导出的数据');
    return;
  }

  // 确定 CSV 的表头
  const headerKeys = Object.keys(data[0]);
  const headers = headerKeys.map(key => (headersMap && headersMap[key]) ? headersMap[key] : key).join(',');

  // 将数据转换为 CSV 格式的字符串
  const csvContent = data
    .map((row) =>
      headerKeys
        .map((key) => {
          const value = row[key];
          // 处理 undefined, null 和包含逗号、引号的值
          if (value === undefined || value === null) {
            return '';
          }
          let stringValue = String(value);
          // 如果值包含逗号、双引号或换行符，则用双引号括起来，并将内部的双引号替换为两个双引号
          if (/[",\n\r]/.test(stringValue)) {
            stringValue = `"${stringValue.replace(/"/g, '""')}"`;
          }
          return stringValue;
        })
        .join(','),
    )
    .join('\n');

  const csv = `${headers}\n${csvContent}`;

  // 创建 Blob 对象
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

  // 创建下载链接并触发下载
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url); // 释放通过 URL.createObjectURL 创建的对象 URL
};