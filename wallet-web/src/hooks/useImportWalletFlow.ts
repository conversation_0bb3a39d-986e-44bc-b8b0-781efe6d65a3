import { useState } from 'react';
import { message } from 'antd';
import { useAppDispatch } from './useAppDispatch';
import { importWallet } from '../store/slices/walletSlice';
import { generateGoogleAuthCode } from '../services/walletService';

export type ImportStep = 'warning' | 'mnemonic' | 'password' | 'google2fa' | 'success';

export const importWarningsList = [
  '我了解助记词是找回钱包的唯一凭证，必须妥善保管',
  '我了解如果有人获取了我的助记词，可以直接盗取我的资产',
  '我了解我应该将助记词保存在安全的地方，不应被他人获取',
  '我了解导入钱包后，我需要设置一个强密码来保护我的钱包',
];

interface UseImportWalletFlowProps {
  onSuccessNavigation: () => void;
  onBack: () => void;
}

export const useImportWalletFlow = ({ onSuccessNavigation, onBack }: UseImportWalletFlowProps) => {
  const dispatch = useAppDispatch();
  const [importStep, setImportStep] = useState<ImportStep>('warning');
  const [mnemonic, setMnemonic] = useState<string>('');
  const [checkedWarnings, setCheckedWarnings] = useState<string[]>([]);
  const [password, setPassword] = useState<string>('');
  const [googleAuthData, setGoogleAuthData] = useState<{ secret: string; qrCode: string }>({ secret: '', qrCode: '' });
  const [loading, setLoading] = useState(false);
  const [loadingGoogleAuth, setLoadingGoogleAuth] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');

  const handleWarningCheck = (warning: string) => {
    if (checkedWarnings.includes(warning)) {
      setCheckedWarnings(checkedWarnings.filter((w) => w !== warning));
    } else {
      setCheckedWarnings([...checkedWarnings, warning]);
    }
  };

  const handleShowMnemonicInput = () => {
    setImportStep('mnemonic');
  };

  const handleMnemonicFinish = (values: { mnemonic: string }) => {
    setMnemonic(values.mnemonic);
    setImportStep('password');
  };

  const handlePasswordFinish = async (values: { password: string }) => {
    setPassword(values.password);
    setLoadingGoogleAuth(true);
    try {
      const data = await generateGoogleAuthCode();
      if (!data || (!data.secret && !data.qrCode)) {
        throw new Error('返回的谷歌验证码数据无效');
      }
      const qrCodeUrl =
        data.qrCode && data.qrCode.startsWith('otpauth://')
          ? data.qrCode
          : data.qrCode
            ? decodeURIComponent(data.qrCode)
            : '';
      setGoogleAuthData({ secret: data.secret || '', qrCode: qrCodeUrl });
      setImportStep('google2fa');
    } catch (error: any) {
      console.error('获取Google验证码失败:', error);
      message.error('获取Google验证码失败: ' + (error.message || '未知错误'));
    } finally {
      setLoadingGoogleAuth(false);
    }
  };

  const handleCopySecretKey = () => {
    navigator.clipboard
      .writeText(googleAuthData.secret)
      .then(() => message.success('密钥已复制到剪贴板'))
      .catch(() => message.error('复制失败，请手动复制'));
  };

  const handleGoogleAuthFinish = async (values: { googleCode: string }) => {
    setLoading(true);
    try {
      const response = await dispatch(
        importWallet({
          mnemonic: mnemonic,
          password: password,
          googleCode: values.googleCode,
          secret: googleAuthData.secret,
        }),
      ).unwrap();
      message.success('钱包导入成功');
      localStorage.setItem('walletAddress', response.address);
      setWalletAddress(response.address);
      setImportStep('success');
    } catch (error: any) {
      message.error(error || '导入钱包失败');
    } finally {
      setLoading(false);
    }
  };
  
  const handleResetWarnings = () => {
    setCheckedWarnings([]);
  };

  const resetImportFlow = () => {
    setImportStep('warning');
    setMnemonic('');
    setCheckedWarnings([]);
    setPassword('');
    setGoogleAuthData({ secret: '', qrCode: '' });
    setLoading(false);
    setLoadingGoogleAuth(false);
    setWalletAddress('');
  };

  return {
    importStep,
    setImportStep,
    mnemonic,
    checkedWarnings,
    password,
    googleAuthData,
    loading,
    loadingGoogleAuth,
    walletAddress,
    handleWarningCheck,
    handleShowMnemonicInput,
    handleMnemonicFinish,
    handlePasswordFinish,
    handleCopySecretKey,
    handleGoogleAuthFinish,
    handleResetWarnings,
    resetImportFlow,
    onSuccessNavigation,
    onBack,
  };
};
