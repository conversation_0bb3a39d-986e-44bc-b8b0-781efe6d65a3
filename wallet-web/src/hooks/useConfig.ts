import { useEffect, useState } from 'react';
import { configService, AppConfig } from '../services/config';

interface UseConfigResult {
  config: AppConfig | null;
  loading: boolean;
  error: string | null;
  apiUrl: string;
}

/**
 * Hook for accessing application configuration
 * Automatically loads configuration on first use
 */
export const useConfig = (): UseConfigResult => {
  const [config, setConfig] = useState<AppConfig | null>(configService.getConfig());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // If config is already loaded, no need to load again
    if (config) {
      return;
    }

    const loadConfig = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const loadedConfig = await configService.loadConfig();
        setConfig(loadedConfig);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load configuration';
        setError(errorMessage);
        console.error('Failed to load configuration:', err);
        
        // Set fallback config even on error
        setConfig({
          API_URL: configService.getApiUrl(),
        });
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, [config]);

  return {
    config,
    loading,
    error,
    apiUrl: config?.API_URL || configService.getApiUrl(),
  };
};

/**
 * Hook for getting just the API URL
 * Simpler version when you only need the API URL
 */
export const useApiUrl = (): string => {
  const { apiUrl } = useConfig();
  return apiUrl;
};

export default useConfig;
