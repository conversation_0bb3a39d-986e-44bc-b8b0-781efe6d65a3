import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Define the Electron API interface
interface ElectronAPI {
  getAppVersion: () => Promise<string>;
  onNavigate: (callback: (path: string) => void) => () => void;
  onUpdateAvailable: (callback: () => void) => () => void;
  onUpdateDownloaded: (callback: () => void) => () => void;
  installUpdate: () => Promise<void>;
}

// Declare the window interface with the electron property
declare global {
  interface Window {
    electron?: ElectronAPI;
  }
}

// Check if running in Electron
export const isElectron = () => {
  return window.electron !== undefined;
};

// Hook to use Electron features
export const useElectron = () => {
  const [appVersion, setAppVersion] = useState<string | null>(null);
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [isUpdateDownloaded, setIsUpdateDownloaded] = useState(false);
  const navigate = useNavigate();

  // Get app version on mount
  useEffect(() => {
    const getVersion = async () => {
      if (isElectron()) {
        try {
          const version = await window.electron!.getAppVersion();
          setAppVersion(version);
        } catch (error) {
          console.error('Failed to get app version:', error);
        }
      }
    };

    getVersion();
  }, []);

  // Set up navigation listener
  useEffect(() => {
    if (isElectron()) {
      // Register navigation handler
      const cleanup = window.electron!.onNavigate((path) => {
        navigate(path);
      });

      return cleanup;
    }
  }, [navigate]);

  // Set up update listeners
  useEffect(() => {
    if (isElectron()) {
      // Register update available handler
      const updateAvailableCleanup = window.electron!.onUpdateAvailable(() => {
        setIsUpdateAvailable(true);
      });

      // Register update downloaded handler
      const updateDownloadedCleanup = window.electron!.onUpdateDownloaded(() => {
        setIsUpdateDownloaded(true);
      });

      return () => {
        updateAvailableCleanup();
        updateDownloadedCleanup();
      };
    }
  }, []);

  // Install update function
  const installUpdate = async () => {
    if (isElectron() && isUpdateDownloaded) {
      try {
        await window.electron!.installUpdate();
      } catch (error) {
        console.error('Failed to install update:', error);
      }
    }
  };

  return {
    isElectron: isElectron(),
    appVersion,
    isUpdateAvailable,
    isUpdateDownloaded,
    installUpdate,
  };
};

export default useElectron;
