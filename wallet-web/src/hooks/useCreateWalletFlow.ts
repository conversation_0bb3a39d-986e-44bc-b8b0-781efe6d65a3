import { useState } from 'react';
import { message } from 'antd';
import { useAppDispatch } from './useAppDispatch';
import { createWallet } from '../store/slices/walletSlice';
import { generateWalletMnemonic, generateGoogleAuthCode } from '../services/walletService';

export type CreateStep = 'warning' | 'mnemonic' | 'verify' | 'password' | 'google2fa' | 'success';

export const warningsList = [
  '我了解助记词是找回钱包的唯一凭证，必须妥善保管',
  '我了解助记词一旦丢失，钱包资产将无法找回',
  '我了解如果有人获取了我的助记词，可以直接盗取我的资产',
  '我了解我应该将助记词抄写在纸上，而不是截图或保存在电子设备中',
];

interface UseCreateWalletFlowProps {
  onSuccessNavigation: () => void;
}

export const useCreateWalletFlow = ({ onSuccessNavigation }: UseCreateWalletFlowProps) => {
  const dispatch = useAppDispatch();
  const [createStep, setCreateStep] = useState<CreateStep>('warning');
  const [mnemonic, setMnemonic] = useState<string>('');
  const [checkedWarnings, setCheckedWarnings] = useState<string[]>([]);
  const [shuffledWords, setShuffledWords] = useState<string[]>([]);
  const [selectedWords, setSelectedWords] = useState<string[]>([]);
  const [verifyError, setVerifyError] = useState<string | undefined>(undefined);
  const [password, setPassword] = useState<string>('');
  const [googleAuthData, setGoogleAuthData] = useState<{ secret: string; qrCode: string }>({ secret: '', qrCode: '' });
  const [loading, setLoading] = useState(false); // General loading for the hook
  const [loadingGoogleAuth, setLoadingGoogleAuth] = useState(false);

  const handleStartCreateWalletProcess = async () => {
    setLoading(true);
    try {
      const generatedMnemonic = await generateWalletMnemonic();
      setMnemonic(generatedMnemonic);
      setCreateStep('warning');
    } catch (error: any) {
      message.error('生成助记词失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  const handleWarningCheck = (warning: string) => {
    if (checkedWarnings.includes(warning)) {
      setCheckedWarnings(checkedWarnings.filter((w) => w !== warning));
    } else {
      setCheckedWarnings([...checkedWarnings, warning]);
    }
  };

  const handleShowMnemonic = () => {
    setCreateStep('mnemonic');
  };

  const handleVerifyMnemonicStep = () => {
    const words = mnemonic.split(' ');
    const shuffled = [...words].sort(() => Math.random() - 0.5);
    setShuffledWords(shuffled);
    setSelectedWords([]);
    setVerifyError(undefined);
    setCreateStep('verify');
  };

  const handleSelectWord = (word: string) => {
    if (!selectedWords.includes(word)) {
      const newSelected = [...selectedWords, word];
      setSelectedWords(newSelected);
    }
  };

  const handleRemoveWord = (index: number) => {
    const newSelected = [...selectedWords];
    newSelected.splice(index, 1);
    setSelectedWords(newSelected);
  };

  const handleConfirmMnemonic = () => {
    const expectedMnemonic = mnemonic;
    const userMnemonic = selectedWords.join(' ');

    if (userMnemonic === expectedMnemonic) {
      setVerifyError(undefined);
      setCreateStep('password');
    } else {
      setVerifyError('助记词顺序不正确，请重新排列');
    }
  };

  const handlePasswordFinish = async (values: { password: string }) => {
    setPassword(values.password);
    setLoadingGoogleAuth(true);
    try {
      const data = await generateGoogleAuthCode();
      if (!data || (!data.secret && !data.qrCode)) {
        throw new Error('返回的谷歌验证码数据无效');
      }
      const qrCodeUrl =
        data.qrCode && data.qrCode.startsWith('otpauth://')
          ? data.qrCode
          : data.qrCode
            ? decodeURIComponent(data.qrCode)
            : '';
      setGoogleAuthData({ secret: data.secret || '', qrCode: qrCodeUrl });
      setCreateStep('google2fa');
    } catch (error: any) {
      console.error('获取Google验证码失败:', error);
      message.error('获取Google验证码失败: ' + (error.message || '未知错误'));
    } finally {
      setLoadingGoogleAuth(false);
    }
  };

  const handleCopySecretKey = () => {
    navigator.clipboard
      .writeText(googleAuthData.secret)
      .then(() => message.success('密钥已复制到剪贴板'))
      .catch(() => message.error('复制失败，请手动复制'));
  };

  const handleGoogleAuthFinish = async (values: { googleCode: string }) => {
    setLoading(true);
    try {
      const response = await dispatch(
        createWallet({
          mnemonic: mnemonic,
          password: password,
          googleCode: values.googleCode,
          secret: googleAuthData.secret,
        }),
      ).unwrap();
      message.success('钱包创建成功');
      localStorage.setItem('walletAddress', response.address);
      setCreateStep('success');
    } catch (error: any) {
      message.error(error || '创建钱包失败');
    } finally {
      setLoading(false);
    }
  };
  
  const handleResetWarnings = () => {
    setCheckedWarnings([]);
  };

  const resetCreateFlow = () => {
    setCreateStep('warning');
    setMnemonic('');
    setCheckedWarnings([]);
    setShuffledWords([]);
    setSelectedWords([]);
    setVerifyError(undefined);
    setPassword('');
    setGoogleAuthData({ secret: '', qrCode: '' });
    setLoading(false);
    setLoadingGoogleAuth(false);
  };

  return {
    createStep,
    setCreateStep,
    mnemonic,
    checkedWarnings,
    shuffledWords,
    selectedWords,
    verifyError,
    password, // Though not directly used by UI, might be useful for debugging or extensions
    googleAuthData,
    loading,
    loadingGoogleAuth,
    handleStartCreateWalletProcess,
    handleWarningCheck,
    handleShowMnemonic,
    handleVerifyMnemonicStep,
    handleSelectWord,
    handleRemoveWord,
    handleConfirmMnemonic,
    handlePasswordFinish,
    handleCopySecretKey,
    handleGoogleAuthFinish,
    handleResetWarnings,
    resetCreateFlow, // To reset the flow if user navigates away or cancels
    onSuccessNavigation, // Propagate this for the final step
  };
};