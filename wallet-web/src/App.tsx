import { ConfigProvider as AntdConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import { HashRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import './App.css';
import { useAppDispatch } from './hooks/useAppDispatch'; // useAppSelector 已在 useAppDispatch 中导出
import ConfigProvider from './components/ConfigProvider';
import MainLayout from './layouts/MainLayout';
import Addresses from './pages/Addresses';
import AssetDetail from './pages/AssetDetail';
import Collection from './pages/Collection';
import Dashboard from './pages/Dashboard';
import FeeManagement from './pages/FeeManagement';
import Login from './pages/Login';
import RechargeRecords from './pages/RechargeRecords';
import Settings from './pages/Settings';
import Transactions from './pages/Transactions';
import Transfer from './pages/Transfer';
import Wallet from './pages/Wallet';
import WalletInit from './pages/WalletInit';
import WithdrawRecords from './pages/WithdrawRecords';
import WithdrawPlan from './pages/WithdrawPlan';
import store from './store';
import { checkWalletStatus } from './store/slices/walletSlice';
import UpdateNotification from './components/UpdateNotification';

// 带布局的私有路由组件，用于需要布局的页面
const LayoutRoute: React.FC<{ element: React.ReactElement }> = ({ element }) => {
  return <MainLayout>{element}</MainLayout>;
};

// 初始化组件，用于在应用启动时获取用户信息和检查钱包状态
const AppInitializer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  // const { isInitialized, isUnlocked } = useAppSelector((state) => state.wallet); // Removed
  // We will now rely on the result of checkWalletStatus and the presence of a token

  // 只在组件挂载时检查一次钱包状态
  useEffect(() => {
    dispatch(checkWalletStatus())
      .unwrap()
      .then((status) => {
        // status is WalletApiApiWalletV1GetPublicWalletInitStatusRes
        const accessToken = localStorage.getItem('accessToken');
        const isWalletInitPage = pathname === '/wallet-init';
        const isLoginPage = pathname === '/login';

        if (!status.is_initialized && !isWalletInitPage) {
          navigate('/wallet-init');
        } else if (status.is_initialized && !accessToken && !isLoginPage && !isWalletInitPage) {
          navigate('/login');
        } else if (status.is_initialized && accessToken && (isLoginPage || isWalletInitPage)) {
          navigate('/dashboard');
        }
      })
      .catch((error) => {
        console.error('检查钱包状态失败:', error);
        // 如果检查状态失败，且不在登录页，则导航到登录页作为回退
        if (pathname !== '/login') {
          navigate('/login');
        }
      });
  }, [dispatch, navigate, pathname]);

  // 依赖认证状态执行导航逻辑
  // The navigation logic has been moved to the useEffect hook that dispatches checkWalletStatus
  // This ensures navigation happens after the status check is complete.

  return <>{children}</>;
};

// 应用内容组件
const AppContent: React.FC = () => {
  return (
    <AppInitializer>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/wallet-init" element={<WalletInit />} />
        <Route path="/dashboard" element={<LayoutRoute element={<Dashboard />} />} />
        <Route path="/wallet" element={<LayoutRoute element={<Wallet />} />} />
        <Route path="/addresses" element={<LayoutRoute element={<Addresses />} />} />
        <Route path="/transactions" element={<LayoutRoute element={<Transactions />} />} />
        <Route path="/recharge" element={<LayoutRoute element={<RechargeRecords />} />} />
        <Route path="/withdraw" element={<LayoutRoute element={<WithdrawRecords />} />} />
        <Route path="/withdraw-plan" element={<LayoutRoute element={<WithdrawPlan />} />} />
        <Route path="/transfer" element={<LayoutRoute element={<Transfer />} />} />
        <Route path="/collection" element={<LayoutRoute element={<Collection />} />} />
        <Route path="/fee-management" element={<LayoutRoute element={<FeeManagement />} />} />
        <Route path="/settings" element={<LayoutRoute element={<Settings />} />} />
        <Route path="/asset/:chain" element={<LayoutRoute element={<AssetDetail />} />} />
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </AppInitializer>
  );
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider>
        <AntdConfigProvider locale={zhCN}>
          <Router>
            <UpdateNotification />
            <AppContent />
          </Router>
        </AntdConfigProvider>
      </ConfigProvider>
    </Provider>
  );
};

export default App;
