export interface Wallet {
  id: string;
  balance: string;
  currency: string;
  status: 'active' | 'frozen' | 'closed';
  createdAt: string;
  updatedAt: string;
}

export interface Transaction {
  id: string;
  amount: string;
  currency: string;
  type: 'deposit' | 'withdrawal' | 'transfer';
  status: 'completed' | 'pending' | 'failed';
  description: string;
  timestamp: string;
  walletId: string;
}

export interface WalletBalance {
  balance: string;
  currency: string;
}

export interface TransferRequest {
  toWalletId: string;
  amount: string;
  description?: string;
}

export interface DepositRequest {
  amount: string;
  paymentMethod: string;
  description?: string;
}

export interface WithdrawRequest {
  amount: string;
  bankAccount: string;
  description?: string;
}
