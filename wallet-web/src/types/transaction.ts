import { GetWalletTransactionRecordStatus } from '../services/api/model/getWalletTransactionRecordStatus';

// 注意：GetWalletTransactionRecordStatus 的导入路径已根据 src/types 的位置进行调整。
// 如果 GetWalletTransactionRecordStatus 是 orval 生成的类型，它应该在 services/api/model/ 目录下。
// 假设它在 'services/api/model/'

export interface Transaction {
  id?: string;
  transaction_id?: string;
  transaction_hash?: string;
  block_hash?: string;
  block_number?: number;
  transaction_time?: string; // 通常是 ISO 8601 字符串或时间戳
  transaction_type?: string; // 具体的交易类型，如 'deposit', 'withdraw', 'transfer', 'miner_fee'
  transaction_status?: GetWalletTransactionRecordStatus;
  sender_address?: string;
  receiver_address?: string;
  amount?: string; // 使用字符串以保持精度
  transaction_fee?: string; // 使用字符串以保持精度
  chain?: string; // 例如 'ETH', 'TRON'
  token_name?: string; // 例如 'USDT', 'ETH', 'TRX'
  is_token?: boolean; // 是否为代币交易
  memo?: string; // 交易备注

  // 兼容旧结构或API可能返回的额外字段
  hash?: string; // 可能与 transaction_hash 重复
  status?: string; // 可能与 transaction_status 重复，但类型可能不同
  from?: string; // 可能与 sender_address 重复
  to?: string; // 可能与 receiver_address 重复
  currency?: string; // 可能与 token_name 或 chain 重复
  fee?: string; // 可能与 transaction_fee 重复
  feeCurrency?: string; // 手续费币种
  network?: string; // 可能与 chain 重复
  description?: string; // 描述信息
  timestamp?: string; // 可能与 transaction_time 重复，格式可能不同
  blockNumber?: number; // 可能与 block_number 重复
  confirmations?: number; // 区块确认数

  // Tron fee details (如果特定于Tron交易，可以考虑更细致的类型设计)
  net_fee?: string;
  energy_fee?: string;

  // ETH fee details (示例)
  effective_gas_price?: string;
  gas_used?: string;
  cumulative_gas_used?: string;
}