#!/usr/bin/env node

/**
 * 这个脚本用于自动添加下划线前缀到未使用的变量
 * 使用方法: node fix-unused-vars.js
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// 运行ESLint并获取结果
function getUnusedVars() {
  try {
    // 使用单引号而不是双引号包裹通配符，避免shell扩展问题
    const output = execSync("npx eslint --format json 'src/**/*.{js,jsx,ts,tsx}'" , { encoding: 'utf8', shell: '/bin/bash' });
    return JSON.parse(output);
  } catch (error) {
    // ESLint 会以非零退出码退出，所以我们需要从错误中提取输出
    if (error.stdout && error.stdout.trim()) {
      try {
        return JSON.parse(error.stdout);
      } catch (jsonError) {
        console.error('解析ESLint输出时出错:', jsonError.message);
        console.log('原始输出:', error.stdout);
      }
    } else {
      console.error('ESLint执行出错:', error.message);
      if (error.stderr) console.error(error.stderr);
    }
    return [];
  }
}

// 处理文件中的未使用变量
function processFile(filePath, unusedVars) {
  console.log(`处理文件: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // 按照行号倒序排序，这样我们从下往上修改，避免修改后行号变化
  unusedVars.sort((a, b) => b.line - a.line || b.column - a.column);

  for (const item of unusedVars) {
    const { line, column, varName } = item;
    
    // 跳过已经有下划线前缀的变量
    if (varName.startsWith('_')) continue;
    
    // 获取该变量所在行的内容
    const lines = content.split('\n');
    const lineContent = lines[line - 1];
    
    // 找到变量名的精确位置
    const varPos = lineContent.indexOf(varName, column - 1);
    if (varPos === -1) continue;
    
    // 添加下划线前缀
    const newLineContent = 
      lineContent.substring(0, varPos) + 
      '_' + 
      lineContent.substring(varPos);
    
    lines[line - 1] = newLineContent;
    content = lines.join('\n');
    modified = true;
    console.log(`  已添加前缀: ${varName} -> _${varName}`);
  }

  // 如果文件被修改，写回文件
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  文件已更新: ${filePath}`);
  } else {
    console.log(`  文件无变化: ${filePath}`);
  }
}

// 主函数
function main() {
  console.log('开始检测未使用的变量...');
  const results = getUnusedVars();
  
  if (!results || !results.length) {
    console.log('没有找到需要处理的文件或未发现未使用变量。');
    return;
  }
  
  // 收集每个文件中的未使用变量
  const fileVars = {};
  
  for (const result of results) {
    const filePath = result.filePath;
    
    if (!fileVars[filePath]) {
      fileVars[filePath] = [];
    }
    
    for (const message of result.messages) {
      // 只处理未使用变量的警告
      if (message.ruleId === 'unused-imports/no-unused-vars' || 
          message.ruleId === '@typescript-eslint/no-unused-vars') {
        const match = message.message.match(/['"]([^'"]+)['"] is .* never used/);
        if (match) {
          const varName = match[1];
          // 如果变量名不以下划线开头，加入到待处理列表
          if (!varName.startsWith('_')) {
            fileVars[filePath].push({
              line: message.line,
              column: message.column,
              varName
            });
          }
        }
      }
    }
  }
  
  // 处理每个文件
  let totalFixed = 0;
  for (const filePath in fileVars) {
    if (fileVars[filePath].length > 0) {
      processFile(filePath, fileVars[filePath]);
      totalFixed += fileVars[filePath].length;
    }
  }
  
  console.log(`处理完成！共修复了 ${totalFixed} 个未使用变量。`);
}

try {
  main();
} catch (error) {
  console.error('执行脚本时发生错误:', error);
  process.exit(1);
}
