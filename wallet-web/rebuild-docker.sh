#!/bin/bash

# 脚本说明：快速重建和启动 Docker 容器
# 功能：停止容器、清理、重新构建（不使用缓存）、启动容器

set -e  # 遇到错误立即退出

echo "🚀 开始重建 Docker 容器..."

# 进入 deployment 目录
cd deployment

echo "📍 当前目录: $(pwd)"

# 停止并删除容器
echo "🛑 停止现有容器..."
docker compose down

# 等待一下确保容器完全停止
sleep 2

# 重新构建镜像（不使用缓存）
echo "🔨 重新构建镜像（不使用缓存）..."
docker compose build --no-cache

# 启动容器
echo "▶️  启动容器..."
docker compose up -d

# 显示容器状态
echo "✅ 容器状态："
docker compose ps

echo "🎉 Docker 容器重建完成！"