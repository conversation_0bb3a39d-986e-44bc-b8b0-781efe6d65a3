# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
README.MD
*.md
doc/
docs/

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
wallet-api
wallet-api-*
bot_gateway
message_processor

# Go specific
vendor/
*.test
*.prof
coverage.out

# Node modules (if any)
node_modules/

# Environment files
.env
.env.local
.env.*.local

# Test files
*_test.go
test/
tests/

# Hack and development tools
hack/

# Backup files
*.bak
*.backup
