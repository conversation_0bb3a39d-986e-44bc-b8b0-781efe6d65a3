/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306_1
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : localhost:3306
 Source Schema         : wallet

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 19/05/2025 22:40:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for address
-- ----------------------------
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address` (
  `id` int NOT NULL AUTO_INCREMENT,
  `wallet_id` int NOT NULL COMMENT '钱包id',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签',
  `bind_status` int NOT NULL DEFAULT '0' COMMENT '绑定状态 (0 = default)',
  `bind_at` datetime DEFAULT NULL COMMENT '绑定时间',
  `last_query_at` datetime DEFAULT NULL COMMENT '最近查询时间',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态 (0 = default)',
  `chain_coin_balance` decimal(32,6) NOT NULL DEFAULT '0.000000' COMMENT 'chain coin 余额',
  `chain_usdt_balance` decimal(32,6) NOT NULL DEFAULT '0.000000' COMMENT 'chain usdt 余额',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `path` int DEFAULT NULL COMMENT '路径',
  `alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `merchantId` int DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_index` (`address`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Wallet addresses';

-- ----------------------------
-- Records of address
-- ----------------------------
BEGIN;
INSERT INTO `address` (`id`, `wallet_id`, `address`, `type`, `label`, `bind_status`, `bind_at`, `last_query_at`, `status`, `chain_coin_balance`, `chain_usdt_balance`, `create_at`, `update_at`, `path`, `alias`, `merchantId`) VALUES (1, 1, 'TLJ1G8RpyBr6Ksdv4XC2vL3ApM3CHbRWHX', 'TRON', '', 1, '2025-05-19 20:49:21', NULL, 2, 0.000000, 54.000000, '2025-05-19 20:48:27', '2025-05-19 22:28:40', 1, '', 0);
INSERT INTO `address` (`id`, `wallet_id`, `address`, `type`, `label`, `bind_status`, `bind_at`, `last_query_at`, `status`, `chain_coin_balance`, `chain_usdt_balance`, `create_at`, `update_at`, `path`, `alias`, `merchantId`) VALUES (2, 1, 'TH4zbRtLDTJodZTjsnwnedLtWmprhiW3aT', 'TRON', '', 1, '2025-05-19 20:49:20', NULL, 2, 54.000000, 0.000000, '2025-05-19 20:48:27', '2025-05-19 22:28:37', 2, '', 0);
INSERT INTO `address` (`id`, `wallet_id`, `address`, `type`, `label`, `bind_status`, `bind_at`, `last_query_at`, `status`, `chain_coin_balance`, `chain_usdt_balance`, `create_at`, `update_at`, `path`, `alias`, `merchantId`) VALUES (3, 1, '******************************************', 'ETH', '', 1, '2025-05-19 20:49:16', NULL, 2, 0.000000, 15.545456, '2025-05-19 20:48:27', '2025-05-19 22:28:44', 1, '', 0);
INSERT INTO `address` (`id`, `wallet_id`, `address`, `type`, `label`, `bind_status`, `bind_at`, `last_query_at`, `status`, `chain_coin_balance`, `chain_usdt_balance`, `create_at`, `update_at`, `path`, `alias`, `merchantId`) VALUES (4, 1, '******************************************', 'ETH', '', 1, '2025-05-19 20:49:14', NULL, 2, 0.050000, 0.000000, '2025-05-19 20:48:27', '2025-05-19 22:28:42', 2, '', 0);
COMMIT;

-- ----------------------------
-- Table structure for password_attempts
-- ----------------------------
DROP TABLE IF EXISTS `password_attempts`;
CREATE TABLE `password_attempts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `attempt_count` int NOT NULL DEFAULT '0',
  `last_attempt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tracks password attempt failures';

-- ----------------------------
-- Records of password_attempts
-- ----------------------------
BEGIN;
INSERT INTO `password_attempts` (`id`, `attempt_count`, `last_attempt`) VALUES (1, 0, '2025-05-19 22:13:19');
COMMIT;

-- ----------------------------
-- Table structure for progress_tasks
-- ----------------------------
DROP TABLE IF EXISTS `progress_tasks`;
CREATE TABLE `progress_tasks` (
  `task_id` varchar(255) NOT NULL,
  `status` varchar(50) NOT NULL,
  `progress` double DEFAULT '0',
  `processed_rows` int DEFAULT '0',
  `total_rows` int DEFAULT '0',
  `error_message` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of progress_tasks
-- ----------------------------
BEGIN;
INSERT INTO `progress_tasks` (`task_id`, `status`, `progress`, `processed_rows`, `total_rows`, `error_message`, `created_at`, `updated_at`) VALUES ('addr_20250519204827_eC84YanN', 'completed', 100, 4, 4, '', '2025-05-19 20:48:27', '2025-05-19 20:48:27');
COMMIT;

-- ----------------------------
-- Table structure for refresh_tokens
-- ----------------------------
DROP TABLE IF EXISTS `refresh_tokens`;
CREATE TABLE `refresh_tokens` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL COMMENT 'Associated user/wallet ID',
  `jti` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'JWT ID, unique identifier for the refresh token',
  `expires_at` timestamp NOT NULL COMMENT 'Expiry timestamp of the refresh token',
  `is_revoked` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Flag indicating if the token has been revoked',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of creation',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp of last update',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_jti` (`jti`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores refresh tokens for JWT authentication';

-- ----------------------------
-- Records of refresh_tokens
-- ----------------------------
BEGIN;
INSERT INTO `refresh_tokens` (`id`, `user_id`, `jti`, `expires_at`, `is_revoked`, `created_at`, `updated_at`) VALUES (1, 1, 'svukrl09v90da05d7pkyjq820025n6qm', '2025-05-26 20:47:55', 0, '2025-05-19 20:47:55', '2025-05-19 20:47:55');
INSERT INTO `refresh_tokens` (`id`, `user_id`, `jti`, `expires_at`, `is_revoked`, `created_at`, `updated_at`) VALUES (2, 1, 'svukrl09v90da06o5kljxtk400ihosta', '2025-05-26 21:49:13', 1, '2025-05-19 21:49:14', '2025-05-19 22:10:03');
INSERT INTO `refresh_tokens` (`id`, `user_id`, `jti`, `expires_at`, `is_revoked`, `created_at`, `updated_at`) VALUES (3, 1, 'svukrl09v90da0749e50zqw6005h8x6q', '2025-05-26 22:10:16', 1, '2025-05-19 22:10:16', '2025-05-19 22:11:10');
INSERT INTO `refresh_tokens` (`id`, `user_id`, `jti`, `expires_at`, `is_revoked`, `created_at`, `updated_at`) VALUES (4, 1, '826ht00eg70da07hne92lao2002sxa17', '2025-05-26 22:27:45', 0, '2025-05-19 22:27:45', '2025-05-19 22:27:45');
COMMIT;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Configuration key',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Configuration value',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `key` (`key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='System configuration key-value pairs';

-- ----------------------------
-- Records of system_config
-- ----------------------------
BEGIN;
INSERT INTO `system_config` (`id`, `key`, `value`, `created_at`, `updated_at`) VALUES (1, 'startup_password_hash', '$2a$10$DRvnY0BAVE/.lA7r1wj1TO.Enf.1EuolM/hW0BBzQKP0ucuCFGxHu', '2025-05-19 20:45:19', '2025-05-19 20:45:19');
COMMIT;

-- ----------------------------
-- Table structure for task_address
-- ----------------------------
DROP TABLE IF EXISTS `task_address`;
CREATE TABLE `task_address` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL COMMENT 'Associated task ID',
  `sender_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Sender address',
  `receiver_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Receiver address',
  `amount` decimal(32,18) NOT NULL COMMENT 'Amount for this specific address pair',
  `fee` decimal(32,18) NOT NULL COMMENT 'Fee for this transaction',
  `network` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Network identifier',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT 'Status of this address task (e.g., pending, success, failed)',
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Reason for failure, if any',
  `transaction_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Resulting transaction hash',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Individual address details within a batch task';

-- ----------------------------
-- Records of task_address
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for tasks
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `network_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'e.g., ETH, TRX',
  `token_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'e.g., USDT, ETH, TRX',
  `task_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'e.g., transfer, collect',
  `execute_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'e.g., manual, cron',
  `task_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Unique identifier for the task batch',
  `task_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'User-defined name for the task',
  `amount` decimal(32,18) NOT NULL COMMENT 'Amount per transaction (if not is_all_amount)',
  `is_all_amount` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Flag indicating if the full balance should be sent',
  `total_amount` decimal(32,18) DEFAULT NULL COMMENT 'Calculated total amount for the task',
  `total_fee` decimal(32,18) DEFAULT NULL COMMENT 'Calculated total fee for the task',
  `task_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Overall status of the task batch',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gas_limit` int DEFAULT NULL COMMENT 'Gas limit for transactions',
  `gas_price` decimal(18,9) DEFAULT NULL COMMENT 'Gas price (e.g., in Gwei)',
  `from_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Source address for single transfers/collections',
  `to_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Destination address for single transfers/collections',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Batch transaction tasks';

-- ----------------------------
-- Records of tasks
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for token_fee_supplements
-- ----------------------------
DROP TABLE IF EXISTS `token_fee_supplements`;
CREATE TABLE `token_fee_supplements` (
  `token_fee_supplement_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `withdraw_plan_id` bigint DEFAULT NULL COMMENT '计划订单id',
  `user_withdraw_id` bigint DEFAULT NULL COMMENT '计划订单id',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '需要补充费用的地址',
  `chain_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链类型 (例如: ERC20, TRC20)',
  `token_symbol` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代币符号 (例如: USDT)',
  `fee_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '费用类型 (例如: gas_fee, energy)',
  `required_amount` decimal(36,4) NOT NULL COMMENT '需要的费用数量 (原生代币单位)',
  `provided_amount` decimal(36,4) DEFAULT '0.0000' COMMENT '已补充的费用数量 (原生代币单位)',
  `energy_amount` decimal(36,4) DEFAULT '0.0000' COMMENT '补充能量数量 trc20 专用',
  `energy_fee` decimal(36,4) DEFAULT '0.0000' COMMENT '补充能量数量 trc20 专用',
  `status` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态 (pending, processing, success, failed, partial_success)',
  `transaction_hash` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '补充费用的交易哈希',
  `error_message` json DEFAULT NULL COMMENT '错误信息',
  `related_task_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联的归集任务ID',
  `retry_count` int unsigned NOT NULL DEFAULT '0' COMMENT '重试次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `energy_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_activating` int NOT NULL DEFAULT '0' COMMENT '是否激活 0 未激活 1 激活中 2 已激活',
  `activate_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '激活hash',
  `activate_amount` decimal(10,2) DEFAULT '0.00' COMMENT '激活消耗trx',
  PRIMARY KEY (`token_fee_supplement_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代币归集费用补充记录表';

-- ----------------------------
-- Records of token_fee_supplements
-- ----------------------------
BEGIN;
INSERT INTO `token_fee_supplements` (`token_fee_supplement_id`, `withdraw_plan_id`, `user_withdraw_id`, `address`, `chain_type`, `token_symbol`, `fee_type`, `required_amount`, `provided_amount`, `energy_amount`, `energy_fee`, `status`, `transaction_hash`, `error_message`, `related_task_id`, `retry_count`, `created_at`, `updated_at`, `energy_id`, `is_activating`, `activate_hash`, `activate_amount`) VALUES (1, 1, 1, 'TH4zbRtLDTJodZTjsnwnedLtWmprhiW3aT', 'TRON', 'TRX', 'energy', 0.0000, 0.0000, 0.0000, 0.0000, 'success', '', '[]', 'withdraw_plan_1', 0, '2025-05-19 22:28:54', '2025-05-19 22:29:55', '', 0, '', 0.00);
INSERT INTO `token_fee_supplements` (`token_fee_supplement_id`, `withdraw_plan_id`, `user_withdraw_id`, `address`, `chain_type`, `token_symbol`, `fee_type`, `required_amount`, `provided_amount`, `energy_amount`, `energy_fee`, `status`, `transaction_hash`, `error_message`, `related_task_id`, `retry_count`, `created_at`, `updated_at`, `energy_id`, `is_activating`, `activate_hash`, `activate_amount`) VALUES (2, 2, 0, 'TLJ1G8RpyBr6Ksdv4XC2vL3ApM3CHbRWHX', 'TRON', 'USDT', 'energy', 0.0000, 0.0000, 0.0000, 0.0000, 'pending', '', '[{\"message\": \"TRON activation amount is invalid or zero: 0, logPrefix: [WithdrawFeeHandler][SupplementID:2]\", \"timestamp\": \"2006-01-02 15:04:05\"}, {\"message\": \"TRON activation amount is invalid or zero: 0, logPrefix: [WithdrawFeeHandler][SupplementID:2]\", \"timestamp\": \"2006-01-02 15:04:05\"}]', 'withdraw_plan_2', 2, '2025-05-19 22:28:56', '2025-05-19 22:31:00', '', 0, '', 0.00);
INSERT INTO `token_fee_supplements` (`token_fee_supplement_id`, `withdraw_plan_id`, `user_withdraw_id`, `address`, `chain_type`, `token_symbol`, `fee_type`, `required_amount`, `provided_amount`, `energy_amount`, `energy_fee`, `status`, `transaction_hash`, `error_message`, `related_task_id`, `retry_count`, `created_at`, `updated_at`, `energy_id`, `is_activating`, `activate_hash`, `activate_amount`) VALUES (3, 3, 2, '******************************************', 'ETH', 'ETH', 'gas_fee', 0.0000, 0.0000, 0.0000, 0.0000, 'success', '', '[]', 'withdraw_plan_3', 0, '2025-05-19 22:28:58', '2025-05-19 22:29:56', '', 0, '', 0.00);
INSERT INTO `token_fee_supplements` (`token_fee_supplement_id`, `withdraw_plan_id`, `user_withdraw_id`, `address`, `chain_type`, `token_symbol`, `fee_type`, `required_amount`, `provided_amount`, `energy_amount`, `energy_fee`, `status`, `transaction_hash`, `error_message`, `related_task_id`, `retry_count`, `created_at`, `updated_at`, `energy_id`, `is_activating`, `activate_hash`, `activate_amount`) VALUES (4, 4, 3, '******************************************', 'ETH', 'USDT', 'gas_fee', 0.0000, 0.0000, 0.0000, 0.0000, 'success', '0xeabd2bb26047e5ebcc6b9dceebe85bd74629ec8347cd9c466d278f157855ea70', '[]', 'withdraw_plan_4', 0, '2025-05-19 22:28:59', '2025-05-19 22:31:02', '', 0, '', 0.00);
COMMIT;

-- ----------------------------
-- Table structure for transactions
-- ----------------------------
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transaction_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'e.g., pending, confirmed, failed',
  `chain` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Blockchain network (e.g., ETH, TRX)',
  `is_token` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is this a token transaction (TRUE) or native coin (FALSE)?',
  `contract_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Token contract address if is_token is TRUE',
  `token_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Token name (e.g., Tether)',
  `sender_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Sender address',
  `receiver_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Receiver address',
  `transaction_time` datetime NOT NULL COMMENT 'Timestamp of the transaction',
  `amount` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Transaction amount as string to handle large numbers',
  `transaction_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'e.g., transfer, contract call',
  `transaction_fee` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT 'Transaction fee as string',
  `status` int NOT NULL DEFAULT '0' COMMENT 'Internal status flag (e.g., 0=new, 1=processed)',
  `block_number` bigint NOT NULL COMMENT 'Block number containing the transaction',
  `block_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Hash of the block containing the transaction',
  `confirmations` int NOT NULL DEFAULT '0' COMMENT 'Number of block confirmations',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Additional notes about the transaction',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `transaction_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Unique hash of the transaction',
  `net_fee` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Network fee component (e.g., Tron)',
  `energy_fee` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Energy fee component (e.g., Tron)',
  `effective_gas_price` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Effective gas price paid (e.g., ETH EIP-1559)',
  `gas_used` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Amount of gas consumed',
  `cumulative_gas_used` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Cumulative gas used in the block',
  `token_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Token symbol (e.g., USDT)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `transaction_hash` (`transaction_hash`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Recorded blockchain transactions';

-- ----------------------------
-- Records of transactions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_recharges
-- ----------------------------
DROP TABLE IF EXISTS `user_recharges`;
CREATE TABLE `user_recharges` (
  `recharges_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token_id` int unsigned NOT NULL DEFAULT '0' COMMENT '币种ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '币种ID',
  `chan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token_contract_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代币合约地址 (for non-native assets)',
  `from_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '来源地址 (发送方地址)',
  `to_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标地址 (平台分配的充值地址)',
  `tx_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链上交易哈希/ID (Should be unique)',
  `error` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '失败原因',
  `amount` decimal(36,6) NOT NULL COMMENT '充值数量',
  `state` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)',
  `confirmations` int unsigned NOT NULL DEFAULT '1' COMMENT '状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间 (e.g., 链上发现时间)',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间 (状态变为Completed的时间)',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_withdraw` int NOT NULL DEFAULT '0' COMMENT '是否插入了提现表',
  `withdraw_id` bigint DEFAULT NULL,
  PRIMARY KEY (`recharges_id`) USING BTREE,
  UNIQUE KEY `uk_tx_hash` (`tx_hash`),
  KEY `idx_uid_coin_state` (`token_id`,`state`),
  KEY `idx_to_address_coin_network` (`to_address`,`token_id`,`chan`),
  KEY `idx_state_created` (`state`,`created_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户充值记录表';

-- ----------------------------
-- Records of user_recharges
-- ----------------------------
BEGIN;
INSERT INTO `user_recharges` (`recharges_id`, `token_id`, `name`, `chan`, `token_contract_address`, `from_address`, `to_address`, `tx_hash`, `error`, `amount`, `state`, `confirmations`, `created_at`, `completed_at`, `updated_at`, `is_withdraw`, `withdraw_id`) VALUES (1, 1, 'TRX', 'TRON', '', 'TA2yHE7XzEdxQiLdB9nVn1yV3TXC5SirFy', 'TH4zbRtLDTJodZTjsnwnedLtWmprhiW3aT', 'd605d51e7452db74d73b64dd2651c02602c3ff060f791e3cb3be13933e39427a', '', 54.000000, 2, 1916, '2025-05-19 20:54:24', '2025-05-19 22:28:37', '2025-05-19 22:28:37', 1, 1);
INSERT INTO `user_recharges` (`recharges_id`, `token_id`, `name`, `chan`, `token_contract_address`, `from_address`, `to_address`, `tx_hash`, `error`, `amount`, `state`, `confirmations`, `created_at`, `completed_at`, `updated_at`, `is_withdraw`, `withdraw_id`) VALUES (2, 2, 'USDT', 'TRON', '', 'TA2yHE7XzEdxQiLdB9nVn1yV3TXC5SirFy', 'TLJ1G8RpyBr6Ksdv4XC2vL3ApM3CHbRWHX', '4d9a8e65cfab38036fafedb612069f515286de0577c55d3836d94f7bf2c9be97', '', 54.000000, 2, 1899, '2025-05-19 20:54:25', '2025-05-19 22:28:40', '2025-05-19 22:28:40', 1, 2);
INSERT INTO `user_recharges` (`recharges_id`, `token_id`, `name`, `chan`, `token_contract_address`, `from_address`, `to_address`, `tx_hash`, `error`, `amount`, `state`, `confirmations`, `created_at`, `completed_at`, `updated_at`, `is_withdraw`, `withdraw_id`) VALUES (3, 3, 'ETH', 'ETH', '', '******************************************', '******************************************', '0x480b99a831b3fe8db9533d4e343d36f6a16349c916ffd8ed686cc9d837c8001b', '', 0.050000, 2, 353, '2025-05-19 21:07:10', '2025-05-19 22:28:42', '2025-05-19 22:28:42', 1, 3);
INSERT INTO `user_recharges` (`recharges_id`, `token_id`, `name`, `chan`, `token_contract_address`, `from_address`, `to_address`, `tx_hash`, `error`, `amount`, `state`, `confirmations`, `created_at`, `completed_at`, `updated_at`, `is_withdraw`, `withdraw_id`) VALUES (4, 4, 'USDT', 'ETH', '', '******************************************', '******************************************', '0xcc2ae3dcefe1ade330a6fa244deaf3d82736217b51e347a0488fb089ad2ff2b1', '', 15.545456, 2, 349, '2025-05-19 21:07:11', '2025-05-19 22:28:44', '2025-05-19 22:28:44', 1, 4);
COMMIT;

-- ----------------------------
-- Table structure for user_withdraws
-- ----------------------------
DROP TABLE IF EXISTS `user_withdraws`;
CREATE TABLE `user_withdraws` (
  `user_withdraws_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token_fee_supplement_id` bigint DEFAULT NULL,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '币种ID',
  `chan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链',
  `from_address` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提币目标地址',
  `to_address` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提币目标地址',
  `amount` decimal(36,6) NOT NULL COMMENT '申请提现金额',
  `handling_fee` decimal(36,6) DEFAULT '0.000000' COMMENT '提现手续费',
  `actual_amount` decimal(36,6) DEFAULT NULL COMMENT '实际到账金额',
  `state` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)',
  `tx_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链上交易哈希/ID',
  `error_message` json DEFAULT NULL COMMENT '失败或错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `checked_at` timestamp NULL DEFAULT NULL COMMENT '审核时间 (审核通过或拒绝的时间)',
  `processing_at` timestamp NULL DEFAULT NULL COMMENT '开始处理时间 (进入“处理中”状态的时间)',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间 (变为“已完成”或“失败”状态的时间)',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `retries` int DEFAULT '0',
  `nergy_state` int DEFAULT '0' COMMENT '0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了',
  `gasfee_state` int NOT NULL DEFAULT '0' COMMENT 'gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了',
  `eth_fee_mode` int NOT NULL DEFAULT '1' COMMENT 'eth 矿工费模式 1 自动 2手动',
  `eth_fee_max` decimal(10,4) NOT NULL DEFAULT '0.0010' COMMENT 'eth 最大矿工费',
  `eth_gas_price` bigint DEFAULT NULL,
  `eth_gas_limit` bigint DEFAULT NULL,
  `trx_fee_max` bigint NOT NULL DEFAULT '100',
  `recharges_id` int DEFAULT NULL COMMENT '充值订单id',
  `gasfee_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'gas费hash',
  `gasfee_amount` decimal(10,2) DEFAULT '0.00' COMMENT 'gas费金额',
  `energy_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`user_withdraws_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户提现记录表';

-- ----------------------------
-- Records of user_withdraws
-- ----------------------------
BEGIN;
INSERT INTO `user_withdraws` (`user_withdraws_id`, `token_fee_supplement_id`, `name`, `chan`, `from_address`, `to_address`, `amount`, `handling_fee`, `actual_amount`, `state`, `tx_hash`, `error_message`, `created_at`, `checked_at`, `processing_at`, `completed_at`, `updated_at`, `retries`, `nergy_state`, `gasfee_state`, `eth_fee_mode`, `eth_fee_max`, `eth_gas_price`, `eth_gas_limit`, `trx_fee_max`, `recharges_id`, `gasfee_hash`, `gasfee_amount`, `energy_msg`) VALUES (1, 1, 'TRX', 'TRON', 'TH4zbRtLDTJodZTjsnwnedLtWmprhiW3aT', 'TQ4KGoY2bybvHtnFuY3dHbgV2ckhbf8aPK', 0.000000, 0.000000, 0.000000, 2, '', '{}', '2025-05-19 22:29:55', NULL, NULL, NULL, '2025-05-19 22:29:55', 0, 2, 2, 1, 0.0000, 0, 0, 0, 0, '', 0.00, '');
INSERT INTO `user_withdraws` (`user_withdraws_id`, `token_fee_supplement_id`, `name`, `chan`, `from_address`, `to_address`, `amount`, `handling_fee`, `actual_amount`, `state`, `tx_hash`, `error_message`, `created_at`, `checked_at`, `processing_at`, `completed_at`, `updated_at`, `retries`, `nergy_state`, `gasfee_state`, `eth_fee_mode`, `eth_fee_max`, `eth_gas_price`, `eth_gas_limit`, `trx_fee_max`, `recharges_id`, `gasfee_hash`, `gasfee_amount`, `energy_msg`) VALUES (2, 3, 'ETH', 'ETH', '******************************************', '******************************************', 0.000000, 0.000000, 0.000000, 2, '', '{}', '2025-05-19 22:29:56', NULL, NULL, NULL, '2025-05-19 22:29:56', 0, 2, 2, 1, 0.0000, 0, 0, 0, 0, '', 0.00, '');
INSERT INTO `user_withdraws` (`user_withdraws_id`, `token_fee_supplement_id`, `name`, `chan`, `from_address`, `to_address`, `amount`, `handling_fee`, `actual_amount`, `state`, `tx_hash`, `error_message`, `created_at`, `checked_at`, `processing_at`, `completed_at`, `updated_at`, `retries`, `nergy_state`, `gasfee_state`, `eth_fee_mode`, `eth_fee_max`, `eth_gas_price`, `eth_gas_limit`, `trx_fee_max`, `recharges_id`, `gasfee_hash`, `gasfee_amount`, `energy_msg`) VALUES (3, 4, 'USDT', 'ETH', '******************************************', '******************************************', 0.000000, 0.000000, 0.000000, 2, '', '{}', '2025-05-19 22:31:02', NULL, NULL, NULL, '2025-05-19 22:31:02', 0, 2, 2, 1, 0.0000, 0, 0, 0, 0, '', 0.00, '');
COMMIT;

-- ----------------------------
-- Table structure for wallets
-- ----------------------------
DROP TABLE IF EXISTS `wallets`;
CREATE TABLE `wallets` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mnemonic` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'BIP39 Mnemonic phrase (should be encrypted)',
  `password` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Password for UI access (consider hashing)',
  `password_hash` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Hashed password for secure storage',
  `collect_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Default collection address (maybe deprecated by chain-specific ones)',
  `google_code` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Google Authenticator secret key (should be encrypted)',
  `miner_private_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Private key for mining/staking (handle with extreme care, encrypt)',
  `miner_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Address associated with the miner private key',
  `strategy_collect_switch` tinyint(1) DEFAULT '0' COMMENT 'Enable/disable strategic collection',
  `eth_collect_threshold` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Threshold for ETH collection',
  `trx_collect_threshold` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Threshold for TRX collection',
  `usdt_collect_threshold` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Threshold for USDT collection',
  `cron_collect_switch` tinyint(1) DEFAULT '0' COMMENT 'Enable/disable scheduled collection via cron',
  `cron_collect_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Cron schedule time (e.g., 0 2 * * *)',
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_unlock_at` datetime DEFAULT NULL COMMENT 'Timestamp of the last successful unlock',
  `unlock_error_count` int DEFAULT '0' COMMENT 'Count of consecutive unlock failures',
  `google_code_switch` tinyint(1) DEFAULT '0' COMMENT 'Enable/disable Google Authenticator requirement',
  `trx_collect_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Specific collection address for TRX chain',
  `eth_collect_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Specific collection address for ETH chain',
  `trx_fee_private_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Private key for paying TRX fees (handle with care, encrypt)',
  `eth_fee_private_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Private key for paying ETH fees (handle with care, encrypt)',
  `trx_fee_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Address holding funds for TRX fees',
  `trx_activate_amount` decimal(10,2) DEFAULT '5.00' COMMENT 'Address holding funds for TRX fees',
  `eth_fee_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Address holding funds for ETH fees',
  `eth_fee_mode` int NOT NULL DEFAULT '1' COMMENT 'eth 矿工费模式 1 自动 2手动',
  `eth_fee_amount` decimal(10,4) NOT NULL DEFAULT '1.0000' COMMENT 'eth固定矿工费发送金额',
  `trx_fee_amount` decimal(10,4) NOT NULL DEFAULT '1.0000' COMMENT 'trx 固定矿工费发送金额',
  `eth_fee_max` decimal(10,4) NOT NULL DEFAULT '0.0010' COMMENT 'eth 最大矿工费',
  `erc20_fee_max` decimal(10,4) NOT NULL DEFAULT '0.0010' COMMENT 'eth 最大矿工费',
  `eth_gas_price` bigint DEFAULT NULL,
  `eth_gas_limit` bigint DEFAULT NULL,
  `erc20_gas_price` bigint DEFAULT NULL,
  `erc20_gas_limit` bigint DEFAULT NULL,
  `trx_fee_mode` int NOT NULL DEFAULT '1' COMMENT 'eth 矿工费模式 1 自动 2手动',
  `trx_fee_max` bigint NOT NULL DEFAULT '100' COMMENT 'trx 交易最多支付手续费',
  `trx_keep_amount` bigint NOT NULL DEFAULT '0' COMMENT '归集保持账户内最低trx 余额 防止交易失败',
  `eth_keep_amount` bigint NOT NULL DEFAULT '0' COMMENT 'eth 归集的时候 预留金额',
  `trc20_min_required_energy` bigint NOT NULL DEFAULT '65000' COMMENT '转账usdt最低需要的能量 手动模式设置',
  `trc20_max_energy_fee` decimal(10,2) NOT NULL DEFAULT '5.00' COMMENT '最大允许消耗的 trx 能量费金额 此处用于购买trx 能量的费用设置',
  `trc20_min_required_bandwidth` bigint NOT NULL DEFAULT '350' COMMENT '最低带宽',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Wallet configuration and sensitive data';

-- ----------------------------
-- Records of wallets
-- ----------------------------
BEGIN;
INSERT INTO `wallets` (`id`, `mnemonic`, `password`, `password_hash`, `collect_address`, `google_code`, `miner_private_key`, `miner_address`, `strategy_collect_switch`, `eth_collect_threshold`, `trx_collect_threshold`, `usdt_collect_threshold`, `cron_collect_switch`, `cron_collect_time`, `create_at`, `update_at`, `last_unlock_at`, `unlock_error_count`, `google_code_switch`, `trx_collect_address`, `eth_collect_address`, `trx_fee_private_key`, `eth_fee_private_key`, `trx_fee_address`, `trx_activate_amount`, `eth_fee_address`, `eth_fee_mode`, `eth_fee_amount`, `trx_fee_amount`, `eth_fee_max`, `erc20_fee_max`, `eth_gas_price`, `eth_gas_limit`, `erc20_gas_price`, `erc20_gas_limit`, `trx_fee_mode`, `trx_fee_max`, `trx_keep_amount`, `eth_keep_amount`, `trc20_min_required_energy`, `trc20_max_energy_fee`, `trc20_min_required_bandwidth`) VALUES (1, '1e5e2a068606e0af79c296c9dd43940b100aacf450546dac5bd4996d3a62b06945f33d4ad34f3e69d27d08be20d8bd130f64a95479e0050f8e2e0a1685c10368bf513b1114d0f3633987216d680ee99627e7e297f531894bc64b46176a7d8cfffe0ab28b978c209f5e952435dacdeeb468f1ca389f807f74bc84eed8a98560ed7ac1972e1d3250b86c904b0a895e757b8a8abf0f55537ffef5d430086b566ec7', '$2a$10$nP7PBja2lAg8LU0rM7xfduzoQqRKYVVvQje7ADVIwSNp/xrv6ustW', '', '', '4c501c472068557aa0e437a8f45ce188c80b361102b77fdc2043162c18f30ee94163a83478e91a7bcafd57d596fba2a8', '', '', 1, '0.001', '1', '10', 0, '', '2025-05-19 20:47:44', '2025-05-19 22:27:45', '2025-05-19 22:27:45', 0, 0, 'TQ4KGoY2bybvHtnFuY3dHbgV2ckhbf8aPK', '******************************************', '4d9573b821904ff6c18eb74065e29b665c63c4703497dcfd4712353a7b27d728', 'ea8634de89346e72d9a83c09b3413b757dc9ff0c3912c7d9f3cc474b7a6a66bc', 'TJPbs3nfSyy47JQuN1471Fu3n7dN6CUyUM', 0.00, '0x868b56DcF7259Cf7f50F5267dDf8030Cd66720c2', 1, 0.0000, 0.0000, 0.0000, 0.0000, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0.00, 0);
COMMIT;

-- ----------------------------
-- Table structure for withdraw_plan
-- ----------------------------
DROP TABLE IF EXISTS `withdraw_plan`;
CREATE TABLE `withdraw_plan` (
  `withdraw_plan_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `chan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链',
  `address` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址',
  `state` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态: 1-待确认, 2-完成',
  `error_message` json DEFAULT NULL COMMENT '失败或错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`withdraw_plan_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户提现记录表';

-- ----------------------------
-- Records of withdraw_plan
-- ----------------------------
BEGIN;
INSERT INTO `withdraw_plan` (`withdraw_plan_id`, `chan`, `address`, `state`, `error_message`, `created_at`, `updated_at`) VALUES (1, 'TRON', 'TH4zbRtLDTJodZTjsnwnedLtWmprhiW3aT', 2, '{}', '2025-05-19 22:28:37', '2025-05-19 22:28:54');
INSERT INTO `withdraw_plan` (`withdraw_plan_id`, `chan`, `address`, `state`, `error_message`, `created_at`, `updated_at`) VALUES (2, 'TRON', 'TLJ1G8RpyBr6Ksdv4XC2vL3ApM3CHbRWHX', 2, '{}', '2025-05-19 22:28:40', '2025-05-19 22:28:56');
INSERT INTO `withdraw_plan` (`withdraw_plan_id`, `chan`, `address`, `state`, `error_message`, `created_at`, `updated_at`) VALUES (3, 'ETH', '******************************************', 2, '{}', '2025-05-19 22:28:42', '2025-05-19 22:28:58');
INSERT INTO `withdraw_plan` (`withdraw_plan_id`, `chan`, `address`, `state`, `error_message`, `created_at`, `updated_at`) VALUES (4, 'ETH', '******************************************', 2, '{}', '2025-05-19 22:28:44', '2025-05-19 22:28:59');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
