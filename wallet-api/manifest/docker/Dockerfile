# --- Build Stage ---
FROM golang:1.24-alpine AS builder

# Install build dependencies (optional, if needed by build process, e.g., CGO)
# RUN apk add --no-cache gcc musl-dev

WORKDIR /build

# Copy go module files and download dependencies first to leverage Docker cache
COPY go.mod go.sum ./
RUN go mod download

# Copy the entire project source code
COPY . .

# Build the application as a static binary
# Ensure CGO_ENABLED=0 for static linking if not using CGO, otherwise adjust build flags
# Using -ldflags "-s -w" to strip debug symbols and reduce binary size
RUN CGO_ENABLED=0 go build -ldflags="-s -w" -o /app/app main.go

# --- Runtime Stage ---
FROM alpine:latest

WORKDIR /app

# Copy the compiled binary from the build stage
COPY --from=builder /app/app /app/app

# Copy configuration files
# Ensure the config directory structure matches what the application expects
COPY manifest/config /app/manifest/config

# Copy resource files (if any are needed at runtime and not packed)
COPY resource /app/resource

# Expose the ports the application listens on
EXPOSE 8001
EXPOSE 50051

# Set the entrypoint to run the application binary
# The specific command (http, grpc, task) will be provided by docker-compose or Kubernetes
ENTRYPOINT ["/app/app"]

# Optional: Run as a non-root user for better security
# RUN addgroup -S appgroup && adduser -S appuser -G appgroup
# USER appuser
