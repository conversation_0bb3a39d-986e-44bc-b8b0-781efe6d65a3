
# # 主网
eth_rpc_url: &eth_rpc_url "http://62.182.82.91:8545"


# 主网 - 使用自建节点
tron_http_rpc_url: &tron_http_rpc_url "62.182.82.91:50051"



tron_grpc_rpc_url: &tron_grpc_rpc_url "62.182.82.91:50051"



tron_api_key_placeholder: &tron_api_key "171a479b-2062-4e80-a91a-e38f9da6d803" # Consider using env var reference if supported


usdt_erc20_contract: &usdt_erc20_contract "******************************************"
usdt_trc20_contract: &usdt_trc20_contract "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"

# usdt_erc20_contract: &usdt_erc20_contract "******************************************"
# usdt_trc20_contract: &usdt_trc20_contract "TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"
eth_confirmations: &eth_confirmations 5
tron_confirmations: &tron_confirmations 5
eth_decimals: &eth_decimals 18
ercusdt_decimals: &ercusdt_decimals 6
tron_decimals: &tron_decimals 6
trc20usdt_decimals:  &trc20usdt_decimals 6

# Blockchain client RPC URLs
blockchain:
  Decimals:
    ETH: *eth_decimals
    TRON: *tron_decimals
    ERC20USDT: *ercusdt_decimals
    TRC20USDT: *trc20usdt_decimals
  ETH:
    rpcUrl: *eth_rpc_url
    erc20ContractAddress: *usdt_erc20_contract
  TRON:
    rpcUrl: *tron_grpc_rpc_url
    trc20ContractAddress: *usdt_trc20_contract
    apiKey: *tron_api_key

app:
  token: "86e63351-8427-49d7-80ce-1ceeea8dd2d7"
# https://goframe.org/docs/web/server-config-file-template
server:
  address: ":9999"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  dumpRouterMap: false

# https://goframe.org/docs/core/glog-config
logger:
  level: "all"
  stdout: true

# https://goframe.org/docs/core/gdb-config-file
database:
  logger:
    path: "logs/database" # 日志文件路径。默认为空，表示关闭，仅输出到终端
    level: "all"
    stdout: true
  default: # 使用 MySQL
    link: "mysql:root:root@tcp(127.0.0.1:3306)/ttw?loc=Local&parseTime=true&charset=utf8mb4" # 确认或修改为您的 MySQL 连接信息
    debug: false # 开发环境建议开启

auth:
  #过期时间
  expireSeconds: 3600 # Access Token 过期时间 (1小时)
  refreshTokenExpireSeconds: 604800 # Refresh Token 过期时间 (7天)
  #刷新时间
  refresh: 600
  #密码尝试次数
  attempt: 5

jwt:
  secret: "YOUR_VERY_SECURE_AND_UNIQUE_JWT_SECRET_KEY_PLEASE_REPLACE"


redis:
  default:
    address: "127.0.0.1:6379"
    pass: "valkey_password"
    db: 5


# Deposit Check Task Configuration
depositCheck:
  enabled: true       # Enable or disable the deposit check task (default: true)
  spec: "# * * * * *" # Cron expression for scheduling the task (default: every minute)
  chains:
    ETH:
      # enabled: false       # Enable ETH checker
      enabled: false       # Enable ETH checker
      confirmations: *eth_confirmations    # Number of block confirmations required
      nativeSymbol: ETH  # Explicitly set native symbol
      rpc: *eth_rpc_url # RPC URL for the specified network (e.g., Sepolia)
      tokens:
        usdt:
          enabled: true   # Enable USDT checking on ETH
          confirmations: *eth_confirmations
          contractAddress: *usdt_erc20_contract # Example Sepolia USDT, replace if needed
          decimals: *ercusdt_decimals     # USDT decimals (usually 6 for ERC20)
    TRON: # Example for TRX (disabled by default)
      enabled: true       # Enable TRX checker
      # enabled: false       # Enable TRX checker
      nativeSymbol: TRX  # Explicitly set native symbol
      confirmations: *tron_confirmations   # Tron confirmations (adjust as needed)
      rpc: *tron_grpc_rpc_url # TRON gRPC API
      httpRpc: *tron_grpc_rpc_url # 也使用 gRPC (纯 gRPC 方案)
      apiKey: *tron_api_key # Required for TronGrid
      tokens:
        usdt:
          enabled: true
          contractAddress: *usdt_trc20_contract # Shasta USDT
          decimals: *trc20usdt_decimals # USDT decimals (usually 6 for TRC20)
          confirmations: *tron_confirmations # Added TRC20 USDT confirmations

depositConfirm:
  enabled: true
  spec: "# * * * * *"


# Fee Handler Task Configuration
feeHandler:
  # Whether the task is enabled
  enabled: true
  # Cron schedule expression (every minute)
  spec: "# * * * * *"
  # Verification schedule expression (every 2 minutes)
  verifySpec: "# * * * * *"
  # Gas fee sender configuration


# Withdraw Plan Processor Task Configuration
withdrawPlanProcessor:
  # Whether the task is enabled
  enabled: true
  # Cron schedule expression (every 5 minutes)
  spec: "*/10 * * * * *"

# Balance Collector Task Configuration
balanceCollector:
  # Whether the task is enabled
  enabled: true
  # Cron schedule expression (every hour)
  spec: "* 0 * * * *"

# Withdrawal Processor Task Configuration (Moved from TOML)
withdrawalProcessor:
  enabled: true
  spec: "*/10 * * * * *"  # Run every 5 seconds
  batchSize: 100
  retry:
    maxRetries: 3
    retryIntervalSec: 5

# Withdraw Fee Handler Task Configuration
withdrawFeeHandler:
  # Whether the task is enabled
  enabled: true
  # Cron schedule expression (every minute)
  spec: "*/10 * * * * *"  # Run every 30 seconds
  # Number of records to process in one batch
  batchSize: 100
  # Gas fee sender configuration (reuses the same configuration as feeHandler)
  gasFee:
    # ETH gas fee configuration
    ETH:
      # Amount of ETH to send for gas fees (in ETH)
      amount: "0.01"
    # TRON energy rental configuration
    TRON:
      # iTRX API key
      apiKey: "A44137431E0D402AB441DB0E06B5D257"
      # iTRX API secret
      apiSecret: "997EBBDC83C0161B8902F2CC1685DCA4F7F7B562118CC186599EC6834FFF4AAD"
      # iTRX API base URL
      apiBaseUrl: "https://itrx.io"
      # Rental period (1H, 1D, 3D, 30D)
      period: "1H"
      # Energy amount to rent
      energy: 65000
      # Rental model (order, collect)
      model: "order"


withdrawStep3Processing:
  enabled: true
  spec: "*/10 * * * * *"  # Run every 5 seconds
  batchSize: 1000  # Process 100 withdrawals in one batch

# Withdrawal Confirmation Task Configuration
withdrawStep4Processing:
  enabled: true
  spec: "*/10 * * * * *"  # Run every 10 seconds
  batchSize: 1000  # Process 100 withdrawals in one batch
  maxRetries: 30  # Maximum number of retries before marking as failed
  defaultConfirmations: 1  # Default number of confirmations required
  minWaitMinutes: 1  # Minimum minutes to wait before checking transaction (prevents checking too-recent transactions)
  # Chain-specific confirmation requirements
  chains:
    ETH:
      confirmations: 1  # Number of confirmations required for ETH
    TRON:
      confirmations: 1  # Number of confirmations required for TRON
security:
  pbkdf2Iterations: 310000