eth_rpc_url: "${WALLET_API_ETH_RPC_URL}"
tron_http_rpc_url: "${WALLET_API_TRON_HTTP_RPC_URL}"
tron_grpc_rpc_url: "${WALLET_API_TRON_GRPC_RPC_URL}"
tron_api_key_placeholder: "${WALLET_API_TRON_API_KEY_PLACEHOLDER}"
usdt_erc20_contract: "${WALLET_API_USDT_ERC20_CONTRACT}"
usdt_trc20_contract: "${WALLET_API_USDT_TRC20_CONTRACT}"
eth_confirmations: ${WALLET_API_ETH_CONFIRMATIONS}
tron_confirmations: ${WALLET_API_TRON_CONFIRMATIONS}
eth_decimals: ${WALLET_API_ETH_DECIMALS}
ercusdt_decimals: ${WALLET_API_ERCUSDT_DECIMALS}
tron_decimals: ${WALLET_API_TRON_DECIMALS}
trc20usdt_decimals: ${WALLET_API_TRC20USDT_DECIMALS}
blockchain:
  Decimals:
    ETH: ${WALLET_API_BLOCKCHAIN_DECIMALS_ETH}
    TRON: ${WALLET_API_BLOCKCHAIN_DECIMALS_TRON}
    ERC20USDT: ${WALLET_API_BLOCKCHAIN_DECIMALS_ERC20USDT}
    TRC20USDT: ${WALLET_API_BLOCKCHAIN_DECIMALS_TRC20USDT}
  ETH:
    rpcUrl: "${WALLET_API_BLOCKCHAIN_ETH_RPCURL}"
    erc20ContractAddress: "${WALLET_API_BLOCKCHAIN_ETH_ERC20CONTRACTADDRESS}"
  TRON:
    rpcUrl: "${WALLET_API_BLOCKCHAIN_TRON_RPCURL}"
    trc20ContractAddress: "${WALLET_API_BLOCKCHAIN_TRON_TRC20CONTRACTADDRESS}"
    apiKey: "${WALLET_API_BLOCKCHAIN_TRON_APIKEY}"
app:
  token: "${WALLET_API_APP_TOKEN}"
server:
  address: "${WALLET_API_SERVER_ADDRESS}"
  openapiPath: "${WALLET_API_SERVER_OPENAPIPATH}"
  swaggerPath: "${WALLET_API_SERVER_SWAGGERPATH}"
  dumpRouterMap: ${WALLET_API_SERVER_DUMPROUTERMAP}
logger:
  level: "${WALLET_API_LOGGER_LEVEL}"
  stdout: ${WALLET_API_LOGGER_STDOUT}
database:
  logger:
    path: "${WALLET_API_DATABASE_LOGGER_PATH}"
    level: "${WALLET_API_DATABASE_LOGGER_LEVEL}"
    stdout: ${WALLET_API_DATABASE_LOGGER_STDOUT}
  default:
    link: "${WALLET_API_DATABASE_DEFAULT_LINK}"
    debug: ${WALLET_API_DATABASE_DEFAULT_DEBUG}
auth:
  expireSeconds: ${WALLET_API_AUTH_EXPIRESECONDS}
  refreshTokenExpireSeconds: ${WALLET_API_AUTH_REFRESHTOKENEXPIRESECONDS}
  refresh: ${WALLET_API_AUTH_REFRESH}
  attempt: ${WALLET_API_AUTH_ATTEMPT}
jwt:
  secret: "${WALLET_API_JWT_SECRET}"
redis:
  default:
    address: "${WALLET_API_REDIS_DEFAULT_ADDRESS}"
    pass: "${WALLET_API_REDIS_DEFAULT_PASS}"
    db: ${WALLET_API_REDIS_DEFAULT_DB}
depositCheck:
  enabled: ${WALLET_API_DEPOSITCHECK_ENABLED}
  spec: "${WALLET_API_DEPOSITCHECK_SPEC}"
  chains:
    ETH:
      enabled: ${WALLET_API_DEPOSITCHECK_CHAINS_ETH_ENABLED}
      confirmations: ${WALLET_API_DEPOSITCHECK_CHAINS_ETH_CONFIRMATIONS}
      nativeSymbol: "${WALLET_API_DEPOSITCHECK_CHAINS_ETH_NATIVESYMBOL}"
      rpc: "${WALLET_API_DEPOSITCHECK_CHAINS_ETH_RPC}"
      tokens:
        usdt:
          enabled: ${WALLET_API_DEPOSITCHECK_CHAINS_ETH_TOKENS_USDT_ENABLED}
          confirmations: ${WALLET_API_DEPOSITCHECK_CHAINS_ETH_TOKENS_USDT_CONFIRMATIONS}
          contractAddress: "${WALLET_API_DEPOSITCHECK_CHAINS_ETH_TOKENS_USDT_CONTRACTADDRESS}"
          decimals: ${WALLET_API_DEPOSITCHECK_CHAINS_ETH_TOKENS_USDT_DECIMALS}
    TRON:
      enabled: ${WALLET_API_DEPOSITCHECK_CHAINS_TRON_ENABLED}
      nativeSymbol: "${WALLET_API_DEPOSITCHECK_CHAINS_TRON_NATIVESYMBOL}"
      confirmations: ${WALLET_API_DEPOSITCHECK_CHAINS_TRON_CONFIRMATIONS}
      rpc: "${WALLET_API_DEPOSITCHECK_CHAINS_TRON_RPC}"
      httpRpc: "${WALLET_API_DEPOSITCHECK_CHAINS_TRON_HTTPRPC}"
      apiKey: "${WALLET_API_DEPOSITCHECK_CHAINS_TRON_APIKEY}"
      tokens:
        usdt:
          enabled: ${WALLET_API_DEPOSITCHECK_CHAINS_TRON_TOKENS_USDT_ENABLED}
          contractAddress: "${WALLET_API_DEPOSITCHECK_CHAINS_TRON_TOKENS_USDT_CONTRACTADDRESS}"
          decimals: ${WALLET_API_DEPOSITCHECK_CHAINS_TRON_TOKENS_USDT_DECIMALS}
          confirmations: ${WALLET_API_DEPOSITCHECK_CHAINS_TRON_TOKENS_USDT_CONFIRMATIONS}
depositConfirm:
  enabled: ${WALLET_API_DEPOSITCONFIRM_ENABLED}
  spec: "${WALLET_API_DEPOSITCONFIRM_SPEC}"
feeHandler:
  enabled: ${WALLET_API_FEEHANDLER_ENABLED}
  spec: "${WALLET_API_FEEHANDLER_SPEC}"
  verifySpec: "${WALLET_API_FEEHANDLER_VERIFYSPEC}"
withdrawPlanProcessor:
  enabled: ${WALLET_API_WITHDRAWPLANPROCESSOR_ENABLED}
  spec: "${WALLET_API_WITHDRAWPLANPROCESSOR_SPEC}"
balanceCollector:
  enabled: ${WALLET_API_BALANCECOLLECTOR_ENABLED}
  spec: "${WALLET_API_BALANCECOLLECTOR_SPEC}"
withdrawalProcessor:
  enabled: ${WALLET_API_WITHDRAWALPROCESSOR_ENABLED}
  spec: "${WALLET_API_WITHDRAWALPROCESSOR_SPEC}"
  batchSize: ${WALLET_API_WITHDRAWALPROCESSOR_BATCHSIZE}
  retry:
    maxRetries: ${WALLET_API_WITHDRAWALPROCESSOR_RETRY_MAXRETRIES}
    retryIntervalSec: ${WALLET_API_WITHDRAWALPROCESSOR_RETRY_RETRYINTERVALSEC}
withdrawFeeHandler:
  enabled: ${WALLET_API_WITHDRAWFEEHANDLER_ENABLED}
  spec: "${WALLET_API_WITHDRAWFEEHANDLER_SPEC}"
  batchSize: ${WALLET_API_WITHDRAWFEEHANDLER_BATCHSIZE}
  gasFee:
    ETH:
      amount: "${WALLET_API_WITHDRAWFEEHANDLER_GASFEE_ETH_AMOUNT}"
    TRON:
      apiKey: "${WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_APIKEY}"
      apiSecret: "${WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_APISECRET}"
      apiBaseUrl: "${WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_APIBASEURL}"
      period: "${WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_PERIOD}"
      energy: ${WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_ENERGY}
      model: "${WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_MODEL}"
withdrawStep3Processing:
  enabled: ${WALLET_API_WITHDRAWSTEP3PROCESSING_ENABLED}
  spec: "${WALLET_API_WITHDRAWSTEP3PROCESSING_SPEC}"
  batchSize: ${WALLET_API_WITHDRAWSTEP3PROCESSING_BATCHSIZE}
withdrawStep4Processing:
  enabled: ${WALLET_API_WITHDRAWSTEP4PROCESSING_ENABLED}
  spec: "${WALLET_API_WITHDRAWSTEP4PROCESSING_SPEC}"
  batchSize: ${WALLET_API_WITHDRAWSTEP4PROCESSING_BATCHSIZE}
  maxRetries: ${WALLET_API_WITHDRAWSTEP4PROCESSING_MAXRETRIES}
  defaultConfirmations: ${WALLET_API_WITHDRAWSTEP4PROCESSING_DEFAULTCONFIRMATIONS}
  minWaitMinutes: ${WALLET_API_WITHDRAWSTEP4PROCESSING_MINWAITMINUTES}
  chains:
    ETH:
      confirmations: ${WALLET_API_WITHDRAWSTEP4PROCESSING_CHAINS_ETH_CONFIRMATIONS}
    TRON:
      confirmations: ${WALLET_API_WITHDRAWSTEP4PROCESSING_CHAINS_TRON_CONFIRMATIONS}
security:
  pbkdf2Iterations: ${WALLET_API_SECURITY_PBKDF2ITERATIONS}
