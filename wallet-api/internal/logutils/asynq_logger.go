package logutils

import (
	"fmt"
	"log"
	"os"
)

// asynqLoggerAdapter 包装标准库 log.Logger 以符合 asynq.Logger 接口
type asynqLoggerAdapter struct {
	logger *log.Logger
}

// NewAsynqLoggerAdapter 创建一个新的 logger 适配器实例
func NewAsynqLoggerAdapter(logger *log.Logger) *asynqLoggerAdapter {
	return &asynqLoggerAdapter{logger: logger}
}

// Debug 实现 asynq.Logger 接口的 Debug 方法
func (a *asynqLoggerAdapter) Debug(args ...interface{}) {
	a.logger.Output(2, fmt.Sprintln(append([]interface{}{"[DEBUG]"}, args...)...))
}

// Info 实现 asynq.Logger 接口的 Info 方法
func (a *asynqLoggerAdapter) Info(args ...interface{}) {
	a.logger.Output(2, fmt.Sprintln(append([]interface{}{"[INFO]"}, args...)...))
}

// Warn 实现 asynq.Logger 接口的 Warn 方法
func (a *asynqLoggerAdapter) Warn(args ...interface{}) {
	a.logger.Output(2, fmt.Sprintln(append([]interface{}{"[WARN]"}, args...)...))
}

// Error 实现 asynq.Logger 接口的 Error 方法
func (a *asynqLoggerAdapter) Error(args ...interface{}) {
	a.logger.Output(2, fmt.Sprintln(append([]interface{}{"[ERROR]"}, args...)...))
}

// Fatal 实现 asynq.Logger 接口的 Fatal 方法
func (a *asynqLoggerAdapter) Fatal(args ...interface{}) {
	a.logger.Output(2, fmt.Sprintln(append([]interface{}{"[FATAL]"}, args...)...))
	os.Exit(1) // Fatal 通常意味着程序需要终止
}
