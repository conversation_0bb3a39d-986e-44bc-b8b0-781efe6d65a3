package service

import (
	"sync"

	"golang.org/x/crypto/bcrypt"
)

var (
	cryptoService ICrypto
	cryptoOnce    sync.Once
)

// RegisterCrypto registers the crypto service implementation
func RegisterCrypto(service ICrypto) {
	cryptoOnce.Do(func() {
		cryptoService = service
	})
}

// Crypto returns the crypto service instance
func Crypto() ICrypto {
	if cryptoService == nil {
		// Auto-register a default implementation if none is provided
		RegisterCrypto(new(defaultCrypto))
	}
	return cryptoService
}

// ICrypto defines the interface for crypto operations
type ICrypto interface {
	// VerifyPassword verifies if the provided password matches the stored hash
	VerifyPassword(password string, hash string) bool
}

// defaultCrypto provides a default implementation of the ICrypto interface
type defaultCrypto struct{}

// VerifyPassword implements the ICrypto.VerifyPassword method
func (c *defaultCrypto) VerifyPassword(password string, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}
