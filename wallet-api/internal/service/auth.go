package service

import (
	"context"
	"sync"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/dao" // dao.RefreshTokens is needed by the constructor
	// "wallet-api/internal/logic/authservice" // REMOVE THIS TO BREAK CYCLE
)

var (
	localAuth       IAuth
	onceAuth        sync.Once
	authConstructor func(dao.RefreshTokensDAO) IAuth // Function to create IAuth instance
)

// RegisterAuth 由 logic 层调用以注册其 Auth 服务实现
func RegisterAuth(constructor func(dao.RefreshTokensDAO) IAuth) {
	if constructor == nil {
		panic("auth service constructor cannot be nil")
	}
	if authConstructor != nil {
		panic("auth service constructor already registered")
		// 或者选择忽略后续的注册，或者记录一个警告
	}
	authConstructor = constructor
}

// Auth 获取认证服务单例
func Auth() IAuth {
	onceAuth.Do(func() {
		if authConstructor == nil {
			panic("auth service constructor not registered. Ensure authlogic.init() is called.")
		}
		localAuth = authConstructor(dao.RefreshTokens) // 使用 dao 包的 RefreshTokens 单例
	})
	return localAuth
}

// IAuth 定义了认证相关的服务接口
type IAuth interface {
	// Login 处理用户登录，成功后返回访问令牌和刷新令牌
	Login(ctx context.Context, userID uint) (accessToken string, refreshToken string, err error)
	// RefreshToken 使用旧的刷新令牌获取新的访问令牌和刷新令牌
	RefreshToken(ctx context.Context, req *v1.RefreshTokenReq) (res *v1.RefreshTokenRes, err error)
	// Logout 处理用户登出，使指定的刷新令牌失效
	Logout(ctx context.Context, req *v1.LogoutReq) (res *v1.LogoutRes, err error)
	// ParseUserIDFromAccessToken 从访问令牌中解析出用户ID
	ParseUserIDFromAccessToken(ctx context.Context, accessTokenString string) (userID uint, err error)
	//GetInitStatus
	GetPublicWalletInitStatus(ctx context.Context, req *v1.GetPublicWalletInitStatusReq) (res *v1.GetPublicWalletInitStatusRes, err error)
}
