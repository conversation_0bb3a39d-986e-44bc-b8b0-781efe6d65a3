package service

import (
	"context"
	"sync"

	v1 "wallet-api/api/wallet/v1"
)

var (
	walletService IWalletService
	walletOnce    sync.Once
)

type VerifyGoogleCodeReq struct {
	GoogleCode string  `json:"google_code"`
	Secret     *string `json:"secret"` //非必填
}

// 注册钱包服务
func RegisterWallet(service IWalletService) {
	walletOnce.Do(func() {
		walletService = service
	})
}

// 获取钱包服务实例
func Wallet() IWalletService {
	return walletService
}

// 定义钱包服务接口
type IWalletService interface {
	// 生成钱包
	GenerateWallet(ctx context.Context, req *v1.GenerateWalletReq) (res *v1.GenerateWalletRes, err error)
	// 生成谷歌验证码
	GenerateGoogleCode(ctx context.Context, req *v1.GenerateGoogleCodeReq) (res *v1.GenerateGoogleCodeRes, err error)
	// 创建钱包
	CreateWallet(ctx context.Context, req *v1.CreateWalletReq) (res *v1.CreateWalletRes, err error)
	// 重置钱包
	ResetWallet(ctx context.Context, req *v1.ResetWalletReq) (res *v1.ResetWalletRes, err error)
	// 检查钱包状态
	CheckStatus(ctx context.Context, req *v1.CheckStatusReq) (res *v1.CheckStatusRes, err error)
	// 修改密码
	ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error)
	// 获取钱包信息
	GetWalletInfo(ctx context.Context, req *v1.GetWalletInfoReq) (res *v1.GetWalletInfoRes, err error)
	// 钱包认证
	Auth(ctx context.Context, req *v1.AuthReq) (res *v1.AuthRes, err error)
	//判断钱包是否存在
	IsWalletExist(ctx context.Context) (bool, error)
	//校验google 验证码是否正确
	VerifyGoogleCode(ctx context.Context, req *VerifyGoogleCodeReq) (bool, error)
	// 重新绑定谷歌验证码
	ReBindGoogleCode(ctx context.Context, req *v1.ReBindGoogleCodeReq) (res *v1.ReBindGoogleCodeRes, err error)
	// 钱包设置 (合并所有设置)
	WalletSetting(ctx context.Context, req *v1.WalletSettingReq) (res *v1.WalletSettingRes, err error)
	// 基本设置 (已废弃,请使用WalletSetting)
	BaseSetting(ctx context.Context, req *v1.BaseSettingReq) (res *v1.BaseSettingRes, err error)
	// // 定时归集设置 (已废弃,请使用WalletSetting)
	// CronCollectSetting(ctx context.Context, req *v1.CronCollectSettingReq) (res *v1.CronCollectSettingRes, err error)
	// // 策略归集设置 (已废弃,请使用WalletSetting)
	// StrategyCollectSetting(ctx context.Context, req *v1.StrategyCollectSettingReq) (res *v1.StrategyCollectSettingRes, err error)
	// 批量创建地址
	BatchCreateAddress(ctx context.Context, req *v1.BatchCreateAddressReq) (res *v1.BatchCreateAddressRes, err error)
	// 查询地址创建任务进度
	GetAddressTaskProgress(ctx context.Context, req *v1.GetAddressTaskProgressReq) (res *v1.GetAddressTaskProgressRes, err error)
	// 获取地址列表
	GetAddressList(ctx context.Context, req *v1.GetAddressListReq) (res *v1.GetAddressListRes, err error)
	// 获取地址页面统计信息
	GetAddressStatistic(ctx context.Context, req *v1.GetAddressStatisticReq) (res *v1.GetAddressStatisticRes, err error)
	// 导出地址
	ExportAddress(ctx context.Context, req *v1.ExportAddressReq) (res *v1.ExportAddressRes, err error)
	// 刷新地址余额
	RefreshAddress(ctx context.Context, req *v1.RefreshAddressReq) (res *v1.RefreshAddressRes, err error)
	// 绑定地址
	BindAddress(ctx context.Context, req *v1.BindAddressReq) (res *v1.BindAddressRes, err error)
	// 获取矿工费地址
	GetFeeAddress(ctx context.Context, req *v1.GetFeeAddressReq) (res *v1.GetFeeAddressRes, err error)
	// 获取归集地址
	GetCollectAddress(ctx context.Context, req *v1.GetCollectAddressReq) (res *v1.GetCollectAddressRes, err error)
	// 提交归集任务
	SubmitCollectTask(ctx context.Context, req *v1.SubmitCollectTaskReq) (res *v1.SubmitCollectTaskRes, err error)
	// 归集任务列表
	CollectTaskList(ctx context.Context, req *v1.CollectTaskListReq) (res *v1.CollectTaskListRes, err error)
	// 获取任务地址统计
	TaskAddressStatistic(ctx context.Context, req *v1.TaskAddressStatisticReq) (res *v1.TaskAddressStatisticRes, err error)
	// 获取任务地址记录
	TaskAddressRecord(ctx context.Context, req *v1.TaskAddressRecordReq) (res *v1.TaskAddressRecordRes, err error)
	// 获取任务记录统计
	CollectRecordStatistic(ctx context.Context, req *v1.CollectRecordStatisticReq) (res *v1.CollectRecordStatisticRes, err error)
	// 导出任务地址记录
	ExportTaskAddressRecord(ctx context.Context, req *v1.ExportTaskAddressRecordReq) (res *v1.ExportTaskAddressRecordRes, err error)
	// 导出任务记录
	ExportTaskRecord(ctx context.Context, req *v1.ExportTaskRecordReq) (res *v1.ExportTaskRecordRes, err error)
	// 导出交易记录
	ExportTransactionRecord(ctx context.Context, req *v1.ExportTransactionRecordReq) (res *v1.ExportTransactionRecordRes, err error)
	// 获取交易记录统计
	GetTransactionRecordStatistic(ctx context.Context, req *v1.GetTransactionRecordStatisticReq) (res *v1.GetTransactionRecordStatisticRes, err error)
	// 获取交易记录
	GetTransactionRecord(ctx context.Context, req *v1.GetTransactionRecordReq) (res *v1.GetTransactionRecordRes, err error)
	// 获取充值记录
	GetRechargeRecord(ctx context.Context, req *v1.GetRechargeRecordReq) (res *v1.GetRechargeRecordRes, err error)
	// 获取充值记录统计
	GetRechargeRecordStatistic(ctx context.Context, req *v1.GetRechargeRecordStatisticReq) (res *v1.GetRechargeRecordStatisticRes, err error)
	// 导出充值记录
	ExportRechargeRecord(ctx context.Context, req *v1.ExportRechargeRecordReq) (res *v1.ExportRechargeRecordRes, err error)
	// 获取提现记录
	GetWithdrawRecord(ctx context.Context, req *v1.GetWithdrawRecordReq) (res *v1.GetWithdrawRecordRes, err error)
	// 获取提现记录统计
	GetWithdrawRecordStatistic(ctx context.Context, req *v1.GetWithdrawRecordStatisticReq) (res *v1.GetWithdrawRecordStatisticRes, err error)
	// 导出提现记录
	ExportWithdrawRecord(ctx context.Context, req *v1.ExportWithdrawRecordReq) (res *v1.ExportWithdrawRecordRes, err error)
	// 创建提现记录
	CreateWithdraw(ctx context.Context, req *v1.CreateWithdrawReq) (res *v1.CreateWithdrawRes, err error)
	// 获取仪表盘统计
	GetDashboardStatistic(ctx context.Context, req *v1.DashboardStatisticReq) (res *v1.DashboardStatisticRes, err error)
	//获取gas
	GetGastracker(ctx context.Context, req *v1.GetGastrackerReq) (res *v1.GetGastrackerRes, err error)
	//获取代币和usdt余额
	GetTokenBalance(ctx context.Context, req *v1.GetTokenBalanceReq) (res *v1.GetTokenBalanceRes, err error)
	//获取tron hash 交易手续费详细信息
	// GetPublicInitializationStatus 检查钱包是否已经创建/初始化 (公共接口使用)
	GetPublicInitializationStatus(ctx context.Context) (isInitialized bool, err error)
	// UpdateWalletBalancesByAddress 更新特定地址在特定链上的原生代币和USDT代币的余额
	UpdateWalletBalancesByAddress(ctx context.Context, chainSymbol string, targetAddress string, tokenSymbol string) error
	// 添加地址到归集计划
	AddAddressToWithdrawPlan(ctx context.Context, req *v1.AddAddressToWithdrawPlanReq) (res *v1.AddAddressToWithdrawPlanRes, err error)
	// 获取归集计划列表
	GetWithdrawPlanList(ctx context.Context, req *v1.GetWithdrawPlanListReq) (res *v1.GetWithdrawPlanListRes, err error)
	// 获取代币费用补充记录列表
	GetTokenFeeSupplements(ctx context.Context, req *v1.GetTokenFeeSupplementsReq) (res *v1.GetTokenFeeSupplementsRes, err error)
	// 更新代币费用补充记录状态
	UpdateTokenFeeSupplementStatus(ctx context.Context, req *v1.UpdateTokenFeeSupplementStatusReq) (res *v1.UpdateTokenFeeSupplementStatusRes, err error)
	// 获取费用统计信息
	GetFeeStatistics(ctx context.Context, req *v1.GetFeeStatisticsReq) (res *v1.GetFeeStatisticsRes, err error)
}
