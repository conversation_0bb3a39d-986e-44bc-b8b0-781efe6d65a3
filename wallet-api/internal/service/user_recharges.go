package service

import (
	"context"
	"wallet-api/internal/model" // Assuming model types are still needed here

	"wallet-api/internal/ports"

	"github.com/gogf/gf/v2/database/gdb" // Import ports for the interface
)

// Assuming model types are still needed here

// IUserRecharges defines the interface for user recharge operations.
// Moved from internal/service/user_recharges.go to break import cycle
type IUserRecharges interface {
	// CreateUserRecharge creates a new user recharge record.
	CreateUserRecharge(ctx context.Context, in *model.UserRechargeCreateInput) (*model.UserRechargeCreateOutput, error)
	// CheckTxHashExists checks if a transaction hash already exists in the user_recharges table.
	CheckTxHashExists(ctx context.Context, txHash string) (bool, error)
	// ProcessNewDeposit handles a newly found deposit: checks existence and creates a record if new.
	// It now uses the DepositInfo structure from the ports package.
	// Returns the output containing the new ID (if created), a boolean indicating if a new record was created, and an error.
	ProcessNewDeposit(ctx context.Context, deposit ports.DepositInfo) (output *model.UserRechargeCreateOutput, created bool, err error)

	// UpdateRechargeStatus updates the status of a user recharge record.
	UpdateRechargeStatus(ctx context.Context, tx gdb.TX, rechargeID uint64, state uint) error
}

// Interface moved to internal/ports/user_recharges.go

var localUserRecharges IUserRecharges // Use the interface from ports

// RegisterUserRecharges registers the user recharges service implementation.
func RegisterUserRecharges(i IUserRecharges) { // Use the interface from ports
	localUserRecharges = i
}

// UserRecharges returns the registered user recharges service instance.
func UserRecharges() IUserRecharges { // Use the interface from ports
	if localUserRecharges == nil {
		panic("implement not found for interface service.IUserRecharges, forgot register?") // Update panic message
	}
	return localUserRecharges
}
