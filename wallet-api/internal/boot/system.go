package boot // 包声明修改为 boot

import (
	"context" // Import errors package for error checking
	// Import codes package for messages
	"wallet-api/internal/utility/crypto" // Import crypto package

	"github.com/gogf/gf/v2/frame/g"
)

// SystemInit 初始化系统资源和服务 (函数名改为导出)
func SystemInit(ctx context.Context) error {

	// 2. 初始化 ClientManager 单例
	crypto.GetInstance()
	g.Log().Infof(ctx, "ClientManager instance initialized.")

	return nil
}
