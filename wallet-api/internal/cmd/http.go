package cmd

import (
	"context"

	// 新增：用于隐藏密码输入

	// Ensuring v2 path for consistency
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"

	"wallet-api/internal/router" // 新增的核心模块
	"wallet-api/internal/utility"

	"github.com/gogf/gf/v2/os/gtime"
)

var (
	// Http command definition
	Http = gcmd.Command{
		Name:  "http",
		Usage: "http",
		Brief: "Start HTTP server only",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server() // Get the default server instance

			err = gtime.SetTimeZone("Asia/Shanghai")
			if err != nil {
				panic(err)
			}

			// Configure routes and middleware
			router.RoutesInit(s) // 初始化应用主路由

			// Set Swagger UI template
			s.SetSwaggerUITemplate(utility.SwaggerUITemplate())

			// Add hook for server running event (optional, for logging)
			g.Log().Info(ctx, "HTTP server starting...") // Log before starting

			// Start HTTP server (blocking)
			s.Run()
			return nil
		},
	}
)
