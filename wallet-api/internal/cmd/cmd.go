package cmd

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"

	"wallet-api/internal/boot" // 添加对新 boot 包的引用
)

// Main 主应用命令
var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "Start http or grpc server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			// 执行系统初始化
			if err := boot.SystemInit(ctx); err != nil {
				return err
			}

			// 默认执行 Http 命令
			g.Log().Info(ctx, "Starting HTTP server after successful system initialization...")
			return Http.Func(ctx, parser)
		},
	}
)
