package cmd

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	// "wallet-api/internal/service" // 移除旧 service 导入
	"wallet-api/internal/boot"
	"wallet-api/internal/task_registry" // 新增注册表导入

	// 显式导入 task 包
	"wallet-api/internal/security/credentialmanager"

	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/gcron"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
)

var (
	Task = gcmd.Command{
		Name:  "task",
		Usage: "task",
		Brief: "start task scheduler",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			//初始化钱包密钥
			if err := credentialmanager.InitStart(ctx); err != nil {
				glog.Errorf(ctx, "初始化钱包密钥失败: %v", err)
				return err
			}
			// 执行系统初始化
			if err := boot.SystemInit(ctx); err != nil {
				return err
			}

			err = gtime.SetTimeZone("Asia/Shanghai")
			if err != nil {
				panic(err)
			}

			glog.Info(ctx, "任务调度器启动...")

			cron := gcron.New()

			// 调用新的注册表应用函数
			err = task_registry.ApplyRegistrations(ctx, cron)
			if err != nil {
				// ApplyRegistrations 内部已经记录了详细错误，这里只记录总体失败信息
				glog.Errorf(ctx, "应用任务注册时发生错误: %v", err)
				// 根据需要决定是否在注册失败时退出，这里选择退出
				return err
			}

			// 检查是否有任务被成功调度
			if cron.Size() == 0 {
				glog.Warning(ctx, "没有任务被成功调度，调度器将不会启动。")
				// 可以选择直接退出或继续执行（取决于是否需要保持进程运行）
				return nil // 或者根据需要返回错误
			}

			glog.Infof(ctx, "共有 %d 个任务被成功调度。", cron.Size())
			cron.Start() // 启动调度器 (非阻塞)

			glog.Info(ctx, "Cron 调度器已启动，等待信号退出...")

			// 使用标准库 os/signal 来监听退出信号
			sigChan := make(chan os.Signal, 1)
			// 监听 SIGINT (Ctrl+C) 和 SIGTERM 信号
			signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

			// 阻塞等待信号
			sig := <-sigChan
			glog.Infof(ctx, "接收到信号 %s，准备退出...", sig.String())
			cron.Close() // 优雅关闭 cron
			glog.Info(ctx, "任务调度器已关闭.")

			return nil // 正常退出时返回 nil
		},
	}
)
