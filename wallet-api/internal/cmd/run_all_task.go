package cmd

import (
	"context"
	"fmt" // Added for placeholder logging

	"github.com/gogf/gf/v2/os/gcmd"
	// "github.com/gogf/gf/v2/os/glog" // Recommended for production logging

	// Import the task logic package
	// Import the task registry

	// 恢复导入 cmd 包以访问 Main
	_ "wallet-api/internal/logic/redis"
	_ "wallet-api/internal/task" // Import task logic to ensure registration

	// _ "wallet-api/internal/logic/transaction" // Import transaction logic to ensure registration
	_ "wallet-api/internal/logic/user_recharges"
	_ "wallet-api/internal/logic/wallet"
	_ "wallet-api/internal/packed"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2" // Add Redis adapter import
)

var (
	RunAllTask = gcmd.Command{
		Name:  "run-all-task",
		Brief: "Manually run all registered task",
		Func:  runAllTaskFunc,
	}
)

// runTaskFunc is the execution logic for the run-task command.
func runAllTaskFunc(ctx context.Context, parser *gcmd.Parser) (err error) {

	// 	#定时生成充值记录
	// go run main.go run-task --name BalanceCollectorTask

	// #充值检查 生成充值记录
	// go run main.go run-task --name DepositCheckTask

	// #充值确认 确认充值记录和 生成提现计划
	// go run main.go run-task --name DepositConfirmTask

	// #根据计划生成手续费记录
	// go run main.go run-task --name WithdrawPlanProcessorTask

	// #检查矿工费订单是否完成 完成后修改计划状态 以及 生成提现记录
	// go run main.go run-task --name WithdrawFeeHandlerTask

	// #根据提现记录逐个完成提币操作 优先提usdt
	// go run main.go run-task --name WithdrawStep3ProcessingTask

	// #检查提现交易是否确认 确认后修改状态为已完成
	// go run main.go run-task --name WithdrawStep4ConfirmTask

	registeredTasks := []string{
		"DepositCheckTask",
		"DepositConfirmTask",
		"WithdrawPlanProcessorTask",
		"WithdrawFeeHandlerTask",
		"WithdrawStep3ProcessingTask",
		"WithdrawStep4ConfirmTask",
	}

	// deposit_check:last_timestamp:trx

	// This loop iterates over registered task names and executes each one
	// by simulating a call to the "run-task" command.
	// It assumes that a command, typically named `RunTask` (a `gcmd.Command` struct),
	// is defined in the same `cmd` package (e.g., in `internal/cmd/run_task.go`)
	// and is accessible here. Please verify the variable name `RunTask` matches
	// the actual definition in `run_task.go`.
	for _, taskName := range registeredTasks {
		// Arguments for the individual "run-task" command for the current task.
		taskArgs := []string{"--name", taskName}

		// Parse these arguments to create a new parser for the specific sub-task.
		// `gcmd.ParseArgs` is the function to use when parsing a specific set of arguments.
		// The first argument is the slice of arguments to parse.
		// The second argument is a map defining supported options and whether they require a value.
		// For "run-task", the "--name" option requires a value.
		supportedRunTaskOptions := map[string]bool{
			"name": true, // The "name" option requires an argument.
		}
		taskParser, err := gcmd.ParseArgs(taskArgs, supportedRunTaskOptions)
		if err != nil {
			// TODO: Replace fmt.Printf with proper logging, e.g., using "github.com/gogf/gf/v2/os/glog".
			fmt.Printf("Error parsing arguments for task '%s': %v. Skipping task.\n", taskName, err)
			// Example with glog:
			// glog.Errorf(ctx, "Failed to parse arguments for task '%s': %v. Skipping task.", taskName, err)
			continue // Skip to the next task
		}

		// Execute the task by calling the `Func` associated with the `RunTask` command.
		// `RunTask` is assumed to be a `gcmd.Command` variable defined elsewhere in the `cmd` package
		// (e.g., in `internal/cmd/run_task.go`) that defines how to run a single task.
		// Its `Func` field points to the actual function that handles a single task execution.
		// Ensure `RunTask` is correctly defined and accessible.
		if RunTask.Func == nil {
			fmt.Printf("RunTask.Func is nil for task '%s'. Ensure RunTask command is correctly defined and its Func is set.\n", taskName)
			// glog.Errorf(ctx, "RunTask.Func is nil for task '%s'. Ensure RunTask command is correctly defined and its Func is set.", taskName)
			continue
		}
		err = RunTask.Func(ctx, taskParser)
		if err != nil {
			// TODO: Replace fmt.Printf with proper logging.
			fmt.Printf("Error running task '%s': %v.\n", taskName, err)
			// Example with glog:
			// glog.Errorf(ctx, "Failed to execute task '%s': %v.", taskName, err)
			// Depending on the desired behavior, you might want to 'continue' to the next task
			// or 'return err' to stop all subsequent tasks.
		}
	}

	return nil
}
