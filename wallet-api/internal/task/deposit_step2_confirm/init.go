package deposit_step2_confirm

import (
	"context"
	"wallet-api/internal/task_registry"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	//注册确认任务
	task_registry.Register(task_registry.TaskInfo{
		Name: "DepositConfirmTask",
		SpecFunc: func(ctx context.Context) (spec string, enabled bool, err error) {
			spec = g.Cfg().MustGet(ctx, "depositConfirm.spec", "# * * * * *").String() // 默认每分钟
			enabled = g.Cfg().MustGet(ctx, "depositConfirm.enabled", false).Bool()     // 默认禁用
			return spec, enabled, nil
		},
		Func:        ConfirmPendingDeposits, // 任务执行函数, 假设 ConfirmPendingDeposits 在本包中定义
		IsSingleton: true,
	})
}
