package deposit_step2_confirm

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// GetTokenAttributes 从配置中读取指定链和代币的属性
func GetTokenAttributes(ctx context.Context, chainSymbol string, tokenSymbol string) (contractAddress string, confirmations uint64, decimals int, err error) {
	logPrefix := fmt.Sprintf("[GetTokenAttributes][chain:%s, token:%s]", chainSymbol, tokenSymbol)

	chainSymbolUpper := strings.ToUpper(chainSymbol)
	tokenSymbolUpper := strings.ToUpper(tokenSymbol) // tokenSymbolUpper 用于原生代币判断和可能的锚点键(如USDT)

	// 检查链配置是否存在
	chainConfigPath := fmt.Sprintf("depositCheck.chains.%s", chainSymbolUpper)
	chainConfigVar, errChainCfg := g.Cfg().Get(ctx, chainConfigPath)
	if errChainCfg != nil {
		err = gerror.Wrapf(errChainCfg, "%s Error getting chain configuration from path %s", logPrefix, chainConfigPath)
		g.Log().Errorf(ctx, "%s", err.Error())
		return
	}
	if chainConfigVar.IsNil() {
		err = gerror.Newf("%s Chain configuration not found for chain: %s (path: %s)", logPrefix, chainSymbolUpper, chainConfigPath)
		g.Log().Errorf(ctx, "%s", err.Error())
		return
	}
	// g.Log().Debugf(ctx, "%s Found chain config at %s", logPrefix, chainConfigPath)

	nativeSymbolPath := fmt.Sprintf("%s.nativeSymbol", chainConfigPath)
	nativeSymbolVar, errNativeSymbol := g.Cfg().Get(ctx, nativeSymbolPath)
	if errNativeSymbol != nil {
		err = gerror.Wrapf(errNativeSymbol, "%s Error getting native symbol from path %s", logPrefix, nativeSymbolPath)
		g.Log().Errorf(ctx, "%s", err.Error())
		return
	}
	if nativeSymbolVar.IsNil() {
		err = gerror.Newf("%s Native symbol configuration not found for chain %s at path %s", logPrefix, chainSymbolUpper, nativeSymbolPath)
		g.Log().Errorf(ctx, "%s", err.Error())
		return
	}
	nativeSymbol := nativeSymbolVar.String()
	// g.Log().Debugf(ctx, "%s Native symbol for chain %s is %s", logPrefix, chainSymbolUpper, nativeSymbol)

	isNativeToken := strings.EqualFold(tokenSymbolUpper, nativeSymbol)

	if isNativeToken {
		// g.Log().Debugf(ctx, "%s Token %s is native token for chain %s", logPrefix, tokenSymbolUpper, chainSymbolUpper)
		contractAddress = "" // 原生代币没有合约地址

		confirmationsPath := fmt.Sprintf("%s.confirmations", chainConfigPath)
		confirmationsVar, errConfirmations := g.Cfg().Get(ctx, confirmationsPath)
		if errConfirmations != nil {
			err = gerror.Wrapf(errConfirmations, "%s Error getting confirmations for native token from path %s", logPrefix, confirmationsPath)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		if confirmationsVar.IsNil() {
			err = gerror.Newf("%s Confirmations configuration not found for native token at path %s", logPrefix, confirmationsPath)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		confirmations = confirmationsVar.Uint64()
		if confirmations == 0 {
			g.Log().Warningf(ctx, "%s Confirmations for native token %s on chain %s is 0 (from %s). Ensure this is intended.", logPrefix, tokenSymbolUpper, chainSymbolUpper, confirmationsPath)
		}
		// g.Log().Debugf(ctx, "%s Confirmations for native token %s on chain %s: %d (from %s)", logPrefix, tokenSymbolUpper, chainSymbolUpper, confirmations, confirmationsPath)

		var nativeDecimalsPathKey string
		switch chainSymbolUpper {
		case "ETH":
			nativeDecimalsPathKey = "eth_decimals" // 直接读取顶层定义的key
		case "TRON":
			nativeDecimalsPathKey = "tron_decimals" // 直接读取顶层定义的key
		default:
			err = gerror.Newf("%s Unsupported native chain for decimals: %s", logPrefix, chainSymbolUpper)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}

		decimalsVal, errDecimals := g.Cfg().Get(ctx, nativeDecimalsPathKey)
		if errDecimals != nil {
			err = gerror.Wrapf(errDecimals, "%s Error getting decimals for native token from path %s", logPrefix, nativeDecimalsPathKey)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		if decimalsVal.IsNil() {
			err = gerror.Newf("%s Decimals configuration not found for native token at path %s", logPrefix, nativeDecimalsPathKey)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		decimals = decimalsVal.Int()
		// g.Log().Debugf(ctx, "%s Decimals for native token %s on chain %s: %d (from %s)", logPrefix, tokenSymbolUpper, chainSymbolUpper, decimals, nativeDecimalsPathKey)
	} else {
		// g.Log().Debugf(ctx, "%s Token %s is a contract token on chain %s", logPrefix, tokenSymbol, chainSymbolUpper)
		tokenConfigKey := strings.ToLower(tokenSymbol) // 转换为小写
		tokenConfigPath := fmt.Sprintf("%s.tokens.%s", chainConfigPath, tokenConfigKey)

		tokenConfigVar, errTokenCfg := g.Cfg().Get(ctx, tokenConfigPath)
		if errTokenCfg != nil {
			err = gerror.Wrapf(errTokenCfg, "%s Error getting token configuration for token %s from path %s", logPrefix, tokenSymbol, tokenConfigPath)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		if tokenConfigVar.IsNil() {
			err = gerror.Newf("%s Token configuration not found for token: %s on chain: %s (path: %s)", logPrefix, tokenSymbol, chainSymbolUpper, tokenConfigPath)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		// g.Log().Debugf(ctx, "%s Found token config at %s", logPrefix, tokenConfigPath)

		// Contract Address
		contractAddressPath := fmt.Sprintf("%s.contractAddress", tokenConfigPath)
		contractAddressVar, errAddr := g.Cfg().Get(ctx, contractAddressPath)
		if errAddr != nil {
			err = gerror.Wrapf(errAddr, "%s Error getting contract address for token %s from path %s", logPrefix, tokenSymbol, contractAddressPath)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		if contractAddressVar.IsNil() {
			g.Log().Warningf(ctx, "%s Contract address configuration not found for token %s on chain %s at path %s. Assuming empty or not applicable.", logPrefix, tokenSymbol, chainSymbolUpper, contractAddressPath)
			contractAddress = ""
		} else {
			contractAddress = contractAddressVar.String()
			if contractAddress == "" {
				g.Log().Warningf(ctx, "%s Contract address for token %s on chain %s is configured as empty string at %s.", logPrefix, tokenSymbol, chainSymbolUpper, contractAddressPath)
			}
		}
		// g.Log().Debugf(ctx, "%s Contract address for token %s on chain %s: %s (from %s)", logPrefix, tokenSymbol, chainSymbolUpper, contractAddress, contractAddressPath)

		// Confirmations
		confirmationsPath := fmt.Sprintf("%s.confirmations", tokenConfigPath)
		confirmationsVar, errConf := g.Cfg().Get(ctx, confirmationsPath)
		if errConf != nil {
			err = gerror.Wrapf(errConf, "%s Error getting confirmations for token %s from path %s", logPrefix, tokenSymbol, confirmationsPath)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		if confirmationsVar.IsNil() {
			err = gerror.Newf("%s Confirmations configuration not found for token %s on chain %s at path %s", logPrefix, tokenSymbol, chainSymbolUpper, confirmationsPath)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}
		confirmations = confirmationsVar.Uint64()
		if confirmations == 0 {
			g.Log().Warningf(ctx, "%s Confirmations for token %s on chain %s is 0 (from %s). Ensure this is intended.", logPrefix, tokenSymbol, chainSymbolUpper, confirmationsPath)
		}
		// g.Log().Debugf(ctx, "%s Confirmations for token %s on chain %s: %d (from %s)", logPrefix, tokenSymbol, chainSymbolUpper, confirmations, confirmationsPath)

		// Decimals
		decimalsPath := fmt.Sprintf("%s.decimals", tokenConfigPath)
		decimalsVal, errDec := g.Cfg().Get(ctx, decimalsPath)

		if errDec != nil {
			err = gerror.Wrapf(errDec, "%s Error getting decimals for token %s from path %s", logPrefix, tokenSymbol, decimalsPath)
			g.Log().Errorf(ctx, "%s", err.Error())
			return
		}

		if decimalsVal.IsNil() {
			var anchorPath string
			// 使用 tokenSymbolUpper (原始大写) 来匹配配置文件中可能的锚点键 (如 USDT)
			if chainSymbolUpper == "ETH" && tokenSymbolUpper == "USDT" {
				anchorPath = "depositCheck.decimals.ercusdt_decimals"
			} else if chainSymbolUpper == "TRON" && tokenSymbolUpper == "USDT" {
				anchorPath = "depositCheck.decimals.trc20usdt_decimals"
			}

			if anchorPath != "" {
				g.Log().Debugf(ctx, "%s Decimals not found at %s for token %s, trying anchor path %s", logPrefix, decimalsPath, tokenSymbol, anchorPath)
				anchorDecimalsVal, errAnchorDec := g.Cfg().Get(ctx, anchorPath)
				if errAnchorDec != nil {
					err = gerror.Wrapf(errAnchorDec, "%s Error getting decimals for token %s from anchor path %s", logPrefix, tokenSymbol, anchorPath)
					g.Log().Errorf(ctx, "%s", err.Error())
					return
				}
				decimalsVal = anchorDecimalsVal // Use value from anchor, whether nil or not, if no error
			}

			if decimalsVal.IsNil() { // Re-check after potentially trying anchor
				finalAnchorPathMsg := ""
				if anchorPath != "" {
					finalAnchorPathMsg = fmt.Sprintf(" (and anchor path '%s' if tried)", anchorPath)
				}
				err = gerror.Newf("%s Decimals configuration not found for token %s on chain %s at path %s%s", logPrefix, tokenSymbol, chainSymbolUpper, decimalsPath, finalAnchorPathMsg)
				g.Log().Errorf(ctx, "%s", err.Error())
				return
			}
		}
		decimals = decimalsVal.Int()
		// g.Log().Debugf(ctx, "%s Decimals for token %s on chain %s: %d", logPrefix, tokenSymbol, chainSymbolUpper, decimals)
	}

	if err == nil { // 只有在前面没有发生错误时才检查这些警告条件
		if confirmations == 0 {
			g.Log().Warningf(ctx, "%s Final confirmations for %s on %s is 0. Ensure this is intended.", logPrefix, tokenSymbol, chainSymbolUpper)
		}
		if decimals == 0 && !isNativeToken && tokenSymbolUpper != "XPX" { // XPX is a special case?
			g.Log().Warningf(ctx, "%s Final decimals for token %s on %s is 0. Ensure this is intended.", logPrefix, tokenSymbol, chainSymbolUpper)
		}
	}
	return
}
