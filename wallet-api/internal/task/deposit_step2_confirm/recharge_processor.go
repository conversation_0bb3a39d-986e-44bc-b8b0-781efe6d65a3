package deposit_step2_confirm

import (
	"context"
	"fmt"
	"sync"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"

	v1 "wallet-api/api/wallet/v1" // For GetCollectAddressReq
	"wallet-api/internal/consts"
	"wallet-api/internal/dao"
	"wallet-api/internal/service"
)

// processSingleRecharge 处理单个充值记录的确认逻辑
func processSingleRecharge(ctx context.Context, recharge *entity.UserRecharges, parentLogPrefix string) error {
	logPrefix := fmt.Sprintf("%s[RechargeID:%d, TxHash:%s]", parentLogPrefix, recharge.RechargesId, recharge.TxHash)
	glog.Infof(ctx, "%s 开始处理...", logPrefix)

	// a. 获取 Token 属性 (合约地址, 确认数, 精度) 从配置
	// recharge.Chan 对应 chainSymbol (e.g., "ETH", "TRON")
	// recharge.Name 对应 tokenSymbol (e.g., "USDT", "ETH", "TRX")
	contractAddress, requiredConfirmations, decimals, err := GetTokenAttributes(ctx, recharge.Chan, recharge.Name)
	if err != nil {
		glog.Errorf(ctx, "%s 获取 Token 属性失败 (Chain: %s, Token: %s): %v", logPrefix, recharge.Chan, recharge.Name, err)
		return err // 返回错误，外层会统计失败次数
	}
	glog.Infof(ctx, "%s Token: %s (%s), 合约: '%s', 需要确认数: %d, 精度: %d", logPrefix, recharge.Name, recharge.Chan, contractAddress, requiredConfirmations, decimals)

	// b. 获取链上确认数
	// 调用 confirmation_fetcher.go 中的函数
	currentConfirmations, err := GetTransactionConfirmations(ctx, recharge.Chan, recharge.TxHash)
	if err != nil {
		glog.Errorf(ctx, "%s 获取链上确认数失败: %v", logPrefix, err)
		// 根据策略，获取失败暂时跳过，不视为处理失败
		return nil // 返回 nil，让外层不计入失败
	}
	glog.Infof(ctx, "%s 当前链上确认数: %d", logPrefix, currentConfirmations)

	// c. 比较确认数
	if currentConfirmations < requiredConfirmations { // currentConfirmations is already uint64
		glog.Infof(ctx, "%s 确认数不足 (%d < %d)，跳过", logPrefix, currentConfirmations, requiredConfirmations)
		// 可选：更新数据库中的 confirmations 字段
		// _, updateErr := dao.UserRecharges.Ctx(ctx).Data(g.Map{dao.UserRecharges.Columns().Confirmations: currentConfirmations}).
		// 	Where(dao.UserRecharges.Columns().UserRechargesId, recharge.UserRechargesId).Update()
		// if updateErr != nil {
		// 	glog.Warningf(ctx, "%s 更新数据库确认数字段失败: %v", logPrefix, updateErr)
		// }
		return nil // 确认数不足，正常跳过，不计入失败
	}

	glog.Infof(ctx, "%s 确认数已达标 (%d >= %d)，准备执行入账操作...", logPrefix, currentConfirmations, requiredConfirmations)

	//需要判断相同的地址是否在提现相关表中存在待操作的订单，如果存在就跳过
	glog.Infof(ctx, "%s 并行检查地址 %s 是否有待处理的提现记录...", logPrefix, recharge.ToAddress)

	// 定义检查结果结构
	type checkResult struct {
		TableName string
		Count     int
		Err       error
	}

	// 使用并行查询优化性能
	var wg sync.WaitGroup
	results := make(chan checkResult, 3) // 缓冲通道，避免阻塞

	// 检查withdraw_plan表
	wg.Add(1)
	go func() {
		defer wg.Done()
		count, err := dao.WithdrawPlan.Ctx(ctx).
			Where("address", recharge.ToAddress).
			Where("state", 1).
			Count()
		results <- checkResult{"提现计划记录", count, err}
	}()

	// 检查token_fee_supplements表
	wg.Add(1)
	go func() {
		defer wg.Done()
		count, err := dao.TokenFeeSupplements.Ctx(ctx).
			Where("address", recharge.ToAddress).
			WhereIn("status", []string{"pending", "processing"}).
			Count()
		results <- checkResult{"手续费补充记录", count, err}
	}()

	// 检查user_withdraws表
	wg.Add(1)
	go func() {
		defer wg.Done()
		count, err := dao.UserWithdraws.Ctx(ctx).
			Where("from_address", recharge.ToAddress).
			WhereIn("state", []int{1}).
			Count()
		results <- checkResult{"用户提现记录", count, err}
	}()

	// 关闭结果通道
	go func() {
		wg.Wait()
		close(results)
	}()

	// 处理结果
	for result := range results {
		if result.Err != nil {
			glog.Errorf(ctx, "%s 查询%s失败: %v", logPrefix, result.TableName, result.Err)
			return result.Err
		}

		if result.Count > 0 {
			// 2. 更新 user_recharges 状态
			_, err = dao.UserRecharges.Ctx(ctx).Data(g.Map{
				dao.UserRecharges.Columns().State:       2, // 2 表示已完成/已入账
				dao.UserRecharges.Columns().CompletedAt: gtime.Now(),
				//确认次数
				dao.UserRecharges.Columns().Confirmations: currentConfirmations,
				dao.UserRecharges.Columns().IsWithdraw:    1,
				dao.UserRecharges.Columns().WithdrawId:    0,
			}).Where(dao.UserRecharges.Columns().RechargesId, recharge.RechargesId).Update()
			if err != nil {
				glog.Errorf(ctx, "%s 更新 user_recharges 状态失败: %v", logPrefix, err)
				return err
			}
			glog.Infof(ctx, "%s 存在待处理的%s，跳过处理", logPrefix, result.TableName)
			return nil
		}
	}

	glog.Infof(ctx, "%s 地址 %s 没有待处理的提现记录，继续处理", logPrefix, recharge.ToAddress)

	// d. 执行入账操作 (数据库事务)
	// 注意：根据任务指示，此子任务不包括实现新的 WalletService 方法或更新余额、插入提现记录的完整逻辑。
	// 资金流水记录操作也已移除。
	// 最小充值金额的判断逻辑暂时保留，但其依赖的 tokenInfo.MinDepositAmount 需要调整或后续处理。
	// 目前假设 recharge.Amount 和 tokenInfo.MinDepositAmount (如果后续从配置获取) 是可比较的。
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 移除旧的 service.Token().GetTokenByID(ctx, recharge.TokenId)

		// 最小充值金额校验已移除

		// 1. 调用余额更新服务
		err := service.Wallet().UpdateWalletBalancesByAddress(ctx, recharge.Chan, recharge.ToAddress, recharge.Name)
		if err != nil {
			glog.Errorf(ctx, "%s 调用 UpdateWalletBalancesByAddress 失败: %v", logPrefix, err)
			return err
		}
		glog.Infof(ctx, "%s 余额更新成功 (Chan: %s, Address: %s, Token: %s)", logPrefix, recharge.Chan, recharge.ToAddress, recharge.Name)

		// 更新 address 表 status
		_, err = dao.Address.Ctx(ctx).TX(tx).Data(g.Map{dao.Address.Columns().Status: 2}).Where(dao.Address.Columns().Address, recharge.ToAddress).Where(dao.Address.Columns().Type, recharge.Chan).Update()
		if err != nil {
			glog.Errorf(ctx, "%s 更新 address 表状态失败 (Addr: %s, Type: %s): %v", logPrefix, recharge.ToAddress, recharge.Chan, err)
			return err
		}
		glog.Infof(ctx, "%s address 表状态更新成功 (Addr: %s, Type: %s, Status: %d)", logPrefix, recharge.ToAddress, recharge.Chan, 2)

		var walletInfoRes *v1.GetWalletInfoRes

		// 4. 获取钱包信息
		walletInfoRes, err = service.Wallet().GetWalletInfo(ctx, &v1.GetWalletInfoReq{})
		if err != nil {
			glog.Errorf(ctx, "%s 获取钱包信息失败: %v", logPrefix, err)
			return err
		}

		var targetCollectAddress string
		switch recharge.Chan {
		case consts.NetworkTypeETH:
			targetCollectAddress = walletInfoRes.WalletInfo.EthCollectAddress
		case consts.NetworkTypeTRX:
			targetCollectAddress = walletInfoRes.WalletInfo.TrxCollectAddress
		default:
			err := fmt.Errorf("不支持的链类型: %s", recharge.Chan)
			glog.Errorf(ctx, "%s %v", logPrefix, err)
			return err
		}

		if targetCollectAddress == "" {
			err := fmt.Errorf("%s 链的归集地址未配置", recharge.Chan)
			glog.Errorf(ctx, "%s %v", logPrefix, err)
			return err
		}
		glog.Infof(ctx, "%s 获取到 %s 归集地址: %s", logPrefix, recharge.Chan, targetCollectAddress)

		var id int64

		//判断计划中是否有State=1 的记录
		var value gdb.Value
		value, err = dao.WithdrawPlan.Ctx(ctx).
			Where("chan", recharge.Chan).
			Where("address", recharge.ToAddress).
			Where("state", 1). // 只查询 state 为 1 (待处理) 的计划进行复用
			Value("withdraw_plan_id")
		if err != nil {
			glog.Errorf(ctx, "%s 查询提现计划失败: %v", logPrefix, err)
			return err
		}
		id = value.Int64()
		if id <= 0 {
			//v2 创建提现计划
			id, err = dao.WithdrawPlan.Ctx(ctx).TX(tx).Data(&entity.WithdrawPlan{
				Chan:         recharge.Chan,
				Address:      recharge.ToAddress,
				State:        1,
				ErrorMessage: "[]", // 默认空的 JSON 对象字符串
			}).InsertAndGetId()

			if err != nil {
				glog.Errorf(ctx, "%s 创建提现计划失败: %v", logPrefix, err)
				return err
			}
		}

		// 2. 更新 user_recharges 状态
		_, err = dao.UserRecharges.Ctx(ctx).TX(tx).Data(g.Map{
			dao.UserRecharges.Columns().State:       2, // 2 表示已完成/已入账
			dao.UserRecharges.Columns().CompletedAt: gtime.Now(),
			//确认次数
			dao.UserRecharges.Columns().Confirmations: currentConfirmations,
			dao.UserRecharges.Columns().IsWithdraw:    1,
			dao.UserRecharges.Columns().WithdrawId:    id,
		}).Where(dao.UserRecharges.Columns().RechargesId, recharge.RechargesId).Update()
		if err != nil {
			glog.Errorf(ctx, "%s 更新 user_recharges 状态失败: %v", logPrefix, err)
			return err
		}
		// glog.Infof(ctx, "%s user_recharges 记录状态更新为已完成", logPrefix)

		// 所有步骤成功
		return nil
	})

	// 处理事务结果
	if err != nil {
		// 事务失败会自动回滚
		glog.Errorf(ctx, "%s 处理充值记录事务失败: %v", logPrefix, err)
		return err // 将事务错误向上传递
	}

	glog.Infof(ctx, "%s 充值记录处理成功！", logPrefix)
	return nil
}
