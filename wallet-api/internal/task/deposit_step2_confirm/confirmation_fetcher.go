package deposit_step2_confirm

import (
	"context"
	"fmt"
	"strings"

	cryptoUtil "wallet-api/internal/utility/crypto"

	ethereum "github.com/ethereum/go-ethereum" // Alias to avoid conflict
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types" // For receipt status
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// GetTransactionConfirmations 获取指定交易的链上确认数
func GetTransactionConfirmations(ctx context.Context, chain string, txHash string) (uint64, error) {
	logPrefix := fmt.Sprintf("[GetTransactionConfirmations][Chain:%s, TxHash:%s]", chain, txHash)
	// TODO: Consider centralizing chain client management instead of dialing each time.

	// 修正链名称以匹配配置文件 (TRX -> TRON)
	configChainName := strings.ToUpper(chain)
	if configChainName == "TRX" {
		configChainName = "TRON"
		glog.Debugf(ctx, "%s Mapping chain name from TRX to TRON for config lookup", logPrefix)
	}

	// 使用读取到的 networkEnv 和修正后的链名称构造正确的配置键 (添加 depositCheck. 前缀)
	rpcConfigKey := fmt.Sprintf("depositCheck.chains.%s.rpc", configChainName)
	rpcUrlVar, err := g.Cfg().Get(ctx, rpcConfigKey)
	if err != nil {
		// 如果是因为键不存在而获取失败，错误信息会包含 "cannot find value with specified key"
		// 否则可能是其他配置读取问题
		return 0, gerror.Wrapf(err, "%s failed to get RPC URL config ('%s')", logPrefix, rpcConfigKey)
	}
	rpcUrl := rpcUrlVar.String()
	if rpcUrl == "" {
		return 0, gerror.Newf("%s RPC URL is empty for config key '%s'", logPrefix, rpcConfigKey)
	}

	switch strings.ToUpper(chain) {
	case "ETH": // 处理以太坊及兼容链 (BSC, Polygon etc. if they use same logic)
		client, err := ethclient.DialContext(ctx, rpcUrl)
		if err != nil {
			return 0, gerror.Wrapf(err, "%s failed to dial ETH RPC '%s'", logPrefix, rpcUrl)
		}
		defer client.Close()

		txHashCommon := common.HexToHash(txHash)
		receipt, err := client.TransactionReceipt(ctx, txHashCommon)
		if err != nil {
			if err == ethereum.NotFound {
				glog.Infof(ctx, "%s transaction not found or not yet mined", logPrefix)
				return 0, nil // 交易未找到，视为 0 确认
			}
			return 0, gerror.Wrapf(err, "%s failed to get transaction receipt", logPrefix)
		}

		// 检查回执状态，如果交易失败，也视为 0 确认或错误
		if receipt.Status == types.ReceiptStatusFailed {
			glog.Warningf(ctx, "%s transaction failed on chain (Status: 0)", logPrefix)
			// 根据业务逻辑决定是返回 0 确认还是错误
			return 0, gerror.Newf("%s transaction failed on chain", logPrefix) // 返回错误可能更合适
		}

		if receipt.BlockNumber == nil {
			// 理论上已确认的交易应该有 BlockNumber，如果没有则可能是有问题
			glog.Warningf(ctx, "%s transaction receipt found but BlockNumber is nil", logPrefix)
			return 0, gerror.Newf("%s transaction receipt missing block number", logPrefix)
		}

		latestBlock, err := client.BlockNumber(ctx)
		if err != nil {
			return 0, gerror.Wrapf(err, "%s failed to get latest block number", logPrefix)
		}

		txBlockNumber := receipt.BlockNumber.Uint64()
		if latestBlock < txBlockNumber {
			// 最新区块号小于交易所在区块号，可能节点数据不同步
			glog.Warningf(ctx, "%s latest block number (%d) is less than transaction block number (%d). Node might be syncing.", logPrefix, latestBlock, txBlockNumber)
			return 0, nil // 暂时返回 0 确认
		}

		confirmations := latestBlock - txBlockNumber + 1
		glog.Debugf(ctx, "%s Latest Block: %d, Tx Block: %d, Confirmations: %d", logPrefix, latestBlock, txBlockNumber, confirmations)
		return confirmations, nil

	case "TRON", "TRX": // 波场链
		// 使用 gRPC 客户端获取交易确认数
		clientManager := cryptoUtil.GetInstance()

		// 获取 TRON gRPC 客户端
		tronClient, err := clientManager.GetDefaultTronClient(ctx)
		if err != nil {
			// 尝试故障转移
			tronClient, err = clientManager.GetTronClientWithFailover(ctx)
			if err != nil {
				return 0, gerror.Wrapf(err, "%s failed to get TRON gRPC client", logPrefix)
			}
		}

		// 获取交易信息
		txInfo, err := tronClient.GetTransactionInfoByID(txHash)
		if err != nil {
			// 尝试故障转移后重试
			tronClient, failoverErr := clientManager.HandleTronError(ctx, err)
			if failoverErr != nil {
				return 0, gerror.Wrapf(err, "%s failed to get transaction info from TRON gRPC", logPrefix)
			}

			// 重试获取交易信息
			txInfo, err = tronClient.GetTransactionInfoByID(txHash)
			if err != nil {
				return 0, gerror.Wrapf(err, "%s failed to get transaction info from TRON gRPC after failover", logPrefix)
			}
		}

		if txInfo == nil {
			// 交易未找到
			glog.Infof(ctx, "%s transaction not found on TRON chain or not yet mined", logPrefix)
			return 0, nil // 视为 0 确认
		}

		// 检查交易状态 (仅当 receipt 存在时)
		if txInfo.GetReceipt() != nil {
			result := txInfo.GetReceipt().GetResult()
			// TRON protobuf Transaction_Result enum:
			// DEFAULT = 0 (成功，通常用于原生TRX转账)
			// SUCCESS = 1 (成功，通常用于智能合约调用)
			// REVERT = 2 (失败)
			// 其他值 = 失败
			if result != 0 && result != 1 { // 0 = DEFAULT (成功), 1 = SUCCESS (成功)
				glog.Warningf(ctx, "%s transaction failed on TRON chain (Receipt Result: %d)", logPrefix, result)
				return 0, gerror.Newf("%s transaction failed on TRON chain (Result: %d)", logPrefix, result)
			}
			glog.Debugf(ctx, "%s TRON transaction receipt result: %d (0=DEFAULT/success, 1=SUCCESS)", logPrefix, result)
		}

		// 获取交易所在区块号
		txBlockNumber := txInfo.GetBlockNumber()
		if txBlockNumber <= 0 {
			glog.Warningf(ctx, "%s transaction info found but block number is invalid (%d)", logPrefix, txBlockNumber)
			return 0, gerror.Newf("%s transaction info has invalid block number (%d)", logPrefix, txBlockNumber)
		}

		// 获取最新区块号
		latestBlock, err := tronClient.GetNowBlock()
		if err != nil {
			return 0, gerror.Wrapf(err, "%s failed to get latest block from TRON gRPC", logPrefix)
		}

		if latestBlock == nil || latestBlock.GetBlockHeader() == nil || latestBlock.GetBlockHeader().GetRawData() == nil {
			return 0, gerror.Newf("%s received invalid latest block data", logPrefix)
		}

		latestBlockNumber := latestBlock.GetBlockHeader().GetRawData().GetNumber()

		// 计算确认数
		if latestBlockNumber < txBlockNumber {
			glog.Warningf(ctx, "%s latest TRON block number (%d) is less than transaction block number (%d). Node might be syncing.", logPrefix, latestBlockNumber, txBlockNumber)
			return 0, nil // 暂时返回 0 确认
		}

		confirmations := latestBlockNumber - txBlockNumber + 1
		glog.Debugf(ctx, "%s Latest TRON Block: %d, Tx Block: %d, Confirmations: %d", logPrefix, latestBlockNumber, txBlockNumber, confirmations)
		return uint64(confirmations), nil

		// 添加其他链的处理...
		// case "BSC":
		// case "POLYGON":
		// ...

	default:
		return 0, gerror.Newf("%s unsupported chain: %s", logPrefix, chain)
	}
}
