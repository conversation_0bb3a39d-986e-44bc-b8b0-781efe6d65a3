package deposit_step2_confirm

import (
	"context"
	"fmt"
	"strings"
	"time"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	// Import task registry

	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
)

// ConfirmPendingDeposits 定时任务：确认待处理的充值订单
func ConfirmPendingDeposits(ctx context.Context) {
	logPrefix := "[ConfirmPendingDeposits]"
	glog.Infof(ctx, "%s 开始执行确认充值任务...", logPrefix)
	startTime := time.Now()

	// 1. 查询待确认的充值记录 (状态为 1)
	var pendingRecharges []*entity.UserRecharges
	// 增加查询条件 state=1，并按创建时间排序，限制数量
	err := dao.UserRecharges.Ctx(ctx).
		Where(dao.UserRecharges.Columns().State, 1).
		OrderAsc(dao.UserRecharges.Columns().CreatedAt).
		Limit(50). // 每次处理 N 条，避免任务过重
		Scan(&pendingRecharges)

	if err != nil {
		glog.Errorf(ctx, "%s 查询待确认充值记录失败: %v", logPrefix, err)
		return
	}

	if len(pendingRecharges) == 0 {
		glog.Infof(ctx, "%s 没有待确认的充值记录。", logPrefix)
		glog.Infof(ctx, "%s 任务执行完毕，耗时: %s", logPrefix, time.Since(startTime))
		return
	}

	glog.Infof(ctx, "%s 发现 %d 条待确认的充值记录，开始处理...", logPrefix, len(pendingRecharges))

	processedCount := 0
	failedCount := 0

	// Structure to hold details of failed recharges
	type FailedRechargeDetail struct {
		RechargeID uint // Changed from uint64 to uint to match entity.UserRecharges.RechargesId
		TxHash     string
		Reason     string
	}
	var failedDetails []FailedRechargeDetail

	// 2. 遍历处理每条记录
	for _, recharge := range pendingRecharges {
		// 为每个订单创建一个新的 context，以防单个订单处理超时影响其他订单
		processCtx := gctx.New()
		// 调用 recharge_processor.go 中的处理函数
		err := processSingleRecharge(processCtx, recharge, logPrefix)
		if err != nil {
			// 错误已在 processSingleRecharge 中记录，这里我们收集详细信息
			failedCount++
			failureReason := "Unknown error"
			if err != nil {
				failureReason = err.Error()
			}
			failedDetails = append(failedDetails, FailedRechargeDetail{
				RechargeID: recharge.RechargesId,
				TxHash:     recharge.TxHash,
				Reason:     failureReason,
			})
		} else {
			processedCount++
		}
	}

	// 任务总结日志
	summaryMessage := fmt.Sprintf("%s 任务执行完毕，成功处理 %d 条，失败 %d 条，总耗时: %s",
		logPrefix, processedCount, failedCount, time.Since(startTime))

	if len(failedDetails) > 0 {
		var failedReport strings.Builder
		failedReport.WriteString("\n处理失败的充值记录详情:\n")
		for i, detail := range failedDetails {
			failedReport.WriteString(fmt.Sprintf("  %d. RechargeID: %d, TxHash: %s, 原因: %s\n",
				i+1, detail.RechargeID, detail.TxHash, detail.Reason))
		}
		summaryMessage += failedReport.String()
	}
	glog.Info(ctx, summaryMessage)
}

// // init 在包初始化时注册 ConfirmPendingDeposits 任务
// func init() {
// 	taskName := "ConfirmPendingDeposits"
// 	task_registry.Register(task_registry.TaskInfo{
// 		Name: taskName,
// 		SpecFunc: func(ctx context.Context) (spec string, enabled bool, err error) {
// 			// 从配置中读取任务的启用状态和 cron 表达式
// 			enabledVal, err := g.Cfg().Get(ctx, "depositConfirm.enabled")
// 			if err != nil {
// 				glog.Warningf(ctx, "Failed to get config for %s.enabled (depositConfirm.enabled): %v. Task will be disabled.", taskName, err)
// 				return "", false, nil // 返回 false 表示禁用，不返回错误
// 			}
// 			enabled = enabledVal.Bool() // 默认为 false

// 			// 仅在启用时才获取 spec
// 			if enabled {
// 				specVal, err := g.Cfg().Get(ctx, "depositConfirm.spec")
// 				if err != nil {
// 					glog.Warningf(ctx, "Failed to get config for %s.spec (depositConfirm.spec): %v. Using empty spec.", taskName, err)
// 					spec = ""
// 				} else {
// 					spec = specVal.String() // 默认为空字符串
// 				}

// 				// 如果任务启用但 spec 为空，记录警告
// 				if spec == "" {
// 					glog.Warningf(ctx, "Task '%s' is enabled but has an empty or missing cron spec in config (depositConfirm.spec). It will not be scheduled.", taskName)
// 				}
// 			} else {
// 				// 如果任务禁用，spec 无意义，设为空
// 				spec = ""
// 			}

// 			return spec, enabled, nil // 返回获取到的 spec 和 enabled 状态
// 		},
// 		Func:        ConfirmPendingDeposits, // 任务执行函数
// 		IsSingleton: true,                   // 确认充值任务通常应作为单例运行，防止并发问题
// 	})
// }
