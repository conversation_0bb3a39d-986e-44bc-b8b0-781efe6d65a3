package balance_collector

import (
	"context"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// BalanceCollectorConfig holds the configuration for the balance collector task
type BalanceCollectorConfig struct {
	Enabled      bool            `json:"enabled"`      // Whether the task is enabled
	Spec         string          `json:"spec"`         // Cron schedule expression
	WalletConfig *entity.Wallets `json:"walletConfig"` // Wallet configuration
}

// GetConfig retrieves the configuration for the balance collector task
func GetConfig(ctx context.Context) (*BalanceCollectorConfig, error) {
	config := &BalanceCollectorConfig{
		Enabled: g.Cfg().MustGet(ctx, "balanceCollector.enabled", false).Bool(),
		Spec:    g.Cfg().MustGet(ctx, "balanceCollector.spec", "* * 0 * * *").String(),
	}

	// Get wallet configuration
	var wallet entity.Wallets
	err := dao.Wallets.Ctx(ctx).Scan(&wallet)
	if err != nil {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "Failed to get wallet configuration: %v", err)
		return nil, err
	}

	config.WalletConfig = &wallet
	return config, nil
}
