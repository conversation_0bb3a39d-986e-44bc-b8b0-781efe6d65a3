package balance_collector

import (
	"context"
	"wallet-api/internal/consts"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// CollectBalances is the main function for the balance collector task
func CollectBalances(ctx context.Context) {
	logPrefix := "[BalanceCollectorTask]"
	g.Log().Infof(ctx, "%s Starting balance collection check...", logPrefix)

	// Get configuration
	config, err := GetConfig(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s Failed to get configuration: %v", logPrefix, err)
		return
	}

	// Check if strategy collection is enabled
	if config.WalletConfig.StrategyCollectSwitch != 1 {
		g.Log().Infof(ctx, "%s Strategy collection is disabled in wallet settings. Skipping task.", logPrefix)
		return
	}

	// Get all bound addresses
	var boundAddresses []*entity.Address
	err = dao.Address.Ctx(ctx).
		WhereIn("bind_status", []int{1, 2}).
		Scan(&boundAddresses)

	if err != nil {
		g.Log().Errorf(ctx, "%s Failed to get bound addresses: %v", logPrefix, err)
		return
	}

	if len(boundAddresses) == 0 {
		g.Log().Infof(ctx, "%s No bound addresses found. Skipping task.", logPrefix)
		return
	}

	g.Log().Infof(ctx, "%s Found %d bound addresses to check", logPrefix, len(boundAddresses))

	// Process each address
	for _, address := range boundAddresses {
		processAddress(ctx, address, config, logPrefix)
	}

	g.Log().Infof(ctx, "%s Balance collection check completed", logPrefix)
}

// processAddress checks the balance of a single address and creates a collection plan if needed
func processAddress(ctx context.Context, address *entity.Address, config *BalanceCollectorConfig, logPrefix string) {
	// Check if there's an existing collection plan with state=1
	count, err := dao.WithdrawPlan.Ctx(ctx).
		Where("address", address.Address).
		Where("state", 1).
		Count()

	if err != nil {
		g.Log().Errorf(ctx, "%s Failed to check existing collection plan for address %s: %v",
			logPrefix, address.Address, err)
		return
	}

	if count > 0 {
		g.Log().Debugf(ctx, "%s Address %s already has a pending collection plan. Skipping.",
			logPrefix, address.Address)
		return
	}

	// Get thresholds from wallet config
	var nativeThreshold, usdtThreshold decimal.Decimal
	var reservedBalance decimal.Decimal
	var networkType string

	// Parse threshold values from strings to float64
	ethThreshold, err := decimal.NewFromString(config.WalletConfig.EthCollectThreshold)
	if err != nil {
		g.Log().Warningf(ctx, "%s Failed to parse ETH threshold '%s': %v",
			logPrefix, config.WalletConfig.EthCollectThreshold, err)
		return
	}

	trxThreshold, err := decimal.NewFromString(config.WalletConfig.TrxCollectThreshold)
	if err != nil {
		g.Log().Warningf(ctx, "%s Failed to parse TRX threshold '%s': %v",
			logPrefix, config.WalletConfig.TrxCollectThreshold, err)
		return
	}

	usdtThresholdValue, err := decimal.NewFromString(config.WalletConfig.UsdtCollectThreshold)
	if err != nil {
		g.Log().Warningf(ctx, "%s Failed to parse USDT threshold '%s': %v",
			logPrefix, config.WalletConfig.UsdtCollectThreshold, err)
		return
	}

	switch address.Type {
	case consts.NetworkTypeETH:
		nativeThreshold = ethThreshold
		usdtThreshold = usdtThresholdValue
		reservedBalance = decimal.NewFromInt(config.WalletConfig.EthKeepAmount)
		networkType = consts.NetworkTypeETH
	case consts.NetworkTypeTRX:
		nativeThreshold = trxThreshold
		usdtThreshold = usdtThresholdValue
		reservedBalance = decimal.NewFromInt(config.WalletConfig.TrxKeepAmount)
		networkType = consts.NetworkTypeTRX
	default:
		g.Log().Warningf(ctx, "%s Unsupported network type %s for address %s. Skipping.",
			logPrefix, address.Type, address.Address)
		return
	}

	// Check if balances exceed thresholds
	nativeBalance := decimal.NewFromFloat(address.ChainCoinBalance)
	usdtBalance := decimal.NewFromFloat(address.ChainUsdtBalance)

	// Calculate collectable amounts (balance - reserved)
	nativeCollectable := nativeBalance.Sub(reservedBalance)
	usdtCollectable := usdtBalance.Sub(usdtThreshold).Sub(reservedBalance) // Subtract reservedBalance

	g.Log().Debugf(ctx, "%s Address %s balances - Native: %s, USDT: %s, Thresholds - Native: %s, USDT: %s, Reserved: %s",
		logPrefix, address.Address, nativeBalance.String(), usdtBalance.String(), nativeThreshold.String(), usdtThreshold.String(), reservedBalance.String())

	// Check if either balance exceeds threshold
	if (nativeCollectable.GreaterThan(decimal.Zero) && nativeCollectable.GreaterThanOrEqual(nativeThreshold)) ||
		(usdtCollectable.GreaterThan(decimal.Zero) && usdtCollectable.GreaterThanOrEqual(usdtThreshold)) {
		// Create collection plan
		createCollectionPlan(ctx, address, networkType, logPrefix)
	}
}

// createCollectionPlan creates a new collection plan for an address
func createCollectionPlan(ctx context.Context, address *entity.Address, networkType string, logPrefix string) {
	// Create data map for insertion
	data := g.Map{
		"chan":       networkType,
		"address":    address.Address,
		"state":      1, // 1-待确认
		"created_at": gtime.Now(),
		"updated_at": gtime.Now(),
	}

	// Execute the insert
	_, err := dao.WithdrawPlan.Ctx(ctx).Insert(data)

	if err != nil {
		g.Log().Errorf(ctx, "%s Failed to create collection plan for address %s: %v",
			logPrefix, address.Address, err)
		return
	}

	g.Log().Infof(ctx, "%s Created collection plan for address %s on network %s",
		logPrefix, address.Address, networkType)
}
