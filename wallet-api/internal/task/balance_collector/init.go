package balance_collector

import (
	"context"
	"wallet-api/internal/task_registry"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	// Register the balance collector task
	task_registry.Register(task_registry.TaskInfo{
		Name: "BalanceCollectorTask",
		SpecFunc: func(ctx context.Context) (spec string, enabled bool, err error) {
			// Read configuration from config file
			configPrefix := "balanceCollector"
			spec = g.Cfg().MustGet(ctx, configPrefix+".spec", "* * 0 * * *").String() // Default: every hour
			enabled = g.Cfg().MustGet(ctx, configPrefix+".enabled", false).Bool()     // Default: disabled

			// Log warning if enabled but spec is empty
			if enabled && spec == "" {
				g.Log().Warningf(ctx, "Task '%s' is enabled but spec is empty or missing in config ('%s.spec'). Task will not run.",
					"BalanceCollectorTask", configPrefix)
			}
			return spec, enabled, nil
		},
		Func:        CollectBalances, // Task execution function
		IsSingleton: true,            // Ensure only one instance runs at a time
	})
}
