package withdraw_step3_processing

import (
	"context"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"wallet-api/internal/dao" // Added for dao.Wallets
	"wallet-api/internal/model/entity"
	"wallet-api/internal/security/credentialmanager" // 新增
	cryptoUtil "wallet-api/internal/utility/crypto"
	"wallet-api/internal/utility/crypto/tron"
	utils "wallet-api/internal/utility/utils"

	"wallet-api/internal/utility/utils/bip39" // Added for NewSeed

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// TronProcessingResult holds the result of Tron processing operations
type TronProcessingResult struct {
	Success      bool                  // Whether the operation was successful
	TxHash       string                // Transaction hash if successful
	UpdatedData  *entity.UserWithdraws // Updated withdrawal data
	ErrorMessage string                // Error message if not successful
}

// ProcessTronWithdrawal processes a Tron withdrawal
func ProcessTronWithdrawal(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	withdrawal *entity.UserWithdraws,
) (*entity.UserWithdraws, error) {
	logger := g.Log()

	// Validate destination address
	if withdrawal.ToAddress != cfg.WalletConfig.TrxCollectAddress {
		errMsg := fmt.Sprintf("%s TRON destination address is not set in config", logPrefix)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// --- Get Seed for Private Key Derivation ---
	// THIS IS A PLACEHOLDER FOR SECURE SEED RETRIEVAL IN A TASK CONTEXT
	walletEntity, err := dao.Wallets.GetWallet(ctx) // Assuming GetWallet fetches the full entity
	if err != nil || walletEntity == nil {
		errMsg := fmt.Sprintf("%s Failed to get wallet entity for seed: %v", logPrefix, err)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}
	if walletEntity.Mnemonic == "" || walletEntity.MnemonicSalt == nil || walletEntity.MnemonicIterations == 0 {
		errMsg := fmt.Sprintf("%s Wallet entity is missing mnemonic, salt, or iterations for seed. WalletID: %d", logPrefix, walletEntity.Id)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}
	// Get password from credential manager
	userPasswordForDecryption, err := credentialmanager.GetPassword(ctx)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get password for mnemonic decryption: %v. Wallet may be locked.", err)
		logger.Errorf(ctx, "%s %s Withdrawal ID: %d", logPrefix, errMsg, withdrawal.UserWithdrawsId)

		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, "Wallet is locked or password not available for decryption.")
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(err, "%s wallet locked, cannot decrypt mnemonic for withdrawal ID %d", logPrefix, withdrawal.UserWithdrawsId)
	}

	decryptedMnemonic, errDec := utils.DecryptStringWithPBKDF2(ctx, walletEntity.Mnemonic, userPasswordForDecryption, walletEntity.MnemonicSalt, walletEntity.MnemonicIterations)
	if errDec != nil {
		errMsg := fmt.Sprintf("Failed to decrypt mnemonic: %v", errDec)
		logger.Errorf(ctx, "%s %s Withdrawal ID: %d", logPrefix, errMsg, withdrawal.UserWithdrawsId)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(errDec, "%s failed to decrypt mnemonic for withdrawal ID %d", logPrefix, withdrawal.UserWithdrawsId)
	}
	seed := bip39.NewSeed(decryptedMnemonic, "")
	// --- End of Seed Retrieval ---

	// Get private key using the derived seed
	privateKey, err := tron.GetPrivateKeyByAddress(ctx, seed, withdrawal.FromAddress)
	if err != nil {
		logger.Errorf(ctx, "%s Failed to get private key: %v", logPrefix, err)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("Failed to get private key: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to get private key", logPrefix)
	}
	if privateKey == "" {
		errMsg := fmt.Sprintf("%s private key is empty", logPrefix)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// Process based on token type
	switch withdrawal.Name {
	case "TRX":
		return processTrxNativeWithdrawal(ctx, cfg, logPrefix, withdrawal, privateKey)
	case "USDT":
		return processTrc20UsdtWithdrawal(ctx, cfg, logPrefix, withdrawal, privateKey)
	default:
		errMsg := fmt.Sprintf("%s unsupported token type: %s", logPrefix, withdrawal.Name)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}
}

// processTrxNativeWithdrawal processes a native TRX withdrawal
func processTrxNativeWithdrawal(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	withdrawal *entity.UserWithdraws,
	privateKey string,
) (*entity.UserWithdraws, error) {
	logger := g.Log()
	// updateData = withdrawal
	updateData := withdrawal

	logger.Infof(ctx, "%s Processing TRX withdrawal: ID=%d, ConfiguredTrxCollectThreshold=%s",
		logPrefix, withdrawal.UserWithdrawsId, cfg.WalletConfig.TrxCollectThreshold)

	// Check if the withdrawal address is active before proceeding
	isActive, err := tron.IsAddressActive(ctx, withdrawal.FromAddress, "", "")
	if err != nil {
		errMsg := fmt.Sprintf("%s Failed to check if address %s is active: %v", logPrefix, withdrawal.FromAddress, err)
		logger.Error(ctx, errMsg)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, errors.New(errMsg)
	}

	if !isActive {
		errMsg := fmt.Sprintf("%s Address %s is not active on TRON network. Cannot send transactions from inactive addresses.", logPrefix, withdrawal.FromAddress)
		logger.Error(ctx, errMsg)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, errors.New(errMsg)
	}

	logger.Infof(ctx, "%s Address %s is active, proceeding with withdrawal", logPrefix, withdrawal.FromAddress)

	//获取余额
	decimalBalance, err := tron.GetTRXBalance(ctx, withdrawal.FromAddress)
	if err != nil {
		errMsg := fmt.Sprintf("%s Failed to get TRX balance: %v", logPrefix, err)
		logger.Error(ctx, errMsg)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, errors.New(errMsg)
	}

	decimalMinCollectAmount, err := decimal.NewFromString(cfg.WalletConfig.TrxCollectThreshold)
	logger.Debugf(ctx, "%s Raw TrxCollectThreshold from config: '%s'", logPrefix, cfg.WalletConfig.TrxCollectThreshold)
	logger.Debugf(ctx, "%s Before balance check (line 107): decimalBalance='%s', decimalMinCollectAmount='%s'",
		logPrefix, decimalBalance.String(), decimalMinCollectAmount.String())

	//再次验证如果低于最小归集金额 则返回错误
	if decimalBalance.LessThan(decimalMinCollectAmount) {
		errMsg := fmt.Sprintf("%s Insufficient balance: %s (balance) < %s (minCollectAmount)", logPrefix, decimalBalance.String(), decimalMinCollectAmount.String())
		logger.Error(ctx, errMsg)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, errors.New(errMsg)
	}

	//预估手续费
	estimatedFeeSUN, err := tron.EstimateTrxFee(ctx, withdrawal.FromAddress, withdrawal.ToAddress, decimalBalance, logPrefix)

	if err != nil {
		errMsg := fmt.Sprintf("%s Failed to estimate TRX fee: %v", logPrefix, err)
		logger.Error(ctx, errMsg)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, errors.New(errMsg)
	}
	logger.Infof(ctx, "Estimated TRX fee: %d SUN", estimatedFeeSUN)

	decimalEstimatedFee := decimal.NewFromInt(estimatedFeeSUN)

	estimatedFeeTrx, err := tron.SunToTrxDecimal(decimalEstimatedFee, 6)

	if err != nil {
		errMsg := fmt.Sprintf("%s Failed to convert estimated fee to TRX: %v", logPrefix, err)
		logger.Error(ctx, errMsg)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, errors.New(errMsg)
	}

	//缓冲倍数Mul(1.1)
	estimatedFeeTrx = estimatedFeeTrx.Mul(decimal.NewFromFloat(1.5))

	logger.Infof(ctx, "Estimated TRX fee: %s TRX", estimatedFeeTrx.String())

	//todo 线上应该更多
	//实际发送的金额= 余额 - 预估手续费 - 保留金额

	// Get the keep amount from configuration (minimum balance to keep in account)
	// TrxKeepAmount is stored as TRX in the database (not SUN)
	// keepAmountTRX := decimal.NewFromInt(cfg.WalletConfig.TrxKeepAmount)

	// logger.Debugf(ctx, "%s TrxKeepAmount from config: %d TRX", logPrefix, cfg.WalletConfig.TrxKeepAmount)

	// If keep amount is 0, use a default minimum to prevent account deactivation
	// TRON requires a minimum balance to keep accounts active and able to send transactions
	// Based on testing, TRON seems to require keeping at least 1 TRX for transaction processing
	// if keepAmountTRX.LessThanOrEqual(decimal.Zero) {
	// 	keepAmountTRX = decimal.NewFromFloat(1.5) // Keep 1.05 TRX minimum - TRON seems to need this much
	// 	logger.Infof(ctx, "%s Using default keep amount: %s TRX (TRON requires significant balance for transactions)", logPrefix, keepAmountTRX.String())
	// }

	//默认余额强制留存1.5个trx
	keepAmountTRX := decimal.NewFromFloat(1.5) // Keep 1.05 TRX minimum - TRON seems to need this much
	logger.Infof(ctx, "%s Using default keep amount: %s TRX (TRON requires significant balance for transactions)", logPrefix, keepAmountTRX.String())

	decimalSendAmount := decimalBalance.Sub(estimatedFeeTrx).Sub(keepAmountTRX)

	logger.Debugf(ctx, "%s Values before final check: decimalBalance='%s', estimatedFeeTrx='%s', keepAmountTRX='%s', decimalSendAmount='%s', decimalMinCollectAmount='%s'",
		logPrefix,
		decimalBalance.String(),
		estimatedFeeTrx.String(),
		keepAmountTRX.String(),
		decimalSendAmount.String(),
		decimalMinCollectAmount.String())

	if decimalSendAmount.LessThanOrEqual(decimalMinCollectAmount) {
		errMsg := fmt.Sprintf("%s Insufficient balance to cover estimated fee and minimum collect amount: sendAmount %s <= minCollectAmount %s. Original balance: %s TRX",
			logPrefix, decimalSendAmount.String(), decimalMinCollectAmount.String(), decimalBalance.String())

		logger.Error(ctx, errMsg)

		updateData.State = WithdrawalStateFailed
		updateData.ErrorMessage = utils.BuildErrorMessageJson(ctx, updateData.ErrorMessage, errMsg) // 添加此行
		updateData.UpdatedAt = gtime.Now()
		return updateData, errors.New(errMsg)
	}

	// Send TRX transaction
	txHash, err := tron.SendTrxTransaction(
		ctx,
		privateKey,
		withdrawal.FromAddress,
		withdrawal.ToAddress,
		decimalSendAmount,
		logPrefix,
	)

	if err != nil {
		errMsg := fmt.Sprintf("%s Failed to send TRX transaction: %v", logPrefix, err)
		logger.Error(ctx, errMsg)

		updateData.UpdatedAt = gtime.Now()
		updateData.State = WithdrawalStateFailed
		updateData.ErrorMessage = utils.BuildErrorMessageJson(ctx, updateData.ErrorMessage, errMsg)
		return updateData, errors.New(errMsg)
	}

	logger.Infof(ctx, "%s TRX transaction sent successfully. Hash: %s", logPrefix, txHash)

	// Update withdrawal record
	updateData.UpdatedAt = gtime.Now()
	updateData.State = WithdrawalStateProcessing
	updateData.TxHash = txHash

	return updateData, nil
}

// processTrc20UsdtWithdrawal processes a TRC20 USDT withdrawal
func processTrc20UsdtWithdrawal(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	withdrawal *entity.UserWithdraws,
	privateKey string,
) (*entity.UserWithdraws, error) {
	logger := g.Log()

	logger.Infof(ctx, "%s Processing TRC20 USDT withdrawal: ID=%d",
		logPrefix, withdrawal.UserWithdrawsId)

	// Get the full TRC20 USDT balance from the address
	decimalUSDTBalance, err := tron.GetTRC20UsdtTokenBalance(ctx, withdrawal.FromAddress)
	if err != nil {
		errMsg := fmt.Sprintf("%s Failed to get TRC20 USDT balance for address %s: %v", logPrefix, withdrawal.FromAddress, err)
		logger.Error(ctx, errMsg)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, errors.New(errMsg)
	}
	amount := decimalUSDTBalance // This is the full balance to be withdrawn

	logger.Infof(ctx, "%s Retrieved TRC20 USDT balance for address %s: %s USDT", logPrefix, withdrawal.FromAddress, amount.String())

	// Check if amount is valid (i.e., balance is greater than zero)
	if amount.LessThanOrEqual(decimal.Zero) {
		errMsg := fmt.Sprintf("%s No USDT balance to withdraw from address %s. Balance: %s USDT",
			logPrefix, withdrawal.FromAddress, amount.String())
		logger.Info(ctx, errMsg) // Log as Info, as it's an expected condition if balance is zero

		// Update withdrawal record to reflect no action taken due to zero balance
		withdrawal.State = WithdrawalStateCompleted // Or a specific state like WithdrawalStateNoBalanceToWithdraw
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, "No USDT balance to withdraw.")
		withdrawal.TxHash = "" // No transaction was made
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, nil // Return nil error as processing is complete (nothing to do)
	}

	// Set fee limit for TRC20 transfers
	// This is the maximum TRX that can be consumed for this transaction
	var feeLimit int64 = 30_000_000 // 30 TRX in SUN units (default)

	// Minimum fee limit for TRC20 USDT transactions (5 TRX)
	// TRC20 transactions without sufficient energy can consume significant TRX
	const minTrc20FeeLimitTRX int64 = 5 // 5 TRX minimum

	// Use configured fee limit if available and reasonable
	// Note: TrxFeeMax is stored in TRX units in the database, but feeLimit needs SUN units
	if cfg.WalletConfig.TrxFeeMax > 0 {
		configuredFeeLimitTRX := cfg.WalletConfig.TrxFeeMax

		// Ensure the configured fee limit is at least the minimum required for TRC20
		if configuredFeeLimitTRX >= minTrc20FeeLimitTRX {
			// Convert TRX to SUN (1 TRX = 1,000,000 SUN)
			feeLimit = configuredFeeLimitTRX * 1_000_000
		} else {
			// Log warning and use minimum instead of dangerously low configured value
			logger.Warningf(ctx, "%s Configured TrxFeeMax (%d TRX) is too low for TRC20 transactions. Using minimum (%d TRX) instead.",
				logPrefix, configuredFeeLimitTRX, minTrc20FeeLimitTRX)
			feeLimit = minTrc20FeeLimitTRX * 1_000_000
		}
	}

	logger.Infof(ctx, "%s Using fee limit: %d SUN (%.1f TRX) for TRC20 USDT transaction",
		logPrefix, feeLimit, float64(feeLimit)/1_000_000)

	// Send TRC20 USDT transaction
	txHash, err := tron.SendTrc20Transaction(
		ctx,
		privateKey,
		withdrawal.FromAddress,
		withdrawal.ToAddress,
		amount,
		feeLimit,
		logPrefix,
	)

	if err != nil {
		errMsg := fmt.Sprintf("%s Failed to send TRC20 USDT transaction: %v", logPrefix, err)
		logger.Error(ctx, errMsg)

		withdrawal.UpdatedAt = gtime.Now()
		withdrawal.ErrorMessage = utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.State = WithdrawalStateFailed
		return withdrawal, errors.New(errMsg)
	}

	logger.Infof(ctx, "%s TRC20 USDT transaction sent successfully. Hash: %s", logPrefix, txHash)

	// Update withdrawal record
	withdrawal.UpdatedAt = gtime.Now()
	withdrawal.State = WithdrawalStateProcessing
	withdrawal.TxHash = txHash

	return withdrawal, nil
}

// CheckTronAccountStatus checks if a Tron account is active and has sufficient resources
func CheckTronAccountStatus(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	address string,
) (bool, int64, int64, error) {
	logger := g.Log()

	// Get client manager instance
	clientManager := cryptoUtil.GetInstance()
	tronClient, err := clientManager.GetDefaultTronClient(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s Failed to get TRON client: %v", logPrefix, err)
		return false, 0, 0, err
	}

	// Get account energy
	energy, err := tron.GetAccountEnergy(ctx, address, "", "")
	if err != nil {
		logger.Errorf(ctx, "%s Failed to get account energy: %v", logPrefix, err)
		return false, 0, 0, err
	}

	// Get account bandwidth
	bandwidth, err := tron.GetAccountBandwidth(ctx, address, "", "")
	if err != nil {
		logger.Errorf(ctx, "%s Failed to get account bandwidth: %v", logPrefix, err)
		return false, 0, 0, err
	}

	// Check if account is active
	isActive := true
	if energy == 0 && bandwidth == 0 {
		// Check if account exists by getting its balance
		account, err := tronClient.GetAccount(address)
		if err != nil || account == nil {
			if err != nil && strings.Contains(err.Error(), "account not found") {
				isActive = false
			} else if account == nil {
				isActive = false
			}
		}

		logger.Warningf(ctx, "%s Account may be inactive: energy=0, bandwidth=0", logPrefix)
	}

	logger.Infof(ctx, "%s Account status: active=%v, energy=%d, bandwidth=%d",
		logPrefix, isActive, energy, bandwidth)

	return isActive, energy, bandwidth, nil
}

// VerifyTronTransaction verifies the status of a Tron transaction
func VerifyTronTransaction(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	txHash string,
) (bool, error) {
	logger := g.Log()

	if txHash == "" {
		return false, fmt.Errorf("%s transaction hash is empty", logPrefix)
	}

	// Get client manager instance
	clientManager := cryptoUtil.GetInstance()
	tronClient, err := clientManager.GetDefaultTronClient(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s Failed to get TRON client: %v", logPrefix, err)
		return false, err
	}

	// Convert hex string to bytes
	txHashBytes, err := hex.DecodeString(txHash)
	if err != nil {
		logger.Errorf(ctx, "%s Failed to decode transaction hash: %v", logPrefix, err)
		return false, err
	}

	// Get transaction info - note the method name is GetTransactionInfoByID (capital ID)
	txInfo, err := tronClient.GetTransactionInfoByID(string(txHashBytes))
	if err != nil {
		logger.Errorf(ctx, "%s Failed to get transaction info: %v", logPrefix, err)
		return false, err
	}

	// Check if transaction exists
	if txInfo == nil {
		logger.Warningf(ctx, "%s Transaction not found: %s", logPrefix, txHash)
		return false, nil
	}

	// Check transaction status
	// TRON protobuf Transaction_Result enum:
	// DEFAULT = 0 (成功，通常用于原生TRX转账)
	// SUCCESS = 1 (成功，通常用于智能合约调用)
	// REVERT = 2 (失败)
	// 其他值 = 失败
	if txInfo.Receipt != nil && txInfo.Receipt.Result != 0 && txInfo.Receipt.Result != 1 {
		logger.Warningf(ctx, "%s Transaction failed with receipt result: %d", logPrefix, txInfo.Receipt.Result)
		return false, nil
	}

	logger.Infof(ctx, "%s Transaction verified successfully: %s", logPrefix, txHash)
	return true, nil
}
