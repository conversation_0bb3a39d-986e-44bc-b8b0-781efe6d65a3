package withdraw_step3_processing

import (
	"context"
	"fmt"
	"wallet-api/internal/task_registry"

	"github.com/gogf/gf/v2/frame/g"
)

const (
	// Task configuration and display names
	taskDisplayName = "WithdrawStep3ProcessingTask"
)

func init() {
	// Register the withdrawal processing task
	task_registry.Register(task_registry.TaskInfo{
		Name: taskDisplayName,
		SpecFunc: func(ctx context.Context) (spec string, enabled bool, err error) {
			logger := g.Log()

			// Get configuration
			config, err := GetConfig(ctx)
			if err != nil {
				logger.Errorf(ctx, "%s: Failed to get configuration: %v", taskDisplayName, err)
				return "", false, err
			}

			spec = config.Spec
			enabled = config.Enabled

			// Validate configuration
			if enabled && spec == "" {
				errMsg := fmt.Sprintf("Task '%s' is enabled but spec is empty or missing in config. Task will not run.",
					taskDisplayName)
				logger.Error(ctx, errMsg)
				return "", false, fmt.E<PERSON><PERSON>("%s", errMsg)
			}

			// Log configuration
			logger.Infof(ctx, "%s: Configuration loaded. Spec='%s', Enabled=%t, BatchSize=%d",
				taskDisplayName, spec, enabled, config.BatchSize)

			return spec, enabled, nil
		},
		Func:        ProcessWithdrawals,
		IsSingleton: true, // Ensure only one instance runs at a time
	})

}
