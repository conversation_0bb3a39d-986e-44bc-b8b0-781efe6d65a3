package withdraw_step3_processing // Changed package name

import (
	"context"
	"fmt"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// FeeHandlerConfig holds the configuration for the fee handler task
type ProcessingHandlerConfig struct {
	Enabled      bool            `json:"enabled"`      // Whether the task is enabled
	Spec         string          `json:"spec"`         // Cron schedule expression
	BatchSize    int             `json:"batchSize"`    // Number of records to process in one batch
	WalletConfig *entity.Wallets `json:"walletConfig"` // Wallet configuration
}

// GetConfig retrieves the configuration for the fee handler task
func GetConfig(ctx context.Context) (*ProcessingHandlerConfig, error) {
	var walletConfig *entity.Wallets
	err := dao.Wallets.Ctx(ctx).Where("id", 1).Scan(&walletConfig)
	if err != nil {
		return nil, fmt.Errorf("wallet config not found: %w", err)
	}
	// Configuration prefix for this task
	configPrefix := "withdrawStep3Processing"

	// Create configuration with default values
	config := &ProcessingHandlerConfig{
		Enabled:      g.Cfg().MustGet(ctx, configPrefix+".enabled", false).Bool(),
		Spec:         g.Cfg().MustGet(ctx, configPrefix+".spec", "*/5 * * * *").String(), // Default: every 5 minutes
		BatchSize:    g.Cfg().MustGet(ctx, configPrefix+".batchSize", 100).Int(),
		WalletConfig: walletConfig,
	}

	logger := g.Log()

	// Log configuration summary
	logger.Infof(ctx, "Withdrawal processing configuration loaded: enabled=%v, batchSize=%d",
		config.Enabled, config.BatchSize)

	return config, nil
}
