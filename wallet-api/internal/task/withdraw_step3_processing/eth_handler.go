package withdraw_step3_processing

import (
	"context"
	"fmt"
	"wallet-api/internal/dao" // Added for dao.Wallets
	"wallet-api/internal/model/entity"
	"wallet-api/internal/security/credentialmanager"  // 新增
	walletCrypto "wallet-api/internal/utility/crypto" // Added for GetErc20ContractAddress
	"wallet-api/internal/utility/crypto/eth"
	util "wallet-api/internal/utility/utils"

	"wallet-api/internal/utility/utils/bip39" // Added for NewSeed

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// EthProcessingResult holds the result of ETH processing operations
type EthProcessingResult struct {
	Success      bool                  // Whether the operation was successful
	TxHash       string                // Transaction hash if successful
	UpdatedData  *entity.UserWithdraws // Updated withdrawal data
	ErrorMessage string                // Error message if not successful
}

// ProcessEthWithdrawal processes an ETH withdrawal
func ProcessEthWithdrawal(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	withdrawal *entity.UserWithdraws,
) (*entity.UserWithdraws, error) {
	logger := g.Log()

	// Validate destination address
	if withdrawal.ToAddress != cfg.WalletConfig.EthCollectAddress {
		errMsg := fmt.Sprintf("%s ETH destination address is not set in config", logPrefix)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// --- Get Seed for Private Key Derivation ---
	// THIS IS A PLACEHOLDER FOR SECURE SEED RETRIEVAL IN A TASK CONTEXT
	// In a real scenario, the user's password should not be directly available here.
	// The seed or decrypted mnemonic should be passed securely to the task,
	// or a mechanism to use a temporarily unlocked wallet session's derived key.

	// 1. Get encrypted mnemonic, salt, iterations from DAO (or cfg if pre-loaded)
	walletEntity, err := dao.Wallets.GetWallet(ctx) // Assuming GetWallet fetches the full entity
	if err != nil || walletEntity == nil {
		errMsg := fmt.Sprintf("%s Failed to get wallet entity for seed: %v", logPrefix, err)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}
	if walletEntity.Mnemonic == "" || walletEntity.MnemonicSalt == nil || walletEntity.MnemonicIterations == 0 {
		errMsg := fmt.Sprintf("%s Wallet entity is missing mnemonic, salt, or iterations for seed. WalletID: %d", logPrefix, walletEntity.Id)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// 2. Get password from credential manager
	userPasswordForDecryption, err := credentialmanager.GetPassword(ctx)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get password for mnemonic decryption: %v. Wallet may be locked.", err)
		logger.Errorf(ctx, "%s %s Withdrawal ID: %d", logPrefix, errMsg, withdrawal.UserWithdrawsId)

		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, "Wallet is locked or password not available for decryption.")
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(err, "%s wallet locked, cannot decrypt mnemonic for withdrawal ID %d", logPrefix, withdrawal.UserWithdrawsId)
	}

	decryptedMnemonic, errDec := util.DecryptStringWithPBKDF2(ctx, walletEntity.Mnemonic, userPasswordForDecryption, walletEntity.MnemonicSalt, walletEntity.MnemonicIterations)
	if errDec != nil {
		errMsg := fmt.Sprintf("Failed to decrypt mnemonic: %v", errDec)
		logger.Errorf(ctx, "%s %s Withdrawal ID: %d", logPrefix, errMsg, withdrawal.UserWithdrawsId)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(errDec, "%s failed to decrypt mnemonic for withdrawal ID %d", logPrefix, withdrawal.UserWithdrawsId)
	}
	seed := bip39.NewSeed(decryptedMnemonic, "")
	// --- End of Seed Retrieval ---

	// Get private key using the derived seed
	privateKey, err := eth.GetPrivateKeyByAddress(ctx, seed, withdrawal.FromAddress)
	if err != nil {
		logger.Errorf(ctx, "%s Failed to get private key: %v", logPrefix, err)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("Failed to get private key: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to get private key", logPrefix)
	}
	if privateKey == "" {
		errMsg := fmt.Sprintf("%s private key is empty", logPrefix)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// Process based on token type
	switch withdrawal.Name {
	case "ETH":
		return processEthNativeWithdrawal(ctx, cfg, logPrefix, withdrawal, privateKey)
	case "USDT":
		return processErc20UsdtWithdrawal(ctx, cfg, logPrefix, withdrawal, privateKey)
	default:
		errMsg := fmt.Sprintf("%s unsupported token type: %s", logPrefix, withdrawal.Name)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}
}

// processEthNativeWithdrawal processes a native ETH withdrawal
func processEthNativeWithdrawal(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	withdrawal *entity.UserWithdraws,
	privateKey string,
) (*entity.UserWithdraws, error) {
	logger := g.Log()

	EthFeeMax := cfg.WalletConfig.EthFeeMax

	if EthFeeMax <= 0 {
		errMsg := fmt.Sprintf("%s ETH fee max is invalid: %f", logPrefix, EthFeeMax)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// Get ETH balance
	balanceStr, err := eth.GetETHBalance(withdrawal.FromAddress)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to get ETH balance: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to get ETH balance", logPrefix)
	}

	balance, err := decimal.NewFromString(balanceStr)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to parse ETH balance: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to parse ETH balance", logPrefix)
	}

	// Validate balance
	if balance.IsZero() {
		errMsg := fmt.Sprintf("%s ETH balance is zero", logPrefix)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}
	// Minimum balance check can be adjusted based on estimated gas or a very small ETH value
	// For now, keeping the original 0.0001 check, but it might need re-evaluation
	if balance.LessThanOrEqual(decimal.NewFromFloat(0.0001)) {
		errMsg := fmt.Sprintf("%s ETH balance is too small: %s", logPrefix, balance.String())
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// Estimate Gas Fee for native ETH transfer
	estimatedGasFeeEth, estimatedGasPriceGwei, estimatedGasLimitUnitsDecimal, err := eth.EstimateNativeEthTransferFee(ctx)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to estimate native ETH transfer fee: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(err, "%s failed to estimate native ETH transfer fee", logPrefix)
	}
	logger.Debugf(ctx, "%s ETH Native Withdrawal - Gas Estimation: FeeETH='%s', PriceGwei='%s', LimitUnits='%s'", logPrefix, estimatedGasFeeEth.String(), estimatedGasPriceGwei.String(), estimatedGasLimitUnitsDecimal.String())

	// Validate if balance is sufficient for the estimated gas fee
	if balance.LessThanOrEqual(estimatedGasFeeEth) {
		errMsg := fmt.Sprintf("%s ETH balance %s is not enough for estimated gas fee %s", logPrefix, balance.String(), estimatedGasFeeEth.String())
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// Calculate amount to send (all balance minus estimated gas fee)
	amountToSendEth := balance.Sub(estimatedGasFeeEth)
	if amountToSendEth.LessThanOrEqual(decimal.Zero) { // Ensure sending a positive amount
		errMsg := fmt.Sprintf("%s ETH amount to send is zero or negative after deducting gas fee: %s", logPrefix, amountToSendEth.String())
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}
	weiAmount := amountToSendEth.Mul(decimal.NewFromInt(1e18))
	amountToSendStr := weiAmount.StringFixed(0)

	logger.Debugf(ctx, "%s ETH Native Withdrawal: balanceStr='%s', balanceDecimal='%s', amountToSendEth='%s', amountToSendInWei='%s'",
		logPrefix, balanceStr, balance.String(), amountToSendEth.String(), amountToSendStr)

	// Prepare gas parameters for SendETH
	gasLimitToSend := uint64(estimatedGasLimitUnitsDecimal.IntPart())
	gasPriceInWeiBigInt := estimatedGasPriceGwei.Mul(decimal.NewFromInt(1_000_000_000)).BigInt()
	gasPriceToSend := gasPriceInWeiBigInt.Uint64()
	maxFee := fmt.Sprintf("%f", EthFeeMax) // Use EthFeeMax from earlier

	logger.Debugf(ctx, "%s ETH Native Withdrawal - Before SendETH: toAddress='%s', amountToSendStr='%s', gasLimitToSend='%d', gasPriceToSend='%d', maxFee='%s', privateKeyPresent=%t",
		logPrefix, cfg.WalletConfig.EthCollectAddress, amountToSendStr, gasLimitToSend, gasPriceToSend, maxFee, privateKey != "")

	// Send ETH transaction
	txHash, err := eth.SendETH(
		ctx,
		privateKey,
		cfg.WalletConfig.EthCollectAddress,
		amountToSendStr,
		gasLimitToSend,
		gasPriceToSend,
		maxFee,
	)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to send ETH: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to send ETH", logPrefix)
	}

	logger.Infof(ctx, "%s ETH sent successfully. Transaction hash: %s", logPrefix, txHash)

	// Update withdrawal record
	withdrawal.UpdatedAt = gtime.Now()
	withdrawal.State = WithdrawalStateProcessing
	withdrawal.TxHash = txHash

	return withdrawal, nil
}

// processErc20UsdtWithdrawal processes an ERC20 USDT withdrawal
func processErc20UsdtWithdrawal(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	withdrawal *entity.UserWithdraws,
	privateKey string,
) (*entity.UserWithdraws, error) {
	logger := g.Log()

	Erc20FeeMax := cfg.WalletConfig.Erc20FeeMax

	if Erc20FeeMax <= 0 {
		errMsg := fmt.Sprintf("%s ERC20 fee max is invalid: %f", logPrefix, Erc20FeeMax)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// Get USDT contract address
	usdtContractAddress, err := walletCrypto.GetErc20ContractAddress(ctx)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to get USDT contract address: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(err, "%s failed to get USDT contract address", logPrefix)
	}
	if usdtContractAddress == "" {
		errMsg := fmt.Sprintf("%s USDT contract address is empty from config", logPrefix)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// Get USDT balance
	balanceStr, err := eth.GetErc20UsdtBalance(withdrawal.FromAddress)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to get USDT balance: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to get USDT balance", logPrefix)
	}

	balance, err := decimal.NewFromString(balanceStr)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to parse USDT balance: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to parse USDT balance", logPrefix)
	}

	// Validate balance
	if balance.IsZero() {
		errMsg := fmt.Sprintf("%s USDT balance is zero", logPrefix)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}
	logger.Debugf(ctx, "%s ERC20 USDT Withdrawal: withdrawal.Amount='%f', balanceStrFromChain='%s', balanceDecimal='%s'",
		logPrefix, withdrawal.Amount, balanceStr, balance.String())

	// Estimate Gas Fee for ERC20 USDT transfer
	// Note: EstimateERC20TransferGas returns a gasLimit that already has a buffer (e.g., 1.9x)
	estimatedGasFeeEth, estimatedGasPriceGwei, estimatedGasLimitUnitsDecimal, err := eth.EstimateERC20TransferGas(ctx, withdrawal.FromAddress, cfg.WalletConfig.EthCollectAddress, usdtContractAddress)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to estimate USDT transfer gas: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(err, "%s failed to estimate USDT transfer gas", logPrefix)
	}
	logger.Debugf(ctx, "%s ERC20 USDT Withdrawal - Gas Estimation: FeeETH='%s', PriceGwei='%s', LimitUnits='%s'", logPrefix, estimatedGasFeeEth.String(), estimatedGasPriceGwei.String(), estimatedGasLimitUnitsDecimal.String())

	// Check if current ETH balance is sufficient for the estimated gas fee
	ethBalanceStr, err := eth.GetETHBalance(withdrawal.FromAddress)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to get ETH balance for gas fee check: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(err, "%s failed to get ETH balance for gas fee check", logPrefix)
	}
	ethBalance, err := decimal.NewFromString(ethBalanceStr)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to parse ETH balance for gas fee check: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(err, "%s failed to parse ETH balance for gas fee check", logPrefix)
	}

	if ethBalance.LessThan(estimatedGasFeeEth) {
		errMsg := fmt.Sprintf("%s ETH balance %s is not enough for estimated USDT gas fee %s", logPrefix, ethBalance.String(), estimatedGasFeeEth.String())
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, errMsg)
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.New(errMsg)
	}

	// Get token decimals
	decimals, err := util.GetTokenDecimals(ctx, "ERC20USDT")
	if err != nil {
		logger.Errorf(ctx, "%s Failed to get token decimals for ERCUSDT: %v", logPrefix, err)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to get token decimals: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to get token decimals", logPrefix)
	}
	logger.Debugf(ctx, "%s ERC20 USDT Withdrawal: Fetched token decimals for ERCUSDT: %d", logPrefix, decimals)

	// Format balance as token string
	logger.Debugf(ctx, "%s ERC20 USDT Withdrawal - Before FormatTokenValueToSmallestUnit: balanceInput='%s', decimalsInput='%d'",
		logPrefix, balance.String(), decimals)
	amountInSmallestUnitDecimal, err := util.FormatTokenValueToSmallestUnit(balance, decimals) // Changed function call
	if err != nil {
		logger.Errorf(ctx, "%s Failed to format balance with FormatTokenValueToSmallestUnit: balance='%s', decimals='%d', error: %v",
			logPrefix, balance.String(), decimals, err)
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to format balance to smallest unit: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(err, "%s failed to format balance to smallest unit", logPrefix)
	}
	formattedAmountToSend := amountInSmallestUnitDecimal.StringFixed(0) // Ensure it's an integer string
	logger.Debugf(ctx, "%s ERC20 USDT Withdrawal - After FormatTokenValueToSmallestUnit: amountInSmallestUnitDecimal='%s', formattedAmountToSendString='%s'", logPrefix, amountInSmallestUnitDecimal.String(), formattedAmountToSend)

	// Prepare gas parameters for SendUSDT
	gasLimitToSend := uint64(estimatedGasLimitUnitsDecimal.IntPart()) // Use the gasLimit from EstimateERC20TransferGas (already buffered internally in gas.go)
	gasPriceInWeiBigInt := estimatedGasPriceGwei.Mul(decimal.NewFromInt(1_000_000_000)).BigInt()
	gasPriceToSend := gasPriceInWeiBigInt.Uint64()
	maxFee := fmt.Sprintf("%f", Erc20FeeMax) // Use Erc20FeeMax from earlier

	logger.Debugf(ctx, "%s ERC20 USDT Withdrawal - Before SendUSDT: toAddress='%s', formattedAmountToSend='%s', gasLimitToSend='%d', gasPriceToSend='%d', maxFee='%s', privateKeyPresent=%t",
		logPrefix, cfg.WalletConfig.EthCollectAddress, formattedAmountToSend, gasLimitToSend, gasPriceToSend, maxFee, privateKey != "")

	// Send USDT transaction
	txHash, err := eth.SendUSDT(
		ctx,
		privateKey,
		cfg.WalletConfig.EthCollectAddress,
		formattedAmountToSend, // Use the correctly formatted integer string
		gasLimitToSend,
		gasPriceToSend,
		maxFee,
	)
	if err != nil {
		withdrawal.State = WithdrawalStateFailed
		withdrawal.ErrorMessage = util.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("failed to send USDT: %v", err))
		withdrawal.UpdatedAt = gtime.Now()
		return withdrawal, gerror.Wrapf(
			err, "%s failed to send USDT", logPrefix)
	}

	logger.Infof(ctx, "%s USDT sent successfully. Transaction hash: %s", logPrefix, txHash)

	// Update withdrawal record
	withdrawal.UpdatedAt = gtime.Now()
	withdrawal.State = WithdrawalStateProcessing
	withdrawal.TxHash = txHash

	return withdrawal, nil
}
