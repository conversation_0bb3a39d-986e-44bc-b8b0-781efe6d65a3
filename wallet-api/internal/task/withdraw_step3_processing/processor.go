package withdraw_step3_processing

import (
	"context"
	"fmt"
	"time"

	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	// "github.com/shopspring/decimal"
)

const (
	// Withdrawal states
	WithdrawalStatePending    = 1 // Pending user confirmation / initial state
	WithdrawalStateProcessing = 2 // Processing (sent to blockchain)
	WithdrawalStateRejected   = 3 // Rejected by admin
	WithdrawalStateCompleted  = 4 // Completed successfully
	WithdrawalStateFailed     = 5 // Failed to process

	// Log prefixes for consistent logging
	LogPrefixProcessor = "[WithdrawStep3Processor]"
)

// ProcessWithdrawals processes pending withdrawals with state = 1.
func ProcessWithdrawals(ctx context.Context) {
	logger := g.Log()
	logger.Infof(ctx, "%s Task started", LogPrefixProcessor)
	startTime := time.Now()

	// 1. Load Configuration
	cfg, err := GetConfig(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s Failed to load configuration: %v. Task cannot proceed.", LogPrefixProcessor, err)
		return
	}

	if !cfg.Enabled {
		logger.Infof(ctx, "%s Task is disabled in configuration. Exiting.", LogPrefixProcessor)
		return
	}

	// 2. Query withdrawals with state=2 (processing) and nergy_state=2
	var pendingWithdrawals []*entity.UserWithdraws
	err = dao.UserWithdraws.Ctx(ctx).
		Where(dao.UserWithdraws.Columns().State, WithdrawalStatePending).
		// Where(dao.UserWithdraws.Columns().CanActAt, "<=", time.Now()). //找到可以处理的订单
		Limit(cfg.BatchSize).
		Order(dao.UserWithdraws.Columns().UserWithdrawsId + " ASC"). // Process oldest first
		Scan(&pendingWithdrawals)

	if err != nil {
		logger.Errorf(ctx, "%s Failed to fetch pending withdrawals: %v", LogPrefixProcessor, err)
		return
	}

	if len(pendingWithdrawals) == 0 {
		logger.Infof(ctx, "%s No withdrawals with state=%d found to process", LogPrefixProcessor, WithdrawalStatePending)
		logger.Infof(ctx, "%s Task finished. Duration: %s", LogPrefixProcessor, time.Since(startTime))
		return
	}

	logger.Infof(ctx, "%s Found %d withdrawals with state=%d to process", LogPrefixProcessor, len(pendingWithdrawals), WithdrawalStatePending)

	// 3. Process each withdrawal
	processedCount := 0
	failedCount := 0
	skippedCount := 0
	retryCount := 0

	for _, withdrawal := range pendingWithdrawals {
		recordLogPrefix := fmt.Sprintf("%s[ID:%d|%s|%s]",
			LogPrefixProcessor,
			withdrawal.UserWithdrawsId,
			withdrawal.Chan,
			withdrawal.Name)

		// Process the withdrawal
		updatedWithdrawal, processErr := processSingleWithdrawal(ctx, cfg, recordLogPrefix, withdrawal)

		if processErr != nil {
			logger.Errorf(ctx, "%s Error processing withdrawal: %v", recordLogPrefix, processErr)
			failedCount++
			if updatedWithdrawal != nil {
				// 尝试将内存中已标记为失败的订单状态持久化到数据库
				_, dbUpdateErr := dao.UserWithdraws.Ctx(ctx).
					Where(dao.UserWithdraws.Columns().UserWithdrawsId, updatedWithdrawal.UserWithdrawsId).
					Data(updatedWithdrawal). // updatedWithdrawal 应包含失败状态和错误信息
					Update()
				if dbUpdateErr != nil {
					logger.Errorf(ctx, "%s CRITICAL: Failed to update withdrawal record (ID: %d) to FAILED state in DB after a processing error. DB Error: %v. Original processing error: %v",
						recordLogPrefix, updatedWithdrawal.UserWithdrawsId, dbUpdateErr, processErr)
					// 即使这里更新失败，主错误已记录，failedCount 已增加。
				} else {
					logger.Infof(ctx, "%s Withdrawal record (ID: %d) successfully updated to FAILED state in DB following a processing error.",
						recordLogPrefix, updatedWithdrawal.UserWithdrawsId)
				}
			} else {
				// 理论上，即使 processSingleWithdrawal 出错，updatedWithdrawal 也应该是被修改过的原 withdrawal 对象，不应为 nil。
				// 但为防止意外情况，添加日志。
				logger.Warningf(ctx, "%s updatedWithdrawal object was nil after a processing error for withdrawal (Original ID from loop: %d). Cannot update DB status for this failure. Original processing error: %v",
					recordLogPrefix, withdrawal.UserWithdrawsId, processErr) // withdrawal 是循环变量
			}
			continue
		}

		// Update the withdrawal record in the database
		if updatedWithdrawal != nil {
			_, err := dao.UserWithdraws.Ctx(ctx).
				Where(dao.UserWithdraws.Columns().UserWithdrawsId, updatedWithdrawal.UserWithdrawsId).
				Data(updatedWithdrawal).
				Update()

			if err != nil {
				logger.Errorf(ctx, "%s Error updating withdrawal record: %v", recordLogPrefix, err)
				failedCount++
				continue
			}
		}

		logger.Infof(ctx, "%s Withdrawal processed successfully", recordLogPrefix)
		processedCount++
	}

	// 4. Log summary
	logger.Infof(ctx, "%s Task finished. Processed: %d, Failed: %d, Retry: %d, Skipped: %d. Duration: %s",
		LogPrefixProcessor, processedCount, failedCount, retryCount, skippedCount, time.Since(startTime))

}

// processSingleWithdrawal processes a single withdrawal.
func processSingleWithdrawal(
	ctx context.Context,
	cfg *ProcessingHandlerConfig,
	logPrefix string,
	withdrawal *entity.UserWithdraws,
) (*entity.UserWithdraws, error) {
	fromAddress := withdrawal.FromAddress
	toAddress := withdrawal.ToAddress
	// decimalAmount := decimal.NewFromFloat(withdrawal.Amount)

	// Validate withdrawal data
	if fromAddress == "" {
		return nil, gerror.Newf(
			"%s source address is empty", logPrefix)
	}

	if toAddress == "" {
		return nil, gerror.Newf(
			"%s destination address is empty", logPrefix)
	}

	// Process based on chain type
	switch withdrawal.Chan {
	case "ETH":
		return ProcessEthWithdrawal(ctx, cfg, logPrefix, withdrawal)
	case "TRON":
		return ProcessTronWithdrawal(ctx, cfg, logPrefix, withdrawal)
	default:
		return nil, gerror.Newf(
			"%s unsupported chain: %s", logPrefix, withdrawal.Chan)
	}
}
