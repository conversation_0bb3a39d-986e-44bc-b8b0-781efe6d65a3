package withdraw_step4_confirm

import (
	"context"
	"encoding/hex"
	"fmt"
	"math/big"

	"wallet-api/internal/model/entity"

	tronclient "github.com/fbsobreira/gotron-sdk/pkg/client"
	troncommon "github.com/fbsobreira/gotron-sdk/pkg/common"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// getTronTransactionDetails fetches amount and fee for a TRON transaction.
func getTronTransactionDetails(ctx context.Context, tronSdkClient *tronclient.GrpcClient, withdrawal *entity.UserWithdraws) (amount decimal.Decimal, fee decimal.Decimal, err error) {
	logger := g.Log()
	txInfo, err := tronSdkClient.GetTransactionInfoByID(withdrawal.TxHash)
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to get TRON transaction info for %s: %w", withdrawal.TxHash, err)
	}

	// Detailed logging for txInfo
	logger.Debugf(ctx, "[TRON Tx Debug %s] txInfo.Id: %s", withdrawal.TxHash, txInfo.GetId())
	logger.Debugf(ctx, "[TRON Tx Debug %s] txInfo.Fee: %d", withdrawal.TxHash, txInfo.GetFee())
	logger.Debugf(ctx, "[TRON Tx Debug %s] txInfo.BlockNumber: %d", withdrawal.TxHash, txInfo.GetBlockNumber())
	logger.Debugf(ctx, "[TRON Tx Debug %s] txInfo.BlockTimeStamp: %d", withdrawal.TxHash, txInfo.GetBlockTimeStamp())
	// logger.Debugf(ctx, "[TRON Tx Debug %s] txInfo.ContractResult: %v", withdrawal.TxHash, txInfo.GetContractResult()) // Can be large
	logger.Debugf(ctx, "[TRON Tx Debug %s] txInfo.GetResult (Packing Status): %s", withdrawal.TxHash, txInfo.GetResult())
	logger.Debugf(ctx, "[TRON Tx Debug %s] txInfo.GetResMessage (Packing Message): %s", withdrawal.TxHash, string(txInfo.GetResMessage()))

	if txInfo.GetReceipt() != nil {
		receipt := txInfo.GetReceipt()
		logger.Debugf(ctx, "[TRON Tx Debug %s] Receipt.GetResult (Contract Execution Status): %s", withdrawal.TxHash, receipt.GetResult())
		logger.Debugf(ctx, "[TRON Tx Debug %s] Receipt.GetEnergyUsage: %d", withdrawal.TxHash, receipt.GetEnergyUsage())
		logger.Debugf(ctx, "[TRON Tx Debug %s] Receipt.GetEnergyFee: %d", withdrawal.TxHash, receipt.GetEnergyFee())
		logger.Debugf(ctx, "[TRON Tx Debug %s] Receipt.GetOriginEnergyUsage: %d", withdrawal.TxHash, receipt.GetOriginEnergyUsage())
		logger.Debugf(ctx, "[TRON Tx Debug %s] Receipt.GetEnergyUsageTotal: %d", withdrawal.TxHash, receipt.GetEnergyUsageTotal())
		logger.Debugf(ctx, "[TRON Tx Debug %s] Receipt.GetNetUsage: %d", withdrawal.TxHash, receipt.GetNetUsage())
		logger.Debugf(ctx, "[TRON Tx Debug %s] Receipt.GetNetFee: %d", withdrawal.TxHash, receipt.GetNetFee())
		// The contract address involved in the transaction is usually part of tx.RawData.Contract[0].GetParameter()
		// or derived from txInfo.GetLog()[i].GetAddress() for events.
		// txInfo.GetContractAddress() is for contract creation transactions.
		// We will rely on LogEntry.Address for contract interaction addresses.
	} else {
		logger.Debugf(ctx, "[TRON Tx Debug %s] txInfo.GetReceipt() is nil", withdrawal.TxHash)
	}

	for i, logEntry := range txInfo.GetLog() {
		logger.Debugf(ctx, "[TRON Tx Debug %s] LogEntry[%d] Address: %s", withdrawal.TxHash, i, troncommon.EncodeCheck(logEntry.GetAddress()))
		for j, topic := range logEntry.GetTopics() {
			logger.Debugf(ctx, "[TRON Tx Debug %s] LogEntry[%d] Topic[%d]: %s", withdrawal.TxHash, i, j, hex.EncodeToString(topic))
		}
		logger.Debugf(ctx, "[TRON Tx Debug %s] LogEntry[%d] Data: %s", withdrawal.TxHash, i, hex.EncodeToString(logEntry.GetData()))
	}

	// Fee in SUN, convert to TRX. Fee is charged regardless of tx success inside the contract.
	txFeeSun := decimal.NewFromInt(txInfo.GetFee()) // Fee for the transaction itself
	if txInfo.GetReceipt() != nil {
		txFeeSun = txFeeSun.Add(decimal.NewFromInt(txInfo.GetReceipt().GetEnergyFee())) // Add energy fee from receipt
		// txFeeSun = txFeeSun.Add(decimal.NewFromInt(txInfo.GetReceipt().GetNetFee())) // Net fee usually part of Fee already
	}
	fee = txFeeSun.Div(decimal.New(1, 6)) // 1 TRX = 1,000,000 SUN

	// Check overall transaction execution status (e.g., SUCCESS, FAILED)
	// Note: For smart contract calls, txInfo.GetResult() might be SUCCESS even if the contract logic reverted.
	// We need to check txInfo.GetReceipt().GetResult() for contract execution status.
	if txInfo.GetResult() != core.TransactionInfo_SUCESS { // This checks if the transaction was packed
		logger.Warningf(ctx, "[TRON Tx Debug %s] Transaction packing failed or reverted by node. Result: %s, Message: %s", withdrawal.TxHash, txInfo.GetResult(), string(txInfo.GetResMessage()))
		// Even if it fails here, a fee might have been charged.
		return decimal.Zero, fee, fmt.Errorf("TRON transaction %s failed to pack or was reverted by node, result: %s, message: %s", withdrawal.TxHash, txInfo.GetResult(), string(txInfo.GetResMessage()))
	}

	// Check transaction receipt status for both native TRX and TRC20 transfers
	// TRON protobuf Transaction_Result enum:
	// DEFAULT = 0 (成功，可用于原生TRX转账和某些TRC20转账)
	// SUCCESS = 1 (成功，通常用于智能合约调用)
	// REVERT = 2 (失败)
	// 其他值 = 失败
	if txInfo.GetReceipt() != nil {
		result := txInfo.GetReceipt().GetResult()
		resultStr := result.String()
		logger.Debugf(ctx, "[TRON Tx Debug %s] Receipt result: %d (%s)", withdrawal.TxHash, result, resultStr)

		// Accept both DEFAULT (0) and SUCCESS (1) as successful states
		if result != core.Transaction_Result_DEFAULT && result != core.Transaction_Result_SUCCESS {
			logger.Errorf(ctx, "[TRON Tx Debug %s] Transaction failed with receipt result: %d (%s)", withdrawal.TxHash, result, resultStr)
			return decimal.Zero, fee, fmt.Errorf("TRON transaction %s failed with receipt result: %d (%s)", withdrawal.TxHash, result, resultStr)
		}
		logger.Infof(ctx, "[TRON Tx Debug %s] Transaction successful with receipt result: %d (%s)", withdrawal.TxHash, result, resultStr)
	} else {
		// Receipt is nil - this can happen for some native TRX transfers
		isTRXNativeTransfer := withdrawal.Name == "TRX"
		if !isTRXNativeTransfer {
			// For TRC20 transfers, receipt is usually required for event logs
			logger.Warningf(ctx, "[TRON Tx Debug %s] TRC20 transaction has no receipt, but will attempt to parse logs anyway", withdrawal.TxHash)
		} else {
			logger.Debugf(ctx, "[TRON Tx Debug %s] Native TRX transaction has no receipt (normal for some cases)", withdrawal.TxHash)
		}
	}

	tokenInfo, err := GetTokenInfo(ctx, withdrawal.Chan, withdrawal.Name)
	if err != nil {
		return decimal.Zero, fee, fmt.Errorf("failed to get token info for %s on %s: %w", withdrawal.Name, withdrawal.Chan, err)
	}

	if withdrawal.Name == "TRX" { // Native TRX transfer
		var foundTRXTransfer bool

		// Need to get the actual transaction because TransactionInfo doesn't have contract info
		// First get the transaction by ID
		tx, err := tronSdkClient.GetTransactionByID(withdrawal.TxHash)
		if err != nil {
			return decimal.Zero, fee, fmt.Errorf("failed to get TRON transaction by ID %s: %w", withdrawal.TxHash, err)
		}

		// Check the contracts in the transaction
		for _, contract := range tx.GetRawData().GetContract() {
			if contract.Type == core.Transaction_Contract_TransferContract {
				transferContract := &core.TransferContract{}
				if errUnmarshal := contract.GetParameter().UnmarshalTo(transferContract); errUnmarshal != nil {
					logger.Warningf(ctx, "[TRON Tx: %s] Failed to unmarshal TRX TransferContract: %v", withdrawal.TxHash, errUnmarshal)
					continue
				}
				recipientAddrBase58 := troncommon.EncodeCheck(transferContract.GetToAddress())
				if recipientAddrBase58 == withdrawal.ToAddress {
					// Amount is in SUN for TransferContract
					amount = decimal.NewFromInt(transferContract.GetAmount()).Div(decimal.New(1, int32(tokenInfo.Decimals)))
					foundTRXTransfer = true
					break
				}
			}
		}
		if !foundTRXTransfer {
			return decimal.Zero, fee, fmt.Errorf("TRX transfer to %s not found in transaction %s", withdrawal.ToAddress, withdrawal.TxHash)
		}
	} else { // TRC20 Token transfer (via TriggerSmartContract)
		eventSigHex := ERC20TransferEventSignature[2:] // Remove "0x" for TRON hex string comparison
		var foundTokenTransfer bool

		logger.Debugf(ctx, "[TRON Tx: %s] Processing TRC20 transfer. Token: %s, Contract: %s",
			withdrawal.TxHash, withdrawal.Name, tokenInfo.ContractAddress)

		for _, logEntry := range txInfo.GetLog() {
			logContractAddrBytes := logEntry.GetAddress() // This is the TRC20 contract address that emitted the log
			// Need to add 0x41 prefix for mainnet TRON addresses
			fullContractAddrBytes := append([]byte{0x41}, logContractAddrBytes...)
			logContractAddrBase58 := troncommon.EncodeCheck(fullContractAddrBytes)

			// Check:
			// 1. Log is from the correct token contract address.
			// 2. Log has 3 topics.
			// 3. Topic0 matches the Transfer event signature.
			if logContractAddrBase58 == tokenInfo.ContractAddress && len(logEntry.GetTopics()) == 3 {
				topic0SigFromLog := hex.EncodeToString(logEntry.GetTopics()[0])
				logger.Debugf(ctx, "[TRON Tx: %s] Found matching contract log. Topic[0]: %s, Expected: %s",
					withdrawal.TxHash, topic0SigFromLog, eventSigHex)
				if topic0SigFromLog == eventSigHex {
					// Topic[2] is the 'to' address (indexed).
					// TRON addresses in topics are 32 bytes. The actual address part (20 bytes) is usually at the end.
					topicToAddressRawBytes := logEntry.GetTopics()[2] // This is 32 bytes

					// Construct the full 21-byte TRON address (0x41 prefix + 20 address bytes)
					var fullAddrBytesForComparison [21]byte
					fullAddrBytesForComparison[0] = byte(0x41)                        // Mainnet prefix 0x41 (TronBytePrefix)
					copy(fullAddrBytesForComparison[1:], topicToAddressRawBytes[12:]) // Last 20 bytes form the core address

					loggedToAddressBase58 := troncommon.EncodeCheck(fullAddrBytesForComparison[:])

					if loggedToAddressBase58 == withdrawal.ToAddress {
						// logEntry.Data contains the non-indexed 'value' parameter.
						tokenValue := new(big.Int).SetBytes(logEntry.GetData())
						amount = decimal.NewFromBigInt(tokenValue, 0).Div(decimal.New(1, int32(tokenInfo.Decimals)))
						foundTokenTransfer = true
						logger.Infof(ctx, "[TRON Tx: %s] Found TRC20 transfer! To: %s, Amount: %s %s",
							withdrawal.TxHash, loggedToAddressBase58, amount.String(), withdrawal.Name)
						break // Found the relevant transfer log
					} else {
						logger.Debugf(ctx, "[TRON Tx: %s] Address mismatch. Expected: %s, Got: %s",
							withdrawal.TxHash, withdrawal.ToAddress, loggedToAddressBase58)
					}
				}
			}
		}
		if !foundTokenTransfer {
			// Add detailed debugging information when transfer is not found
			logger.Warningf(ctx, "[TRON Tx: %s] TRC20 Transfer event to %s for token %s (contract: %s) not found in logs.",
				withdrawal.TxHash, withdrawal.ToAddress, withdrawal.Name, tokenInfo.ContractAddress)

			// Log all events found for debugging
			logger.Debugf(ctx, "[TRON Tx: %s] Total log entries found: %d", withdrawal.TxHash, len(txInfo.GetLog()))
			logger.Debugf(ctx, "[TRON Tx: %s] Looking for contract address: %s", withdrawal.TxHash, tokenInfo.ContractAddress)
			logger.Debugf(ctx, "[TRON Tx: %s] Looking for recipient address: %s", withdrawal.TxHash, withdrawal.ToAddress)
			logger.Debugf(ctx, "[TRON Tx: %s] Expected Transfer event signature: %s", withdrawal.TxHash, eventSigHex)

			for i, logEntry := range txInfo.GetLog() {
				logContractAddrBytes := logEntry.GetAddress()
				// Need to add 0x41 prefix for mainnet TRON addresses
				fullContractAddrBytes := append([]byte{0x41}, logContractAddrBytes...)
				logContractAddrBase58 := troncommon.EncodeCheck(fullContractAddrBytes)
				logger.Debugf(ctx, "[TRON Tx: %s] Log[%d] contract address: %s (matches expected: %v)",
					withdrawal.TxHash, i, logContractAddrBase58, logContractAddrBase58 == tokenInfo.ContractAddress)

				if len(logEntry.GetTopics()) > 0 {
					topic0SigFromLog := hex.EncodeToString(logEntry.GetTopics()[0])
					logger.Debugf(ctx, "[TRON Tx: %s] Log[%d] topic[0] signature: %s (matches Transfer: %v)",
						withdrawal.TxHash, i, topic0SigFromLog, topic0SigFromLog == eventSigHex)
				}

				if len(logEntry.GetTopics()) >= 3 {
					// Also log topic[1] which should be the 'from' address
					if len(logEntry.GetTopics()) > 1 {
						topicFromAddressRawBytes := logEntry.GetTopics()[1]
						var fullFromAddrBytes [21]byte
						fullFromAddrBytes[0] = byte(0x41)
						copy(fullFromAddrBytes[1:], topicFromAddressRawBytes[12:])
						loggedFromAddressBase58 := troncommon.EncodeCheck(fullFromAddrBytes[:])
						logger.Debugf(ctx, "[TRON Tx: %s] Log[%d] 'from' address from topic[1]: %s",
							withdrawal.TxHash, i, loggedFromAddressBase58)
					}

					topicToAddressRawBytes := logEntry.GetTopics()[2]
					var fullAddrBytesForComparison [21]byte
					fullAddrBytesForComparison[0] = byte(0x41)
					copy(fullAddrBytesForComparison[1:], topicToAddressRawBytes[12:])
					loggedToAddressBase58 := troncommon.EncodeCheck(fullAddrBytesForComparison[:])
					logger.Debugf(ctx, "[TRON Tx: %s] Log[%d] 'to' address from topic[2]: %s (matches expected: %v)",
						withdrawal.TxHash, i, loggedToAddressBase58, loggedToAddressBase58 == withdrawal.ToAddress)
				}
			}

			return decimal.Zero, fee, fmt.Errorf("TRC20 transfer to %s for token %s not found in transaction logs", withdrawal.ToAddress, withdrawal.Name)
		}
	}
	return amount, fee, nil
}
