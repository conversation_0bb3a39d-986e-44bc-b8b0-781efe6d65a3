package withdraw_step4_confirm

import (
	"context"
	"fmt"

	"wallet-api/internal/model/entity"
	"wallet-api/internal/task/deposit_step2_confirm"

	"github.com/gogf/gf/v2/frame/g"
)

// checkTransactionConfirmation checks if a transaction is confirmed on the blockchain.
// This function relies on GetTransactionConfirmations from deposit_step2_confirm and getRequiredConfirmations.
func checkTransactionConfirmation(ctx context.Context, cfg *ConfirmationHandlerConfig, withdrawal *entity.UserWithdraws, logPrefix string) (bool, error) {
	logger := g.Log()

	requiredConfirmations, err := getRequiredConfirmations(ctx, cfg, withdrawal.Chan, withdrawal.Name)
	if err != nil {
		// Log warning but proceed with default, as per original logic
		logger.Warningf(ctx, "%s Failed to get required confirmations: %v. Using default value: %d",
			logPrefix, err, cfg.DefaultConfirmations)
		requiredConfirmations = uint64(cfg.DefaultConfirmations)
	}
	if requiredConfirmations == 0 { // A safeguard if default is 0 and no specific config found
		requiredConfirmations = 1 // At least 1 confirmation needed
		logger.Warningf(ctx, "%s Required confirmations was 0, defaulting to 1.", logPrefix)
	}

	// Get current confirmations from blockchain using the function from deposit task
	// Ensure deposit_step2_confirm.GetTransactionConfirmations is suitable for withdrawals too.
	confirmations, err := deposit_step2_confirm.GetTransactionConfirmations(ctx, withdrawal.Chan, withdrawal.TxHash)
	if err != nil {
		return false, fmt.Errorf("failed to get transaction confirmations from chain for tx %s: %w", withdrawal.TxHash, err)
	}

	logger.Infof(ctx, "%s Transaction has %d confirmations (required: %d)",
		logPrefix, confirmations, requiredConfirmations)

	return confirmations >= requiredConfirmations, nil
}

// getRequiredConfirmations gets the required confirmations for a chain/token.
// This function reads from configuration like "withdrawStep4Processing.chains.ETH.confirmations".
func getRequiredConfirmations(ctx context.Context, cfgGlobal *ConfirmationHandlerConfig, chain string, token string) (uint64, error) {
	// Configuration path for chain-specific confirmations
	// Example: "withdrawStep4Processing.chains.ETH.confirmations"
	// The 'token' parameter is not used in this specific config lookup in the original code,
	// but could be used for token-specific overrides if desired.
	configPrefix := fmt.Sprintf("withdrawStep4Processing.chains.%s.confirmations", chain)
	confirmationsVar, err := g.Cfg().Get(ctx, configPrefix)
	if err != nil {
		// This error means the key path itself is invalid or Cfg() is not working.
		// However, g.Cfg().Get() returns a nil error if key not found, but confirmationsVar.IsEmpty() will be true.
		g.Log().Debugf(ctx, "Error accessing config key %s: %v. Will use default.", configPrefix, err)
	}

	if !confirmationsVar.IsEmpty() {
		return uint64(confirmationsVar.Int()), nil
	}

	// If chain-specific config is not found or empty, use the global default from ConfirmationHandlerConfig
	// This global default was loaded by GetConfig() at the start of ProcessWithdrawalConfirmations.
	// The original code had another lookup here for "withdrawStep4Processing.defaultConfirmations",
	// but it's cleaner to use the already loaded cfgGlobal.DefaultConfirmations.
	if cfgGlobal.DefaultConfirmations > 0 {
		return uint64(cfgGlobal.DefaultConfirmations), nil
	}

	// Fallback to hardcoded defaults ONLY if no configuration whatsoever (including global default) is found or is zero.
	// This part is from the original code and can be a last resort.
	g.Log().Warningf(ctx, "No specific or default confirmations found in config for chain %s. Falling back to hardcoded chain defaults.", chain)
	switch chain {
	case "ETH":
		return 12, nil // A more common default for ETH
	case "TRON":
		return 19, nil // TRON is typically faster to confirm, but this is for irreversibility.
	default:
		// If it's an unknown chain and no default was configured, it's safer to error or use a high default.
		return 0, fmt.Errorf("unsupported chain for confirmations: %s, and no default configured", chain)
	}
}
