package withdraw_step4_confirm

import (
	"context"
	"fmt"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// ConfirmationHandlerConfig holds the configuration for the confirmation handler task
type ConfirmationHandlerConfig struct {
	Enabled              bool            `json:"enabled"`              // Whether the task is enabled
	Spec                 string          `json:"spec"`                 // Cron schedule expression
	BatchSize            int             `json:"batchSize"`            // Number of records to process in one batch
	MaxRetries           int             `json:"maxRetries"`           // Maximum number of retries before marking as failed
	DefaultConfirmations int             `json:"defaultConfirmations"` // Default number of confirmations required
	MinWaitMinutes       int             `json:"minWaitMinutes"`       // Minimum minutes to wait before checking transaction (default: 1)
	WalletConfig         *entity.Wallets `json:"walletConfig"`         // Wallet configuration
}

// GetConfig retrieves the configuration for the confirmation handler task
func GetConfig(ctx context.Context) (*ConfirmationHandlerConfig, error) {
	var walletConfig *entity.Wallets
	err := dao.Wallets.Ctx(ctx).Where("id", 1).Scan(&walletConfig)
	if err != nil {
		return nil, fmt.Errorf("wallet config not found: %w", err)
	}

	// Configuration prefix for this task
	configPrefix := "withdrawStep4Processing"

	// Create configuration with default values
	config := &ConfirmationHandlerConfig{
		Enabled:              g.Cfg().MustGet(ctx, configPrefix+".enabled", false).Bool(),
		Spec:                 g.Cfg().MustGet(ctx, configPrefix+".spec", "*/10 * * * *").String(), // Default: every 10 seconds
		BatchSize:            g.Cfg().MustGet(ctx, configPrefix+".batchSize", 100).Int(),
		MaxRetries:           g.Cfg().MustGet(ctx, configPrefix+".maxRetries", 30).Int(),           // Default: 30 retries
		DefaultConfirmations: g.Cfg().MustGet(ctx, configPrefix+".defaultConfirmations", 12).Int(), // Default: 12 confirmations
		MinWaitMinutes:       g.Cfg().MustGet(ctx, configPrefix+".minWaitMinutes", 1).Int(),        // Default: 1 minute
		WalletConfig:         walletConfig,
	}

	logger := g.Log()

	// Log configuration summary
	logger.Infof(ctx, "Withdrawal confirmation configuration loaded: enabled=%v, batchSize=%d, maxRetries=%d, defaultConfirmations=%d, minWaitMinutes=%d",
		config.Enabled, config.BatchSize, config.MaxRetries, config.DefaultConfirmations, config.MinWaitMinutes)

	return config, nil
}
