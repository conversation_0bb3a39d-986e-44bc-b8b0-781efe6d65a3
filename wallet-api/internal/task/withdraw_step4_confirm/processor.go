package withdraw_step4_confirm

import (
	"context"
	"fmt"
	"strings"
	"time"

	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"
	"wallet-api/internal/utility/utils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ProcessWithdrawalConfirmations processes withdrawals with state = 2 (Processing)
// and checks if their transactions are confirmed on the blockchain, then updates details.
func ProcessWithdrawalConfirmations(ctx context.Context) {
	logger := g.Log()
	logger.Infof(ctx, "%s Task started", LogPrefixProcessor)
	startTime := time.Now()

	cfg, err := GetConfig(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s Failed to load configuration: %v. Task cannot proceed.", LogPrefixProcessor, err)
		return
	}

	if !cfg.Enabled {
		logger.Infof(ctx, "%s Task is disabled in configuration. Exiting.", LogPrefixProcessor)
		return
	}

	// Calculate minimum time threshold for processing (skip recent transactions)
	minProcessTime := gtime.Now().Add(-time.Duration(cfg.MinWaitMinutes) * time.Minute)

	var processingWithdrawals []*entity.UserWithdraws
	err = dao.UserWithdraws.Ctx(ctx).
		Where(dao.UserWithdraws.Columns().State, WithdrawalStateProcessing).
		WhereNotNull(dao.UserWithdraws.Columns().TxHash).                     // Ensure TxHash is not null
		Where(dao.UserWithdraws.Columns().TxHash+" !=", "").                  // Ensure TxHash is not an empty string
		Where(dao.UserWithdraws.Columns().UpdatedAt+" <= ?", minProcessTime). // Only process transactions older than minWaitMinutes
		Limit(cfg.BatchSize).
		OrderAsc(dao.UserWithdraws.Columns().UserWithdrawsId). // Process oldest first
		Scan(&processingWithdrawals)

	if err != nil {
		logger.Errorf(ctx, "%s Failed to fetch processing withdrawals: %v", LogPrefixProcessor, err)
		return
	}

	if len(processingWithdrawals) == 0 {
		logger.Infof(ctx, "%s No withdrawals with state=2 and non-empty TxHash found to process (excluding transactions newer than %d minutes).", LogPrefixProcessor, cfg.MinWaitMinutes)
		logger.Infof(ctx, "%s Task finished. Duration: %s", LogPrefixProcessor, time.Since(startTime))
		return
	}

	logger.Infof(ctx, "%s Found %d withdrawals with state=2 to check (excluding transactions newer than %d minutes)", LogPrefixProcessor, len(processingWithdrawals), cfg.MinWaitMinutes)

	processedCount := 0
	completedCount := 0
	failedCount := 0
	skippedNoUpdateNeeded := 0

	for _, withdrawal := range processingWithdrawals {
		recordLogPrefix := fmt.Sprintf("%s[ID:%d|Chain:%s|Token:%s|Tx:%s]",
			LogPrefixProcessor,
			withdrawal.UserWithdrawsId,
			withdrawal.Chan,
			withdrawal.Name,
			withdrawal.TxHash)

		// Double-check time constraint (additional safety check)
		timeSinceUpdate := gtime.Now().Sub(withdrawal.UpdatedAt)
		if timeSinceUpdate < time.Duration(cfg.MinWaitMinutes)*time.Minute {
			logger.Debugf(ctx, "%s Skipping withdrawal - too recent (updated %v ago, minimum wait: %d minutes)",
				recordLogPrefix, timeSinceUpdate, cfg.MinWaitMinutes)
			skippedNoUpdateNeeded++
			continue
		}

		logger.Debugf(ctx, "%s Processing withdrawal: State=%d, Retries=%d, UpdatedAt=%s",
			recordLogPrefix, withdrawal.State, withdrawal.Retries, withdrawal.UpdatedAt.Format("2006-01-02 15:04:05"))

		// Check transaction confirmation status using existing logic
		// `checkTransactionConfirmation` depends on `getRequiredConfirmations` and `deposit_step2_confirm.GetTransactionConfirmations`
		confirmed, errConfirm := checkTransactionConfirmation(ctx, cfg, withdrawal, recordLogPrefix)
		if errConfirm != nil {
			// Check if this is a transaction failure error (not just a temporary network issue)
			isTransactionFailed := isTransactionFailureError(errConfirm)
			if isTransactionFailed {
				logger.Warningf(ctx, "%s Transaction explicitly failed on blockchain: %v. Marking as failed.", recordLogPrefix, errConfirm)
				// Mark transaction as failed immediately
				updateFields := g.Map{
					dao.UserWithdraws.Columns().State:        WithdrawalStateFailed,
					dao.UserWithdraws.Columns().CompletedAt:  gtime.Now(),
					dao.UserWithdraws.Columns().UpdatedAt:    gtime.Now(),
					dao.UserWithdraws.Columns().ErrorMessage: utils.BuildErrorMessageJson(ctx, withdrawal.ErrorMessage, fmt.Sprintf("Transaction failed on blockchain: %v", errConfirm)),
				}

				result, errUpdate := dao.UserWithdraws.Ctx(ctx).
					Data(updateFields).
					Where(dao.UserWithdraws.Columns().UserWithdrawsId, withdrawal.UserWithdrawsId).
					Update()
				if errUpdate != nil {
					logger.Errorf(ctx, "%s Error updating failed withdrawal record: %v", recordLogPrefix, errUpdate)
				} else {
					rowsAffected, _ := result.RowsAffected()
					logger.Infof(ctx, "%s Successfully marked withdrawal as failed due to blockchain transaction failure. Rows affected: %d", recordLogPrefix, rowsAffected)
					if rowsAffected == 0 {
						logger.Warningf(ctx, "%s No rows were updated - record may not exist or condition didn't match", recordLogPrefix)
					}
					failedCount++
					processedCount++
				}
				continue
			} else {
				logger.Debugf(ctx, "%s Error checking transaction confirmation: %v. Skipping update for this record.", recordLogPrefix, errConfirm)
				// This is likely a temporary error (network issue, node sync, etc.), skip for now
				continue
			}
		}

		updateFields := g.Map{
			dao.UserWithdraws.Columns().UpdatedAt: gtime.Now(),
		}
		needsDBUpdate := false
		currentErrorMessage := withdrawal.ErrorMessage // Preserve existing errors

		if confirmed {
			updateFields[dao.UserWithdraws.Columns().State] = WithdrawalStateCompleted
			updateFields[dao.UserWithdraws.Columns().CompletedAt] = gtime.Now()
			needsDBUpdate = true

			// Get transaction details (amount, fee) from the chain
			txChainAmount, txChainFee, errDetails := getTransactionDetailsFromChain(ctx, withdrawal, recordLogPrefix)

			if errDetails != nil {
				logger.Errorf(ctx, "%s Error getting transaction details from chain: %v. Withdrawal amount/fee will not be updated from chain data.", recordLogPrefix, errDetails)
				errorDetailMsg := fmt.Sprintf("Chain detail fetch error: %v", errDetails)
				currentErrorMessage = utils.BuildErrorMessageJson(ctx, currentErrorMessage, errorDetailMsg)
				// NOTE: Even if fetching details fails, we still mark as Completed because the transaction *is* confirmed.
				// The error message will reflect the detail fetching issue.
			} else {
				logger.Infof(ctx, "%s Successfully fetched transaction details from chain. Amount: %s, Fee: %s", recordLogPrefix, txChainAmount.String(), txChainFee.String())
				// Update 'amount' (DECIMAL(36,8)) and 'handling_fee' (DECIMAL(36,18)) in the database
				// Using StringFixed to ensure correct precision for DECIMAL types when passed as strings.
				updateFields[dao.UserWithdraws.Columns().Amount] = txChainAmount.StringFixed(8)
				updateFields[dao.UserWithdraws.Columns().HandlingFee] = txChainFee.StringFixed(18)
			}

			if currentErrorMessage == "" || currentErrorMessage == "null" { // If no prior errors and no new errors from detail fetching
				updateFields[dao.UserWithdraws.Columns().ErrorMessage] = "[]" // Set to empty JSON array
			} else {
				updateFields[dao.UserWithdraws.Columns().ErrorMessage] = currentErrorMessage
			}

			logger.Infof(ctx, "%s Transaction confirmed. Marking as completed.", recordLogPrefix)
			completedCount++
		} else { // Not confirmed yet
			if withdrawal.Retries >= cfg.MaxRetries {
				updateFields[dao.UserWithdraws.Columns().State] = WithdrawalStateFailed
				updateFields[dao.UserWithdraws.Columns().CompletedAt] = gtime.Now() // Or a specific FailedAt timestamp
				newErrorMsg := "Transaction confirmation timed out after maximum retries"
				updateFields[dao.UserWithdraws.Columns().ErrorMessage] = utils.BuildErrorMessageJson(ctx, currentErrorMessage, newErrorMsg)
				logger.Warningf(ctx, "%s Max retries (%d) exceeded. Marking as failed. Error: %s", recordLogPrefix, cfg.MaxRetries, newErrorMsg)
				failedCount++
				needsDBUpdate = true
			} else {
				updateFields[dao.UserWithdraws.Columns().Retries] = withdrawal.Retries + 1
				logger.Infof(ctx, "%s Transaction not yet confirmed. Incrementing retry counter to %d.", recordLogPrefix, withdrawal.Retries+1)
				needsDBUpdate = true
			}
		}

		if needsDBUpdate {
			_, errUpdate := dao.UserWithdraws.Ctx(ctx).
				Data(updateFields).
				Where(dao.UserWithdraws.Columns().UserWithdrawsId, withdrawal.UserWithdrawsId).
				Update()
			if errUpdate != nil {
				logger.Errorf(ctx, "%s Error updating withdrawal record: %v", recordLogPrefix, errUpdate)
				// If update fails, counts might not reflect actual DB state for this item.
				// Consider how to handle this, e.g., requeue or mark with specific error.
				continue // Continue to the next withdrawal record
			}
			processedCount++ // Count as processed if an update attempt was made and successful.
		} else {
			skippedNoUpdateNeeded++ // Count if no DB update was deemed necessary for this record in this iteration.
		}
	}

	logger.Infof(ctx, "%s Task finished. DB Updates Attempted: %d, Completed: %d, Failed (Timeout): %d, Skipped (Too Recent/No Update Needed): %d. Duration: %s",
		LogPrefixProcessor, processedCount, completedCount, failedCount, skippedNoUpdateNeeded, time.Since(startTime))
}

// isTransactionFailureError determines if an error indicates that a transaction has explicitly failed on the blockchain
// rather than being a temporary network/node issue that should be retried.
func isTransactionFailureError(err error) bool {
	if err == nil {
		return false
	}

	errorMsg := err.Error()

	// TRON specific failure indicators
	if strings.Contains(errorMsg, "transaction failed on TRON chain") {
		return true
	}
	if strings.Contains(errorMsg, "OUT_OF_ENERGY") {
		return true
	}
	if strings.Contains(errorMsg, "OUT_OF_TIME") {
		return true
	}
	if strings.Contains(errorMsg, "contract execution failed") {
		return true
	}

	// ETH specific failure indicators
	if strings.Contains(errorMsg, "transaction failed on-chain") {
		return true
	}
	if strings.Contains(errorMsg, "receipt status 0") {
		return true
	}

	// General blockchain failure indicators
	if strings.Contains(errorMsg, "transaction reverted") {
		return true
	}
	if strings.Contains(errorMsg, "execution reverted") {
		return true
	}

	return false
}
