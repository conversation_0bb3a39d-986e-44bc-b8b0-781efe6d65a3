package withdraw_step4_confirm

import (
	"context"
	"fmt"

	"wallet-api/internal/model/entity"
	"wallet-api/internal/utility/crypto"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// getTransactionDetails<PERSON>rom<PERSON>hain is a wrapper to call chain-specific detail fetchers.
func getTransactionDetailsFromChain(ctx context.Context, withdrawal *entity.UserWithdraws, recordLogPrefix string) (amount decimal.Decimal, fee decimal.Decimal, err error) {
	logger := g.Log()
	cm := crypto.GetInstance() // Get singleton instance of ClientManager

	switch withdrawal.Chan {
	case "ETH":
		client, errClient := cm.GetDefaultEthClient(ctx)
		if errClient != nil {
			return decimal.Zero, decimal.Zero, fmt.Errorf("failed to get default ETH client: %w", errClient)
		}
		logger.Debugf(ctx, "%s Fetching ETH tx details for hash: %s, token: %s, to: %s", recordLogPrefix, withdrawal.TxHash, withdrawal.Name, withdrawal.ToAddress)
		return getEthTransactionDetails(ctx, client, withdrawal)
	case "TRON":
		client, errClient := cm.GetDefaultTronClient(ctx)
		if errClient != nil {
			return decimal.Zero, decimal.Zero, fmt.Errorf("failed to get default TRON client: %w", errClient)
		}
		logger.Debugf(ctx, "%s Fetching TRON tx details for hash: %s, token: %s, to: %s", recordLogPrefix, withdrawal.TxHash, withdrawal.Name, withdrawal.ToAddress)
		return getTronTransactionDetails(ctx, client, withdrawal)
	default:
		return decimal.Zero, decimal.Zero, fmt.Errorf("unsupported chain '%s' for getting tx details", withdrawal.Chan)
	}
}
