package withdraw_step4_confirm

import (
	"context"
	"fmt"
	"math/big"

	"wallet-api/internal/model/entity"

	"github.com/ethereum/go-ethereum/common"
	ethtypes "github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// getEthTransactionDetails fetches amount and fee for an Ethereum transaction.
func getEthTransactionDetails(ctx context.Context, ethSdkClient *ethclient.Client, withdrawal *entity.UserWithdraws) (amount decimal.Decimal, fee decimal.Decimal, err error) {
	logger := g.Log()
	txHash := common.HexToHash(withdrawal.TxHash)

	tx, isPending, err := ethSdkClient.TransactionByHash(ctx, txHash)
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to get transaction by hash %s: %w", withdrawal.TxHash, err)
	}
	if isPending {
		// This state should ideally be caught by confirmation checks, but good to have.
		return decimal.Zero, decimal.Zero, fmt.Errorf("transaction %s is still pending, cannot fetch details yet", withdrawal.TxHash)
	}

	receipt, err := ethSdkClient.TransactionReceipt(ctx, txHash)
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to get transaction receipt for %s: %w", withdrawal.TxHash, err)
	}
	if receipt.Status == ethtypes.ReceiptStatusFailed {
		// If tx failed on chain, there might be a fee, but the transfer amount is effectively zero or reverted.
		// Calculate fee anyway as it was consumed.
		gasUsed := decimal.NewFromBigInt(new(big.Int).SetUint64(receipt.GasUsed), 0)
		gasPrice := decimal.NewFromBigInt(tx.GasPrice(), 0)
		txFeeWei := gasUsed.Mul(gasPrice)
		fee = txFeeWei.Div(decimal.New(1, 18)) // Convert Wei to ETH
		return decimal.Zero, fee, fmt.Errorf("transaction %s failed on-chain (receipt status 0)", withdrawal.TxHash)
	}

	// Calculate fee
	gasUsed := decimal.NewFromBigInt(new(big.Int).SetUint64(receipt.GasUsed), 0)
	gasPrice := decimal.NewFromBigInt(tx.GasPrice(), 0)
	txFeeWei := gasUsed.Mul(gasPrice)
	fee = txFeeWei.Div(decimal.New(1, 18)) // Convert Wei to ETH (1e18)

	tokenInfo, err := GetTokenInfo(ctx, withdrawal.Chan, withdrawal.Name)
	if err != nil {
		return decimal.Zero, fee, fmt.Errorf("failed to get token info for %s on %s: %w", withdrawal.Name, withdrawal.Chan, err)
	}

	if withdrawal.Name == "ETH" { // Native ETH transfer
		// For native ETH, tx.Value() is the amount transferred.
		// We assume the withdrawal.ToAddress is the recipient of this value.
		// A more robust check could be `if tx.To() != nil && tx.To().Hex() == common.HexToAddress(withdrawal.ToAddress).Hex()`
		amount = decimal.NewFromBigInt(tx.Value(), 0).Div(decimal.New(1, int32(tokenInfo.Decimals)))
	} else { // ERC20 Token transfer
		logTransferSigHash := common.HexToHash(ERC20TransferEventSignature)
		var foundTokenTransfer bool
		for _, vLog := range receipt.Logs {
			// Check:
			// 1. Log is from the correct token contract address.
			// 2. Log has 3 topics (event signature, indexed from, indexed to).
			// 3. Topic0 matches the Transfer event signature.
			if vLog.Address == common.HexToAddress(tokenInfo.ContractAddress) &&
				len(vLog.Topics) == 3 && vLog.Topics[0] == logTransferSigHash {

				// Topics[2] is the recipient address (indexed).
				recipientAddressFromLog := common.BytesToAddress(vLog.Topics[2].Bytes())

				if recipientAddressFromLog == common.HexToAddress(withdrawal.ToAddress) {
					// vLog.Data contains the non-indexed 'value' parameter.
					tokenValue := new(big.Int).SetBytes(vLog.Data)
					amount = decimal.NewFromBigInt(tokenValue, 0).Div(decimal.New(1, int32(tokenInfo.Decimals)))
					foundTokenTransfer = true
					break
				}
			}
		}
		if !foundTokenTransfer {
			logger.Warningf(ctx, "[ETH Tx: %s] ERC20 Transfer event to %s for token %s (contract: %s) not found in logs.",
				withdrawal.TxHash, withdrawal.ToAddress, withdrawal.Name, tokenInfo.ContractAddress)
			// Return the fee, but amount is zero as the specific transfer was not found.
			return decimal.Zero, fee, fmt.Errorf("ERC20 transfer to %s for token %s not found in transaction logs", withdrawal.ToAddress, withdrawal.Name)
		}
	}
	return amount, fee, nil
}
