package withdraw_step4_confirm

import (
	"context"
	
	"github.com/gogf/gf/v2/frame/g"
	util "wallet-api/internal/utility/utils"
)

type TokenInfo struct {
	ContractAddress string
	Decimals        int
}

// GetTokenInfo retrieves contract address and decimals for a given token on a chain.
// It reads from the application's configuration.
// Ensure entity.TokenInfo is defined (e.g., in wallet-api/internal/model/entity/token_info.go)
/*
   Example entity.TokenInfo in wallet-api/internal/model/entity/token_info.go:
   package entity
   type TokenInfo struct {
       ContractAddress string
       Decimals        int
   }
*/
func GetTokenInfo(ctx context.Context, chain string, tokenName string) (TokenInfo, error) {
	// Handle native coins explicitly as they don't have a contract address in the same way
	// and their decimals are well-known.
	if chain == "ETH" && tokenName == "ETH" {

		ethDecimals, err := util.GetTokenDecimals(ctx, "ETH")
		if err != nil {
			return TokenInfo{}, err
		}

		return TokenInfo{ContractAddress: "", Decimals: ethDecimals}, nil
	}
	if chain == "TRON" && tokenName == "TRX" {
		trxDecimals, err := util.GetTokenDecimals(ctx, "TRON")
		if err != nil {
			return TokenInfo{}, err
		}
		return TokenInfo{ContractAddress: "", Decimals: trxDecimals}, nil
	}

	var contractAddressVal string

	var ethDecimals int
	var err error
	if chain == "TRON" && tokenName == "USDT" {
		contractAddressVal = g.Cfg().MustGet(ctx, "usdt_trc20_contract").String()
		ethDecimals, err = util.GetTokenDecimals(ctx, "ERC20USDT")
		if err != nil {
			return TokenInfo{}, err
		}
	}
	if chain == "ETH" && tokenName == "USDT" {
		contractAddressVal = g.Cfg().MustGet(ctx, "usdt_erc20_contract").String()
		ethDecimals, err = util.GetTokenDecimals(ctx, "ERC20USDT") // Corrected: Was TRC20USDT
		if err != nil {
			return TokenInfo{}, err
		}
	}

	return TokenInfo{ContractAddress: contractAddressVal, Decimals: ethDecimals}, nil
}
