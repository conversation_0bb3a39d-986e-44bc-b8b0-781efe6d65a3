package withdraw_step4_confirm

const (
	// Withdrawal states
	WithdrawalStatePending    = 1 // Pending user confirmation / initial state
	WithdrawalStateProcessing = 2 // Processing (sent to blockchain)
	WithdrawalStateRejected   = 3 // Rejected by admin
	WithdrawalStateCompleted  = 4 // Completed successfully
	WithdrawalStateFailed     = 5 // Failed to process

	// Log prefixes for consistent logging
	LogPrefixProcessor = "[WithdrawStep4Processor]"

	// TRC20TransferSignature is the function selector for transfer(address,uint256)
	TRC20TransferSignature = "a9059cbb"
	// ERC20TransferEventSignature is the Keccak256 hash of "Transfer(address,address,uint256)"
	ERC20TransferEventSignature = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"
)
