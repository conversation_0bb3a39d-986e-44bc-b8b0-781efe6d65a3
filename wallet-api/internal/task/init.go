package task

import (
	// 导入各个任务模块，以便执行它们各自的 init() 函数进行任务注册
	// _ "wallet-api/internal/task/balance_collector"         // 余额收集器
	_ "wallet-api/internal/task/deposit_step1_check"       // 存款第一步检查
	_ "wallet-api/internal/task/deposit_step2_confirm"     // 存款第二步确认
	_ "wallet-api/internal/task/withdraw_step1_plan"       // 提款第一步计划处理器
	_ "wallet-api/internal/task/withdraw_step2_fee"        // 提款第三步手续费处理器
	_ "wallet-api/internal/task/withdraw_step3_processing" // 统一提现处理服务
	_ "wallet-api/internal/task/withdraw_step4_confirm"    // 提现确认服务
)
