package withdraw_step2_fee

import (
	"context"
	"fmt"
	"time"
	"wallet-api/internal/model/entity"
	"wallet-api/internal/utility/crypto/tron"
	tronwallet "wallet-api/internal/utility/crypto/tron/tron-wallet"

	// Assuming this utility is compatible

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// EnergyRentalResponse represents the response from the energy rental API
type EnergyRentalResponse struct {
	Serial string `json:"serial"` // Order ID
	Amount int    `json:"amount"` // Energy amount
}

// TronEnergyProcessingDetails holds structured information about TRON energy processing.
type TronEnergyProcessingDetails struct {
	Action       string `json:"action"` // Describes the action taken, e.g., "energy_sufficient", "energy_requirement_updated", "energy_rented"
	Message      string `json:"message"`
	OrderID      string `json:"orderId,omitempty"`      // Order ID if energy was rented.
	EnergyAmount int    `json:"energyAmount,omitempty"` // Energy amount required or rented.
}

// processTronEnergy processes a TRON energy supplement
func processTrc20UsdtEnergy(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}

	TRON_CONFIG := cfg.GasFee.TRON

	grpcNodeURL := TRON_CONFIG.GRPCNodeURL
	grpcApiKey := TRON_CONFIG.GRPCApiKey
	itrxApiKey := TRON_CONFIG.ApiKey
	itrxApiBaseUrl := TRON_CONFIG.ApiBaseUrl
	privateKey := TRON_CONFIG.PrivateKey

	if len(grpcNodeURL) <= 0 || len(grpcApiKey) <= 0 || len(itrxApiKey) <= 0 || len(itrxApiBaseUrl) <= 0 || len(privateKey) <= 0 {
		errMsg := fmt.Sprintf("TRON configuration missing in withdrawFeeHandler.gasFee.TRON, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	// get iTRX platform data
	platformData, err := tron.GetItrxPlatformData(ctx, itrxApiKey, itrxApiBaseUrl)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get iTRX platform data: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	//1.判断是否激活
	isActive, activeCheckErr := tron.IsAddressActive(ctx, supplement.Address, grpcNodeURL, grpcApiKey)
	if activeCheckErr != nil {
		errMsg := fmt.Sprintf("Failed to check if TRON address %s is active: %v", supplement.Address, activeCheckErr)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	// isActive = false //测试

	//没有激活开始激活打币交易 然后退出保存数据
	if !isActive {

		activateAmount := decimal.NewFromFloat(cfg.WalletConfig.TrxActivateAmount)

		if activateAmount.LessThanOrEqual(decimal.Zero) {
			errMsg := fmt.Sprintf("TRON activation amount is invalid or zero: %s, logPrefix: %s", activateAmount.String(), logPrefix)
			logger.Error(ctx, errMsg)
			return nil, fmt.Errorf(errMsg)
		}

		tx, err := tron.SendTrxTransaction(ctx, privateKey,
			cfg.WalletConfig.TrxFeeAddress, supplement.Address, activateAmount, logPrefix)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to send TRX transaction for activation: %v", err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}

		data.ActivateHash = tx
		data.ActivateAmount = activateAmount.InexactFloat64()
		data.IsActivating = 1

		return data, nil

	} else {
		//2.激活后 开始能量逻辑
		data.UpdatedAt = gtime.Now()
		data.IsActivating = 2
	}
	logger.Info(ctx, fmt.Sprintf("%s TRON address %s is active.", logPrefix, supplement.Address))

	// 首先检查带宽，如果带宽充足则无需补充TRX
	// 如果原有配置为空，尝试使用depositCheck配置中的TRON RPC配置
	if grpcNodeURL == "" {
		grpcNodeURL = g.Cfg().MustGet(ctx, "depositCheck.chains.TRON.rpc", "").String()
	}
	if grpcApiKey == "" {
		grpcApiKey = g.Cfg().MustGet(ctx, "depositCheck.chains.TRON.apiKey", "").String()
	}

	bandwidth, err := tron.GetAccountBandwidth(ctx, supplement.Address, grpcNodeURL, grpcApiKey)
	if err != nil {
		// 如果获取带宽失败，记录错误但继续检查TRX余额
		logger.Warning(ctx, fmt.Sprintf("%s failed to get bandwidth for address %s: %v, will check TRX balance", logPrefix, supplement.Address, err))
		//报错退出
		return nil, fmt.Errorf("failed to get bandwidth for address %s: %v", supplement.Address, err)

	}

	if bandwidth >= cfg.WalletConfig.Trc20MinRequiredBandwidth {
		// 带宽充足，直接标记为成功，无需补充TRX
		logger.Info(ctx, fmt.Sprintf("%s Bandwidth %d is sufficient (>= %d), marking TRX supplement as success without transfer", logPrefix, bandwidth, cfg.WalletConfig.Trc20MinRequiredBandwidth))
		data.TrxSupplementNeeded = 3
		data.TrxSupplementStatus = "success"
		data.UpdatedAt = gtime.Now()
		// 继续进行能量检查逻辑，不返回
	} else {
		// 带宽不足，需要检查TRX余额
		logger.Info(ctx, fmt.Sprintf("%s Bandwidth %d is insufficient (< %d), checking TRX balance", logPrefix, bandwidth, cfg.WalletConfig.Trc20MinRequiredBandwidth))

		// 先获取当前地址的USDT余额
		currentUsdtBalance, err := tron.GetTRC20UsdtTokenBalance(ctx, supplement.Address)
		if err != nil {
			errMsg := fmt.Sprintf("failed to get USDT balance for address %s: %v", supplement.Address, err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}

		// 检查USDT余额是否低于触发费用阈值 到此处说明 1 小额充值，2免费带宽不够
		triggerFeeThreshold := decimal.NewFromFloat(cfg.WalletConfig.Trc20TriggerFeeAmount)
		if currentUsdtBalance.LessThan(triggerFeeThreshold) {
			errMsg := fmt.Sprintf("USDT balance %s is below trigger fee threshold %s, cannot proceed with TRX supplement",
				currentUsdtBalance.String(), triggerFeeThreshold.String())
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}

		//获取 trx 余额
		currentTrxBalance, err := tron.GetTRXBalance(ctx, supplement.Address)
		if err != nil {
			errMsg := fmt.Sprintf("failed to get TRX balance for address %s: %v", supplement.Address, err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}

		//转账旷工费金额
		minTrxRequired := decimal.NewFromFloat(cfg.WalletConfig.TrxFeeAmount)
		//有最低限制低于 0.5 进行报错
		if minTrxRequired.LessThanOrEqual(decimal.NewFromFloat(0.5)) {
			errMsg := fmt.Sprintf("Failed TrxFeeAmount: %v", err)
			logger.Error(ctx, fmt.Sprintf("%s  %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}

		//如果余额低于最小的金额进行标记 标记之后此处结束
		if currentTrxBalance.LessThan(minTrxRequired) {
			logger.Info(ctx, fmt.Sprintf("%s TRX balance %s is insufficient (< %s TRX), marking for TRX supplement", logPrefix, currentTrxBalance.String(), minTrxRequired.String()))
			// 标记需要TRX补充
			data.TrxSupplementNeeded = 1
			data.TrxSupplementStatus = "pending"
			data.TrxBalanceBefore = currentTrxBalance.InexactFloat64()
			data.UpdatedAt = gtime.Now()
			return data, nil
		}

		logger.Info(ctx, fmt.Sprintf("%s TRX balance %s is sufficient (>= %s TRX), proceeding with energy logic", logPrefix, currentTrxBalance.String(), minTrxRequired.String()))
	}

	//获取需要的能量费用
	requiredEnergy := platformData.UsdtEnergyNeedOld
	logger.Info(ctx, fmt.Sprintf("%s Required energy for USDT transfer: %d", logPrefix, requiredEnergy))
	logger.Info(ctx, fmt.Sprintf("%s [LOG_DEBUG] Initial requiredEnergy from platformData.UsdtEnergyNeedOld: %d", logPrefix, requiredEnergy))

	//获取账户内的能量
	energy, err := tron.GetAccountEnergy(ctx, supplement.Address, grpcNodeURL, grpcApiKey)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get TRON energy: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	var sunConsumedDecimal = decimal.NewFromInt(0)
	// 能量不足 需要租用能量
	if energy <= int64(requiredEnergy) {
		logger.Info(ctx, fmt.Sprintf("%s TRON energy is insufficient: %d, need %d", logPrefix, energy, requiredEnergy))
		err = tron.ValidateItrxPlatformData(platformData, requiredEnergy)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to validate platform data for energy rental: %v", err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}

		configPrefix := "withdrawFeeHandler.gasFee.TRON"

		// trc20_max_energy_fee
		trc20MaxEnergyFee := decimal.NewFromFloat(cfg.WalletConfig.Trc20MaxEnergyFee)

		rentResponse, rentErr := tron.RentEnergyWithConfig(ctx, supplement.Address, requiredEnergy, "1H", configPrefix, trc20MaxEnergyFee)
		if rentErr != nil {
			errMsg := fmt.Sprintf("Failed to rent energy using RentEnergyWithConfig: %v", rentErr)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}
		logger.Info(ctx, fmt.Sprintf("%s [LOG_DEBUG] After tron.RentEnergyWithConfig: rentResponse.Serial: %s, rentResponse.Amount (int): %d", logPrefix, rentResponse.Serial, rentResponse.Amount))

		data.Status = "processing" // Status indicating energy rental is in progress
		logger.Info(ctx, fmt.Sprintf("%s [LOG_DEBUG] Before assigning to data.EnergyAmount: data pointer: %p, rentResponse.Amount (int): %d, current data.EnergyAmount: %f", logPrefix, data, rentResponse.Amount, data.EnergyAmount))
		data.TransactionHash = ""
		data.EnergyId = rentResponse.Serial         // Store energy rental order ID in EnergyId field
		data.EnergyAmount = float64(requiredEnergy) // Use requested energy, not the sun cost from API response for this field

		//计算能量费用
		sunConsumedDecimal := decimal.NewFromInt(int64(rentResponse.Amount))
		const tronDecimals = 6 // TRX typically has 6 decimal places
		//转为 trx 费用
		trxFeeDecimal, convErr := tron.SunToTrxDecimal(sunConsumedDecimal, tronDecimals)
		if convErr != nil {
			logger.Errorf(ctx, "%s Failed to convert sun cost (%d) to TRX for EnergyFee: %v", logPrefix, rentResponse.Amount, convErr)
			return nil, fmt.Errorf("failed to convert sun cost to TRX for EnergyFee: %v", convErr)

		} else {
			data.EnergyFee = trxFeeDecimal.InexactFloat64()
			logger.Infof(ctx, "%s [LOG_DEBUG] Calculated EnergyFee: %f TRX from %d SUN (rentResponse.Amount)", logPrefix, data.EnergyFee, rentResponse.Amount)
		}
		logger.Info(ctx, fmt.Sprintf("%s [LOG_DEBUG] After assignments: data pointer: %p, data.EnergyAmount (requested): %f, data.EnergyFee (cost in TRX): %f, rentResponse.Amount (cost in SUN): %d", logPrefix, data, data.EnergyAmount, data.EnergyFee, rentResponse.Amount))
		data.UpdatedAt = gtime.Now()
		return data, nil // Return data for status update, no error here as rental was initiated
	}

	//创建订单
	userWithdrawId, err := createUserWithdrawFromSupplement(ctx, supplement, logPrefix)
	if err != nil {
		errMsg := fmt.Sprintf("failed to create user withdraw: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	logger.Info(ctx, fmt.Sprintf("%s TRON energy is sufficient: %d, need %d", logPrefix, energy, requiredEnergy))

	data.RequiredAmount = float64(requiredEnergy)
	data.ProvidedAmount = sunConsumedDecimal.InexactFloat64()

	data.Status = "success"
	data.UpdatedAt = gtime.Now()
	data.UserWithdrawId = int64(userWithdrawId)
	return data, nil
}

// verifyTronTransaction verifies the status of a TRON energy rental
func verifyTronTransaction(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}
	TRON_CONFIG := cfg.GasFee.TRON

	grpcNodeURL := TRON_CONFIG.GRPCNodeURL
	grpcApiKey := TRON_CONFIG.GRPCApiKey
	// itrxApiKey := TRON_CONFIG.ApiKey
	// itrxApiBaseUrl := TRON_CONFIG.ApiBaseUrl
	// privateKey := TRON_CONFIG.PrivateKey
	// Check if transaction hash (order ID) is empty
	if len(supplement.EnergyId) <= 0 {
		errMsg := "Order ID (EnergyId) is empty for verification"
		logger.Warning(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg) // Return error as this is unexpected for a "processing" supplement
	}

	// Use GetEnergyRentalStatusWithConfig which reads API credentials from config internally
	configPrefix := "withdrawFeeHandler.gasFee.TRON"
	status, err := tron.GetEnergyRentalStatusWithConfig(ctx, supplement.EnergyId, configPrefix)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get energy rental status: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// Check if energy rental is completed
	// iTRX Order Statuses: (0, '超时关闭'), (10, '等待支付'), (20, '已支付'), (30, '委托准备中'),
	// (31, '部分委托'), (32, '异常重试中'), (40, '正常完成'),
	// (41, '退款终止'), (43, '异常终止')
	if status != "40" { // "40" is '正常完成' (success)
		// Check for states that are still in progress and don't represent a final error yet
		if status == "10" || status == "20" || status == "30" || status == "31" || status == "32" {
			// "10": 等待支付, "20": 已支付, "30": 委托准备中, "31": 部分委托, "32": 异常重试中
			logger.Info(ctx, fmt.Sprintf("%s Energy rental is still in progress (status: %s). Waiting for next verification.", logPrefix, status))
			// Return nil, nil to indicate no error, but also no data to update status to success.
			// The caller (processing_verifier) will see nil error and nil updateData, and skip DB update for this iteration.
			return nil, nil
		}
		// Any other status is considered a failure for this verification attempt.
		errMsg := fmt.Sprintf("Energy rental has non-success status: %s", status)
		logger.Warning(ctx, fmt.Sprintf("%s %s. This will be retried or marked as failed by caller.", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg) // Return error to be logged by caller
	}

	// Status is "40" (success), now verify energy on account
	if grpcNodeURL == "" {
		errMsg := "TRON gRPC node URL not configured for energy check (withdrawFeeHandler.gasFee.TRON.grpcNodeURL)"
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	energy, err := tron.GetAccountEnergy(ctx, supplement.Address, grpcNodeURL, grpcApiKey)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get TRON energy after rental: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	if supplement.EnergyAmount <= 0 {
		// This case implies an issue with the recorded requested amount, as a rental process
		// should typically be for a positive amount of energy.
		logger.Warningf(ctx, "%s Requested EnergyAmount in supplement is %f (zero or negative). This is unusual after a rental attempt. Current chain energy: %d", logPrefix, supplement.EnergyAmount, energy)
		// If energy is negative, it's definitely an error. If non-negative, we might proceed cautiously or log further.
		if energy < 0 {
			errMsg := fmt.Sprintf("TRON energy is negative (%d) and recorded requested/supplemented energy amount is invalid (%f)", energy, supplement.EnergyAmount)
			logger.Errorf(ctx, "%s %s", logPrefix, errMsg)
			return nil, fmt.Errorf(errMsg)
		}
	} else {
		// Requested energy is positive, check if actual energy is at least 80% of the requested amount.
		// This threshold (0.8) can be made configurable if needed.
		expectedMinEnergyOnChain := int64(supplement.EnergyAmount * 0.8)

		//todo 测试的时候 设置为0 上线关闭
		expectedMinEnergyOnChain = 0

		if energy < expectedMinEnergyOnChain {
			errMsg := fmt.Sprintf("TRON energy is insufficient after rental: %d, expected at least ~%d (80%% of requested %f)", energy, expectedMinEnergyOnChain, supplement.EnergyAmount)
			logger.Warning(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}
	}

	bandwidth, err := tron.GetAccountBandwidth(ctx, supplement.Address, grpcNodeURL, grpcApiKey)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get TRON bandwidth: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	logger.Info(ctx, fmt.Sprintf("%s Current account energy: %d, bandwidth: %d after successful rental.", logPrefix, energy, bandwidth))

	logger.Info(ctx, fmt.Sprintf("%s TRON energy rental verified and energy is sufficient: %d", logPrefix, energy))

	// Energy is sufficient, proceed to create user withdraw 校验成功开始创建提现订单
	userWithdrawId, err := createUserWithdrawFromSupplement(ctx, supplement, logPrefix)
	if err != nil {
		errMsg := fmt.Sprintf("failed to create user withdraw: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	data.UserWithdrawId = int64(userWithdrawId)
	data.Status = "success" // Mark as success
	data.UpdatedAt = gtime.Now()
	return data, nil // Return data for status update
}

// processTrxFee processes a TRX fee supplement
func processTrxFee(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}
	logger.Infof(ctx, "%s Starting native TRON fee processing for supplement ID: %d, Address: %s", logPrefix, supplement.TokenFeeSupplementId, supplement.Address)

	//获取余额
	balance, err := tron.GetTRXBalance(ctx, supplement.Address)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get TRX balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	minAmount, err := decimal.NewFromString(cfg.WalletConfig.TrxCollectThreshold)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to convert TRX collect threshold to decimal: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	if balance.LessThan(minAmount) {
		errMsg := fmt.Sprintf("TRX balance (%s) is less than collect threshold (%s)", balance.String(), cfg.WalletConfig.TrxCollectThreshold)
		logger.Info(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	userWithdrawId, err := createUserWithdrawFromSupplement(ctx, supplement, logPrefix)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to create user withdraw: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	data.UserWithdrawId = int64(userWithdrawId)
	data.Status = "success"
	data.UpdatedAt = gtime.Now()
	return data, nil
}

// processTrc20UsdtTrxSupplement 处理TRC20 USDT的TRX补充逻辑
func processTrc20UsdtTrxSupplement(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}

	// 如果状态已经是success（因为带宽充足），直接返回
	if supplement.TrxSupplementStatus == "success" {
		logger.Info(ctx, fmt.Sprintf("%s TRX supplement already marked as success (bandwidth sufficient), skipping", logPrefix))

		data.TrxSupplementNeeded = 3
		data.TrxSupplementStatus = "success"
		data.UpdatedAt = gtime.Now()

		return data, nil
	}

	TRON_CONFIG := cfg.GasFee.TRON
	privateKey := TRON_CONFIG.PrivateKey

	if len(privateKey) <= 0 {
		errMsg := fmt.Sprintf("TRON private key missing in configuration, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	// 获取当前TRX余额
	currentBalance, err := tron.GetTRXBalance(ctx, supplement.Address)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get TRX balance for address %s: %v", supplement.Address, err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	logger.Info(ctx, fmt.Sprintf("%s Current TRX balance for address %s: %s", logPrefix, supplement.Address, currentBalance.String()))

	// 检查是否需要补充TRX（少于2 TRX）
	minTrxRequired := decimal.NewFromFloat(cfg.WalletConfig.TrxFeeAmount)

	if minTrxRequired.LessThanOrEqual(decimal.NewFromFloat(0.5)) {
		errMsg := fmt.Sprintf("Failed TrxFeeAmount: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s  %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	if currentBalance.GreaterThanOrEqual(minTrxRequired) {
		logger.Info(ctx, fmt.Sprintf("%s TRX balance %s is sufficient (>= 2 TRX), no supplement needed", logPrefix, currentBalance.String()))
		// 余额充足，标记为不需要补充
		data.TrxSupplementNeeded = 3
		data.TrxSupplementStatus = "success"
		data.UpdatedAt = gtime.Now()
		return data, nil
	}

	// 补充trx
	supplementAmount := decimal.NewFromFloat(cfg.WalletConfig.TrxFeeAmount)

	if supplementAmount.LessThanOrEqual(decimal.NewFromFloat(0.5)) {
		errMsg := fmt.Sprintf("Failed TrxFeeAmount: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s  %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	logger.Info(ctx, fmt.Sprintf("%s TRX balance %s is insufficient, need to supplement %s TRX", logPrefix, currentBalance.String(), supplementAmount.String()))

	// 发送TRX补充交易
	txHash, err := tron.SendTrxTransaction(ctx, privateKey,
		cfg.WalletConfig.TrxFeeAddress, supplement.Address, supplementAmount, logPrefix)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to send TRX supplement transaction: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	logger.Info(ctx, fmt.Sprintf("%s TRX supplement transaction sent successfully. Hash: %s, Amount: %s", logPrefix, txHash, supplementAmount.String()))

	// 更新补充状态
	data.TrxSupplementNeeded = 2
	data.TrxSupplementStatus = "processing"
	data.TrxSupplementHash = txHash
	data.TrxSupplementAmount = supplementAmount.InexactFloat64()
	data.TrxBalanceBefore = currentBalance.InexactFloat64()
	data.UpdatedAt = gtime.Now()
	return data, nil
}

// verifyTrc20UsdtTrxSupplement 验证TRC20 USDT的TRX补充状态
func verifyTrc20UsdtTrxSupplement(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}

	if supplement.TrxSupplementHash == "" {
		errMsg := "TRX supplement hash is empty for verification"
		logger.Warning(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// 验证TRX交易状态 - 使用简化的验证逻辑
	TRON_CONFIG := cfg.GasFee.TRON
	grpcNodeURL := TRON_CONFIG.GRPCNodeURL
	grpcApiKey := TRON_CONFIG.GRPCApiKey

	if len(grpcNodeURL) <= 0 {
		errMsg := fmt.Sprintf("TRON gRPC node URL missing in configuration, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	// 创建TRON客户端进行交易验证
	conn := tronwallet.NewGrpcClient(grpcNodeURL, 10*time.Second, grpcApiKey)
	if conn == nil {
		errMsg := "Failed to create TRON gRPC client for transaction verification"
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	defer conn.Stop()

	err = conn.Start(grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		errMsg := fmt.Sprintf("Failed to start TRON gRPC client: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// 直接使用交易哈希字符串，不需要转换为字节
	logger.Info(ctx, fmt.Sprintf("%s Verifying TRX transaction with hash: %s", logPrefix, supplement.TrxSupplementHash))

	// 获取交易信息 - 直接使用哈希字符串
	txInfo, err := conn.GetTransactionInfoByID(supplement.TrxSupplementHash)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get transaction info: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// 检查交易是否存在
	if txInfo == nil {
		logger.Info(ctx, fmt.Sprintf("%s Transaction not found, still pending: %s", logPrefix, supplement.TrxSupplementHash))
		return nil, nil
	}

	// 检查交易状态
	// 对于TRON交易：
	// - 如果txInfo.Receipt为nil，通常表示原生TRX转账成功（已被打包进区块）
	// - 如果txInfo.Receipt不为nil，则检查Receipt.Result枚举值：
	//   * core.Transaction_Result_SUCCESS 表示成功
	//   * 其他值表示失败
	//   * 对于原生TRX转账，Receipt.Result可能为"DEFAULT"，这也被认为是成功
	if txInfo.Receipt != nil {
		resultStr := txInfo.Receipt.Result.String()
		logger.Info(ctx, fmt.Sprintf("%s [DEBUG] TRX transaction receipt result: %s, hash: %s", logPrefix, resultStr, supplement.TrxSupplementHash))

		// 对于原生TRX转账，允许"DEFAULT"状态，其他情况必须是"SUCCESS"
		if resultStr != "SUCCESS" && resultStr != "DEFAULT" {
			// 交易明确失败
			logger.Error(ctx, fmt.Sprintf("%s TRX supplement transaction failed with result: %s, hash: %s", logPrefix, resultStr, supplement.TrxSupplementHash))
			data := &entity.TokenFeeSupplements{}
			data.TrxSupplementStatus = "failed"
			data.UpdatedAt = gtime.Now()
			return data, nil
		}
		// Receipt.Result为"SUCCESS"或"DEFAULT"表示成功，继续执行后续逻辑
		logger.Info(ctx, fmt.Sprintf("%s TRX supplement transaction verified as successful with result: %s", logPrefix, resultStr))
	} else {
		// Receipt为nil，通常表示原生TRX转账成功
		logger.Info(ctx, fmt.Sprintf("%s TRX supplement transaction has no receipt (typical for native TRX transfers), considering as successful", logPrefix))
	}
	// 如果Receipt为nil或Result为"SUCCESS"/"DEFAULT"，都认为交易成功

	// 交易成功，获取最新余额
	newBalance, err := tron.GetTRXBalance(ctx, supplement.Address)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get updated TRX balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	logger.Info(ctx, fmt.Sprintf("%s TRX supplement transaction verified successfully. New balance: %s", logPrefix, newBalance.String()))

	// 更新状态为成功
	data.TrxSupplementStatus = "success"
	data.TrxSupplementNeeded = 3
	data.TrxBalanceAfter = newBalance.InexactFloat64()
	data.UpdatedAt = gtime.Now()

	return data, nil
}

// 默认为0 打一次款后设置为 1 检查是否激活 成功后 set为2
func processTrc20UsdtActivate(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}

	TRON_CONFIG := cfg.GasFee.TRON

	grpcNodeURL := TRON_CONFIG.GRPCNodeURL
	grpcApiKey := TRON_CONFIG.GRPCApiKey
	itrxApiKey := TRON_CONFIG.ApiKey
	itrxApiBaseUrl := TRON_CONFIG.ApiBaseUrl
	privateKey := TRON_CONFIG.PrivateKey

	if len(grpcNodeURL) <= 0 || len(grpcApiKey) <= 0 || len(itrxApiKey) <= 0 || len(itrxApiBaseUrl) <= 0 || len(privateKey) <= 0 {
		errMsg := fmt.Sprintf("TRON configuration missing in withdrawFeeHandler.gasFee.TRON, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	// Check if the recipient address is active before proceeding with energy rental logic
	isActive, activeCheckErr := tron.IsAddressActive(ctx, supplement.Address, grpcNodeURL, grpcApiKey)
	if activeCheckErr != nil {
		// errMsg := fmt.Sprintf("Failed to check if TRON address %s is active: %v", supplement.Address, activeCheckErr)
		// logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, nil
	}

	if isActive {
		logger.Info(ctx, fmt.Sprintf("%s TRON address %s is active.", logPrefix, supplement.Address))
		data.UpdatedAt = gtime.Now()
		data.IsActivating = 2
		return data, nil
	}
	return nil, nil

}
