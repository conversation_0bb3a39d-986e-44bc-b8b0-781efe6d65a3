package withdraw_step2_fee

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// ProcessFeeHandler is the main task function that processes fee handling for withdrawals
func ProcessFeeHandler(ctx context.Context) {
	logPrefix := "[WithdrawFeeHandler]"
	logger := g.Log()
	logger.Info(ctx, fmt.Sprintf("%s Task started", logPrefix))
	startTime := time.Now()

	// Get task configuration
	cfg, err := GetConfig(ctx)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s Failed to get configuration: %v", logPrefix, err))
		return
	}

	// 1. Process pending token fee supplements
	processPendingTokenFeeSupplements(ctx, cfg, logPrefix) // This will now call the function in pending_processor.go

	// 2. Process processing token fee supplements
	processProcessingTokenFeeSupplements(ctx, cfg, logPrefix) // This will now call the function in processing_verifier.go

	logger.Info(ctx, fmt.Sprintf("%s Task finished. Duration: %s", logPrefix, time.Since(startTime)))
}
