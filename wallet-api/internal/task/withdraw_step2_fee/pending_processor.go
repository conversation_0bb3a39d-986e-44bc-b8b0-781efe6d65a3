package withdraw_step2_fee

import (
	"context"
	"fmt"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"
	utils "wallet-api/internal/utility/utils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// processPendingTokenFeeSupplements processes token fee supplements with status=pending
func processPendingTokenFeeSupplements(ctx context.Context, cfg *FeeHandlerConfig, logPrefix string) {
	// Process USDT orders first
	processUsdtOrders(ctx, cfg, logPrefix)

	// Then process native coin orders
	processNativeCoinOrders(ctx, cfg, logPrefix)
}

// processUsdtOrders processes USDT token fee supplements with status=pending
func processUsdtOrders(ctx context.Context, cfg *FeeHandlerConfig, logPrefix string) {
	logger := g.Log()

	// Query database for USDT token fee supplements with status=pending
	var pendingSupplements []*entity.TokenFeeSupplements
	err := dao.TokenFeeSupplements.Ctx(ctx).
		Where(dao.TokenFeeSupplements.Columns().Status, "pending").
		Where(dao.TokenFeeSupplements.Columns().TokenSymbol, "USDT").
		Limit(cfg.BatchSize).
		Scan(&pendingSupplements)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s Failed to query pending USDT token fee supplements: %v", logPrefix, err))
		return
	}

	if len(pendingSupplements) == 0 {
		logger.Info(ctx, fmt.Sprintf("%s No pending USDT token fee supplements found", logPrefix))
		return
	}

	logger.Info(ctx, fmt.Sprintf("%s Found %d pending USDT token fee supplements", logPrefix, len(pendingSupplements)))

	// Process each pending USDT supplement
	for _, supplement := range pendingSupplements {
		// Create a more specific log prefix for this supplement ID
		supplementLogPrefix := fmt.Sprintf("%s[SupplementID:%d]", logPrefix, supplement.TokenFeeSupplementId)

		var processingErr error
		var updateData *entity.TokenFeeSupplements

		// Check if the address has enough balance/energy
		switch {
		// ETH gas fee for USDT
		case supplement.ChainType == "ETH" && supplement.FeeType == "gas_fee":
			updateData, processingErr = processErc20UsdtGasFee(ctx, supplement, cfg, supplementLogPrefix)

		// TRON energy for USDT
		case supplement.ChainType == "TRON" && supplement.FeeType == "energy":
			logger.Info(ctx, fmt.Sprintf("%s [DEBUG] Processing TRON USDT supplement - IsActivating=%d, TrxSupplementStatus=%s",
				supplementLogPrefix, supplement.IsActivating, supplement.TrxSupplementStatus))

			//如果等于 1 说明需要补充trx
			if supplement.TrxSupplementNeeded == 1 {
				logger.Info(ctx, fmt.Sprintf("%s [DEBUG] TrxSupplementNeeded=1, calling processTrc20UsdtTrxSupplement", supplementLogPrefix))
				updateData, processingErr = processTrc20UsdtTrxSupplement(ctx, supplement, cfg, supplementLogPrefix)

			} else if supplement.TrxSupplementNeeded == 2 {
				//等于 2 说明补充中 会把补充状态修改为成功或者失败
				logger.Info(ctx, fmt.Sprintf("%s [DEBUG] TrxSupplementNeeded=2, calling verifyTrc20UsdtTrxSupplement", supplementLogPrefix))
				updateData, processingErr = verifyTrc20UsdtTrxSupplement(ctx, supplement, cfg, supplementLogPrefix)
			} else if supplement.TrxSupplementNeeded == 0 || supplement.TrxSupplementNeeded == 3 {
				//0未处理 ，3成功
				updateData, processingErr = processTrc20UsdtEnergy(ctx, supplement, cfg, supplementLogPrefix)
			}
			goto updateDatabase

		default:
			logger.Warning(ctx, fmt.Sprintf("%s Unsupported supplement type: %s/%s", supplementLogPrefix, supplement.ChainType, supplement.FeeType))
			continue // Skip to the next supplement
		}

	updateDatabase:
		// Check for errors from processing functions
		if processingErr != nil {
			logger.Error(ctx, fmt.Sprintf("%s Processing error for supplement ID %d: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, processingErr))

			updateFieldsMap := g.Map{
				dao.TokenFeeSupplements.Columns().UpdatedAt:    gtime.Now(),
				dao.TokenFeeSupplements.Columns().ErrorMessage: utils.BuildErrorMessageJson(ctx, supplement.ErrorMessage, processingErr.Error()),
			}

			currentRetryCount := supplement.RetryCount
			if currentRetryCount < 3 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().RetryCount] = currentRetryCount + 1
				// Status remains as is (e.g. "pending") for the next retry attempt
				logger.Infof(ctx, "%s Incrementing retry count to %d for supplement ID %d.", supplementLogPrefix, currentRetryCount+1, supplement.TokenFeeSupplementId)
			} else {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().Status] = "failed"
				updateFieldsMap[dao.TokenFeeSupplements.Columns().RetryCount] = currentRetryCount // Store the final retry count (e.g., 3)
				logger.Errorf(ctx, "%s Max retries (%d) reached for supplement ID %d. Marking as failed.", supplementLogPrefix, currentRetryCount, supplement.TokenFeeSupplementId)
			}

			_, dbErr := dao.TokenFeeSupplements.Ctx(ctx).
				Data(updateFieldsMap).
				Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
				Update()
			if dbErr != nil {
				logger.Errorf(ctx, "%s Failed to update supplement ID %d after processing error: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, dbErr)
			}
			continue // Important: Skip to the next supplement after handling the error (retry or fail)
		}

		// Update the supplement status if no processing error OR if updateData is available from a successful processing step
		if updateData != nil {
			updateFieldsMap := g.Map{}
			if updateData.Status != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().Status] = updateData.Status
			}
			// Always update UpdatedAt for any change
			updateFieldsMap[dao.TokenFeeSupplements.Columns().UpdatedAt] = gtime.Now()

			if updateData.UserWithdrawId != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().UserWithdrawId] = updateData.UserWithdrawId
			}
			if updateData.TransactionHash != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().TransactionHash] = updateData.TransactionHash
			}
			// If updateData provides an ErrorMessage, it means the processing function itself determined an error state
			// This is different from processingErr above. This message might not need to be JSON formatted unless specified.
			// For now, if updateData.ErrorMessage is set, we use it as is.

			if updateData.EnergyAmount != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().EnergyAmount] = updateData.EnergyAmount
			}
			if updateData.EnergyId != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().EnergyId] = updateData.EnergyId
			}
			if updateData.EnergyFee != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().EnergyFee] = updateData.EnergyFee
			}
			// If processing was successful and status is changing to success, we might want to clear RetryCount or ErrorMessage
			if updateData.Status != "success" && updateData.ErrorMessage != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().ErrorMessage] = utils.BuildErrorMessageJson(ctx, supplement.ErrorMessage, updateData.ErrorMessage)
			}
			// +++++++++++++ Roo: 新增代码开始 - 添加 RequiredAmount 和 ProvidedAmount 到更新Map +++++++++++++
			if updateData.RequiredAmount != 0 { // 或者其他合适的条件，例如检查是否已设置的标志
				updateFieldsMap[dao.TokenFeeSupplements.Columns().RequiredAmount] = updateData.RequiredAmount
			}
			if updateData.ProvidedAmount != 0 { // 或者其他合适的条件
				updateFieldsMap[dao.TokenFeeSupplements.Columns().ProvidedAmount] = updateData.ProvidedAmount
			}
			// +++++++++++++ Roo: 新增代码结束 ++++++++++++

			// 添加TRX补充相关字段的更新
			if updateData.TrxSupplementNeeded != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().TrxSupplementNeeded] = updateData.TrxSupplementNeeded
			}
			if updateData.TrxSupplementStatus != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().TrxSupplementStatus] = updateData.TrxSupplementStatus
			}
			if updateData.TrxSupplementHash != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().TrxSupplementHash] = updateData.TrxSupplementHash
			}
			if updateData.TrxSupplementAmount != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().TrxSupplementAmount] = updateData.TrxSupplementAmount
			}
			if updateData.TrxBalanceBefore != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().TrxBalanceBefore] = updateData.TrxBalanceBefore
			}
			if updateData.TrxBalanceAfter != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().TrxBalanceAfter] = updateData.TrxBalanceAfter
			}

			// 添加激活相关字段的更新
			if updateData.ActivateHash != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().ActivateHash] = updateData.ActivateHash
			}
			if updateData.ActivateAmount != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().ActivateAmount] = updateData.ActivateAmount
			}
			if updateData.IsActivating != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().IsActivating] = updateData.IsActivating
			}

			if len(updateFieldsMap) > 0 {
				_, err := dao.TokenFeeSupplements.Ctx(ctx).
					Data(updateFieldsMap).
					Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
					Update()
				if err != nil {
					logger.Error(ctx, fmt.Sprintf("%s Failed to update supplement status for ID %d with map: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, err))
					// continue // If update fails here, it's a separate issue from processingErr
				}
			} else {
				logger.Info(ctx, fmt.Sprintf("%s No fields to update for supplement ID %d from updateData.", supplementLogPrefix, supplement.TokenFeeSupplementId))
			}
		} else { // This case: processingErr was nil, AND updateData is also nil.
			logger.Error(ctx, fmt.Sprintf("%s CRITICAL: updateData is nil AND processingErr is nil for supplement ID %d. This indicates a logic flaw in the processing function.", supplementLogPrefix, supplement.TokenFeeSupplementId))
			// Mark as failed to prevent reprocessing or to highlight the issue.
			_, err := dao.TokenFeeSupplements.Ctx(ctx).
				Data(g.Map{
					dao.TokenFeeSupplements.Columns().Status:       "failed",
					dao.TokenFeeSupplements.Columns().ErrorMessage: utils.BuildErrorMessageJson(ctx, supplement.ErrorMessage, fmt.Errorf("Internal error: updateData is nil without processing error. Supplement ID: %d", supplement.TokenFeeSupplementId).Error()),
					dao.TokenFeeSupplements.Columns().UpdatedAt:    gtime.Now(),
				}).
				Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
				Update()
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("%s Failed to mark supplement ID %d as failed after critical nil updateData: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, err))
			}
			// continue // Skip to the next supplement
		}
	}
}

// processNativeCoinOrders processes native coin token fee supplements with status=pending
func processNativeCoinOrders(ctx context.Context, cfg *FeeHandlerConfig, logPrefix string) {
	logger := g.Log()

	// Query database for native coin token fee supplements with status=pending
	var pendingSupplements []*entity.TokenFeeSupplements
	err := dao.TokenFeeSupplements.Ctx(ctx).
		Where(dao.TokenFeeSupplements.Columns().Status, "pending").
		Where(dao.TokenFeeSupplements.Columns().TokenSymbol+" <> ?", "USDT").
		Limit(cfg.BatchSize).
		Scan(&pendingSupplements)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s Failed to query pending native coin token fee supplements: %v", logPrefix, err))
		return
	}

	if len(pendingSupplements) == 0 {
		logger.Info(ctx, fmt.Sprintf("%s No pending native coin token fee supplements found", logPrefix))
		return
	}

	logger.Info(ctx, fmt.Sprintf("%s Found %d pending native coin token fee supplements", logPrefix, len(pendingSupplements)))

	// Process each pending native coin supplement
	for _, supplement := range pendingSupplements {
		// Create a more specific log prefix for this supplement ID
		supplementLogPrefix := fmt.Sprintf("%s[SupplementID:%d]", logPrefix, supplement.TokenFeeSupplementId)

		//判断是否存在同地址的 usdt 订单 如果存在就跳过
		usdtSupplementCount, err := dao.TokenFeeSupplements.Ctx(ctx).
			Where(dao.TokenFeeSupplements.Columns().Address, supplement.Address).
			Where(dao.TokenFeeSupplements.Columns().Status, "processing").
			Where(dao.TokenFeeSupplements.Columns().TokenSymbol, "USDT").
			Count()
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("%s Failed to query USDT token fee supplement: %v", logPrefix, err))
			continue
		}
		if usdtSupplementCount > 0 {
			logger.Info(ctx, fmt.Sprintf("%s Found %d USDT token fee supplements for the same address, skipping", logPrefix, usdtSupplementCount))
			continue
		}

		// Check if the address has enough balance/energy
		var processingErr error
		var updateData *entity.TokenFeeSupplements
		switch {
		// ETH gas fee for native ETH
		case supplement.ChainType == "ETH" && supplement.FeeType == "gas_fee":
			updateData, processingErr = processEthFee(ctx, supplement, cfg, supplementLogPrefix)

		// TRON energy for native TRX
		case supplement.ChainType == "TRON" && supplement.FeeType == "energy":
			updateData, processingErr = processTrxFee(ctx, supplement, cfg, supplementLogPrefix)

		default:
			logger.Warning(ctx, fmt.Sprintf("%s Unsupported supplement type: %s/%s", supplementLogPrefix, supplement.ChainType, supplement.FeeType))
			continue
		}

		// Check for errors from processing functions
		if processingErr != nil {
			logger.Error(ctx, fmt.Sprintf("%s Processing error for supplement ID %d: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, processingErr))

			updateFieldsMap := g.Map{
				dao.TokenFeeSupplements.Columns().UpdatedAt:    gtime.Now(),
				dao.TokenFeeSupplements.Columns().ErrorMessage: utils.BuildErrorMessageJson(ctx, supplement.ErrorMessage, processingErr.Error()),
			}

			currentRetryCount := supplement.RetryCount
			if currentRetryCount < 5 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().RetryCount] = currentRetryCount + 1
				logger.Infof(ctx, "%s Incrementing retry count to %d for supplement ID %d.", supplementLogPrefix, currentRetryCount+1, supplement.TokenFeeSupplementId)
			} else {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().Status] = "failed"
				updateFieldsMap[dao.TokenFeeSupplements.Columns().RetryCount] = currentRetryCount
				logger.Errorf(ctx, "%s Max retries (%d) reached for supplement ID %d. Marking as failed.", supplementLogPrefix, currentRetryCount, supplement.TokenFeeSupplementId)
			}

			_, dbErr := dao.TokenFeeSupplements.Ctx(ctx).
				Data(updateFieldsMap).
				Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
				Update()
			if dbErr != nil {
				logger.Errorf(ctx, "%s Failed to update supplement ID %d after processing error: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, dbErr)
			}
			continue // Important: Skip to the next supplement
		}

		// Update the supplement status if no processing error OR if updateData is available
		if updateData != nil {
			updateFieldsMap := g.Map{}
			if updateData.Status != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().Status] = updateData.Status
			}
			updateFieldsMap[dao.TokenFeeSupplements.Columns().UpdatedAt] = gtime.Now()

			if updateData.UserWithdrawId != 0 {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().UserWithdrawId] = updateData.UserWithdrawId
			}
			if updateData.TransactionHash != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().TransactionHash] = updateData.TransactionHash
			}
			if updateData.ErrorMessage != "" {
				updateFieldsMap[dao.TokenFeeSupplements.Columns().ErrorMessage] = utils.BuildErrorMessageJson(ctx, supplement.ErrorMessage, updateData.ErrorMessage)
			}
			if updateData.Status == "success" {
				// updateFieldsMap[dao.TokenFeeSupplements.Columns().RetryCount] = 0
				// updateFieldsMap[dao.TokenFeeSupplements.Columns().ErrorMessage] = ""
			}
			// Add other fields from updateData to updateFieldsMap if they are set and need to be updated

			if len(updateFieldsMap) > 0 {
				_, err := dao.TokenFeeSupplements.Ctx(ctx).
					Data(updateFieldsMap).
					Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
					Update()
				if err != nil {
					logger.Error(ctx, fmt.Sprintf("%s Failed to update supplement status for ID %d with map: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, err))
					// continue
				}
			} else {
				logger.Info(ctx, fmt.Sprintf("%s No fields to update for supplement ID %d from updateData.", supplementLogPrefix, supplement.TokenFeeSupplementId))
			}
		} else { // processingErr was nil, but updateData is also nil.
			logger.Error(ctx, fmt.Sprintf("%s CRITICAL: updateData is nil AND processingErr is nil for supplement ID %d. This indicates a logic flaw.", supplementLogPrefix, supplement.TokenFeeSupplementId))
			_, err := dao.TokenFeeSupplements.Ctx(ctx).
				Data(g.Map{
					dao.TokenFeeSupplements.Columns().Status:       "failed",
					dao.TokenFeeSupplements.Columns().ErrorMessage: utils.BuildErrorMessageJson(ctx, supplement.ErrorMessage, fmt.Errorf("Internal error: updateData is nil without processing error. Supplement ID: %d", supplement.TokenFeeSupplementId).Error()),
					dao.TokenFeeSupplements.Columns().UpdatedAt:    gtime.Now(),
				}).
				Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
				Update()
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("%s Failed to mark supplement ID %d as failed after critical nil updateData: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, err))
			}
			// continue
		}
	}
}
