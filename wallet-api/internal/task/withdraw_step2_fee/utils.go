package withdraw_step2_fee

import (
	"context"
	"database/sql"
	"encoding/json" // Added import for json
	"fmt"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/consts"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"
	"wallet-api/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ErrorLogEntry defines the structure for a single error log.
type ErrorLogEntry struct {
	Timestamp string `json:"timestamp"`
	Message   string `json:"message"`
}

// createUserWithdrawFromSupplement creates a user_withdraws record from a token_fee_supplements record
func createUserWithdrawFromSupplement(ctx context.Context, supplement *entity.TokenFeeSupplements, logPrefix string) (uint, error) {
	logger := g.Log()

	// Get wallet information
	walletInfoRes, err := service.Wallet().GetWalletInfo(ctx, &v1.GetWalletInfoReq{})
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s Failed to get wallet info: %v", logPrefix, err))
		return 0, fmt.Errorf("failed to get wallet info: %w", err)
	}

	// Determine target collect address
	var targetCollectAddress string
	switch supplement.ChainType {
	case consts.NetworkTypeETH:
		targetCollectAddress = walletInfoRes.WalletInfo.EthCollectAddress
	case consts.NetworkTypeTRX:
		targetCollectAddress = walletInfoRes.WalletInfo.TrxCollectAddress
	default:
		logger.Error(ctx, fmt.Sprintf("%s Unsupported chain type: %s", logPrefix, supplement.ChainType))
		return 0, fmt.Errorf("unsupported chain type: %s", supplement.ChainType)
	}

	if targetCollectAddress == "" {
		logger.Error(ctx, fmt.Sprintf("%s Collection address for chain %s is not configured", logPrefix, supplement.ChainType))
		return 0, fmt.Errorf("collection address for chain %s is not configured", supplement.ChainType)
	}

	// Extract withdraw plan ID from related task ID
	var withdrawPlanId uint = 0

	if supplement.RelatedTaskId != "" {
		_, err := fmt.Sscanf(supplement.RelatedTaskId, "withdraw_plan_%d", &withdrawPlanId)
		if err != nil {
			logger.Warning(ctx, fmt.Sprintf("%s Failed to extract withdraw plan ID from related task ID: %v", logPrefix, err))
		}
	}

	// Check if a user_withdraws record already exists for this supplement ID
	var existingWithdraw *entity.UserWithdraws
	err = dao.UserWithdraws.Ctx(ctx).
		Where(dao.UserWithdraws.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
		Scan(&existingWithdraw)

	if err != nil && err != sql.ErrNoRows {
		logger.Error(ctx, fmt.Sprintf("%s Failed to query existing user_withdraws record by supplement ID %d: %v", logPrefix, supplement.TokenFeeSupplementId, err))
		return 0, err
	}

	if existingWithdraw != nil {
		logger.Info(ctx, fmt.Sprintf("%s Found existing user_withdraws record with ID %d for supplement ID %d. Skipping creation.", logPrefix, existingWithdraw.UserWithdrawsId, supplement.TokenFeeSupplementId))
		// Optionally, update UpdatedAt or other fields if necessary
		// _, updateErr := dao.UserWithdraws.Ctx(ctx).Data(g.Map{
		//	 dao.UserWithdraws.Columns().UpdatedAt: gtime.Now(),
		// }).WherePri(existingWithdraw.UserWithdrawsId).Update()
		// if updateErr != nil {
		//	 logger.Warning(ctx, fmt.Sprintf("%s Failed to update existing user_withdraws record %d: %v", logPrefix, existingWithdraw.UserWithdrawsId, updateErr))
		// }
		return existingWithdraw.UserWithdrawsId, nil
	}

	// Create user_withdraws record if not found
	withdraw := &entity.UserWithdraws{
		Name:                 supplement.TokenSymbol,
		Chan:                 supplement.ChainType,
		FromAddress:          supplement.Address,
		ToAddress:            targetCollectAddress,
		Amount:               supplement.RequiredAmount,
		HandlingFee:          0, // Default handling fee is 0
		ActualAmount:         supplement.RequiredAmount,
		State:                1,
		TxHash:               "",
		ErrorMessage:         "[]", // Initialize with empty string instead of "{}"
		CreatedAt:            gtime.Now(),
		UpdatedAt:            gtime.Now(),
		Retries:              0,
		NergyState:           2,                                      // 2 = Energy confirmed (skip the energy check)
		GasfeeState:          2,                                      // 2 = Gas fee confirmed (skip the gas fee check)
		TokenFeeSupplementId: int64(supplement.TokenFeeSupplementId), // Ensure this is correctly linking
		EthFeeMode:           walletInfoRes.WalletInfo.EthFeeMode,
		EthFeeMax:            walletInfoRes.WalletInfo.EthFeeMax,
		EthGasPrice:          walletInfoRes.WalletInfo.EthGasPrice,
		EthGasLimit:          walletInfoRes.WalletInfo.EthGasLimit,
		TrxFeeMax:            walletInfoRes.WalletInfo.TrxFeeMax,
		CanActAt:             gtime.Now(),
	}

	// Insert the record
	result, err := dao.UserWithdraws.Ctx(ctx).Data(withdraw).Insert()
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s Failed to create user_withdraws record (WithdrawPlanID: %d, SupplementID: %d): %v", logPrefix, withdrawPlanId, supplement.TokenFeeSupplementId, err))
		return 0, err
	}

	// Get the inserted ID
	id, err := result.LastInsertId()
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s Failed to get last insert ID: %v", logPrefix, err))
		return 0, err
	}

	logger.Info(ctx, fmt.Sprintf("%s Created user_withdraws record with ID: %d", logPrefix, id))
	return uint(id), nil
}

// buildErrorMessageJson formats an error message into a JSON array string.
// It appends the newError to a list of existing errors (if any) in existingErrorJson.
func buildErrorMessageJson(ctx context.Context, existingErrorJson string, newErr error) string {
	logger := g.Log()
	var errorLogs []ErrorLogEntry

	if existingErrorJson != "" {
		if err := json.Unmarshal([]byte(existingErrorJson), &errorLogs); err != nil {
			// Log the unmarshal error and start with a fresh list, possibly including the old raw message as the first entry.
			logger.Warningf(ctx, "Failed to unmarshal existing ErrorMessage JSON '%s': %v. Initializing new log array.", existingErrorJson, err)
			// Fallback: treat the old message as a single entry if it couldn't be parsed as an array.
			// This might happen if the old format was a plain string.
			errorLogs = append(errorLogs, ErrorLogEntry{
				Timestamp: gtime.Now().Format("2006-01-02 15:04:05"), // Or a fixed "unknown" timestamp
				Message:   fmt.Sprintf("Unparsable existing error: %s", existingErrorJson),
			})
		}
	}

	newErrorEntry := ErrorLogEntry{
		Timestamp: gtime.Now().Format("2006-01-02 15:04:05"),
		Message:   newErr.Error(),
	}
	errorLogs = append(errorLogs, newErrorEntry)

	updatedErrorMsgBytes, marshalErr := json.Marshal(errorLogs)
	if marshalErr != nil {
		logger.Errorf(ctx, "Failed to marshal error messages: %v. Falling back to a JSON-formatted error entry for this failure.", marshalErr)
		// 创建一个描述序列化失败的JSON条目
		fallbackEntry := ErrorLogEntry{
			Timestamp: gtime.Now().Format("2006-01-02 15:04:05"),
			Message:   fmt.Sprintf("Internal error: Failed to marshal detailed error log. Current error: %s. Marshalling issue: %s", newErr.Error(), marshalErr.Error()),
		}
		// 尝试序列化这个单一的回退条目
		fallbackJsonBytes, finalMarshalErr := json.Marshal([]ErrorLogEntry{fallbackEntry})
		if finalMarshalErr != nil {
			// 极不可能发生，但作为最终回退，返回一个硬编码的有效JSON错误字符串
			logger.Errorf(ctx, "FATAL: Failed to marshal even the fallback error entry: %v", finalMarshalErr)
			return fmt.Sprintf(`[{"timestamp":"%s","message":"FATAL: System error in logging facility. Original error: %s"}]`, gtime.Now().Format("2006-01-02 15:04:05"), newErr.Error())
		}
		return string(fallbackJsonBytes)
	}
	return string(updatedErrorMsgBytes)
}
