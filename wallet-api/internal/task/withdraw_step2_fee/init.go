package withdraw_step2_fee

import (
	"context"
	"fmt"
	"wallet-api/internal/task_registry"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	// Register fee handler task
	task_registry.Register(task_registry.TaskInfo{
		Name: "WithdrawFeeHandlerTask",
		SpecFunc: func(ctx context.Context) (spec string, enabled bool, err error) {
			cfg, err := GetConfig(ctx)
			if err != nil {
				g.Log().Error(ctx, fmt.Sprintf("%s Failed to get configuration: %v", "WithdrawFeeHandlerTask", err))
				return "", false, err
			}
			spec = cfg.Spec       // Default: every minute
			enabled = cfg.Enabled // Default: disabled

			// If task is enabled but spec is empty, log warning
			if enabled && spec == "" {
				g.Log().Error(ctx, "Task '%s' is enabled but spec is empty or missing in config ('%s.spec'). Task will not run.", "WithdrawFeeHandlerTask", "withdrawFeeHandler")
				return "", false, fmt.Errorf("task spec is empty")
			}
			return spec, enabled, nil
		},
		Func:        ProcessFeeHandler, // Task execution function
		IsSingleton: true,              // Ensure only one instance runs at a time
	})
}
