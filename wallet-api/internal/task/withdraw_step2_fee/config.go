package withdraw_step2_fee // Changed package name

import (
	"context"
	"fmt"
	"strings"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"
	"wallet-api/internal/security/credentialmanager" // Added import
	"wallet-api/internal/utility/crypto/eth"
	"wallet-api/internal/utility/crypto/tron"

	"github.com/gogf/gf/v2/errors/gerror" // Added import
	"github.com/gogf/gf/v2/frame/g"
)

// FeeHandlerConfig holds the configuration for the fee handler task
type FeeHandlerConfig struct {
	Enabled                bool                `json:"enabled"`                // Whether the task is enabled
	Spec                   string              `json:"spec"`                   // Cron schedule expression
	BatchSize              int                 `json:"batchSize"`              // Number of records to process in one batch
	MaxVerificationRetries uint                `json:"maxVerificationRetries"` // Maximum number of verification retries for a supplement
	GasFee                 GasFeeHandlerConfig `json:"gasFee"`                 // Gas fee configuration
	WalletConfig           *entity.Wallets     `json:"walletConfig"`           // Wallet configuration

}

// GasFeeHandlerConfig holds the configuration for gas fee handling
type GasFeeHandlerConfig struct {
	ETH  ETHGasFeeConfig  `json:"ETH"`  // ETH gas fee configuration
	TRON TRONEnergyConfig `json:"TRON"` // TRON energy configuration
}

// ETHGasFeeConfig holds the configuration for ETH gas fee handling
type ETHGasFeeConfig struct {
	PrivateKey string `json:"privateKey"` // Fallback private key
	Address    string `json:"address"`    // Fallback address
	Amount     string `json:"amount"`     // Amount of ETH to send for gas fees
	NodeURL    string `json:"nodeURL"`    // Ethereum node URL
	ChainID    string `json:"chainID"`    // Ethereum Chain ID
	GasLimit   string `json:"gasLimit"`   // Optional Gas Limit for transactions
	GasPrice   string `json:"gasPrice"`   // Optional Gas Price for transactions (in GWei or Wei, to be parsed)
	EthRpcUrl  string `json:"ethRpcUrl"`  // Optional ETH RPC URL
}

// TRONEnergyConfig holds the configuration for TRON energy rental
type TRONEnergyConfig struct {
	PrivateKey  string `json:"privateKey"`  // Fallback private key
	Address     string `json:"address"`     // Fallback address
	ApiKey      string `json:"apiKey"`      // iTRX API key
	ApiSecret   string `json:"apiSecret"`   // iTRX API secret
	ApiBaseUrl  string `json:"apiBaseUrl"`  // iTRX API base URL
	Period      string `json:"period"`      // Rental period (1H, 1D, 3D, 30D)
	Energy      int    `json:"energy"`      // Energy amount to rent
	Model       string `json:"model"`       // Rental model (order, collect)
	GRPCNodeURL string `json:"grpcNodeURL"` // TRON RPC node URL
	GRPCApiKey  string `json:"grpcApiKey"`  // TRON RPC API key
}

// GetConfig retrieves the configuration for the fee handler task
func GetConfig(ctx context.Context) (*FeeHandlerConfig, error) {
	// Check if wallet is unlocked before attempting to get private keys
	if !credentialmanager.IsUnlocked(ctx) {
		g.Log().Error(ctx, "FeeHandlerTask: Wallet is locked, cannot retrieve fee private keys.")
		return nil, gerror.New("wallet is locked, cannot retrieve fee private keys")
	}

	// Get decrypted fee private keys from credential manager
	ethFeePrivateKey, err := credentialmanager.GetDecryptedEthFeePrivateKey(ctx)
	if err != nil {
		// This error should ideally not happen if IsUnlocked is true, but as a safeguard:
		g.Log().Errorf(ctx, "FeeHandlerTask: Failed to get decrypted ETH fee private key from manager: %v", err)
		return nil, gerror.Wrap(err, "failed to get decrypted ETH fee private key")
	}
	// g.Log().Debug(ctx, "FeeHandlerTask: Decrypted ETH fee private key retrieved successfully. %s", ethFeePrivateKey)

	trxFeePrivateKey, err := credentialmanager.GetDecryptedTrxFeePrivateKey(ctx)

	if err != nil {
		// This error should ideally not happen if IsUnlocked is true, but as a safeguard:
		g.Log().Errorf(ctx, "FeeHandlerTask: Failed to get decrypted TRX fee private key from manager: %v", err)
		return nil, gerror.Wrap(err, "failed to get decrypted TRX fee private key")
	}
	// g.Log().Debug(ctx, "FeeHandlerTask: Decrypted TRX fee private key retrieved successfully. %s", trxFeePrivateKey)

	// Get wallet config for addresses and other settings (no longer need private keys from here)
	var walletConfig *entity.Wallets
	err = dao.Wallets.Ctx(ctx).Where("id", 1).Scan(&walletConfig)
	if err != nil {
		return nil, fmt.Errorf("wallet config not found: %w", err)
	}
	if walletConfig == nil {
		return nil, fmt.Errorf("wallet config not found (nil entity)")
	}

	configPrefix := "withdrawFeeHandler"

	ethConfigPrefix := configPrefix + ".gasFee.ETH"
	tronConfigPrefix := configPrefix + ".gasFee.TRON"

	// Use addresses from walletConfig
	trx_fee_address := walletConfig.TrxFeeAddress
	if trx_fee_address == "" {
		g.Log().Error(ctx, fmt.Sprintf("%s TRX fee address not configured", "FeeHandlerTask"))
		return nil, fmt.Errorf("TRX fee address not configured")
	}

	eth_fee_address := walletConfig.EthFeeAddress
	if eth_fee_address == "" {
		g.Log().Error(ctx, fmt.Sprintf("%s ETH fee address not configured", "FeeHandlerTask"))
		return nil, fmt.Errorf("ETH fee address not configured")
	}

	//校验私钥和地址是否一致 (using decrypted private keys)
	if trxFeePrivateKey != "" { // Only validate if private key is available
		trx_fee_address_from_private_key, err := tron.GetAddressFromPrivateKey(trxFeePrivateKey)
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("%s TRX fee private key is invalid: %v", "FeeHandlerTask", err))
			return nil, fmt.Errorf("TRX fee private key is invalid")
		}
		if trx_fee_address_from_private_key != trx_fee_address {
			g.Log().Error(ctx, fmt.Sprintf("%s TRX fee address from private key (%s) does not match configured address (%s)", "FeeHandlerTask", trx_fee_address_from_private_key, trx_fee_address))
			return nil, fmt.Errorf("TRX fee address mismatch")
		}
	} else {
		g.Log().Warning(ctx, "FeeHandlerTask: TRX fee private key is empty, skipping address validation.")
	}

	if ethFeePrivateKey != "" { // Only validate if private key is available
		eth_fee_address_from_private_key, err := eth.GetAddressFromPrivateKey(ethFeePrivateKey)
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("%s ETH fee private key is invalid: %v", "FeeHandlerTask", err))
			return nil, fmt.Errorf("ETH fee private key is invalid")
		}

		uppercaseAddress := strings.ToUpper(eth_fee_address_from_private_key)
		uppercaseConfigAddress := strings.ToUpper(eth_fee_address)
		if uppercaseConfigAddress != uppercaseAddress {
			g.Log().Error(ctx, fmt.Sprintf("%s ETH fee address from private key (%s) does not match configured address (%s)", "FeeHandlerTask", eth_fee_address_from_private_key, eth_fee_address))
			return nil, fmt.Errorf("ETH fee address mismatch")
		}
	} else {
		g.Log().Warning(ctx, "FeeHandlerTask: ETH fee private key is empty, skipping address validation.")
	}

	config := &FeeHandlerConfig{
		Enabled:                g.Cfg().MustGet(ctx, configPrefix+".enabled", false).Bool(),
		Spec:                   g.Cfg().MustGet(ctx, configPrefix+".spec", "* * * * *").String(),
		BatchSize:              g.Cfg().MustGet(ctx, configPrefix+".batchSize", 10).Int(),
		MaxVerificationRetries: g.Cfg().MustGet(ctx, configPrefix+".maxVerificationRetries", 3).Uint(), // Default to 3 retries
		GasFee: GasFeeHandlerConfig{
			ETH: ETHGasFeeConfig{
				PrivateKey: ethFeePrivateKey, // Use decrypted key
				Address:    eth_fee_address,
				Amount:     g.Cfg().MustGet(ctx, ethConfigPrefix+".amount", "0.01").String(),
				NodeURL:    g.Cfg().MustGet(ctx, ethConfigPrefix+".nodeURL", "").String(),   // Read nodeURL
				ChainID:    g.Cfg().MustGet(ctx, ethConfigPrefix+".chainID", "1").String(),  // Read chainID, default to "1" (Mainnet)
				GasLimit:   g.Cfg().MustGet(ctx, ethConfigPrefix+".gasLimit", "0").String(), // Default to "0" (use utility default)
				GasPrice:   g.Cfg().MustGet(ctx, ethConfigPrefix+".gasPrice", "0").String(), // Default to "0" (use suggest gas price)
				EthRpcUrl:  g.Cfg().MustGet(ctx, ethConfigPrefix+".ethRpcUrl", "").String(), // Default to "0" (use suggest gas price)
			},
			TRON: TRONEnergyConfig{
				PrivateKey:  trxFeePrivateKey, // Use decrypted key
				Address:     trx_fee_address,
				ApiKey:      g.Cfg().MustGet(ctx, tronConfigPrefix+".apiKey", "").String(),
				ApiSecret:   g.Cfg().MustGet(ctx, tronConfigPrefix+".apiSecret", "").String(),
				ApiBaseUrl:  g.Cfg().MustGet(ctx, tronConfigPrefix+".apiBaseUrl", "https://itrx.io").String(),
				Period:      g.Cfg().MustGet(ctx, tronConfigPrefix+".period", "1H").String(),
				Energy:      g.Cfg().MustGet(ctx, tronConfigPrefix+".energy", 65000).Int(),
				Model:       g.Cfg().MustGet(ctx, tronConfigPrefix+".model", "order").String(),
				GRPCNodeURL: g.Cfg().MustGet(ctx, "tron_grpc_rpc_url", "").String(),
				GRPCApiKey:  g.Cfg().MustGet(ctx, "tron_api_key_placeholder", "").String(),
			},
		},
		WalletConfig: walletConfig, // Keep walletConfig for other settings
	}

	// Fallback for ETH NodeURL if not specifically set under feeHandler.gasFee.ETH.nodeURL
	if config.GasFee.ETH.NodeURL == "" {
		config.GasFee.ETH.NodeURL = g.Cfg().MustGet(ctx, "depositCheck.chains.ETH.rpc", "").String()
	}
	if config.GasFee.ETH.NodeURL == "" {
		g.Log().Warning(ctx, "ETH node URL is not configured under feeHandler.gasFee.ETH.nodeURL or depositCheck.chains.ETH.rpc")
		return nil, fmt.Errorf("ETH node URL is not configured")
	}

	// The private key and address validation is now done above using the decrypted keys.
	// Remove the redundant checks here.

	// Check if TRON gas fee configuration is valid (ApiKey and Address checks remain)
	if config.GasFee.TRON.ApiKey == "" {
		g.Log().Error(ctx, fmt.Sprintf("%s TRON API key not configured", "FeeHandlerTask"))
		return nil, fmt.Errorf("TRON API key not configured")
	}

	if config.GasFee.TRON.Address == "" {
		g.Log().Error(ctx, fmt.Sprintf("%s TRON gas fee address not configured", "FeeHandlerTask"))
		return nil, fmt.Errorf("TRON gas fee address not configured")
	}

	// The TRON private key and address validation is now done above using the decrypted keys.
	// Remove the redundant checks here.

	return config, nil
}
