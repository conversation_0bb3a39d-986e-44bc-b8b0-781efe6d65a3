package withdraw_step2_fee

import (
	"context"
	"errors" // Added for errors.Is
	"fmt"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"
	utils "wallet-api/internal/utility/utils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// processProcessingTokenFeeSupplements processes token fee supplements with status=processing
func processProcessingTokenFeeSupplements(ctx context.Context, cfg *FeeHandlerConfig, logPrefix string) {
	logger := g.Log()

	// Query database for token fee supplements with status=processing
	var processingSupplements []*entity.TokenFeeSupplements
	err := dao.TokenFeeSupplements.Ctx(ctx).
		Where(dao.TokenFeeSupplements.Columns().Status, "processing").
		Limit(cfg.BatchSize).
		Scan(&processingSupplements)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s Failed to query processing token fee supplements: %v", logPrefix, err))
		return
	}

	if len(processingSupplements) == 0 {
		logger.Info(ctx, fmt.Sprintf("%s No processing token fee supplements found", logPrefix))
		return
	}

	logger.Info(ctx, fmt.Sprintf("%s Found %d processing token fee supplements", logPrefix, len(processingSupplements)))

	// Process each processing supplement
	for _, supplement := range processingSupplements {
		// Create a more specific log prefix for this supplement ID
		supplementLogPrefix := fmt.Sprintf("%s[SupplementID:%d]", logPrefix, supplement.TokenFeeSupplementId)

		var verifyErr error
		var updateData *entity.TokenFeeSupplements

		// Verify transaction status
		switch supplement.ChainType {
		case "ETH":
			updateData, verifyErr = verifyEthTransaction(ctx, supplement, cfg, supplementLogPrefix)
		case "TRON":
			updateData, verifyErr = verifyTronTransaction(ctx, supplement, cfg, supplementLogPrefix)
		default:
			logger.Warning(ctx, fmt.Sprintf("%s Unsupported chain type: %s", supplementLogPrefix, supplement.ChainType))
			continue
		}

		if verifyErr != nil {
			// Use errTransactionPending defined in eth_handler.go or a shared errors file within the package
			// Note: Direct comparison 'verifyErr == errTransactionPending' works for package-level var errors.
			// errors.Is() is more robust if errTransactionPending might be wrapped.
			if errors.Is(verifyErr, errTransactionPending) {
				logger.Info(ctx, fmt.Sprintf("%s Transaction for supplement ID %d (Chain: %s) is pending. No database update at this time.", supplementLogPrefix, supplement.TokenFeeSupplementId, supplement.ChainType))
				continue // Skip to the next supplement, no status change, no error message update in DB for pending
			} else {
				// This is a real error, proceed to update DB with 'failed' status (after retries) and JSON error message
				logger.Error(ctx, fmt.Sprintf("%s Verification error for supplement ID %d (Chain: %s): %v", supplementLogPrefix, supplement.TokenFeeSupplementId, supplement.ChainType, verifyErr))

				errorMessageJson := utils.BuildErrorMessageJson(ctx, supplement.ErrorMessage, verifyErr.Error())

				updateFieldsMap := g.Map{
					dao.TokenFeeSupplements.Columns().UpdatedAt:    gtime.Now(),
					dao.TokenFeeSupplements.Columns().ErrorMessage: errorMessageJson,
				}

				currentRetryCount := supplement.RetryCount
				maxRetries := cfg.MaxVerificationRetries // Assume cfg.MaxVerificationRetries is defined in FeeHandlerConfig
				if maxRetries == 0 {                     // Default to 3 if not configured or 0
					maxRetries = 3
				}

				if currentRetryCount < maxRetries {
					updateFieldsMap[dao.TokenFeeSupplements.Columns().RetryCount] = currentRetryCount + 1
					// Status remains 'processing' for retry on verification
					logger.Infof(ctx, "%s Incrementing retry count to %d for supplement ID %d.", supplementLogPrefix, currentRetryCount+1, supplement.TokenFeeSupplementId)
				} else {
					updateFieldsMap[dao.TokenFeeSupplements.Columns().Status] = "failed"
					logger.Errorf(ctx, "%s Max retries (%d) reached for supplement ID %d. Marking as failed.", supplementLogPrefix, maxRetries, supplement.TokenFeeSupplementId)
				}

				_, dbErr := dao.TokenFeeSupplements.Ctx(ctx).
					Data(updateFieldsMap).
					Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
					Update()
				if dbErr != nil {
					logger.Errorf(ctx, "%s Failed to update supplement ID %d after verification error: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, dbErr)
				}
				continue // Skip to the next supplement
			}
		} else if updateData == nil {
			// This case means the verification function completed without error but didn't return data for an immediate update.
			// This is now treated as a "pending" or "no-op" state, awaiting the next check cycle,
			// consistent with how TRON energy rental in-progress states are handled (returning nil, nil).
			logger.Info(ctx, fmt.Sprintf("%s Verification for supplement ID %d (Chain: %s) returned no update data. Assuming pending/no-op. Will re-check.", supplementLogPrefix, supplement.TokenFeeSupplementId, supplement.ChainType))
			continue // Skip database update for this iteration, move to the next supplement.
		} else {
			// verifyErr is nil and updateData is not nil, proceed with updating based on updateData
			updateOpFields := g.Map{
				dao.TokenFeeSupplements.Columns().UpdatedAt: gtime.Now(),
			}
			if updateData.Status != "" {
				updateOpFields[dao.TokenFeeSupplements.Columns().Status] = updateData.Status
			}
			if updateData.UserWithdrawId != 0 {
				updateOpFields[dao.TokenFeeSupplements.Columns().UserWithdrawId] = updateData.UserWithdrawId
			}
			if updateData.TransactionHash != "" {
				updateOpFields[dao.TokenFeeSupplements.Columns().TransactionHash] = updateData.TransactionHash
			}
			if updateData.EnergyAmount != 0 {
				updateOpFields[dao.TokenFeeSupplements.Columns().EnergyAmount] = updateData.EnergyAmount
			}

			// 添加TRX补充相关字段的更新
			if updateData.TrxSupplementNeeded != 0 {
				updateOpFields[dao.TokenFeeSupplements.Columns().TrxSupplementNeeded] = updateData.TrxSupplementNeeded
			}
			if updateData.TrxSupplementStatus != "" {
				updateOpFields[dao.TokenFeeSupplements.Columns().TrxSupplementStatus] = updateData.TrxSupplementStatus
			}
			if updateData.TrxSupplementHash != "" {
				updateOpFields[dao.TokenFeeSupplements.Columns().TrxSupplementHash] = updateData.TrxSupplementHash
			}
			if updateData.TrxSupplementAmount != 0 {
				updateOpFields[dao.TokenFeeSupplements.Columns().TrxSupplementAmount] = updateData.TrxSupplementAmount
			}
			if updateData.TrxBalanceBefore != 0 {
				updateOpFields[dao.TokenFeeSupplements.Columns().TrxBalanceBefore] = updateData.TrxBalanceBefore
			}
			if updateData.TrxBalanceAfter != 0 {
				updateOpFields[dao.TokenFeeSupplements.Columns().TrxBalanceAfter] = updateData.TrxBalanceAfter
			}

			if updateData.Status == "success" {
				updateOpFields[dao.TokenFeeSupplements.Columns().ErrorMessage] = "[]" // Clear error message on success
				// Optionally reset retry count on success
				// updateOpFields[dao.TokenFeeSupplements.Columns().RetryCount] = 0
			} else if updateData.ErrorMessage != "" {
				// If updateData carries an error message and status is not success,
				// it should ideally have come via verifyErr.
				// However, if it's set here, ensure it's JSON or convert it.
				// This path is less ideal; errors should primarily flow via verifyErr.
				// Forcing it to be JSON here if it's provided.
				updateOpFields[dao.TokenFeeSupplements.Columns().ErrorMessage] = utils.BuildErrorMessageJson(ctx, supplement.ErrorMessage, updateData.ErrorMessage)
			}

			if len(updateOpFields) > 1 { // UpdatedAt is always present, so > 1 means other fields changed
				_, dbErr := dao.TokenFeeSupplements.Ctx(ctx).
					Data(updateOpFields).
					Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, supplement.TokenFeeSupplementId).
					Update()
				if dbErr != nil {
					logger.Error(ctx, fmt.Sprintf("%s Failed to update supplement status for ID %d: %v", supplementLogPrefix, supplement.TokenFeeSupplementId, dbErr))
					continue // Continue to next supplement on update failure
				}
			} else {
				logger.Info(ctx, fmt.Sprintf("%s No significant fields to update from verify step for supplement ID %d (Original Status: %s).", supplementLogPrefix, supplement.TokenFeeSupplementId, supplement.Status))
			}

			// Update withdraw plan status if supplement processing was successful
			if updateData.Status == "success" {
				// Ensure WithdrawPlanId is valid before attempting update
				if supplement.WithdrawPlanId > 0 {
					_, withdrawPlanErr := dao.WithdrawPlan.Ctx(ctx).
						Data(g.Map{dao.WithdrawPlan.Columns().State: 2}). // Assuming 2 means processed/completed
						Where(dao.WithdrawPlan.Columns().WithdrawPlanId, supplement.WithdrawPlanId).
						Where(dao.WithdrawPlan.Columns().State, 1). // Only update if current state is 1 (e.g., pending)
						Update()
					if withdrawPlanErr != nil {
						logger.Error(ctx, fmt.Sprintf("%s Failed to update withdraw plan (ID: %d) status: %v", supplementLogPrefix, supplement.WithdrawPlanId, withdrawPlanErr))
					} else {
						logger.Info(ctx, fmt.Sprintf("%s Successfully updated withdraw plan (ID: %d) state to 2 after supplement success.", supplementLogPrefix, supplement.WithdrawPlanId))
					}
				} else {
					logger.Warning(ctx, fmt.Sprintf("%s WithdrawPlanId is not set for supplement ID %d. Cannot update withdraw plan status.", supplementLogPrefix, supplement.TokenFeeSupplementId))
				}
			}
			// Log the final status update attempt
			if updateData.Status != "" {
				logger.Info(ctx, fmt.Sprintf("%s Processed supplement ID %d. Final status: %s", supplementLogPrefix, supplement.TokenFeeSupplementId, updateData.Status))
			}
		}
	}
}
