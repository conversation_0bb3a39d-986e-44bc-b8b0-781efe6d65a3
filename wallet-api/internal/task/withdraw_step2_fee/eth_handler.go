package withdraw_step2_fee

import (
	"context"
	"errors" // Added import for errors
	"fmt"
	"wallet-api/internal/model/entity"
	cryptoUtil "wallet-api/internal/utility/crypto"
	utilityEth "wallet-api/internal/utility/crypto/eth" // Renamed import for clarity

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// errTransactionPending is used to indicate that a transaction is still pending confirmation.
var errTransactionPending = errors.New("transaction is pending")

// EthFeeProcessingDetails holds structured information about ETH fee processing.
type EthFeeProcessingDetails struct {
	Action          string // Describes the action taken, e.g., "balance_sufficient", "gas_fee_sent"
	Message         string // A summary message of the action.
	TransactionHash string `json:"transactionHash,omitempty"` // Transaction hash if ETH was sent.
	Amount          string `json:"amount,omitempty"`          // ETH amount estimated or sent.
}

// processErc20UsdtGasFee processes an ETH gas fee supplement
func processErc20UsdtGasFee(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}

	// Get USDT contract address from config
	usdtContractAddress := g.Cfg().MustGet(ctx, "usdt_erc20_contract", "").String()
	if len(usdtContractAddress) <= 0 {
		errMsg := "USDT contract address not configured"
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// Get wallet configuration for collection address
	walletConfig := cfg.WalletConfig
	if walletConfig == nil {
		errMsg := "Wallet configuration not found"
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// Determine collection address based on chain type
	var collectionAddress string

	collectionAddress = walletConfig.EthCollectAddress

	if len(collectionAddress) <= 0 {
		errMsg := fmt.Sprintf("Collection address not configured for chain type: %s", supplement.ChainType)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// Get USDT balance
	usdBalance, err := utilityEth.GetErc20UsdtBalance(supplement.Address)
	if err != nil {
		errMsg := fmt.Sprintf("failed to get USDT balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	logger.Infof(ctx, "%s %s USDT balance: %s", logPrefix, supplement.Address, usdBalance)
	// Convert USDT balance to decimal
	usdtBalanceDecimal, err := decimal.NewFromString(usdBalance)

	if err != nil {
		errMsg := fmt.Sprintf("failed to parse USDT balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	//获取归集金额
	decimalUsdtCollectThreshold, err := decimal.NewFromString(cfg.WalletConfig.UsdtCollectThreshold)
	if err != nil {
		errMsg := fmt.Sprintf("failed to parse USDT collect threshold: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	if decimalUsdtCollectThreshold.LessThanOrEqual(decimal.Zero) {
		errMsg := fmt.Sprintf("USDT collect threshold is invalid: %s", decimalUsdtCollectThreshold.String())
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	//比较当前的余额和归集金额
	if usdtBalanceDecimal.LessThan(decimalUsdtCollectThreshold) {
		errMsg := fmt.Sprintf("Address %s USDT balance is insufficient: %s USDT, need %s USDT (UsdtCollectThreshold)",
			supplement.Address,
			usdtBalanceDecimal.String(),
			decimalUsdtCollectThreshold.String())
		logger.Info(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	//处理手续费
	var requiredAmountDecimal decimal.Decimal

	requiredAmountDecimal, _, _, estimateErr := utilityEth.EstimateERC20TransferGas(ctx, supplement.Address, collectionAddress, usdtContractAddress)
	if estimateErr != nil {
		errMsg := fmt.Sprintf("failed to estimate gas fee for USDT transfer: %v", estimateErr)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	if err != nil {
		errMsg := fmt.Sprintf("failed to parse estimated gas fee '%s': %v", requiredAmountDecimal, err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// 计算实际需要发送的金额，增加20%的缓冲
	// requiredAmountDecimal = requiredAmountDecimal.Mul(decimal.NewFromFloat(1.2)) // Removed second buffer as per plan
	logger.Info(ctx, fmt.Sprintf("%s Estimated gas fee for USDT transfer: %s ETH", logPrefix, requiredAmountDecimal.String()))

	if requiredAmountDecimal.LessThanOrEqual(decimal.Zero) {
		logger.Warning(ctx, fmt.Sprintf("%s Required amount is zero or negative: %s ETH", logPrefix, requiredAmountDecimal.String()))
		return nil, fmt.Errorf("required amount is zero or negative: %s ETH", requiredAmountDecimal.String())
	}

	ethBalance, err := utilityEth.GetETHBalance(supplement.Address)
	if err != nil {
		errMsg := fmt.Sprintf("failed to get ETH balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	//转化为 decimal
	// Convert ETH balance to decimal
	ethBalanceDecimal, err := decimal.NewFromString(ethBalance)
	if err != nil {
		errMsg := fmt.Sprintf("failed to parse USDT balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	//todo上线之后关闭
	//判断是否大于gas费 直接创建订单标记订单完成结束
	if ethBalanceDecimal.GreaterThanOrEqual(requiredAmountDecimal) {
		logger.Infof(ctx, "%s ETH balance is sufficient: %s ETH, need %s ETH", logPrefix, ethBalanceDecimal.String(), requiredAmountDecimal.String())
		userWithdrawId, err := createUserWithdrawFromSupplement(ctx, supplement, logPrefix)
		if err != nil {
			errMsg := fmt.Sprintf("failed to create user withdraw: %v", err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return nil, fmt.Errorf(errMsg)
		}
		data.Status = "success"
		data.UpdatedAt = gtime.Now()
		data.UserWithdrawId = int64(userWithdrawId)
		return data, nil
	}

	//开始处理gas不够的情况
	logger.Infof(ctx, "%s ETH balance is insufficient: %s ETH, need %s ETH", logPrefix, ethBalanceDecimal.String(), requiredAmountDecimal.String())
	//获取私钥
	privateKey := cfg.GasFee.ETH.PrivateKey
	if len(privateKey) <= 0 {
		errMsg := "ETH gas fee private key not configured"
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// +++++++++++++ Roo: 新增日志开始 (1/3) - 确认手续费来源地址和其RPC余额 +++++++++++++
	feeSourceAddress, derivationErr := utilityEth.GetAddressFromPrivateKey(privateKey)
	if derivationErr != nil {
		logger.Errorf(ctx, "%s [LOG-DEBUG] Failed to derive fee source address from EthFeePrivateKey: %v", logPrefix, derivationErr)
		// 如果地址派生失败，这是一个严重问题，应该阻止后续操作
		return nil, fmt.Errorf("failed to derive fee source address from EthFeePrivateKey: %w", derivationErr)
	}
	logger.Infof(ctx, "%s [LOG-DEBUG] Using fee source address (derived from EthFeePrivateKey): %s", logPrefix, feeSourceAddress)

	actualFeeSourceBalanceStr, balanceErr := utilityEth.GetETHBalance(feeSourceAddress)
	if balanceErr != nil {
		logger.Errorf(ctx, "%s [LOG-DEBUG] Failed to get ETH balance for fee source address %s via RPC: %v", logPrefix, feeSourceAddress, balanceErr)
		// 即使查询余额失败，也可能需要继续尝试发送，取决于策略，但至少我们记录了错误
	} else {
		logger.Infof(ctx, "%s [LOG-DEBUG] ETH Balance of fee source address %s (queried via RPC): %s ETH", logPrefix, feeSourceAddress, actualFeeSourceBalanceStr)
	}
	// +++++++++++++ Roo: 新增日志结束 (1/3) +++++++++++++

	// 将ETH金额转换为Wei单位的整数字符串（1 ETH = 10^18 Wei）
	ethAmountFloat := requiredAmountDecimal
	weiAmount := ethAmountFloat.Mul(decimal.NewFromInt(1e18))
	amountToSendStr := weiAmount.StringFixed(0) // 移除小数部分，保留整数
	logger.Infof(ctx, "%s [LOG-DEBUG] Attempting to send ETH. Required (ETH): %s, Converted to Wei: %s", logPrefix, requiredAmountDecimal.String(), amountToSendStr)

	// +++++++++++++ Roo: 新增日志开始 (2/3) - 打印调用SendETH前的参数 +++++++++++++
	logger.Infof(ctx, "%s [LOG-DEBUG] Calling utilityEth.SendETH with params: toAddress=%s, amountStr(Wei)=%s, gasLimitIn=0, gasPriceInWei=0, maxGasFeeEth=\"0.001\"", logPrefix, supplement.Address, amountToSendStr)
	// +++++++++++++ Roo: 新增日志结束 (2/3) +++++++++++++

	txHash, sendErr := utilityEth.SendETH(
		ctx,
		privateKey,
		supplement.Address,
		amountToSendStr,
		0,
		0,
		"0.001",
	)

	if sendErr != nil {
		errMsg := fmt.Sprintf("failed to send ETH for gas fees using SendETH: %v", sendErr)
		// +++++++++++++ Roo: 新增日志开始 (3/3) - 明确记录SendETH返回的错误 +++++++++++++
		logger.Errorf(ctx, "%s [LOG-DEBUG] utilityEth.SendETH returned error: %v", logPrefix, sendErr)
		// +++++++++++++ Roo: 新增日志结束 (3/3) +++++++++++++
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf("failed to send ETH for gas fees: %w", sendErr)
	}

	logger.Infof(ctx, "%s Sent ETH for gas fees, txHash: %s", logPrefix, txHash)

	// 将Wei金额转换为ETH后再赋值给ProvidedAmount
	// providedAmountEth, err := decimal.NewFromString(amountToSendStr) // Roo: Commented out as it's no longer used
	// if err != nil { // Roo: Commented out as it's no longer used
	// 	errMsg := fmt.Sprintf("failed to parse provided amount: %v", err)
	// 	logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
	// 	return nil, fmt.Errorf(errMsg)
	// } // Roo: Commented out as it's no longer used
	data.RequiredAmount = requiredAmountDecimal.InexactFloat64()
	// data.ProvidedAmount = providedAmountEth.InexactFloat64() // 旧的，导致存了Wei
	data.ProvidedAmount = requiredAmountDecimal.InexactFloat64() // 新的，确保存的是ETH单位，因为这就是我们尝试提供的ETH数量

	data.Status = "processing"
	data.TransactionHash = txHash
	data.UpdatedAt = gtime.Now()

	return data, nil
}

// verifyEthTransaction verifies the status of an ETH transaction
func verifyEthTransaction(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}
	// Check if transaction hash is empty
	if len(supplement.TransactionHash) <= 0 {
		errMsg := "Transaction hash is empty"
		logger.Warning(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	clientManager := cryptoUtil.GetInstance()
	client, err := clientManager.GetDefaultEthClient(ctx)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to connect to ETH node at %s", logPrefix, cfg.GasFee.ETH.EthRpcUrl)
	}
	glog.Infof(ctx, "%s Connected to ETH node at %s", logPrefix, cfg.GasFee.ETH.EthRpcUrl)

	var tx *types.Transaction
	// 3. Get chain ID
	tx, isPending, err := client.TransactionByHash(ctx, common.HexToHash(supplement.TransactionHash))
	//存在hash 但是未拉到交易可能是正在打包
	if err != nil {
		logger.Warning(ctx, fmt.Sprintf("%s may be pending to get transaction by hash", logPrefix))
		return nil, nil
	}

	//进行中的交易不返回错误
	if isPending {
		logger.Warning(ctx, fmt.Sprintf("%s Transaction is pending", logPrefix))
		// For pending transactions, return a specific error to indicate this state.
		// The caller (processing_verifier) can then specifically handle this case.
		return nil, nil
	}

	if tx == nil {
		errMsg := "Transaction not found"
		logger.Warning(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	// Transaction is confirmed, check if balance is sufficient
	balance, err := utilityEth.GetETHBalance(supplement.Address)
	if err != nil {
		errMsg := fmt.Sprintf("failed to get ETH balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf("failed to get ETH balance: %w", err)
	}

	// // Convert balance to decimal
	balanceDecimal, err := decimal.NewFromString(balance)
	if err != nil {
		errMsg := fmt.Sprintf("failed to parse ETH balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf("failed to parse ETH balance: %w", err)
	}
	if balanceDecimal.LessThanOrEqual(decimal.Zero) {
		errMsg := fmt.Sprintf("ETH balance is insufficient after fee transfer: %s ETH", balanceDecimal.String())
		logger.Warning(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	userWithdrawId, err := createUserWithdrawFromSupplement(ctx, supplement, logPrefix)
	if err != nil {
		errMsg := fmt.Sprintf("failed to create user withdraw: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	data.Status = "success"
	data.UpdatedAt = gtime.Now()
	data.UserWithdrawId = int64(userWithdrawId)
	return data, nil
}

// processEthFee processes an ETH fee supplement for native ETH (not ERC20)
// This function will estimate the fee required for a native ETH transfer and update the supplement record.
// It does not actually send ETH, as the primary purpose is fee estimation for now.
func processEthFee(ctx context.Context, supplement *entity.TokenFeeSupplements, cfg *FeeHandlerConfig, logPrefix string) (updateData *entity.TokenFeeSupplements, err error) {
	logger := g.Log()
	data := &entity.TokenFeeSupplements{}
	logger.Infof(ctx, "%s Starting native ETH fee processing for supplement ID: %d, Address: %s", logPrefix, supplement.TokenFeeSupplementId, supplement.Address)

	requiredAmountDecimal, gasLimit, gasPrice, estimateErr := utilityEth.EstimateNativeEthTransferFee(ctx)
	if estimateErr != nil {
		errMsg := fmt.Sprintf("Failed to estimate native ETH transfer fee: %v", estimateErr)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg) // Exit if estimation fails
	}

	logger.Infof(ctx, "%s Successfully estimated native ETH transfer fee. ETH: %s, GasLimit: %s, GasPrice: %s Wei",
		logPrefix, requiredAmountDecimal, gasLimit.String(), gasPrice.String())

	// requiredAmountDecimal, err := decimal.NewFromString(estimatedFeeEthStr)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to parse estimated native ETH transfer fee: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg) // Exit if parsing fails
	}

	finalRequiredAmountDecimal := requiredAmountDecimal
	logger.Infof(ctx, "%s Final required amount for native ETH fee (after potential buffer): %s ETH", logPrefix, finalRequiredAmountDecimal.String())

	//获取当前的 ETH 余额
	balance, err := utilityEth.GetETHBalance(supplement.Address)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get ETH balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}
	balanceDecimal, err := decimal.NewFromString(balance)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to parse ETH balance: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf("failed to parse ETH balance: %w", err)
	}
	if balanceDecimal.LessThanOrEqual(finalRequiredAmountDecimal) {
		errMsg := fmt.Sprintf("ETH balance is insufficient: %s ETH, need %s ETH", balanceDecimal.String(), finalRequiredAmountDecimal.String())
		logger.Infof(ctx, "%s %s", logPrefix, errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	userWithdrawId, err := createUserWithdrawFromSupplement(ctx, supplement, logPrefix)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to create user withdraw: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return nil, fmt.Errorf(errMsg)
	}

	data.Status = "success"
	data.UpdatedAt = gtime.Now()
	data.UserWithdrawId = int64(userWithdrawId)

	return data, nil
}
