package deposit_step1_check

import (
	"context"
	"fmt"

	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"
	"wallet-api/internal/ports"

	"github.com/ethereum/go-ethereum/common"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/glog"
)

// fetchChainAddresses 负责获取指定链的用户地址列表
// 源文件逻辑: internal/logic/task/deposit_check_impl.go:104-114
func fetchChainAddresses(ctx context.Context, chainNameUpper string, logPrefix string) ([]*entity.Address, error) {
	var userAddresses []*entity.Address
	err := dao.Address.Ctx(ctx).Where(dao.Address.Columns().Type, chainNameUpper).Where(dao.Address.Columns().BindStatus, 1).Scan(&userAddresses)
	if err != nil {
		errMsg := fmt.Sprintf("%s Failed to fetch user addresses: %v", logPrefix, err)
		glog.Error(ctx, errMsg)
		// 返回错误而不是 continue
		return nil, gerror.Wrap(err, errMsg)
	}
	if len(userAddresses) == 0 {
		glog.Infof(ctx, "%s No active user addresses found to check.", logPrefix)
		// 返回空切片和 nil 错误，表示没有地址，但不是一个错误状态
		return []*entity.Address{}, nil
	}
	glog.Infof(ctx, "%s Found %d active user addresses to check.", logPrefix, len(userAddresses))
	return userAddresses, nil
}

// prepareTokenData 负责获取和处理代币信息
// 源文件逻辑: internal/logic/task/deposit_check_impl.go:121-192
// 修改: 去除tokens表相关的逻辑，固定校验 TRX, TRC20USDT, ETH, ERC20USDT
func prepareTokenData(ctx context.Context, chainNameUpper string, chainCfg ports.ChainConfig, logPrefix string) (
	tokenConfigs map[string]ports.TokenConfig, // 返回有效的 token 配置
	tokenIDs map[string]uint,
	tokenDecimals map[string]uint8,
	tokenContractAddresses map[string]string,
	nativeSymbol string, // 新增：返回确定的原生代币符号
	err error, // 返回处理过程中遇到的第一个严重错误
) {
	// 初始化返回值
	tokenConfigs = make(map[string]ports.TokenConfig) // 存储有效的、启用的 token 配置
	tokenIDs = make(map[string]uint)                  // Symbol -> TokenID
	tokenDecimals = make(map[string]uint8)            // Symbol -> Decimals
	tokenContractAddresses = make(map[string]string)  // Symbol -> ContractAddress (lowercase)

	// 1. 确定需要获取信息的代币符号 (原生币 + 配置中启用的代币)
	// 优先使用配置中的 nativeSymbol，否则使用链名
	// nativeSymbol 已在返回值中声明，此处直接赋值
	nativeSymbol = chainNameUpper // 赋值
	if chainCfg.NativeSymbol != "" {
		nativeSymbol = chainCfg.NativeSymbol // 直接使用配置中的原生符号，不进行大小写转换
		glog.Debugf(ctx, "%s Using native symbol from config: %s (Original chain name: %s)", logPrefix, nativeSymbol, chainNameUpper)
	}

	// 2. 硬编码支持的代币信息 (去除tokens表相关的逻辑)
	// 只支持 TRX, TRC20USDT, ETH, ERC20USDT
	switch chainNameUpper {
	case "TRON":
		// 设置 TRX 原生代币信息
		tokenIDs["TRX"] = 1 // 假设 TRX 的 TokenID 为 1，根据实际情况调整

		// 只处理 USDT 代币配置
		usdtConfig, usdtExists := chainCfg.Tokens["usdt"]
		if usdtExists && usdtConfig.Enabled && usdtConfig.ContractAddress != "" {
			symbolUpper := "USDT"
			contractAddressFromCfg := usdtConfig.ContractAddress

			// 验证合约地址 (TRC20 不需要特殊验证)
			isValidFormat := true

			if isValidFormat {
				tokenContractAddresses[symbolUpper] = contractAddressFromCfg
				glog.Infof(ctx, "%s Using %s config: Contract=%s", logPrefix, symbolUpper, contractAddressFromCfg)

				// 设置 USDT 精度
				decimals := usdtConfig.Decimals
				if decimals == 0 {
					decimals = 6 // TRC20 USDT 默认精度为 6
				}
				tokenDecimals[symbolUpper] = decimals
				glog.Infof(ctx, "%s Using %s decimals from config: %d", logPrefix, symbolUpper, decimals)

				// 设置 USDT TokenID
				tokenIDs[symbolUpper] = 2 // 假设 USDT 的 TokenID 为 2，根据实际情况调整

				// 将有效的配置加入最终返回的 map
				tokenConfigs[symbolUpper] = usdtConfig
			} else {
				glog.Warningf(ctx, "%s %s contract address '%s' is invalid in config. Skipping this token.", logPrefix, symbolUpper, contractAddressFromCfg)
			}
		} else {
			glog.Warningf(ctx, "%s USDT token not enabled or contract address not configured for TRON chain.", logPrefix)
		}

	case "ETH":
		// 设置 ETH 原生代币信息
		tokenIDs["ETH"] = 3 // 假设 ETH 的 TokenID 为 3，根据实际情况调整

		// 只处理 USDT 代币配置
		usdtConfig, usdtExists := chainCfg.Tokens["usdt"]
		if usdtExists && usdtConfig.Enabled && usdtConfig.ContractAddress != "" {
			symbolUpper := "USDT"
			contractAddressFromCfg := usdtConfig.ContractAddress

			// 验证 ETH 链上的合约地址格式
			isValidFormat := common.IsHexAddress(contractAddressFromCfg)

			if isValidFormat {
				tokenContractAddresses[symbolUpper] = contractAddressFromCfg
				glog.Infof(ctx, "%s Using %s config: Contract=%s", logPrefix, symbolUpper, contractAddressFromCfg)

				// 设置 USDT 精度
				decimals := usdtConfig.Decimals
				if decimals == 0 {
					decimals = 6 // ERC20 USDT 默认精度为 6
				}
				tokenDecimals[symbolUpper] = decimals
				glog.Infof(ctx, "%s Using %s decimals from config: %d", logPrefix, symbolUpper, decimals)

				// 设置 USDT TokenID
				tokenIDs[symbolUpper] = 4 // 假设 ERC20 USDT 的 TokenID 为 4，根据实际情况调整

				// 将有效的配置加入最终返回的 map
				tokenConfigs[symbolUpper] = usdtConfig
			} else {
				glog.Warningf(ctx, "%s %s contract address '%s' is invalid in config. Skipping this token.", logPrefix, symbolUpper, contractAddressFromCfg)
			}
		} else {
			glog.Warningf(ctx, "%s USDT token not enabled or contract address not configured for ETH chain.", logPrefix)
		}

	default:
		glog.Warningf(ctx, "%s Unsupported chain: %s. Only TRON and ETH chains are supported.", logPrefix, chainNameUpper)
	}

	// 记录原生代币信息
	if _, ok := tokenIDs[nativeSymbol]; ok {
		glog.Debugf(ctx, "%s Native token %s has TokenID: %d", logPrefix, nativeSymbol, tokenIDs[nativeSymbol])
	} else {
		glog.Warningf(ctx, "%s Native token %s not configured for chain %s.", logPrefix, nativeSymbol, chainNameUpper)
	}

	// 如果之前的步骤没有设置错误，则 err 为 nil
	return tokenConfigs, tokenIDs, tokenDecimals, tokenContractAddresses, nativeSymbol, err
}

// buildLookupTables 负责根据用户地址和有效的代币配置构建查找表
// 源文件逻辑: internal/logic/task/deposit_check_impl.go:117-120 & 194-208
// 修改: 只处理 TRX, TRC20USDT, ETH, ERC20USDT 这些代币
func buildLookupTables(ctx context.Context, userAddresses []*entity.Address, tokenConfigs map[string]ports.TokenConfig, chainNameUpper string, nativeSymbol string, logPrefix string) ( // 添加 nativeSymbol 参数
	nativeAddrToUser map[string]uint, // map[lowercase_address]userId
	tokenAddrToUser map[string]uint, // map[lowercase_address]userId (for configured tokens)
	nativeAddressesSet map[string]bool, // set of lowercase native addresses
	tokenAddressesSet map[string]bool, // set of lowercase token addresses
	originalNativeAddresses map[string]string, // map[lowercase_address]original_case_address
	originalTokenAddresses map[string]string, // 新增: map[lowercase_token_address]original_case_address
) {
	// 初始化返回值
	nativeAddrToUser = make(map[string]uint)
	tokenAddrToUser = make(map[string]uint)
	nativeAddressesSet = make(map[string]bool)
	tokenAddressesSet = make(map[string]bool)
	originalNativeAddresses = make(map[string]string) // Initialize the new map
	originalTokenAddresses = make(map[string]string)  // 初始化新增的 map

	// 填充地址查找表
	for _, ua := range userAddresses {
		// ua.Address 是用户在该链上的地址
		// ua.Type 是该地址对应的链名称 (如 "ETH", "TRX")
		// 不再进行地址大小写转换，直接使用原始地址
		addr := ua.Address

		// 只处理 TRON, ETH 链上的地址
		if chainNameUpper != "TRON" && chainNameUpper != "ETH" {
			continue
		}

		// 所有地址都视为原生币地址
		// 因为我们只关心 TRX 和 ETH 原生币，以及它们对应的 USDT
		nativeAddressesSet[addr] = true
		originalNativeAddresses[addr] = addr       // 存储原始地址
		nativeAddrToUser[addr] = uint(ua.WalletId) // 使用 WalletId 作为 userId

		// 同时将地址添加到代币地址集合中，用于检查 USDT 转账
		// 这样每个地址都会同时检查原生币和 USDT 代币转账
		if _, exists := tokenConfigs["USDT"]; exists {
			tokenAddressesSet[addr] = true
			originalTokenAddresses[addr] = addr       // 存储原始地址
			tokenAddrToUser[addr] = uint(ua.WalletId) // 使用 WalletId 作为 userId
		}
	}

	glog.Infof(ctx, "%s Built lookup tables: Native addresses=%d, Token addresses=%d", logPrefix, len(nativeAddressesSet), len(tokenAddressesSet))

	return
}
