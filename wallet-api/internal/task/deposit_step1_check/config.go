package deposit_step1_check

import (
	"context"
	"fmt" // 需要用于错误格式化

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog" // 需要用于日志记录

	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"
	"wallet-api/internal/ports" // 需要 ports.ChainConfig
)

// DepositCheckConfig 定义存款检查任务的配置结构
type DepositCheckConfig struct {
	Enabled bool   `json:"enabled"`
	Spec    string `json:"spec"`
	// Network string                       `json:"network"` // "mainnet" or "testnet" - Removed as RPC/ContractAddress are now direct strings
	Chains       map[string]ports.ChainConfig `json:"chains"` // Use ChainConfig from ports
	WalletConfig entity.Wallets               `json:"wallet_config"`
}

// loadDepositCheckConfig 加载并验证存款检查任务的配置。
// 如果配置不存在、无效或任务被禁用，则返回错误。
// 如果任务被禁用，返回 (nil, nil)。
func loadDepositCheckConfig(ctx context.Context) (*DepositCheckConfig, error) {
	var config DepositCheckConfig
	configVal := g.Cfg().MustGet(ctx, "depositCheck") // MustGet 在 key 不存在时会 panic，但下面会检查 IsNil

	// 检查配置值是否为空或不存在
	if configVal.IsNil() {
		// 返回一个明确的错误，而不是仅记录警告
		return nil, fmt.Errorf("deposit check config 'depositCheck' not found or is empty")
	}

	// 尝试将配置值扫描到结构体中
	if err := configVal.Scan(&config); err != nil {
		// 返回解析错误，而不是 Fatal
		return nil, fmt.Errorf("failed to load depositCheck config: %w", err)
	}

	// 检查任务是否在配置中被禁用
	if !config.Enabled {
		glog.Info(ctx, "Deposit check task is disabled in config.")
		// 返回 nil, nil 表示任务已禁用，无需继续执行，但不是一个错误
		return nil, nil
	}
	//加载钱包配置

	var walletConfig *entity.Wallets
	err := dao.Wallets.Ctx(ctx).Where("id", 1).Scan(&walletConfig)
	if err != nil {
		return nil, fmt.Errorf("wallet config not found: %w", err)
	}
	if walletConfig == nil {
		return nil, fmt.Errorf("wallet config not found (nil entity)")
	}
	config.WalletConfig = *walletConfig

	// 配置加载并验证成功
	glog.Info(ctx, "Deposit check config loaded.") // Removed Network field reference
	return &config, nil
}
