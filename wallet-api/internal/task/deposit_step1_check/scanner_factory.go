package deposit_step1_check

import (
	"context"
	"fmt"

	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/glog"

	"wallet-api/internal/model/entity"
	"wallet-api/internal/ports"
	"wallet-api/internal/service"
	"wallet-api/internal/task/deposit_step1_check/scanner"
	trxscanner "wallet-api/internal/task/deposit_step1_check/scanner/trx"
)

// createChainScanner 根据链配置创建相应的 ChainScanner 实例。
// 它还负责创建和管理特定于链的客户端（如 ethclient）。
// 返回创建的扫描器和需要关闭的客户端列表（目前只有 ethclient）。
// 修改: 只支持 TRX, TRC20USDT, ETH, ERC20USDT 这些代币
func createChainScanner(
	ctx context.Context,
	chainNameUpper string,
	chainCfg ports.ChainConfig,
	rpcUrl string, // 显式传入 RPC URL
	// network string, // Removed - No longer needed as RPC/ContractAddress are direct strings
	logPrefix string,
	// --- 依赖注入 ---
	redisClient *gredis.Redis, // 传入 Redis 客户端
	userRechargesService service.IUserRecharges, // 传入 UserRecharges 服务
	depositHandler func(context.Context, service.IUserRecharges, ports.DepositInfo) error, // 传入存款处理函数
	// --- 数据准备结果 ---
	userAddresses []*entity.Address, // 传入从 DB 获取的原始地址列表 (TRON case 需要)
	tokenConfigs map[string]ports.TokenConfig, // 传入有效的代币配置 (TRON case 需要)
	tokenIDs map[string]uint,
	tokenDecimals map[string]uint8,
	tokenContractAddresses map[string]string,
	nativeAddrToUser map[string]uint, // 通用查找表
	tokenAddrToUser map[string]uint, // 通用查找表
	nativeAddressesSet map[string]bool, // 通用查找表
	tokenAddressesSet map[string]bool, // 通用查找表
	originalNativeAddrs map[string]string, // 新增：小写地址到原始大小写地址的映射
	originalTokenAddrs map[string]string, // 新增：小写代币地址到原始大小写地址的映射
) (ports.ChainScanner, []*ethclient.Client, error) {

	var clientsToClose []*ethclient.Client // 用于收集需要关闭的客户端

	// 只支持 ETH 和 TRON 链
	if chainNameUpper != "ETH" && chainNameUpper != "TRON" {
		glog.Warningf(ctx, "%s Unsupported chain type '%s'. Only ETH and TRON are supported. Skipping.", logPrefix, chainNameUpper)
		return nil, nil, fmt.Errorf("%s unsupported chain type '%s'. Only ETH and TRON are supported", logPrefix, chainNameUpper)
	}

	switch chainNameUpper {
	case "ETH":
		// 初始化 ETH 客户端
		ethClient, err := ethclient.Dial(rpcUrl)
		if err != nil {
			glog.Errorf(ctx, "%s Failed to connect to ETH node %s: %v. Cannot create scanner.", logPrefix, rpcUrl, err)
			return nil, nil, fmt.Errorf("%s failed to connect to ETH node %s: %w", logPrefix, rpcUrl, err) // 返回错误，不继续
		}
		clientsToClose = append(clientsToClose, ethClient) // Add client to close later

		// 提取 ETH 特定的 Token 信息 (确保 key 存在)
		usdtContractAddress := tokenContractAddresses["USDT"] // Might be "" if not configured/valid
		usdtDecimals := tokenDecimals["USDT"]                 // Use default 0 if not found (should have fallback)
		ethTokenId := tokenIDs["ETH"]
		usdtTokenId := tokenIDs["USDT"]

		// 确保 ETH 和 USDT 的 TokenID 有值
		if ethTokenId == 0 {
			glog.Warningf(ctx, "%s ETH token ID not found. Using default value 3.", logPrefix)
			ethTokenId = 3 // 使用默认值
		}

		// 确保 USDT 的 TokenID 和精度有值
		if _, usdtEnabled := tokenConfigs["USDT"]; usdtEnabled {
			if usdtTokenId == 0 {
				glog.Warningf(ctx, "%s USDT token ID not found. Using default value 4.", logPrefix)
				usdtTokenId = 4 // 使用默认值
			}

			if usdtDecimals == 0 {
				glog.Warningf(ctx, "%s USDT decimals is 0. Using default value 6.", logPrefix)
				usdtDecimals = 6 // 使用默认值
			}
		}

		ethScanner := scanner.NewEthScanner(
			ethClient,
			redisClient,
			chainCfg,
			nativeAddrToUser,   // 使用通用的查找表
			tokenAddrToUser,    // 使用通用的查找表
			nativeAddressesSet, // 使用通用的查找表
			tokenAddressesSet,  // 使用通用的查找表
			usdtContractAddress,
			usdtDecimals,
			ethTokenId,
			usdtTokenId,
			userRechargesService,
			depositHandler,
		)
		glog.Infof(ctx, "%s ETH Scanner created.", logPrefix)
		return ethScanner, clientsToClose, nil

	case "TRON":
		// Extract TRON and USDT specific info (ensure keys exist)
		usdtContractAddress := tokenContractAddresses["USDT"] // 来自 prepareTokenData
		usdtDecimals := tokenDecimals["USDT"]                 // 来自 prepareTokenData
		trxTokenId := tokenIDs["TRX"]                         // 来自 prepareTokenData (TRX 是 TRON 链的原生代币)
		usdtTokenId := tokenIDs["USDT"]                       // 来自 prepareTokenData

		// 确保 TRX 和 USDT 的 TokenID 有值
		if trxTokenId == 0 {
			glog.Warningf(ctx, "%s TRX token ID not found. Using default value 1.", logPrefix)
			trxTokenId = 1 // 使用默认值
		}

		// 确保 USDT 的 TokenID 和精度有值
		if _, usdtEnabled := tokenConfigs["USDT"]; usdtEnabled {
			if usdtTokenId == 0 {
				glog.Warningf(ctx, "%s USDT token ID not found. Using default value 2.", logPrefix)
				usdtTokenId = 2 // 使用默认值
			}

			if usdtDecimals == 0 {
				glog.Warningf(ctx, "%s USDT decimals is 0. Using default value 6.", logPrefix)
				usdtDecimals = 6 // 使用默认值
			}
		}

		// Create TrxScanner instance using pure gRPC approach
		trxScanner, err := trxscanner.NewTrxScanner(
			redisClient,
			chainCfg,
			nativeAddrToUser,   // 使用通用的查找表
			tokenAddrToUser,    // 使用通用的查找表
			nativeAddressesSet, // 使用通用的查找表
			tokenAddressesSet,  // 使用通用的查找表
			usdtContractAddress,
			usdtDecimals,
			trxTokenId,
			usdtTokenId,
			userRechargesService,
			depositHandler,
			originalNativeAddrs, // Pass the original native addresses map
			originalTokenAddrs,  // 传递原始代币地址 map
		)
		if err != nil {
			return nil, nil, gerror.Wrapf(err, "%s failed to create TrxScanner", logPrefix)
		}
		glog.Infof(ctx, "%s TRON Scanner created.", logPrefix)
		return trxScanner, nil, nil // No clients to close for TRON HTTP client

	default:
		// 这个分支不应该被执行，因为我们在函数开始时已经检查了链类型
		glog.Warningf(ctx, "%s Unsupported chain type '%s'. Skipping.", logPrefix, chainNameUpper)
		return nil, nil, fmt.Errorf("%s unsupported chain type '%s'", logPrefix, chainNameUpper)
	}
}
