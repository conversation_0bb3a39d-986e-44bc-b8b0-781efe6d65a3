package deposit_step1_check

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/ethereum/go-ethereum/ethclient" // Needed for closing clients returned by factory
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"

	"wallet-api/internal/ports" // Contains ChainScanner, DepositInfo, ChainConfig, TokenConfig
	"wallet-api/internal/service"
)

// CheckUserDeposits 检查用户充值情况的任务 (重构后, 协调流程)
func CheckUserDeposits(ctx context.Context) {
	defer func() {
		if r := recover(); r != nil {
			glog.Errorf(ctx, "Panic recovered in CheckUserDeposits: %+v", r)
		}
	}()

	glog.Info(ctx, "开始执行充值检查任务...")

	// 1. 加载配置
	config, err := loadDepositCheckConfig(ctx)
	if err != nil {
		glog.Errorf(ctx, "Failed to load deposit check config: %v. Task cannot run.", err)
		return
	}
	if config == nil {
		// loadDepositCheckConfig logs the reason (disabled or not found)
		return
	}
	glog.Info(ctx, "Deposit check config loaded.") // Removed Network field reference

	// 2. 获取 Redis 和 UserRecharges 服务实例 (通用)
	redisClient := service.Redis().Client() // Needed for createChainScanner
	if redisClient == nil {
		glog.Error(ctx, "Failed to get default Redis client instance.")
		return
	}
	userRechargesService := service.UserRecharges()
	if userRechargesService == nil {
		glog.Error(ctx, "Failed to get UserRecharges service instance.")
		return
	}

	// 3. 准备扫描器列表
	var scanners []ports.ChainScanner      // Use the interface from ports package
	var clientsToClose []*ethclient.Client // Keep track of clients to close

	// 4. 遍历配置中的所有链
	for chainName, chainCfg := range config.Chains {
		chainNameUpper := strings.ToUpper(chainName) // Ensure consistent casing
		logPrefix := fmt.Sprintf("[CheckUserDeposits:%s]", chainNameUpper)

		if !chainCfg.Enabled {
			glog.Infof(ctx, "%s Chain is disabled in config. Skipping.", logPrefix)
			continue
		}

		// Directly use the Rpc field which is now a string
		rpcUrl := chainCfg.Rpc
		if rpcUrl == "" {
			glog.Errorf(ctx, "%s RPC URL not configured. Skipping.", logPrefix)
			continue
		}
		glog.Infof(ctx, "%s Chain enabled. RPC: %s", logPrefix, rpcUrl)

		// 5. 数据准备
		userAddresses, err := fetchChainAddresses(ctx, chainNameUpper, logPrefix)
		if err != nil {
			// fetchChainAddresses logs the error
			continue
		}
		if len(userAddresses) == 0 {
			// fetchChainAddresses logs the info
			continue
		}

		// 调用 prepareTokenData 并接收 nativeSymbol
		// Removed config.Network argument from prepareTokenData call
		tokenConfigs, tokenIDs, tokenDecimals, tokenContractAddresses, nativeSymbol, err := prepareTokenData(ctx, chainNameUpper, chainCfg, logPrefix)
		if err != nil {
			// prepareTokenData logs the error
			continue
		}

		// 将 nativeSymbol 传递给 buildLookupTables
		nativeAddrToUser, tokenAddrToUser, nativeAddressesSet, tokenAddressesSet, originalNativeAddresses, originalTokenAddresses := buildLookupTables(ctx, userAddresses, tokenConfigs, chainNameUpper, nativeSymbol, logPrefix) // 接收新增的 originalTokenAddresses
		// buildLookupTables logs info about table sizes

		// 6. 创建扫描器实例
		scannerInstance, clientsToCloseForChain, err := createChainScanner(
			ctx,
			chainNameUpper,
			chainCfg,
			rpcUrl,
			// Removed config.Network argument from createChainScanner call
			logPrefix,
			redisClient,
			userRechargesService,
			handleDeposit, // Pass the function reference from deposit_handler.go
			userAddresses, // Add missing argument
			tokenConfigs,
			tokenIDs,
			tokenDecimals,
			tokenContractAddresses,
			nativeAddrToUser,
			tokenAddrToUser,
			nativeAddressesSet,
			tokenAddressesSet,
			originalNativeAddresses, // Pass the new map here
			originalTokenAddresses,  // 传递新增的 originalTokenAddresses
		)

		if err != nil {
			glog.Errorf(ctx, "%s Failed to create scanner: %v. Skipping chain.", logPrefix, err)
			// Close any clients that might have been created before the error
			for _, client := range clientsToCloseForChain {
				if client != nil {
					client.Close()
				}
			}
			continue
		}

		scanners = append(scanners, scannerInstance)
		clientsToClose = append(clientsToClose, clientsToCloseForChain...) // Correct variable name
		glog.Infof(ctx, "%s Scanner created successfully.", logPrefix)
	}

	// Close all opened clients after setup
	defer func() {
		for _, client := range clientsToClose {
			if client != nil {
				client.Close()
			}
		}
	}()

	// 8. 并发执行所有扫描器
	if len(scanners) == 0 {
		glog.Info(ctx, "No enabled or supported chain scanners to run.")
		glog.Info(ctx, "充值检查任务执行完毕 (无扫描器).") // Add completion log here
		return
	}

	var wg sync.WaitGroup
	glog.Infof(ctx, "Starting scans for %d chains concurrently...", len(scanners))

	for _, sc := range scanners {
		wg.Add(1)
		go func(s ports.ChainScanner) { // Use the interface from ports package
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					// Use a new context for logging within the goroutine if ctx might be cancelled
					glog.Errorf(gctx.New(), "Panic recovered during %s scan: %+v", s.GetChainName(), r)
				}
			}()
			// Use a new context for the scan itself
			scanCtx := gctx.New() // Create a new context for each scan goroutine
			glog.Infof(scanCtx, "Starting scan for %s chain...", s.GetChainName())
			err := s.Scan(scanCtx) // Pass the new context
			if err != nil {
				glog.Errorf(scanCtx, "%s chain scan failed: %v", s.GetChainName(), err) // Log with scanCtx
			} else {
				glog.Infof(scanCtx, "%s chain scan completed.", s.GetChainName())
			}
		}(sc)
	}

	wg.Wait()
	glog.Info(ctx, "All chain deposit checks completed.")
}

// --- handleDeposit function is now in deposit_handler.go ---
// --- Old scanning logic removed ---
