package deposit_step1_check

import (
	"context"
	"wallet-api/internal/task_registry"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	// depositCheckTask 定义了充值检查任务的详细信息
	task_registry.Register(task_registry.TaskInfo{
		Name: "DepositCheckTask",
		SpecFunc: func(ctx context.Context) (spec string, enabled bool, err error) {
			// 从配置中读取 spec 和 enabled
			// 提供默认值以防配置缺失
			spec = g.Cfg().MustGet(ctx, "depositCheck.spec", "# * * * * *").String() // 默认每分钟
			enabled = g.Cfg().MustGet(ctx, "depositCheck.enabled", false).Bool()     // 默认禁用
			// MustGet 不会返回错误，如果键不存在则使用默认值
			return spec, enabled, nil
		},
		Func:        CheckUserDeposits, // 任务执行函数, 假设 CheckUserDeposits 在本包中定义
		IsSingleton: true,
	})
}
