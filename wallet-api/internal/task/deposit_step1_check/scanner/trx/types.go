package trx

// TronGridBaseResponse 是 TronGrid API 响应的基础结构
type TronGridBaseResponse struct {
	Success bool          `json:"success"`
	Meta    *TronGridMeta `json:"meta"`
	Error   string        `json:"error,omitempty"` // 某些错误可能直接在顶层返回
}

// TronGridMeta 包含分页信息
type TronGridMeta struct {
	At       int64          `json:"at"`
	PageSize int            `json:"page_size"`
	Links    *TronGridLinks `json:"links,omitempty"` // 指向下一页的链接
}

// TronGridLinks 包含下一页的 URL
type TronGridLinks struct {
	Next string `json:"next,omitempty"`
}

// TronTransaction 代表 TronGrid 返回的 TRX 交易信息
type TronTransaction struct {
	Ret              []map[string]interface{} `json:"ret"`
	Signature        []string                 `json:"signature"`
	TxID             string                   `json:"txID"`
	NetUsage         int64                    `json:"net_usage"`
	RawDataHex       string                   `json:"raw_data_hex"`
	NetFee           int64                    `json:"net_fee"`
	EnergyUsage      int64                    `json:"energy_usage"`
	BlockNumber      int64                    `json:"blockNumber"`
	BlockTimestamp   int64                    `json:"block_timestamp"` // 毫秒时间戳
	EnergyFee        int64                    `json:"energy_fee"`
	EnergyUsageTotal int64                    `json:"energy_usage_total"`
	RawData          struct {
		Contract []struct {
			Parameter struct {
				Value   map[string]interface{} `json:"value"`
				TypeURL string                 `json:"type_url"`
			} `json:"parameter"`
			Type string `json:"type"` // e.g., "TransferContract"
		} `json:"contract"`
		RefBlockBytes string `json:"ref_block_bytes"`
		RefBlockHash  string `json:"ref_block_hash"`
		Expiration    int64  `json:"expiration"`
		Timestamp     int64  `json:"timestamp"`
	} `json:"raw_data"`
	InternalTransactions []interface{} `json:"internal_transactions"` // 根据需要定义更具体的结构
}

// TronTransactionsResponse 是 /v1/accounts/{address}/transactions 的响应结构
type TronTransactionsResponse struct {
	TronGridBaseResponse
	Data []TronTransaction `json:"data"`
}

// TronTrc20Transaction 代表 TronGrid 返回的 TRC20 交易信息
type TronTrc20Transaction struct {
	TransactionID string `json:"transaction_id"`
	TokenInfo     struct {
		Symbol   string `json:"symbol"`
		Address  string `json:"address"`
		Decimals int    `json:"decimals"`
		Name     string `json:"name"`
	} `json:"token_info"`
	BlockTimestamp int64  `json:"block_timestamp"` // 毫秒时间戳
	From           string `json:"from"`
	To             string `json:"to"`
	Type           string `json:"type"`  // e.g., "Transfer"
	Value          string `json:"value"` // 字符串形式的金额
}

// TronTrc20TransactionsResponse 是 /v1/accounts/{address}/transactions/trc20 的响应结构
type TronTrc20TransactionsResponse struct {
	TronGridBaseResponse
	Data []TronTrc20Transaction `json:"data"`
}

// TronTransactionInfo 代表 /wallet/gettransactioninfobyid 的响应结构
// 注意：这是一个根据常见 Tron API 响应推测的结构，可能需要根据实际 API 文档调整
type TronTransactionInfo struct {
	ID              string                   `json:"id"` // 交易哈希
	Fee             int64                    `json:"fee"`
	BlockNumber     int64                    `json:"blockNumber"`
	BlockTimeStamp  int64                    `json:"blockTimeStamp"` // 毫秒时间戳
	ContractResult  []string                 `json:"contractResult"` // 通常为空数组
	ContractAddress string                   `json:"contract_address"`
	Receipt         TronTransactionReceipt   `json:"receipt"`
	Log             []TronTransactionInfoLog `json:"log"`
	// ... 可能还有其他字段，如 internal_transactions, raw_data 等
}

// TronTransactionReceipt 包含交易执行结果信息
type TronTransactionReceipt struct {
	EnergyUsage       int64  `json:"energy_usage"`
	EnergyFee         int64  `json:"energy_fee"`
	OriginEnergyUsage int64  `json:"origin_energy_usage"`
	EnergyUsageTotal  int64  `json:"energy_usage_total"`
	NetUsage          int64  `json:"net_usage"`
	NetFee            int64  `json:"net_fee"`
	Result            string `json:"result"` // e.g., "SUCCESS", "REVERT"
	// ... 其他字段
}

// TronTransactionInfoLog 包含交易事件日志
type TronTransactionInfoLog struct {
	Address string   `json:"address"`
	Topics  []string `json:"topics"`
	Data    string   `json:"data"`
}

// TronBlockInfo 代表 /wallet/getnowblock 的响应结构
type TronBlockInfo struct {
	BlockID     string `json:"blockID"`
	BlockHeader struct {
		RawData struct {
			Number         int64  `json:"number"`
			TxTrieRoot     string `json:"txTrieRoot"`
			WitnessAddress string `json:"witness_address"`
			ParentHash     string `json:"parentHash"`
			Version        int32  `json:"version"`
			Timestamp      int64  `json:"timestamp"` // 毫秒时间戳
		} `json:"raw_data"`
		WitnessSignature string `json:"witness_signature"`
	} `json:"block_header"`
}
