package trx

import (
	"context"
	"fmt"

	"wallet-api/internal/ports" // Corrected import path based on go.mod
	"wallet-api/internal/service"
	cryptoUtil "wallet-api/internal/utility/crypto"

	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// TrxScanner 结构体定义 - 纯 gRPC 架构
type TrxScanner struct {
	clientManager           *cryptoUtil.ClientManager // Use existing ClientManager for gRPC
	tronGrpcClient          *client.GrpcClient         // gRPC client for all operations
	redisClient             *gredis.Redis
	chainCfg                ports.ChainConfig
	nativeAddrToUser        map[string]uint // TRX 地址 (lowercase Base58) -> 用户 ID
	usdtAddrToUser          map[string]uint // USDT 地址 (lowercase Base58) -> 用户 ID
	nativeAddressesSet      map[string]bool // 存储所有需要扫描的 TRX 地址 (lowercase Base58)
	usdtAddressesSet        map[string]bool // 存储所有需要扫描的 USDT 地址 (lowercase Base58)
	usdtContractAddress     string
	usdtDecimals            uint8
	trxTokenId              uint
	usdtTokenId             uint
	userRechargesSvc        service.IUserRecharges
	handleDepositFunc       func(context.Context, service.IUserRecharges, ports.DepositInfo) error // Updated signature
	logger                  *glog.Logger
	originalNativeAddresses map[string]string // Added: map[lowercase_address]original_case_address
	originalTokenAddresses  map[string]string // 新增: map[lowercase_token_address]original_case_address
}

// NewTrxScanner 创建一个新的 TrxScanner 实例 - 纯 gRPC 架构
func NewTrxScanner(
	redisClient *gredis.Redis,
	chainCfg ports.ChainConfig,
	nativeAddrToUser map[string]uint,
	usdtAddrToUser map[string]uint,
	nativeAddressesSet map[string]bool,
	usdtAddressesSet map[string]bool,
	usdtContractAddress string,
	usdtDecimals uint8,
	trxTokenId uint,
	usdtTokenId uint,
	userRechargesSvc service.IUserRecharges,
	handleDepositFunc func(context.Context, service.IUserRecharges, ports.DepositInfo) error,
	originalNativeAddrs map[string]string,
	originalTokenAddrs map[string]string,
) (*TrxScanner, error) {
	logger := g.Log("TRX")                        
	clientManager := cryptoUtil.GetInstance()    // Get singleton ClientManager for gRPC

	return &TrxScanner{
		clientManager:           clientManager,
		tronGrpcClient:          nil,                   // Will be set when needed
		redisClient:             redisClient,
		chainCfg:                chainCfg,
		nativeAddrToUser:        nativeAddrToUser,
		usdtAddrToUser:          usdtAddrToUser,
		nativeAddressesSet:      nativeAddressesSet,
		usdtAddressesSet:        usdtAddressesSet,
		usdtContractAddress:     usdtContractAddress,
		usdtDecimals:            usdtDecimals,
		trxTokenId:              trxTokenId,
		usdtTokenId:             usdtTokenId,
		userRechargesSvc:        userRechargesSvc,
		handleDepositFunc:       handleDepositFunc,
		logger:                  logger,
		originalNativeAddresses: originalNativeAddrs, // Assign the new field
		originalTokenAddresses:  originalTokenAddrs,  // 赋值新增字段
	}, nil
}

// ensureTronGrpcClient 确保有可用的 TRON gRPC 客户端
func (s *TrxScanner) ensureTronGrpcClient(ctx context.Context) (*client.GrpcClient, error) {
	if s.tronGrpcClient != nil {
		return s.tronGrpcClient, nil
	}
	
	// 使用 ClientManager 获取默认的 TRON 客户端
	tronClient, err := s.clientManager.GetDefaultTronClient(ctx)
	if err != nil {
		// 如果默认客户端失败，尝试故障转移
		tronClient, err = s.clientManager.GetTronClientWithFailover(ctx)
		if err != nil {
			return nil, err
		}
	}
	
	s.tronGrpcClient = tronClient
	return tronClient, nil
}

// getLatestBlockTimestamp 使用 gRPC 获取最新区块时间戳
func (s *TrxScanner) getLatestBlockTimestamp(ctx context.Context) (int64, error) {
	tronClient, err := s.ensureTronGrpcClient(ctx)
	if err != nil {
		return 0, err
	}
	
	block, err := tronClient.GetNowBlock()
	if err != nil {
		// 尝试故障转移
		s.tronGrpcClient = nil // Reset client to force reconnection
		tronClient, failoverErr := s.clientManager.HandleTronError(ctx, err)
		if failoverErr != nil {
			return 0, failoverErr
		}
		s.tronGrpcClient = tronClient
		
		// 重试获取区块
		block, err = tronClient.GetNowBlock()
		if err != nil {
			return 0, err
		}
	}
	
	if block == nil || block.GetBlockHeader() == nil || block.GetBlockHeader().GetRawData() == nil {
		return 0, fmt.Errorf("received invalid block data")
	}
	
	return block.GetBlockHeader().GetRawData().GetTimestamp(), nil
}

// 注意：gRPC API 不直接支持按时间戳范围查询交易
// 这需要使用不同的方法，比如轮询最新交易或使用事件监听
// 对于现在的迁移，我们需要重新设计扫描逻辑

// GetChainName 返回链的名称
func (s *TrxScanner) GetChainName() string {
	return "TRON"
}

// Scan 实现扫描逻辑
func (s *TrxScanner) Scan(ctx context.Context) error {
	s.logger.Infof(ctx, "Starting TRON/TRC20 USDT scan...")

	// 1. 获取最新区块时间戳
	latestTimestamp, err := s.getLatestBlockTimestamp(ctx)
	if err != nil {
		s.logger.Errorf(ctx, "Failed to get latest block timestamp: %v", err)
		return err
	}
	s.logger.Debugf(ctx, "Latest block timestamp: %d", latestTimestamp)

	// 2. 扫描 TRX 交易
	trxScanErr := s.scanTrxTransactions(ctx, latestTimestamp)
	if trxScanErr != nil {
		// 错误已在内部记录，但我们仍然尝试扫描 USDT
		s.logger.Errorf(ctx, "TRX scan encountered an error: %v. Proceeding with USDT scan.", trxScanErr)
		// 不直接返回，以便 USDT 扫描可以进行，但最终会返回错误
	}

	// 3. 扫描 TRC20 USDT 交易 (如果配置了)
	var usdtScanErr error
	if s.usdtContractAddress != "" && len(s.usdtAddressesSet) > 0 {
		usdtScanErr = s.scanTrc20Transactions(ctx, latestTimestamp)
		if usdtScanErr != nil {
			// 错误已在内部记录
			s.logger.Errorf(ctx, "TRC20 USDT scan encountered an error: %v.", usdtScanErr)
		}
	} else {
		s.logger.Infof(ctx, "Skipping TRC20 USDT scan: contract address or addresses set is empty.")
	}

	s.logger.Infof(ctx, "Finished TRON/TRC20 USDT scan.")

	// 如果任何一个扫描步骤出错，则返回第一个错误
	if trxScanErr != nil {
		return trxScanErr
	}
	return usdtScanErr // 返回 USDT 扫描的错误（如果存在）或 nil
}
