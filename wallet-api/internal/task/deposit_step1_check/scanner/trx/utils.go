package trx

import (
	"context"
	"fmt"

	"math/big"

	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/os/glog" // 假设 gredis 使用 v9
)

const (
	trxRedisLastTimestampKey  = "deposit_check:last_timestamp:trx"
	usdtRedisLastTimestampKey = "deposit_check:last_timestamp:trx_usdt"
)

// updateLastCheckedTimestamp 更新 Redis 中的时间戳检查点
func updateLastCheckedTimestamp(ctx context.Context, redisClient *gredis.Redis, redisKey string, timestamp int64, logger *glog.Logger) error {
	_, err := redisClient.Set(ctx, redisKey, timestamp)
	if err != nil {
		logger.Errorf(ctx, "CRITICAL: Failed to update last checked timestamp in Redis key '%s' to %d: %v", redisKey, timestamp, err)
		// 更新检查点失败是一个严重问题，可能导致重复处理或丢失交易
		// Use fmt.Errorf
		return fmt.Errorf("failed to update timestamp checkpoint in Redis key '%s': %w", redisKey, err)
	}
	logger.Infof(ctx, "Successfully updated Redis key '%s' to timestamp %d.", redisKey, timestamp)
	return nil
}

// convertTrxAmountToString 将代币的最小单位 (string from API) 根据精度转换为字符串表示
func convertTrxAmountToString(amountStr string, decimals uint8) (string, error) {
	amountInt, ok := new(big.Int).SetString(amountStr, 10)
	if !ok {
		// 如果原始金额字符串无法解析为整数，这是一个错误
		return "", fmt.Errorf("failed to parse amount string '%s' to big.Int", amountStr)
	}
	// SetString 成功不代表 amountInt 不是 nil，虽然理论上不会，但检查更安全
	if amountInt == nil {
		return "0", fmt.Errorf("parsed amount string '%s' resulted in nil big.Int", amountStr) // 返回错误更明确
	}
	// 如果金额为0或负数，直接返回 "0" 或进行相应处理（根据业务逻辑，这里先返回 "0"）
	if amountInt.Sign() <= 0 {
		return "0", nil
	}

	amountFloat := new(big.Float).SetInt(amountInt)
	// 计算 10 的 decimals 次方
	divisor := new(big.Float).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals)), nil))

	// 防止除以0（虽然 decimals > 0 应该保证了 divisor > 0）
	if divisor.Sign() <= 0 {
		return "", fmt.Errorf("divisor calculated from decimals %d is not positive", decimals)
	}

	resultFloat := new(big.Float).Quo(amountFloat, divisor)
	// FormatFloat 'f' 表示普通小数格式, -1 表示尽可能精确的小数位数
	// 可以考虑限制小数位数，例如 8 位: return resultFloat.Text('f', 8), nil
	return resultFloat.Text('f', -1), nil
}

// convertSunToTrxString 将 sun (int64) 转换为 TRX (string)，固定6位小数
func convertSunToTrxString(sunAmount int64) string {
	// 1 TRX = 1,000,000 sun
	const sunPerTrx = 1_000_000
	const decimals = 6

	// 使用 big.Int 进行计算以避免溢出
	amountInt := big.NewInt(sunAmount)

	// 如果金额为0或负数，直接返回 "0"
	if amountInt.Sign() <= 0 {
		return "0"
	}

	amountFloat := new(big.Float).SetInt(amountInt)
	divisor := new(big.Float).SetInt64(sunPerTrx)

	resultFloat := new(big.Float).Quo(amountFloat, divisor)

	// 格式化为字符串，保留6位小数
	return resultFloat.Text('f', decimals)
}
