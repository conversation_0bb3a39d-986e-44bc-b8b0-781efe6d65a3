package trx

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/big"

	"wallet-api/internal/ports"

	"github.com/fbsobreira/gotron-sdk/pkg/common"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"
)

// scanTrxTransactionsByBlocks 使用 gRPC 通过区块扫描 TRX 交易
func (s *TrxScanner) scanTrxTransactionsByBlocks(ctx context.Context, latestTimestamp int64) error {
	s.logger.Infof(ctx, "Starting gRPC-based TRX transaction scan...")

	// 从 Redis 获取上次扫描的区块高度
	lastBlockKey := "deposit_check:last_block:trx"
	lastBlockVar, err := s.redisClient.Get(ctx, lastBlockKey)
	if err != nil {
		s.logger.Warningf(ctx, "Failed to get last block from Redis, starting from current block: %v", err)
	}

	var lastBlockStr string
	if lastBlockVar != nil {
		lastBlockStr = lastBlockVar.String()
	}

	tronClient, err := s.ensureTronGrpcClient(ctx)
	if err != nil {
		return fmt.Errorf("failed to get TRON gRPC client: %w", err)
	}

	// 获取当前最新区块
	latestBlock, err := tronClient.GetNowBlock()
	if err != nil {
		return fmt.Errorf("failed to get latest block: %w", err)
	}

	latestBlockNum := latestBlock.GetBlockHeader().GetRawData().GetNumber()
	s.logger.Infof(ctx, "Latest block number: %d", latestBlockNum)

	// 确定起始区块
	var startBlock int64
	if lastBlockStr != "" && lastBlockStr != "0" {
		startBlock = gconv.Int64(lastBlockStr) + 1
	} else {
		// 如果是第一次运行，从最新区块开始（只处理新交易）
		startBlock = latestBlockNum
		s.logger.Infof(ctx, "First run, starting from latest block: %d", startBlock)
	}

	// 限制扫描区块数量以避免过长的处理时间
	const maxBlocksPerScan = 100
	endBlock := startBlock + maxBlocksPerScan
	if endBlock > latestBlockNum {
		endBlock = latestBlockNum
	}

	if startBlock > latestBlockNum {
		s.logger.Infof(ctx, "No new blocks to scan (start: %d, latest: %d)", startBlock, latestBlockNum)
		return nil
	}

	s.logger.Infof(ctx, "Scanning blocks from %d to %d", startBlock, endBlock)

	var processedCount int
	for blockNum := startBlock; blockNum <= endBlock; blockNum++ {
		block, err := tronClient.GetBlockByNum(blockNum)
		if err != nil {
			s.logger.Errorf(ctx, "Failed to get block %d: %v", blockNum, err)
			// 如果获取区块失败，不能更新进度，应该中断扫描等待下次重试
			return fmt.Errorf("failed to get block %d: %w", blockNum, err)
		}

		if block == nil {
			s.logger.Warningf(ctx, "Block %d is nil", blockNum)
			// 区块为空也应该更新进度，避免卡在这个区块
		} else {
			// 处理区块中的所有交易
			for _, txWrapper := range block.GetTransactions() {
				tx := txWrapper.GetTransaction()
				if tx == nil || tx.GetRawData() == nil {
					continue
				}

				// 检查是否是 TRX 转账交易
				txData := tx.GetRawData()
				for _, contract := range txData.GetContract() {
					if contract.GetType() == core.Transaction_Contract_TransferContract {
						// 解析转账合约
						if err := s.processTrxTransaction(ctx, tx, contract, blockNum); err != nil {
							s.logger.Errorf(ctx, "Failed to process TRX transaction: %v", err)
							// 处理交易失败不应该中断整个扫描，继续处理其他交易
						} else {
							processedCount++
						}
					} else if contract.GetType() == core.Transaction_Contract_TriggerSmartContract {
						// 检查是否是 TRC20 转账
						if err := s.processTrc20Transaction(ctx, tx, contract, blockNum); err != nil {
							s.logger.Errorf(ctx, "Failed to process TRC20 transaction: %v", err)
							// 处理交易失败不应该中断整个扫描，继续处理其他交易
						} else {
							processedCount++
						}
					}
				}
			}
		}

		// 每处理完一个区块就立即更新 Redis 进度（关键修复）
		_, err = s.redisClient.Set(ctx, lastBlockKey, blockNum)
		if err != nil {
			// 这是一个严重问题，可能导致重复处理或跳过区块
			s.logger.Errorf(ctx, "CRITICAL: Failed to update last block %d in Redis (%s): %v. Stopping scan to prevent data inconsistency.", blockNum, lastBlockKey, err)
			// 中断扫描，避免数据不一致
			return fmt.Errorf("failed to update block progress to %d in Redis: %w", blockNum, err)
		}

		// 每处理 10 个区块记录一次进度日志
		if blockNum%10 == 0 || blockNum == endBlock {
			s.logger.Infof(ctx, "Processed block %d, found %d transactions so far, updated Redis progress", blockNum, processedCount)
		}
	}

	s.logger.Infof(ctx, "Completed gRPC-based TRX transaction scan. Processed %d transactions from blocks %d-%d", processedCount, startBlock, endBlock)
	return nil
}

// processTrxTransaction 处理单个 TRX 转账交易
func (s *TrxScanner) processTrxTransaction(ctx context.Context, tx *core.Transaction, contract *core.Transaction_Contract, blockNum int64) error {
	// 计算交易哈希
	rawData, err := proto.Marshal(tx.GetRawData())
	if err != nil {
		return fmt.Errorf("failed to marshal transaction raw data: %w", err)
	}
	hash := sha256.Sum256(rawData)
	txHash := hex.EncodeToString(hash[:])

	s.logger.Debugf(ctx, "Processing TRX transaction: %s in block %d", txHash, blockNum)

	// 解析 TransferContract
	transferContract := &core.TransferContract{}
	if err := contract.GetParameter().UnmarshalTo(transferContract); err != nil {
		return fmt.Errorf("failed to unmarshal TransferContract: %w", err)
	}

	// 获取发送者和接收者地址
	fromAddr := common.EncodeCheck(transferContract.GetOwnerAddress())
	toAddr := common.EncodeCheck(transferContract.GetToAddress())
	amountSun := transferContract.GetAmount() // 金额单位为 SUN

	s.logger.Debugf(ctx, "TRX Transfer: From=%s, To=%s, Amount=%d SUN, TxHash=%s", fromAddr, toAddr, amountSun, txHash)

	// 检查接收地址是否在我们监控的集合中
	if _, exists := s.nativeAddressesSet[toAddr]; !exists {
		// 不是我们监控的地址，跳过
		return nil
	}

	// 检查金额是否有效
	if amountSun <= 0 {
		s.logger.Warningf(ctx, "Invalid TRX amount %d SUN for transaction %s", amountSun, txHash)
		return nil
	}

	// 查找用户 ID
	userId, userExists := s.nativeAddrToUser[toAddr]
	if !userExists {
		s.logger.Warningf(ctx, "TRX deposit to address %s found, but no user mapping exists. TxID: %s", toAddr, txHash)
		return nil
	}

	// 转换金额从 SUN 到 TRX (1 TRX = 1,000,000 SUN)
	amountTRX := decimal.NewFromInt(amountSun).Div(decimal.New(1, 6))
	amountTrxStr := amountTRX.String()

	s.logger.Infof(ctx, "Detected TRX deposit: UserID=%d, To=%s, From=%s, Amount=%s TRX (Raw: %d SUN), TxID=%s",
		userId, toAddr, fromAddr, amountTrxStr, amountSun, txHash)

	// 创建存款信息
	depositInfo := ports.DepositInfo{
		TxHash:      txHash,
		FromAddress: fromAddr,
		ToAddress:   toAddr,
		Amount:      amountTrxStr,
		BlockNumber: uint64(blockNum),
		TokenSymbol: "TRX",
		Chain:       s.GetChainName(),
		UserID:      userId,
		TokenID:     s.trxTokenId,
	}

	// 调用存款处理函数
	return s.handleDepositFunc(ctx, s.userRechargesSvc, depositInfo)
}

// processTrc20Transaction 处理单个 TRC20 交易
func (s *TrxScanner) processTrc20Transaction(ctx context.Context, tx *core.Transaction, contract *core.Transaction_Contract, blockNum int64) error {
	// 计算交易哈希
	rawData, err := proto.Marshal(tx.GetRawData())
	if err != nil {
		return fmt.Errorf("failed to marshal transaction raw data: %w", err)
	}
	hash := sha256.Sum256(rawData)
	txHash := hex.EncodeToString(hash[:])

	s.logger.Debugf(ctx, "Processing TRC20 transaction: %s in block %d", txHash, blockNum)

	// 检查是否为 USDT 合约地址，如果配置为空则跳过
	if s.usdtContractAddress == "" {
		return nil
	}

	// 为了获取 TRC20 转账信息，我们需要查询交易日志
	// 因为智能合约调用的具体参数在 TriggerSmartContract 中不易解析
	tronClient, err := s.ensureTronGrpcClient(ctx)
	if err != nil {
		return fmt.Errorf("failed to get TRON gRPC client: %w", err)
	}

	// 获取交易信息以访问日志
	txInfo, err := tronClient.GetTransactionInfoByID(txHash)
	if err != nil {
		s.logger.Warningf(ctx, "Failed to get transaction info for %s: %v", txHash, err)
		return nil // 不是致命错误，继续处理其他交易
	}

	// Transfer 事件签名: keccak256("Transfer(address,address,uint256)")
	const transferEventSig = "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"

	// 解析交易日志中的 Transfer 事件
	for _, logEntry := range txInfo.GetLog() {
		// 检查是否是 Transfer 事件 (3 个 topics: 签名, from, to)
		if len(logEntry.GetTopics()) != 3 {
			continue
		}

		// 检查事件签名
		topic0 := hex.EncodeToString(logEntry.GetTopics()[0])
		if topic0 != transferEventSig {
			continue
		}

		// 解析合约地址
		contractAddrBytes := append([]byte{0x41}, logEntry.GetAddress()...)
		contractAddr := common.EncodeCheck(contractAddrBytes)

		// 检查是否是我们监控的 USDT 合约
		if contractAddr != s.usdtContractAddress {
			continue
		}

		// 解析 'to' 地址 (Topic[2])
		var toAddrBytes [21]byte
		toAddrBytes[0] = 0x41                               // TRON 主网前缀
		copy(toAddrBytes[1:], logEntry.GetTopics()[2][12:]) // 取后 20 个字节
		toAddr := common.EncodeCheck(toAddrBytes[:])

		// 检查接收地址是否在我们监控的集合中
		if _, exists := s.usdtAddressesSet[toAddr]; !exists {
			continue // 不是我们监控的地址，跳过
		}

		// 解析 'from' 地址 (Topic[1])
		var fromAddrBytes [21]byte
		fromAddrBytes[0] = 0x41
		copy(fromAddrBytes[1:], logEntry.GetTopics()[1][12:])
		fromAddr := common.EncodeCheck(fromAddrBytes[:])

		// 解析转账金额 (从 data 字段)
		amountRaw := new(big.Int).SetBytes(logEntry.GetData())
		if amountRaw.Cmp(big.NewInt(0)) <= 0 {
			s.logger.Warningf(ctx, "Invalid TRC20 amount %s for transaction %s", amountRaw.String(), txHash)
			continue
		}

		// 查找用户 ID
		userId, userExists := s.usdtAddrToUser[toAddr]
		if !userExists {
			s.logger.Warningf(ctx, "TRC20 USDT deposit to address %s found, but no user mapping exists. TxID: %s", toAddr, txHash)
			continue
		}

		// 转换金额考虑精度 (USDT 通常是 6 位小数)
		amountDecimal := decimal.NewFromBigInt(amountRaw, 0).Div(decimal.New(1, int32(s.usdtDecimals)))
		amountStr := amountDecimal.String()

		s.logger.Infof(ctx, "Detected TRC20 USDT deposit: UserID=%d, To=%s, From=%s, Amount=%s USDT (Raw: %s), TxID=%s",
			userId, toAddr, fromAddr, amountStr, amountRaw.String(), txHash)

		// 创建存款信息
		depositInfo := ports.DepositInfo{
			TxHash:      txHash,
			FromAddress: fromAddr,
			ToAddress:   toAddr,
			Amount:      amountStr,
			BlockNumber: uint64(blockNum),
			TokenSymbol: "USDT",
			Chain:       s.GetChainName(),
			UserID:      userId,
			TokenID:     s.usdtTokenId,
		}

		// 调用存款处理函数
		if err := s.handleDepositFunc(ctx, s.userRechargesSvc, depositInfo); err != nil {
			return err
		}
	}

	return nil
}

// 替换原来的基于 HTTP API 的扫描方法
func (s *TrxScanner) scanTrxTransactions(ctx context.Context, latestTimestamp int64) error {
	return s.scanTrxTransactionsByBlocks(ctx, latestTimestamp)
}

func (s *TrxScanner) scanTrc20Transactions(ctx context.Context, latestTimestamp int64) error {
	// TRC20 扫描现在集成在区块扫描中
	s.logger.Infof(ctx, "TRC20 scanning is now integrated with block-based scanning")
	return nil
}
