package scanner

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	// "time" // Removed unused import

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/ethereum/go-ethereum/params"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/errors/gerror"

	// "github.com/gogf/gf/v2/frame/g" // Removed unused import
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"

	// "wallet-api/internal/task" // No longer needed for ChainConfig
	"wallet-api/internal/ports" // Contains ChainScanner, DepositInfo, ChainConfig, TokenConfig
	"wallet-api/internal/service"
)

const (
	maxBlocksPerFilterLogsRequest = 5 // 每次 FilterLogs 调用的最大区块数量
)

// 全局变量，用于解析 ERC20 Transfer 事件
var (
	erc20ABI             abi.ABI
	transferEventSigHash common.Hash
)

func init() {
	// 解析 ERC20 ABI，特别是 Transfer 事件
	var err error
	erc20ABI, err = abi.JSON(strings.NewReader(`[{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"}]`))
	if err != nil {
		// 在 init 函数中，如果 ABI 解析失败，通常应该 panic
		panic(fmt.Sprintf("无法解析 ERC20 ABI: %v", err))
	}
	// 获取 Transfer 事件的签名哈希
	transferEventSigHash = erc20ABI.Events["Transfer"].ID
}

// EthScanner 实现 ChainScanner 接口，用于扫描 ETH 链上的存款
type EthScanner struct {
	ethClient            *ethclient.Client
	redisClient          *gredis.Redis
	chainCfg             ports.ChainConfig // Use ChainConfig from ports package
	nativeAddrToUser     map[string]uint
	usdtAddrToUser       map[string]uint
	nativeAddressesSet   map[string]bool
	usdtAddressesSet     map[string]bool
	usdtContractAddress  string
	usdtDecimals         uint8
	ethTokenId           uint
	usdtTokenId          uint
	userRechargesService service.IUserRecharges
	handleDepositFunc    func(context.Context, service.IUserRecharges, ports.DepositInfo) error // Updated signature
}

// NewEthScanner 创建一个新的 EthScanner 实例
func NewEthScanner(
	ethClient *ethclient.Client,
	redisClient *gredis.Redis,
	chainCfg ports.ChainConfig, // Use ChainConfig from ports package
	nativeAddrToUser map[string]uint,
	usdtAddrToUser map[string]uint,
	nativeAddressesSet map[string]bool,
	usdtAddressesSet map[string]bool,
	usdtContractAddress string,
	usdtDecimals uint8,
	ethTokenId uint,
	usdtTokenId uint,
	userRechargesService service.IUserRecharges,
	handleDepositFunc func(context.Context, service.IUserRecharges, ports.DepositInfo) error, // Updated signature
) *EthScanner {
	return &EthScanner{
		ethClient:            ethClient,
		redisClient:          redisClient,
		chainCfg:             chainCfg,
		nativeAddrToUser:     nativeAddrToUser,
		usdtAddrToUser:       usdtAddrToUser,
		nativeAddressesSet:   nativeAddressesSet,
		usdtAddressesSet:     usdtAddressesSet,
		usdtContractAddress:  usdtContractAddress,
		usdtDecimals:         usdtDecimals,
		ethTokenId:           ethTokenId,
		usdtTokenId:          usdtTokenId,
		userRechargesService: userRechargesService,
		handleDepositFunc:    handleDepositFunc,
	}
}

// GetChainName 返回扫描器处理的链名称
func (s *EthScanner) GetChainName() string {
	return "ETH"
}

// Scan 执行 ETH 链的存款扫描逻辑
func (s *EthScanner) Scan(ctx context.Context) error {
	logPrefix := fmt.Sprintf("[%sScanner.Scan]", s.GetChainName())
	redisKey := fmt.Sprintf("deposit_check:last_block:%s", s.GetChainName()) // 使用原始链名，不进行大小写转换

	// 1. 获取区块范围
	var fromBlock uint64
	lastProcessedBlockVar, err := s.redisClient.Get(ctx, redisKey)
	if err != nil {
		glog.Errorf(ctx, "%s 从 Redis 获取最后处理区块失败 (%s): %v", logPrefix, redisKey, err)
		glog.Warningf(ctx, "%s 无法从 Redis 获取区块号，将获取链上最新区块号并减去 100 作为起始区块", logPrefix)

		// 获取链上最新区块号
		latestBlock, latestErr := s.ethClient.BlockNumber(ctx)
		if latestErr != nil {
			glog.Errorf(ctx, "%s 获取最新区块号失败: %v", logPrefix, latestErr)
			return gerror.Wrap(latestErr, "获取最新区块号失败")
		}

		// 计算初始区块号：最新区块号 - 100
		if latestBlock > 100 {
			fromBlock = latestBlock - 100
		} else {
			fromBlock = 1 // 如果最新区块号小于等于 100，从区块 1 开始
		}

		// 初始化 Redis，存储 fromBlock - 1
		initialValue := fromBlock - 1
		_, setErr := s.redisClient.Set(ctx, redisKey, initialValue)
		if setErr != nil {
			glog.Errorf(ctx, "%s 无法初始化 Redis 中的起始区块号 (%s=%d): %v", logPrefix, redisKey, initialValue, setErr)
			return gerror.Wrapf(setErr, "无法初始化 Redis 中的起始区块号 (%s)", redisKey)
		}
		glog.Infof(ctx, "%s 首次运行或 Redis 键丢失，获取到最新区块号: %d，设置起始扫描区块: %d，已将前一个区块写入 Redis (%s=%d)", logPrefix, latestBlock, fromBlock, redisKey, initialValue)

	} else if lastProcessedBlockVar == nil || lastProcessedBlockVar.IsNil() {
		glog.Warningf(ctx, "%s Redis 中未找到最后处理区块 (%s)，将获取链上最新区块号并减去 100 作为起始区块", logPrefix, redisKey)

		// 获取链上最新区块号
		latestBlock, latestErr := s.ethClient.BlockNumber(ctx)
		if latestErr != nil {
			glog.Errorf(ctx, "%s 获取最新区块号失败: %v", logPrefix, latestErr)
			return gerror.Wrap(latestErr, "获取最新区块号失败")
		}

		// 计算初始区块号：最新区块号 - 100
		if latestBlock > 100 {
			fromBlock = latestBlock - 100
		} else {
			fromBlock = 1 // 如果最新区块号小于等于 100，从区块 1 开始
		}

		// 初始化 Redis，存储 fromBlock - 1
		initialValue := fromBlock - 1
		_, setErr := s.redisClient.Set(ctx, redisKey, initialValue)
		if setErr != nil {
			glog.Errorf(ctx, "%s 无法初始化 Redis 中的起始区块号 (%s=%d): %v", logPrefix, redisKey, initialValue, setErr)
			return gerror.Wrapf(setErr, "无法初始化 Redis 中的起始区块号 (%s)", redisKey)
		}
		glog.Infof(ctx, "%s 首次运行，获取到最新区块号: %d，设置起始扫描区块: %d，已将前一个区块写入 Redis (%s=%d)", logPrefix, latestBlock, fromBlock, redisKey, initialValue)

	} else {
		lastProcessedBlock := lastProcessedBlockVar.Uint64()
		fromBlock = lastProcessedBlock + 1
		glog.Infof(ctx, "%s 从 Redis 获取到最后处理区块: %d，本次扫描将从区块 %d 开始。", logPrefix, lastProcessedBlock, fromBlock)
	}

	latestBlock, err := s.ethClient.BlockNumber(ctx)
	if err != nil {
		glog.Errorf(ctx, "%s 获取最新区块号失败: %v", logPrefix, err)
		return gerror.Wrap(err, "获取最新区块号失败")
	}
	toBlock := latestBlock

	// 考虑 RPC 限制和潜在延迟，可以限制单次扫描的最大区块数
	// maxScanBlocks := uint64(1000) // 示例：每次最多扫描 1000 个块
	// if toBlock > fromBlock+maxScanBlocks-1 {
	// 	toBlock = fromBlock + maxScanBlocks - 1
	// 	glog.Infof(ctx, "%s 扫描范围过大，本次限制扫描到区块 %d", logPrefix, toBlock)
	// }

	if fromBlock > toBlock {
		glog.Infof(ctx, "%s 起始区块 (%d) 大于最新区块 (%d)，本次不执行扫描。", logPrefix, fromBlock, toBlock)
		return nil
	}

	glog.Infof(ctx, "%s 开始检查 %s 充值，区块范围: %d - %d (最新: %d)", logPrefix, s.GetChainName(), fromBlock, toBlock, latestBlock)

	// 2. 一次性获取 USDT 事件日志
	logsByBlock := make(map[uint64][]types.Log)
	usdtContractAddrCommon := common.HexToAddress(s.usdtContractAddress) // 转换为 common.Address

	if len(s.usdtAddressesSet) > 0 && s.usdtContractAddress != "" {
		var allLogs []types.Log // 用于收集所有批次的日志
		currentFromBlock := fromBlock

		for currentFromBlock <= toBlock {
			currentToBlock := currentFromBlock + maxBlocksPerFilterLogsRequest - 1
			if currentToBlock > toBlock {
				currentToBlock = toBlock
			}

			query := ethereum.FilterQuery{
				FromBlock: big.NewInt(int64(currentFromBlock)),
				ToBlock:   big.NewInt(int64(currentToBlock)),
				Addresses: []common.Address{usdtContractAddrCommon},
				Topics: [][]common.Hash{
					{transferEventSigHash}, // event signature
					nil,                    // from address (any)
					nil,                    // to address (any - we filter in memory)
				},
			}
			glog.Debugf(ctx, "%s Filtering USDT logs from %d to %d for contract %s (Batch)", logPrefix, currentFromBlock, currentToBlock, s.usdtContractAddress)
			batchLogs, err := s.ethClient.FilterLogs(ctx, query)
			if err != nil {
				glog.Errorf(ctx, "%s 过滤 ERC20 Transfer 日志失败 (Batch %d-%d): %v", logPrefix, currentFromBlock, currentToBlock, err)
				return gerror.Wrapf(err, "过滤 ERC20 Transfer 日志失败 (Batch %d-%d)", currentFromBlock, currentToBlock)
			}
			allLogs = append(allLogs, batchLogs...)
			glog.Debugf(ctx, "%s Batch %d-%d fetched %d logs", logPrefix, currentFromBlock, currentToBlock, len(batchLogs))
			currentFromBlock = currentToBlock + 1
		}
		glog.Infof(ctx, "%s 过滤到 %d 条原始 USDT Transfer 日志 (所有批次)", logPrefix, len(allLogs))

		// 按区块号组织日志并初步过滤 'to' 地址
		for _, vLog := range allLogs { // 使用 allLogs 而不是 logs
			if vLog.Removed {
				continue
			}
			// 确保 Topics 足够解析 'to' 地址
			if len(vLog.Topics) < 3 {
				continue
			}
			// 解析 'to' 地址 (Topics[2])
			toAddress := common.HexToAddress(vLog.Topics[2].Hex()).Hex()
			// 检查 'to' 地址是否在我们关心的 USDT 地址集合中
			if _, exists := s.usdtAddressesSet[toAddress]; exists {
				if _, ok := logsByBlock[vLog.BlockNumber]; !ok {
					logsByBlock[vLog.BlockNumber] = make([]types.Log, 0)
				}
				logsByBlock[vLog.BlockNumber] = append(logsByBlock[vLog.BlockNumber], vLog)
			}
		}
		glog.Infof(ctx, "%s 按区块整理后，有 %d 个区块包含相关的 USDT 日志", logPrefix, len(logsByBlock))
	} else {
		glog.Info(ctx, "%s USDT 地址集合为空或合约地址未配置，跳过 USDT 日志获取。", logPrefix)
	}

	// 3. 按区块处理
	for blockNum := fromBlock; blockNum <= toBlock; blockNum++ {
		blockStartTime := gtime.Now()
		block, err := s.ethClient.BlockByNumber(ctx, big.NewInt(int64(blockNum)))
		if err != nil {
			glog.Warningf(ctx, "%s 获取区块 %d 失败: %v", logPrefix, blockNum, err)
			if err == ethereum.NotFound {
				glog.Warningf(ctx, "%s 区块 %d 未找到，可能尚未产生或节点数据不完整。", logPrefix, blockNum)
			}
			// 重要：如果获取区块失败，不能更新 Redis 检查点，否则会跳过该块
			// 应该中断本次扫描，等待下次重试
			return gerror.Wrapf(err, "获取区块 %d 失败，中断扫描", blockNum)
		}

		blockProcessed := false // 标记当前区块是否已处理（找到交易或日志）

		// a. 处理原生 ETH 交易
		if len(s.nativeAddressesSet) > 0 {
			for i, tx := range block.Transactions() {
				if tx.To() == nil { // 跳过合约创建等没有 To 地址的交易
					continue
				}
				toAddr := tx.To().Hex()
				if _, exists := s.nativeAddressesSet[toAddr]; exists && tx.Value().Cmp(big.NewInt(0)) > 0 {
					sender, err := getSenderAddress(ctx, s.ethClient, tx, block.Hash(), uint(i)) // 传递 ethClient
					if err != nil {
						glog.Warningf(ctx, "%s 无法获取原生 ETH 交易 %s 的发送者地址: %v", logPrefix, tx.Hash().Hex(), err)
						continue
					}

					userID, ok := s.nativeAddrToUser[toAddr]
					if !ok {
						glog.Errorf(ctx, "%s 严重错误：在 nativeAddrToUser 中找不到地址 %s", logPrefix, toAddr)
						continue
					}

					amountStr := convertWeiToEtherString(tx.Value())
					confirmations := latestBlock - block.NumberU64() + 1

					deposit := ports.DepositInfo{
						TxHash:        tx.Hash().Hex(),
						FromAddress:   sender.Hex(),
						ToAddress:     tx.To().Hex(), // 使用交易中的原始 To 地址
						Amount:        amountStr,
						BlockNumber:   block.NumberU64(),
						TokenSymbol:   "ETH", // Use native token symbol
						Chain:         s.GetChainName(),
						UserID:        userID,
						TokenID:       s.ethTokenId,
						Confirmations: confirmations,
					}
					glog.Debugf(ctx, "%s 发现原生 %s 存款: %+v", logPrefix, s.GetChainName(), deposit)
					blockProcessed = true

					// 同步调用 handleDepositFunc
					handleErr := s.handleDepositFunc(gctx.New(), s.userRechargesService, deposit)
					if handleErr != nil {
						glog.Errorf(ctx, "%s 处理原生 ETH 存款失败 (Block: %d, Tx: %s): %v. 停止扫描.", logPrefix, blockNum, deposit.TxHash, handleErr)
						// 如果处理失败，则不更新检查点并返回错误
						return gerror.Wrapf(handleErr, "处理原生 ETH 存款失败 (Block: %d, Tx: %s)", blockNum, deposit.TxHash)
					}
				}
			}
		}

		// b. 处理 USDT 事件日志
		if blockLogs, ok := logsByBlock[blockNum]; ok && len(s.usdtAddressesSet) > 0 {
			for _, vLog := range blockLogs {
				// 再次验证日志未被移除 (虽然 FilterLogs 应该处理了)
				if vLog.Removed {
					continue
				}
				// 解析事件
				if len(vLog.Topics) < 3 || vLog.Topics[0] != transferEventSigHash {
					continue
				}
				fromAddress := common.HexToAddress(vLog.Topics[1].Hex())
				toAddress := common.HexToAddress(vLog.Topics[2].Hex())
				toAddr := toAddress.Hex()

				// 查找 UserID
				userID, userOk := s.usdtAddrToUser[toAddr]
				if !userOk {
					glog.Errorf(ctx, "%s 严重错误：在 usdtAddrToUser 中找不到地址 %s (Log Tx: %s)", logPrefix, toAddr, vLog.TxHash.Hex())
					continue
				}

				// 解析金额
				var transferEvent struct {
					Value *big.Int
				}
				err := erc20ABI.UnpackIntoInterface(&transferEvent, "Transfer", vLog.Data)
				if err != nil {
					glog.Errorf(ctx, "%s 解析 USDT Transfer 事件数据失败: Tx=%s, Error=%v", logPrefix, vLog.TxHash.Hex(), err)
					continue
				}
				if transferEvent.Value == nil || transferEvent.Value.Cmp(big.NewInt(0)) <= 0 {
					continue // 跳过金额为 0 或解析失败的事件
				}

				amountStr := convertAmountToString(transferEvent.Value, s.usdtDecimals)
				confirmations := latestBlock - vLog.BlockNumber + 1

				deposit := ports.DepositInfo{
					TxHash:        vLog.TxHash.Hex(),
					FromAddress:   fromAddress.Hex(),
					ToAddress:     toAddress.Hex(), // 使用日志中的原始 To 地址
					Amount:        amountStr,
					BlockNumber:   vLog.BlockNumber,
					TokenSymbol:   "USDT", // Use token symbol
					Chain:         s.GetChainName(),
					UserID:        userID,
					TokenID:       s.usdtTokenId,
					Confirmations: confirmations,
				}
				glog.Debugf(ctx, "%s 发现 USDT 存款: %+v", logPrefix, deposit)
				blockProcessed = true

				// 同步调用 handleDepositFunc
				handleErr := s.handleDepositFunc(gctx.New(), s.userRechargesService, deposit)
				if handleErr != nil {
					glog.Errorf(ctx, "%s 处理 USDT 存款失败 (Block: %d, Tx: %s): %v. 停止扫描.", logPrefix, blockNum, deposit.TxHash, handleErr)
					// 如果处理失败，则不更新检查点并返回错误
					return gerror.Wrapf(handleErr, "处理 USDT 存款失败 (Block: %d, Tx: %s)", blockNum, deposit.TxHash)
				}
			}
		}

		// c. 更新检查点 (处理完当前区块后立即更新)
		_, setErr := s.redisClient.Set(ctx, redisKey, blockNum)
		if setErr != nil {
			// 这是一个严重问题，可能导致重复处理或跳过区块
			glog.Errorf(ctx, "%s 无法将当前扫描区块号 (%d) 更新到 Redis (%s): %v. 停止扫描以防止数据不一致.", logPrefix, blockNum, redisKey, setErr)
			// 中断扫描
			return gerror.Wrapf(setErr, "无法将当前扫描区块号 %d 更新到 Redis (%s)，中断扫描", blockNum, redisKey)
		}
		// glog.Debugf(ctx, "%s 成功将当前扫描区块号 %d 更新到 Redis (%s)", logPrefix, blockNum, redisKey)

		blockDuration := gtime.Now().Sub(blockStartTime)
		if blockProcessed || blockNum%100 == 0 { // Log progress periodically or if processed
			glog.Infof(ctx, "%s 处理完区块 %d (耗时: %s)", logPrefix, blockNum, blockDuration)
		}
	} // End of block loop

	// 4. 扫描完成 (不再需要等待 goroutine)
	glog.Infof(ctx, "%s ETH 链扫描完成 (区块 %d-%d)。", logPrefix, fromBlock, toBlock)
	return nil
}

// --- ETH Specific Helper Functions ---

// getSenderAddress 从交易中恢复发送者地址
func getSenderAddress(ctx context.Context, client *ethclient.Client, tx *types.Transaction, blockHash common.Hash, txIndex uint) (common.Address, error) {
	// 对于 EIP-155 之前的交易，直接使用 V, R, S 签名恢复
	// 对于 EIP-155 及之后的交易，需要 ChainID
	// go-ethereum 的 `TransactionReader` 接口和 `AsMessage` 方法处理了这些复杂性

	// 尝试使用缓存的签名者
	var signer types.Signer
	if tx.Type() == types.LegacyTxType {
		// 尝试获取链 ID，如果失败则使用 HomesteadSigner
		chainID, err := client.ChainID(ctx)
		if err != nil {
			glog.Warningf(ctx, "getSenderAddress: 获取 ChainID 失败，将使用 HomesteadSigner: %v", err)
			signer = types.HomesteadSigner{}
		} else {
			signer = types.NewEIP155Signer(chainID)
		}
	} else {
		// 对于 EIP-2930 和 EIP-1559 交易，使用 LondonSigner
		chainID, err := client.ChainID(ctx)
		if err != nil {
			// 对于较新的交易类型，ChainID 是必需的
			glog.Errorf(ctx, "getSenderAddress: 获取 ChainID 失败，无法确定 EIP-2930/1559 交易的发送者: %v", err)
			return common.Address{}, gerror.Wrap(err, "获取 ChainID 失败，无法确定发送者")
		}
		signer = types.NewLondonSigner(chainID)
	}

	sender, err := types.Sender(signer, tx)
	if err != nil {
		// 如果上面的方法失败（例如，对于非常旧的、非 EIP-155 的交易在某些客户端实现中可能存在问题），
		// 可以尝试使用 TransactionReceipt 中的 From 字段（但这需要额外的 RPC 调用）
		// receipt, receiptErr := client.TransactionReceipt(ctx, tx.Hash())
		// if receiptErr == nil && receipt != nil {
		// 	msg, msgErr := tx.AsMessage(signer, nil) // Use appropriate base fee if needed
		// 	if msgErr == nil {
		// 		return msg.From(), nil
		// 	}
		// }
		// 如果都失败，返回错误
		return common.Address{}, gerror.Wrapf(err, "无法使用 signer 恢复发送者地址 (Tx: %s)", tx.Hash().Hex())
	}
	return sender, nil
}

// convertWeiToEtherString 将 Wei (big.Int) 转换为 Ether (string)
func convertWeiToEtherString(wei *big.Int) string {
	if wei == nil {
		return "0"
	}
	// 使用 big.Float 进行高精度计算
	weiFloat := new(big.Float).SetInt(wei)
	etherFloat := new(big.Float).Quo(weiFloat, big.NewFloat(params.Ether)) // 1 Ether = 10^18 Wei
	// FormatFloat 控制输出格式，'f' 表示普通小数格式，-1 表示尽可能精确的小数位数
	return etherFloat.Text('f', -1)
}

// convertAmountToString 将代币的最小单位 (big.Int) 根据精度转换为字符串表示
func convertAmountToString(amount *big.Int, decimals uint8) string {
	if amount == nil {
		return "0"
	}
	amountFloat := new(big.Float).SetInt(amount)
	divisor := new(big.Float).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals)), nil))
	resultFloat := new(big.Float).Quo(amountFloat, divisor)
	// FormatFloat 控制输出格式，'f' 表示普通小数格式，-1 表示尽可能精确的小数位数
	// 或者可以指定一个固定的小数位数，例如 8 位：resultFloat.Text('f', 8)
	return resultFloat.Text('f', -1)
}
