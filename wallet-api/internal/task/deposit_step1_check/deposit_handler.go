package deposit_step1_check

import (
	"context"
	"fmt"
	"time"

	"wallet-api/internal/ports" // 确保路径正确
	"wallet-api/internal/service"

	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

// handleDeposit 处理单个存款记录（通用逻辑，将被传递给 Scanner）
// 返回 error 以便调用者知道处理是否成功，从而决定是否更新检查点
func handleDeposit(ctx context.Context, rechargeService service.IUserRecharges, deposit ports.DepositInfo) error {
	// 注意：这个函数现在同步执行，panic 会被上层捕获（如果上层有 defer recover）
	logPrefix := fmt.Sprintf("[handleDeposit:%s:%s]", deposit.Chain, deposit.TxHash)

	decimalAmount, err := decimal.NewFromString(deposit.Amount)
	if err != nil {
		glog.Errorf(ctx, "%s 无效的金额格式: %s", logPrefix, deposit.Amount)
		return fmt.Errorf("%s 无效的金额格式: %s", logPrefix, deposit.Amount)
	}
	// 检查金额是否为负数
	if decimalAmount.LessThan(decimal.Zero) {
		glog.Errorf(ctx, "%s 存款金额不能为负数: %s", logPrefix, deposit.Amount)
		return fmt.Errorf("%s 存款金额不能为负数: %s", logPrefix, deposit.Amount)
	}
	// 检查金额是否为零
	if decimalAmount.Equal(decimal.Zero) {
		glog.Errorf(ctx, "%s 存款金额不能为零: %s", logPrefix, deposit.Amount)
		return fmt.Errorf("%s 存款金额不能为零: %s", logPrefix, deposit.Amount)
	}
	//判断金额是否过小
	var minAmount decimal.Decimal

	cfg, err := loadDepositCheckConfig(ctx)
	if err != nil {
		glog.Errorf(ctx, "Failed to load deposit check config: %v", err)
		return fmt.Errorf("failed to load deposit check config: %v", err)
	}

	switch deposit.TokenSymbol {
	case "TRX":
		minAmount = decimal.NewFromFloat(cfg.WalletConfig.TrxMinTakeAmount) // 示例：最小存款金额为 0.0001
	case "ETH":
		minAmount = decimal.NewFromFloat(cfg.WalletConfig.EthMinTakeAmount) // 示例：最小存款金额为 0.0001
	case "USDT":
		minAmount = decimal.NewFromFloat(cfg.WalletConfig.UsdtMinTakeAmount) // 示例：最小存款金额为 0.0001
	default:
		return fmt.Errorf("%s 无效的代币类型: %s", logPrefix, deposit.TokenSymbol)
		//minAmount = decimal.NewFromFloat(0.001) // 示例：最小存款金额为 0.0005
	}
	// 检查金额是否小于最小值
	if decimalAmount.LessThan(minAmount) {
		glog.Errorf(ctx, "%s 存款金额过小: %s", logPrefix, deposit.Amount)
		return fmt.Errorf("%s 存款金额过小: %s", logPrefix, deposit.Amount)
	}

	// 使用带有超时的 context 来防止数据库操作卡死
	handleCtx, cancel := context.WithTimeout(ctx, 30*time.Second) // 示例：30秒超时
	defer cancel()

	// 使用 ProcessNewDeposit 处理存款（包含检查和创建逻辑）
	output, created, err := rechargeService.ProcessNewDeposit(handleCtx, deposit) // Pass deposit directly
	if err != nil {
		// ProcessNewDeposit 内部应该处理唯一约束等错误，这里只记录通用错误
		glog.Errorf(handleCtx, "%s 处理新存款失败: %v", logPrefix, err)
		// 返回错误，以便 Scanner 知道处理失败
		return err
	}

	if created && output != nil { // Check if output is not nil before accessing
		glog.Infof(handleCtx, "%s 成功记录新的存款: ID=%d, UserID=%d, Amount=%s %s", logPrefix, output.UserRechargesId, deposit.UserID, deposit.Amount, deposit.TokenSymbol) // Use UserRechargesId
	} else if !created {
		glog.Debugf(handleCtx, "%s 存款记录已存在或未处理，跳过。", logPrefix)
	} // If !created and output is nil, an error occurred and was logged previously

	// 处理成功或记录已存在，返回 nil error
	return nil
}
