package withdraw_step1_plan

import (
	"context"
	"fmt"

	// "strconv" // Kept for potential future use, though balances are now decimal
	"time"
	// v1 "wallet-api/api/wallet/v1" // Removed as GetWalletInfo is no longer called
	"wallet-api/internal/consts"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	// "wallet-api/internal/service" // Removed as GetWalletInfo is no longer called
	eth "wallet-api/internal/utility/crypto/eth"
	"wallet-api/internal/utility/crypto/tron"
	utils "wallet-api/internal/utility/utils"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

const (
	tokenSymbolUSDT = "USDT"

	MaxStep1Retries            = 3
	StateStep1FailedMaxRetries = -1 // 状态：步骤1处理失败且达到最大重试次数
)

// ProcessWithdrawPlans processes withdraw plans with state=1
func ProcessWithdrawPlans(ctx context.Context) {
	logPrefix := "[WithdrawPlanProcessor]"
	glog.Infof(ctx, "%s Task started.", logPrefix)
	startTime := time.Now()

	var withdrawPlans []*entity.WithdrawPlan
	err := dao.WithdrawPlan.Ctx(ctx).
		Where(dao.WithdrawPlan.Columns().State, 1).
		WhereLT(dao.WithdrawPlan.Columns().RetryCount, MaxStep1Retries). // 只处理重试次数小于最大次数的
		Scan(&withdrawPlans)

	if err != nil {
		glog.Warningf(ctx, "%s Failed to fetch withdraw plans: %v", logPrefix, err)
		return
	}

	if len(withdrawPlans) == 0 {
		glog.Infof(ctx, "%s No pending withdraw plans found.", logPrefix)
		glog.Infof(ctx, "%s Task finished. Duration: %s", logPrefix, time.Since(startTime))
		return
	}

	glog.Infof(ctx, "%s Found %d pending withdraw plans to process.", logPrefix, len(withdrawPlans))

	// Fetch global wallet settings (thresholds, collection addresses, etc.) once
	globalWalletSettings, err := dao.Wallets.GetWallet(ctx)
	if err != nil {
		glog.Warningf(ctx, "%s Failed to fetch global wallet settings: %v. Skipping processing.", logPrefix, err)
		return
	}
	if globalWalletSettings == nil {
		glog.Warningf(ctx, "%s Global wallet settings are nil. Skipping processing.", logPrefix)
		return
	}

	for _, plan := range withdrawPlans {
		planLogPrefix := fmt.Sprintf("%s[PlanID:%d]", logPrefix, plan.WithdrawPlanId)
		glog.Infof(ctx, "%s Processing for address: %s, chain: %s",
			planLogPrefix, plan.Address, plan.Chan)

		transactionErr := g.DB().Transaction(ctx, func(transactionCtx context.Context, tx gdb.TX) error {
			processingErr := processSingleWithdrawPlan(transactionCtx, globalWalletSettings, tx, plan, planLogPrefix)
			if processingErr != nil {
				return processingErr // Rolls back transaction
			}

			_, updateErr := tx.Model(dao.WithdrawPlan.Table()).Ctx(transactionCtx).
				Data(g.Map{
					dao.WithdrawPlan.Columns().State:     2, // Mark as processed for this stage
					dao.WithdrawPlan.Columns().UpdatedAt: gtime.Now(),
				}).
				Where(dao.WithdrawPlan.Columns().WithdrawPlanId, plan.WithdrawPlanId).
				Update()

			if updateErr != nil {
				glog.Warningf(transactionCtx, "%s Failed to update plan state to 'processed' IN TX: %v", planLogPrefix, updateErr)
				return updateErr // Rolls back transaction
			}
			glog.Infof(transactionCtx, "%s Plan processing and state update successful within transaction.", planLogPrefix)
			return nil // Commits transaction
		})

		if transactionErr != nil {
			glog.Warningf(ctx, "%s Transaction failed for PlanID %d: %v", planLogPrefix, plan.WithdrawPlanId, transactionErr)
			newErrStr := transactionErr.Error()

			// 获取最新的 plan 数据以更新 RetryCount 和 ErrorMessage
			currentPlanData := &entity.WithdrawPlan{}
			scanErr := dao.WithdrawPlan.Ctx(ctx).
				Where(dao.WithdrawPlan.Columns().WithdrawPlanId, plan.WithdrawPlanId).
				Scan(currentPlanData)

			if scanErr != nil {
				glog.Errorf(ctx, "%s Failed to read plan data for PlanID %d before updating error and retry count: %v. Original transaction error: %s", planLogPrefix, plan.WithdrawPlanId, scanErr, newErrStr)
				// 如果无法读取 plan，至少记录原始错误，然后继续处理下一个 plan。
				// 错误信息和重试次数可能不会更新，但主查询的 RetryCount < MaxStep1Retries 会防止无限重试。
			} else {
				newRetryCount := currentPlanData.RetryCount + 1
				// 使用 currentPlanData.ErrorMessage 来追加错误，而不是传入的 plan.ErrorMessage，后者可能不是最新的
				jsonErrorMsg := utils.BuildErrorMessageJson(ctx, currentPlanData.ErrorMessage, newErrStr)

				updateDataMap := g.Map{
					dao.WithdrawPlan.Columns().ErrorMessage: jsonErrorMsg,
					dao.WithdrawPlan.Columns().RetryCount:   newRetryCount,
					dao.WithdrawPlan.Columns().UpdatedAt:    gtime.Now(),
				}

				if newRetryCount >= MaxStep1Retries {
					glog.Warningf(ctx, "%s PlanID %d reached max retries (%d). Setting state to %d.", planLogPrefix, plan.WithdrawPlanId, MaxStep1Retries, StateStep1FailedMaxRetries)
					updateDataMap[dao.WithdrawPlan.Columns().State] = StateStep1FailedMaxRetries
				}

				_, updateFailureErr := dao.WithdrawPlan.Ctx(ctx).
					Data(updateDataMap).
					Where(dao.WithdrawPlan.Columns().WithdrawPlanId, plan.WithdrawPlanId).
					Update()

				if updateFailureErr != nil {
					glog.Warningf(ctx, "%s Failed to update plan (ID: %d) with error message, retry count, and potentially new state (POST-TX): %v. Original transaction error: %s", planLogPrefix, plan.WithdrawPlanId, updateFailureErr, newErrStr)
				}
			}
			continue // 继续处理下一个 plan
		}
		glog.Infof(ctx, "%s Successfully processed plan (transaction committed).", planLogPrefix)
	}
	glog.Infof(ctx, "%s Task finished. Processed %d plans. Duration: %s", logPrefix, len(withdrawPlans), time.Since(startTime))
}

// processSingleWithdrawPlan handles the logic for a single withdrawal plan within a transaction.
func processSingleWithdrawPlan(ctx context.Context, globalSettings *entity.Wallets, tx gdb.TX, plan *entity.WithdrawPlan, logPrefix string) error {
	// The check for existing pending supplements is now done within checkAndCreateSupplementRecords for each token type.

	nativeBalance, usdtBalance, err := getChainBalances(ctx, plan.Address, plan.Chan, logPrefix)
	if err != nil {
		// Error already logged by getChainBalances
		return fmt.Errorf("failed to get chain balances: %w", err)
	}
	glog.Infof(ctx, "%s Balances fetched - Native: %s, USDT: %s", logPrefix, nativeBalance.String(), usdtBalance.String())

	return checkAndCreateSupplementRecords(ctx, tx, globalSettings, plan, nativeBalance, usdtBalance, logPrefix)
}

// hasExistingPendingSupplement checks if a pending token_fee_supplement record already exists for the specific plan, chain, and token.
func hasExistingPendingSupplement(ctx context.Context, tx gdb.TX, planID uint, chainType string, tokenSymbol string, logPrefix string) (bool, error) {
	count, err := tx.Model(dao.TokenFeeSupplements.Table()).Ctx(ctx).
		Where(dao.TokenFeeSupplements.Columns().WithdrawPlanId, planID).
		Where(dao.TokenFeeSupplements.Columns().ChainType, chainType).
		Where(dao.TokenFeeSupplements.Columns().TokenSymbol, tokenSymbol).
		Where(dao.TokenFeeSupplements.Columns().Status, "pending").
		Count()

	if err != nil {
		glog.Warningf(ctx, "%s Failed to count existing pending token_fee_supplements for PlanID %d, ChainType %s, TokenSymbol %s: %v", logPrefix, planID, chainType, tokenSymbol, err)
		return false, err
	}
	return count > 0, nil
}

// getChainBalances fetches native and USDT balances for a given address and chain.
func getChainBalances(ctx context.Context, address, chain, logPrefix string) (nativeBalance decimal.Decimal, usdtBalance decimal.Decimal, err error) {
	nativeBalance = decimal.Zero
	usdtBalance = decimal.Zero

	switch chain {
	case consts.NetworkTypeETH:
		ethRawBalance, fetchErr := eth.GetETHBalance(address)
		if fetchErr != nil {
			glog.Warningf(ctx, "%s Failed to get ETH balance for %s: %v", logPrefix, address, fetchErr)
			return nativeBalance, usdtBalance, fmt.Errorf("failed to get ETH balance: %w", fetchErr)
		}
		nativeBalance, err = decimal.NewFromString(ethRawBalance)
		if err != nil {
			glog.Warningf(ctx, "%s Failed to parse ETH balance string '%s': %v", logPrefix, ethRawBalance, err)
			return decimal.Zero, usdtBalance, fmt.Errorf("failed to parse ETH balance: %w", err)
		}

		usdtEthRawBalance, fetchErr := eth.GetErc20UsdtBalance(address)
		if fetchErr != nil {
			glog.Warningf(ctx, "%s Failed to get ERC20 USDT balance for %s (will assume 0): %v", logPrefix, address, fetchErr)
			// Do not return error, allow processing with usdtBalance = 0
		} else {
			usdtBalance, err = decimal.NewFromString(usdtEthRawBalance)
			if err != nil {
				glog.Warningf(ctx, "%s Failed to parse ERC20 USDT balance string '%s' (will assume 0): %v", logPrefix, usdtEthRawBalance, err)
				usdtBalance = decimal.Zero // Reset on parsing error
			}
		}

	case consts.NetworkTypeTRX:
		nativeBalance, err = tron.GetTRXBalance(ctx, address) // Assuming this returns decimal.Decimal
		if err != nil {
			glog.Warningf(ctx, "%s Failed to get TRX balance for %s: %v", logPrefix, address, err)
			return decimal.Zero, usdtBalance, fmt.Errorf("failed to get TRX balance: %w", err)
		}

		usdtBalance, err = tron.GetTRC20UsdtTokenBalance(ctx, address) // Assuming this returns decimal.Decimal
		if err != nil {
			glog.Warningf(ctx, "%s Failed to get TRC20 USDT balance for %s (will assume 0): %v", logPrefix, address, err)
			usdtBalance = decimal.Zero // Reset to Zero, do not return error for this
			err = nil                  // Clear error so it doesn't propagate from USDT fetch failure
		}

	default:
		return nativeBalance, usdtBalance, fmt.Errorf("unsupported chain type: %s", chain)
	}
	return nativeBalance, usdtBalance, nil
}

// checkAndCreateSupplementRecords checks balances against thresholds and creates supplement records independently for native and token.
func checkAndCreateSupplementRecords(
	ctx context.Context,
	tx gdb.TX,
	globalSettings *entity.Wallets,
	plan *entity.WithdrawPlan,
	nativeBalance, usdtBalance decimal.Decimal,
	logPrefix string,
) error {
	// Parse thresholds from global settings
	usdtCollectThreshold, err := decimal.NewFromString(globalSettings.UsdtCollectThreshold)
	if err != nil {
		errMsg := fmt.Sprintf("failed to parse USDT collect threshold '%s': %v", globalSettings.UsdtCollectThreshold, err)
		glog.Warningf(ctx, "%s %s", logPrefix, errMsg)
		return fmt.Errorf(errMsg) // Critical error, cannot proceed
	}

	var nativeCollectThreshold decimal.Decimal
	var nativeTokenSymbol string

	switch plan.Chan {
	case consts.NetworkTypeETH:
		nativeCollectThreshold, err = decimal.NewFromString(globalSettings.EthCollectThreshold)
		nativeTokenSymbol = consts.CoinTypeETH
		if err != nil {
			errMsg := fmt.Sprintf("failed to parse ETH collect threshold '%s': %v", globalSettings.EthCollectThreshold, err)
			glog.Warningf(ctx, "%s %s", logPrefix, errMsg)
			return fmt.Errorf(errMsg) // Critical error, cannot proceed
		}
	case consts.NetworkTypeTRX:
		nativeCollectThreshold, err = decimal.NewFromString(globalSettings.TrxCollectThreshold)
		nativeTokenSymbol = consts.CoinTypeTRX
		if err != nil {
			errMsg := fmt.Sprintf("failed to parse TRX collect threshold '%s': %v", globalSettings.TrxCollectThreshold, err)
			glog.Warningf(ctx, "%s %s", logPrefix, errMsg)
			return fmt.Errorf(errMsg) // Critical error, cannot proceed
		}
	default:
		return fmt.Errorf("unsupported chain type for threshold: %s", plan.Chan) // Critical error
	}

	var supplementAttempted bool = false
	var supplementCreatedSuccessfully bool = false
	var usdtSupplementCreated bool = false // Track if USDT supplement was created
	var firstErrorEncountered error = nil

	// --- Process USDT Supplement ---
	// Check for existing pending USDT supplement for this specific plan and token
	usdtSupplementExists, checkUsdtErr := hasExistingPendingSupplement(ctx, tx, plan.WithdrawPlanId, plan.Chan, tokenSymbolUSDT, logPrefix)
	if checkUsdtErr != nil {
		glog.Warningf(ctx, "%s Failed to check for existing pending USDT supplement for PlanID %d: %v", logPrefix, plan.WithdrawPlanId, checkUsdtErr)
		return fmt.Errorf("failed to check existing USDT supplement for PlanID %d: %w", plan.WithdrawPlanId, checkUsdtErr)
	}

	if usdtSupplementExists {
		glog.Infof(ctx, "%s Existing pending USDT token_fee_supplement found for PlanID %d, Chain %s. Skipping USDT supplement creation.", logPrefix, plan.WithdrawPlanId, plan.Chan)
		usdtSupplementCreated = true // Consider existing supplement as "created"
	} else {
		if usdtBalance.IsPositive() && usdtBalance.GreaterThanOrEqual(usdtCollectThreshold) {
			supplementAttempted = true
			glog.Infof(ctx, "%s USDT balance %s meets threshold %s. Attempting USDT supplement.", logPrefix, usdtBalance.String(), usdtCollectThreshold.String())
			err := createSupplementRecordDB(ctx, tx, globalSettings, plan, tokenSymbolUSDT, usdtBalance, logPrefix)
			if err == nil {
				glog.Infof(ctx, "%s Successfully created %s token_fee_supplement.", logPrefix, tokenSymbolUSDT)
				supplementCreatedSuccessfully = true
				usdtSupplementCreated = true
			} else {
				glog.Warningf(ctx, "%s Failed to create %s token_fee_supplement: %v", logPrefix, tokenSymbolUSDT, err)
				if firstErrorEncountered == nil {
					firstErrorEncountered = fmt.Errorf("failed to create USDT token_fee_supplement: %w", err)
				}
			}
		} else if usdtBalance.IsPositive() {
			glog.Infof(ctx, "%s USDT balance %s is positive but below threshold %s. Skipping USDT supplement.", logPrefix, usdtBalance.String(), usdtCollectThreshold.String())
		} else {
			glog.Infof(ctx, "%s USDT balance is not positive (%s). Skipping USDT supplement.", logPrefix, usdtBalance.String())
		}
	}

	// --- Process Native Token Supplement ---
	// Skip native token supplement if USDT supplement was created (existing or new)
	if usdtSupplementCreated {
		glog.Infof(ctx, "%s USDT token_fee_supplement exists or was created for PlanID %d, Chain %s. Skipping native token (%s) supplement creation as they don't need supplementing.", logPrefix, plan.WithdrawPlanId, plan.Chan, nativeTokenSymbol)
	} else {
		// Check for existing pending native token supplement for this specific plan and token
		nativeSupplementExists, checkNativeErr := hasExistingPendingSupplement(ctx, tx, plan.WithdrawPlanId, plan.Chan, nativeTokenSymbol, logPrefix)
		if checkNativeErr != nil {
			glog.Warningf(ctx, "%s Failed to check for existing pending native token (%s) supplement for PlanID %d: %v", logPrefix, nativeTokenSymbol, plan.WithdrawPlanId, checkNativeErr)
			return fmt.Errorf("failed to check existing native token (%s) supplement for PlanID %d: %w", nativeTokenSymbol, plan.WithdrawPlanId, checkNativeErr)
		}

		if nativeSupplementExists {
			glog.Infof(ctx, "%s Existing pending native token (%s) token_fee_supplement found for PlanID %d, Chain %s. Skipping native token supplement creation.", logPrefix, nativeTokenSymbol, plan.WithdrawPlanId, plan.Chan)
		} else {
			if nativeBalance.GreaterThanOrEqual(nativeCollectThreshold) { // Check if native balance is sufficient for its own supplement
				supplementAttempted = true
				glog.Infof(ctx, "%s Native balance %s (%s) meets threshold %s. Attempting %s supplement.",
					logPrefix, nativeTokenSymbol, nativeBalance.String(), nativeCollectThreshold.String(), nativeTokenSymbol)
				err := createSupplementRecordDB(ctx, tx, globalSettings, plan, nativeTokenSymbol, nativeBalance, logPrefix)
				if err == nil {
					glog.Infof(ctx, "%s Successfully created %s token_fee_supplement.", logPrefix, nativeTokenSymbol)
					supplementCreatedSuccessfully = true
				} else {
					glog.Warningf(ctx, "%s Failed to create %s token_fee_supplement: %v", logPrefix, nativeTokenSymbol, err)
					if firstErrorEncountered == nil {
						firstErrorEncountered = fmt.Errorf("failed to create %s token_fee_supplement: %w", nativeTokenSymbol, err)
					}
				}
			} else {
				glog.Infof(ctx, "%s Native balance %s (%s) is less than collect threshold %s. Skipping %s supplement.",
					logPrefix, nativeTokenSymbol, nativeBalance.String(), nativeCollectThreshold.String(), nativeTokenSymbol)
			}
		}
	}

	// --- Final Logging and Return ---
	if firstErrorEncountered != nil {
		// An error occurred during one of the supplement creations that should cause rollback
		glog.Warningf(ctx, "%s Encountered an error during supplement creation: %v. Transaction will be rolled back.", logPrefix, firstErrorEncountered)
		return firstErrorEncountered
	}

	if supplementCreatedSuccessfully {
		glog.Infof(ctx, "%s At least one supplement record was successfully created.", logPrefix)
	} else if supplementAttempted {
		// This case means an attempt was made (balance was sufficient for at least one token)
		// but creation failed for non-DB-error reasons or reasons already logged by createSupplementRecordDB
		// and not captured by firstErrorEncountered.
		// Or, createSupplementRecordDB had an issue but didn't return an error (less likely).
		// The key is that no error requiring transaction rollback was returned.
		glog.Infof(ctx, "%s Supplement creation was attempted for at least one token, but no records were successfully created. Check previous logs for details if attempts were made. No critical errors occurred that warrant rolling back the transaction.", logPrefix)
	} else {
		// No supplement was attempted (e.g., both balances below thresholds or not positive)
		glog.Infof(ctx, "%s No supplement records were created as no token met the criteria for supplement attempt (e.g., balances below thresholds or not positive).", logPrefix)
	}

	return nil // No critical errors that require transaction rollback.
}

// createSupplementRecordDB creates a token_fee_supplements record in the database.
// The 'amount' parameter is the detected balance, used here for logging.
// RequiredAmount and ProvidedAmount in the entity will be set to "0".
func createSupplementRecordDB(
	ctx context.Context,
	tx gdb.TX,
	globalSettings *entity.Wallets,
	plan *entity.WithdrawPlan,
	tokenSymbol string,
	amount decimal.Decimal, // Detected balance, used for logging
	logPrefix string,
) error {
	var targetCollectAddress string
	var feeType string // gas_fee for ETH, energy for TRX

	switch plan.Chan {
	case consts.NetworkTypeETH:
		targetCollectAddress = globalSettings.EthCollectAddress
		feeType = "gas_fee"
	case consts.NetworkTypeTRX:
		targetCollectAddress = globalSettings.TrxCollectAddress
		feeType = "energy"
	default:
		return fmt.Errorf("unsupported chain type '%s' for supplement creation", plan.Chan)
	}

	if targetCollectAddress == "" {
		return fmt.Errorf("collection address for chain %s is not configured in global wallet settings", plan.Chan)
	}
	glog.Infof(ctx, "%s Collection address for %s (%s): %s. Detected amount for potential collection: %s", logPrefix, tokenSymbol, plan.Chan, targetCollectAddress, amount.String())

	supplement := &entity.TokenFeeSupplements{
		WithdrawPlanId:      int64(plan.WithdrawPlanId),
		Address:             plan.Address,
		ChainType:           plan.Chan,
		TokenSymbol:         tokenSymbol,
		FeeType:             feeType,
		RequiredAmount:      0, // As per new requirement
		ProvidedAmount:      0, // As per new requirement
		EnergyAmount:        0,
		ErrorMessage:        "[]",
		Status:              "pending",
		RelatedTaskId:       fmt.Sprintf("withdraw_plan_%d", plan.WithdrawPlanId),
		RetryCount:          0,
		TrxSupplementStatus: "pending",
		IsActivating:        0, //默认未激活
		TrxSupplementNeeded: 0, //默认不需要TRX补充
		CreatedAt:           gtime.Now(),
		UpdatedAt:           gtime.Now(),
	}

	result, err := tx.Model(dao.TokenFeeSupplements.Table()).Ctx(ctx).Data(supplement).Insert()
	if err != nil {
		return fmt.Errorf("failed to insert token_fee_supplements for %s: %w", tokenSymbol, err)
	}

	id, _ := result.LastInsertId()
	glog.Infof(ctx, "%s Created token_fee_supplements record for %s with ID: %d (Detected balance was %s, Required/Provided set to 0)", logPrefix, tokenSymbol, id, amount.String())
	return nil
}
