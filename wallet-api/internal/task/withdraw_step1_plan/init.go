package withdraw_step1_plan

import (
	"context"
	"wallet-api/internal/task_registry"

	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	// Register withdraw plan processor task
	task_registry.Register(task_registry.TaskInfo{
		Name: "WithdrawPlanProcessorTask",
		SpecFunc: func(ctx context.Context) (spec string, enabled bool, err error) {
			// Use a config key for this task
			configPrefix := "withdrawPlanProcessor"
			spec = g.Cfg().MustGet(ctx, configPrefix+".spec", "*/10 * * * * *").String() // Default: every 5 minutes
			enabled = g.Cfg().MustGet(ctx, configPrefix+".enabled", false).Bool()        // Default: disabled

			// Log warning if enabled but spec is empty
			if enabled && spec == "" {
				g.Log().Warningf(ctx, "Task '%s' is enabled but spec is empty or missing in config ('%s.spec'). Task will not run.",
					"WithdrawPlanProcessorTask", configPrefix)
			}
			return spec, enabled, nil
		},
		Func:        ProcessWithdrawPlans, // Task execution function
		IsSingleton: true,                 // Ensure only one instance runs at a time
	})
}
