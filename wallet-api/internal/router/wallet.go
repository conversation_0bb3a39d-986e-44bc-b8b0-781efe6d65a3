package router

import (
	"wallet-api/internal/controller/wallet"
	"wallet-api/internal/middleware/auth"

	"github.com/gogf/gf/v2/net/ghttp"
)

// routesInit 初始化路由和中间件
func RoutesInit(s *ghttp.Server) {
	s.Group("/", func(group *ghttp.RouterGroup) {
		// 注册中间件
		group.Middleware(
			middlewareCORS,
			ghttp.MiddlewareHandlerResponse,
			auth.WalletValidationMiddleware,
			auth.JWTAuthMiddleware,
		)

		// 绑定控制器
		group.Bind(
			wallet.NewV1(),
		)
	})
}

// middlewareCORS 跨域中间件 - 允许所有请求
func middlewareCORS(r *ghttp.Request) {
	r.Response.CORS(ghttp.CORSOptions{
		AllowOrigin:      "*",                                            // 允许所有域名
		AllowMethods:     "GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH",      // 允许所有常用方法
		AllowHeaders:     "*",                                            // 允许所有请求头
		ExposeHeaders:    "*",                                            // 暴露所有响应头
		AllowCredentials: "true",                                         // 允许携带凭证
		MaxAge:           86400,                                          // 预检请求缓存24小时
	})
	r.Middleware.Next()
}
