// /internal/consts/task.go
package consts

// 任务状态常量  completed,processing,failed,pending,canceled
const (
	TaskStatusCompleted  = "completed"  // 完成
	TaskStatusProcessing = "processing" // 处理中
	TaskStatusFailed     = "failed"     // 失败
	TaskStatusPending    = "pending"    // 待处理
	TaskStatusCanceled   = "canceled"   // 取消
)

// 任务类型常量
const (
	TaskTypeMany2One = "many2one" // 多对一转账类型
	TaskTypeOne2Many = "one2many" // 一对多转账类型
)

// 任务地址状态常量  completed,processing,failed,pending,canceled
const (
	TaskAddressStatusCompleted  = "completed"  // 完成
	TaskAddressStatusFailed     = "failed"     // 失败
	TaskAddressStatusCanceled   = "canceled"   // 取消
	TaskAddressStatusPending    = "pending"    // 待处理
	TaskAddressStatusProcessing = "processing" // 处理中
)

// 执行类型常量
const (
	ExecuteTypeManual = "manual" // 手动执行
	ExecuteTypeAuto   = "auto"   // 自动执行
)

// 地址类型常量
const (
	AddressTypeFrom = "from" // 转出地址
	AddressTypeTo   = "to"   // 转入地址
)
