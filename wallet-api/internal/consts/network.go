package consts

// 网络类型常量
const (
	NetworkTypeETH = "ETH"  // 以太坊网络
	NetworkTypeTRX = "TRON" // 波场网络
)

// 代币类型
const (
	CoinTypeETH  = "ETH"  // 以太坊
	CoinTypeTRX  = "TRX"  // 波场
	CoinTypeUSDT = "USDT" // USDT
)

// 判断是否在TypeList中
func IsCoinType(coinType string) bool {
	for _, v := range []string{CoinTypeETH, CoinTypeTRX, CoinTypeUSDT} {
		if v == coinType {
			return true
		}
	}
	return false
}

// 判断是否在NetworkTypeList中
func IsNetworkType(networkType string) bool {
	for _, v := range []string{NetworkTypeETH, NetworkTypeTRX} {
		if v == networkType {
			return true
		}
	}
	return false
}
