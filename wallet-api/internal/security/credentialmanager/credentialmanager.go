package credentialmanager

import (
	// "bufio"
	"context" // Recommended to add for potential future logging context
	"errors"
	// "fmt"
	// "os"
	// "strings"
	"sync"

	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity" // Added for wallet entity
	// "wallet-api/internal/utility/utils"
	util "wallet-api/internal/utility/utils" // Added for decryption

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g" // For logging, if desired
	// "github.com/pquerna/otp/totp"
	// "golang.org/x/crypto/bcrypt"
	// "golang.org/x/term"
)

var (
	cachedPassword         string
	cachedEthFeePrivateKey string
	cachedTrxFeePrivateKey string
	isUnlocked             bool
	mu                     sync.RWMutex
)

// ErrWalletLocked 表示钱包已锁定，密码在内存中不可用。
var ErrWalletLocked = errors.New("wallet is locked, password not available in memory")

func InitStart(ctx context.Context) error {

	// --- BEGIN WALLET UNLOCK SEQUENCE ---
	g.Log().Info(ctx, "Attempting to unlock wallet...")

	// 1. 检查钱包是否已初始化并获取钱包实体
	// 注意：实际项目中，DAO 调用通常通过 Service 层或 Repository 层进行封装
	// 这里为了简化，直接使用 dao.Wallets，但实际应考虑项目的分层结构
	walletEntity, errDb := dao.Wallets.GetWallet(ctx) // 假设 dao.Wallets 是已实例化的 DAO 对象
	if errDb != nil {
		g.Log().Fatalf(ctx, "CRITICAL: Failed to query wallet status during startup: %v. Server cannot start.", errDb)
		return gerror.Wrap(errDb, "failed to query wallet status")
	}
	if walletEntity == nil {
		// g.Log().Warning(ctx, "Wallet not initialized. HTTP server will start, but wallet features requiring unlock will fail. Please initialize wallet first.")
		// 对于需要密码的后台任务，如果钱包未初始化，它们将无法获取密码并会失败。
		// 考虑是否应在此处退出，如果未初始化的钱包不允许服务器运行。
		// 例如:
		g.Log().Fatal(ctx, "CRITICAL: Wallet not initialized. Server cannot start without an initialized wallet for unlocking.")
		return gerror.New("wallet not initialized")
	} else {
		// 钱包已初始化，继续解锁流程
		// reader := bufio.NewReader(os.Stdin)
		//
		// fmt.Print("Enter Master Wallet Password: ")
		// bytePassword, errReadPass := term.ReadPassword(int(os.Stdin.Fd()))
		// if errReadPass != nil {
		// 	g.Log().Fatalf(ctx, "CRITICAL: Failed to read master password: %v. Server cannot start.", errReadPass)
		// 	// return gerror.Wrap(errReadPass, "failed to read master password")
		// 	os.Exit(1)
		// }
		// fmt.Println() // ReadPassword 后通常需要换行以保持控制台整洁
		// userInputPassword := strings.TrimSpace(string(bytePassword))
		// // 清理 bytePassword
		// for i := range bytePassword {
		// 	bytePassword[i] = 0
		// }

		// if userInputPassword == "" {
		// 	g.Log().Debug(ctx, "CRITICAL: Master password not provided. Server cannot start.")
		// 	os.Exit(1)
		// }

		// // 2. 验证密码
		// errVal := bcrypt.CompareHashAndPassword([]byte(walletEntity.PasswordHash), []byte(userInputPassword))
		// if errVal != nil {
		// 	g.Log().Debug(ctx, "CRITICAL: Master password validation failed: %v. Server cannot start.", errVal)
		// 	//结束程序
		// 	os.Exit(1)
		// }
		// g.Log().Info(ctx, "Master password validated successfully.")

		// 3. 如果启用了谷歌验证，则进行验证
		// if walletEntity.GoogleCode != "" && walletEntity.GoogleSecretSalt != nil && len(walletEntity.GoogleSecretSalt) > 0 {
		// 	fmt.Print("Enter Google Authenticator Code: ")
		// 	userInputGoogleCode, _ := reader.ReadString('\n')
		// 	userInputGoogleCode = strings.TrimSpace(userInputGoogleCode)
		// 	if userInputGoogleCode == "" {
		// 		g.Log().Debug(ctx, "CRITICAL: Google Authenticator code not provided. Server cannot start.")
		// 		// return gerror.New("Google Authenticator code not provided")
		// 		os.Exit(1)

		// 	}

		// 	decryptedGoogleSecret, errDecGS := utils.DecryptStringWithPBKDF2(ctx, walletEntity.GoogleCode, userInputPassword, walletEntity.GoogleSecretSalt, walletEntity.GoogleSecretIterations)
		// 	if errDecGS != nil {
		// 		g.Log().Debug(ctx, "CRITICAL: Failed to decrypt Google Authenticator secret: %v. Server cannot start.", errDecGS)
		// 		// return gerror.Wrap(errDecGS, "failed to decrypt Google Authenticator secret")
		// 		os.Exit(1)
		// 	}

		// 	validTOTP := totp.Validate(userInputGoogleCode, decryptedGoogleSecret)
		// 	if !validTOTP {
		// 		g.Log().Debug(ctx, "CRITICAL: Google Authenticator code validation failed. Server cannot start.")
		// 		// return gerror.New("Google Authenticator code validation failed")
		// 		os.Exit(1)
		// 	}
		// 	g.Log().Info(ctx, "Google Authenticator code validated successfully.")
		// } else {
		// 	g.Log().Info(ctx, "Google Authenticator not configured or enabled for this wallet. Skipping TOTP validation.")
		// }

		var userInputPassword = "sprouts+888"

		// 4. 缓存密码
		// 缓存密码和解密后的费用私钥
		// 在 HTTP 启动时，walletEntity 已经从数据库加载
		SetPassword(ctx, userInputPassword, walletEntity)

		// 清理内存中的明文密码（尽力而为）
		tempPasswordBytes := []byte(userInputPassword)
		for i := range tempPasswordBytes {
			tempPasswordBytes[i] = 0
		}
		userInputPassword = ""

		g.Log().Info(ctx, "Wallet unlocked successfully and password cached.")
	}
	// --- END WALLET UNLOCK SEQUENCE ---

	return nil
}

// SetPassword 在成功验证后缓存密码和解密后的费用私钥。
// 此函数应仅在认证成功后调用。
func SetPassword(ctx context.Context, password string, wallet *entity.Wallets) {
	mu.Lock()
	defer mu.Unlock()

	if wallet == nil {
		g.Log().Error(ctx, "CredentialManager: SetPassword received nil wallet entity.")
		// Still set password and unlocked state if wallet is nil, but fee keys will be empty.
		cachedPassword = password
		cachedEthFeePrivateKey = ""
		cachedTrxFeePrivateKey = ""
		isUnlocked = true
		g.Log().Info(ctx, "Wallet has been unlocked and password cached securely in memory (fee keys empty due to nil wallet).")
		return
	}

	cachedPassword = password

	// Attempt to decrypt fee private keys
	// Check if decryption parameters are available
	if wallet.MnemonicSalt == nil || len(wallet.MnemonicSalt) == 0 || wallet.MnemonicIterations <= 0 {
		g.Log().Warningf(ctx, "CredentialManager: Mnemonic salt or iterations missing for wallet ID %d. Cannot decrypt fee private keys.", wallet.Id)
		cachedEthFeePrivateKey = ""
		cachedTrxFeePrivateKey = ""
	} else {
		// Decrypt EthFeePrivateKey if it exists
		if wallet.EthFeePrivateKey != "" {
			decryptedEthKey, err := util.DecryptStringWithPBKDF2(ctx, wallet.EthFeePrivateKey, password, wallet.MnemonicSalt, wallet.MnemonicIterations)
			if err != nil {
				g.Log().Errorf(ctx, "CredentialManager: Failed to decrypt EthFeePrivateKey for wallet ID %d: %v", wallet.Id, err)
				cachedEthFeePrivateKey = "" // Store empty string on decryption failure
			} else {
				cachedEthFeePrivateKey = decryptedEthKey
				g.Log().Debugf(ctx, "CredentialManager: Successfully decrypted EthFeePrivateKey for wallet ID %d.", wallet.Id)
			}
		} else {
			cachedEthFeePrivateKey = "" // Store empty string if encrypted key is empty
		}

		// Decrypt TrxFeePrivateKey if it exists
		if wallet.TrxFeePrivateKey != "" {
			decryptedTrxKey, err := util.DecryptStringWithPBKDF2(ctx, wallet.TrxFeePrivateKey, password, wallet.MnemonicSalt, wallet.MnemonicIterations)
			if err != nil {
				g.Log().Errorf(ctx, "CredentialManager: Failed to decrypt TrxFeePrivateKey for wallet ID %d: %v", wallet.Id, err)
				cachedTrxFeePrivateKey = "" // Store empty string on decryption failure
			} else {
				cachedTrxFeePrivateKey = decryptedTrxKey
				g.Log().Debugf(ctx, "CredentialManager: Successfully decrypted TrxFeePrivateKey for wallet ID %d.", wallet.Id)
			}
		} else {
			cachedTrxFeePrivateKey = "" // Store empty string if encrypted key is empty
		}
	}

	// g.Log().Debugf(ctx, "CredentialManager: Cached EthFeePrivateKey: %s", cachedEthFeePrivateKey)
	// g.Log().Debugf(ctx, "CredentialManager: Cached TrxFeePrivateKey: %s", cachedTrxFeePrivateKey)
	isUnlocked = true
	g.Log().Info(ctx, "Wallet has been unlocked, password and fee private keys cached securely in memory.")
}

// GetPassword 检索缓存的密码。
// 如果钱包已锁定（密码未设置），则返回错误。
func GetPassword(ctx context.Context) (string, error) {
	mu.RLock()
	defer mu.RUnlock()
	if !isUnlocked || cachedPassword == "" {
		g.Log().Warning(ctx, "Attempted to get password while wallet is locked.")
		return "", ErrWalletLocked
	}
	return cachedPassword, nil
}

// IsUnlocked 检查钱包密码是否已缓存。
func IsUnlocked(ctx context.Context) bool {
	mu.RLock()
	defer mu.RUnlock()
	return isUnlocked
}

// GetDecryptedEthFeePrivateKey retrieves the cached decrypted ETH fee private key.
// Returns ErrWalletLocked if the wallet is not unlocked.
func GetDecryptedEthFeePrivateKey(ctx context.Context) (string, error) {
	mu.RLock()
	defer mu.RUnlock()
	if !isUnlocked {
		g.Log().Warning(ctx, "CredentialManager: Attempted to get decrypted EthFeePrivateKey while wallet is locked.")
		return "", ErrWalletLocked
	}
	return cachedEthFeePrivateKey, nil
}

// GetDecryptedTrxFeePrivateKey retrieves the cached decrypted TRX fee private key.
// Returns ErrWalletLocked if the wallet is not unlocked.
func GetDecryptedTrxFeePrivateKey(ctx context.Context) (string, error) {
	mu.RLock()
	defer mu.RUnlock()
	if !isUnlocked {
		g.Log().Warning(ctx, "CredentialManager: Attempted to get decrypted TrxFeePrivateKey while wallet is locked.")
		return "", ErrWalletLocked
	}
	return cachedTrxFeePrivateKey, nil
}
