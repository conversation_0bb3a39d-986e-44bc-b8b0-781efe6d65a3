package user_recharges

import (
	"context"
	"strconv" // Added for amount conversion
	"wallet-api/internal/dao"
	"wallet-api/internal/model"
	"wallet-api/internal/model/entity" // Added for entity struct
	"wallet-api/internal/service"

	// checker "wallet-api/internal/task/checker" // No longer needed here
	"wallet-api/internal/ports" // Import ports for DepositInfo and IUserRecharges interface

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type sUserRecharges struct{}

func init() {
	service.RegisterUserRecharges(New())
}

// New creates and returns a new instance of the user recharges service.
func New() service.IUserRecharges { // Return the interface type from ports
	return &sUserRecharges{}
}

// CreateUserRecharge creates a new user recharge record.
// CreateUserRecharge creates a new user recharge record with Pending status.
func (s *sUserRecharges) CreateUserRecharge(ctx context.Context, in *model.UserRechargeCreateInput) (*model.UserRechargeCreateOutput, error) {
	// Convert amount string to float64
	amount, err := strconv.ParseFloat(in.Amount, 64)
	if err != nil {
		return nil, gerror.Wrapf(err, "failed to parse amount string '%s'", in.Amount)
	}

	// Create the entity to be inserted
	rechargeEntity := entity.UserRecharges{
		TokenId:              in.TokenId,
		Name:                 in.Name,
		Chan:                 in.Chan,
		TokenContractAddress: in.TokenContractAddress,
		FromAddress:          in.FromAddress,
		ToAddress:            in.ToAddress,
		TxHash:               in.TxHash,
		Amount:               amount,
		State:                uint(1),                // Set state to Pending (1)
		Confirmations:        uint(in.Confirmations), // Convert uint64 to uint
		// Note: CreatedAt and UpdatedAt are usually handled by GoFrame automatically
	}

	// Insert the record into the database
	res, err := dao.UserRecharges.Ctx(ctx).Data(rechargeEntity).Insert()
	if err != nil {
		// Log the error for debugging purposes
		g.Log().Errorf(ctx, "Failed to insert user recharge record: %+v, error: %v", rechargeEntity, err)
		return nil, gerror.Wrap(err, "failed to insert user recharge record into database")
	}

	// GoFrame v2 Insert doesn't directly return LastInsertId in the Result.
	// If the primary key is auto-increment and defined in the entity tag,
	// GoFrame might automatically populate it back into the `rechargeEntity` struct.
	// However, relying on this behavior might be implicit.
	// For now, we return an output without the ID, as getting it might require another query
	// or confirmation of GoFrame's auto-population behavior for this specific setup.
	// If ID is needed later, this part might need adjustment.

	// Check if ID was populated back (depends on DB driver and GF config)
	insertedID, _ := res.LastInsertId() // Attempt to get ID, might be 0 if not supported/returned

	g.Log().Infof(ctx, "Successfully created pending user recharge record. Input: %+v, InsertedID (if available): %d", in, insertedID)

	output := &model.UserRechargeCreateOutput{
		// UserRechargesId: rechargeEntity.Id, // Assuming Id field exists and is populated. Adjust if needed.
		// For now, returning 0 or an empty struct as per instructions if ID is not easily available.
		// Let's assume we can get it from LastInsertId for now, but be aware of the note above.
		UserRechargesId: uint(insertedID),
	}

	return output, nil
}

// CheckTxHashExists checks if a transaction hash already exists in the user_recharges table.
func (s *sUserRecharges) CheckTxHashExists(ctx context.Context, txHash string) (bool, error) {
	count, err := dao.UserRecharges.Ctx(ctx).Where(dao.UserRecharges.Columns().TxHash, txHash).Count()
	if err != nil {
		// In GoFrame v2, Count() returns 0 and nil error if no records found.
		// We only need to handle actual database errors.
		// Let's wrap the error for better context if it's a real DB error.
		// Note: gdb.ErrNoRows is typically for Scan operations, not Count.
		return false, gerror.Wrapf(err, "database error checking txHash existence for %s", txHash)
	}

	// If count > 0, the record exists.
	return count > 0, nil
}

// ProcessNewDeposit handles a newly found deposit: checks existence and creates a record if new.
// It now uses the DepositInfo structure from the ports package.
// Returns the output containing the new ID (if created), a boolean indicating if a new record was created, and an error.
func (s *sUserRecharges) ProcessNewDeposit(ctx context.Context, deposit ports.DepositInfo) (output *model.UserRechargeCreateOutput, created bool, err error) { // Use ports.DepositInfo (value type)
	g.Log().Debugf(ctx, "Processing potential deposit: UserID=%d, TokenID=%d, Chain=%s, Amount=%s, TxHash=%s, Confirmations=%d",
		deposit.UserID, deposit.TokenID, deposit.Chain, deposit.Amount, deposit.TxHash, deposit.Confirmations)

	// 1. Check if TxHash already exists
	exists, checkErr := s.CheckTxHashExists(ctx, deposit.TxHash)
	if checkErr != nil {
		g.Log().Errorf(ctx, "Failed to check TxHash existence for %s: %v", deposit.TxHash, checkErr)
		// Wrap the error and return
		return nil, false, gerror.Wrapf(checkErr, "failed to check TxHash existence for %s", deposit.TxHash)
	}

	if exists {
		// 3. TxHash already exists, skip processing
		g.Log().Debugf(ctx, "Deposit transaction already exists, skipping (TxHash: %s)", deposit.TxHash)
		return nil, false, nil // Return nil output, false for created, and nil error
	}

	// 2. TxHash does not exist, create a new pending recharge record
	input := model.UserRechargeCreateInput{
		UserId:               uint64(deposit.UserID), // Convert uint to uint64
		TokenId:              deposit.TokenID,
		Name:                 deposit.TokenSymbol, // Use TokenSymbol from deposit info
		Chan:                 deposit.Chain,
		TokenContractAddress: "", // Assuming not available in DepositInfo, adjust if needed
		FromAddress:          deposit.FromAddress,
		ToAddress:            deposit.ToAddress,
		TxHash:               deposit.TxHash,
		Amount:               deposit.Amount,
		Confirmations:        deposit.Confirmations,
		// Status is set to Pending by default in CreateUserRecharge service
	}

	createOutput, createErr := s.CreateUserRecharge(ctx, &input)
	if createErr != nil {
		g.Log().Errorf(ctx, "Failed to create user recharge record for TxHash %s (UserID: %d): %v",
			deposit.TxHash, deposit.UserID, createErr)
		// Wrap the error and return
		return nil, false, gerror.Wrapf(createErr, "failed to create user recharge record for TxHash %s", deposit.TxHash)
	}

	// Successfully created
	if createOutput != nil {
		g.Log().Infof(ctx, "Successfully created pending user recharge record (ID: %d) for TxHash %s",
			createOutput.UserRechargesId, deposit.TxHash)
	} else {
		// Should ideally not happen if creation was successful without error, but log just in case
		g.Log().Infof(ctx, "Successfully initiated creation of pending user recharge record for TxHash %s (Output is nil)", deposit.TxHash)
	}

	// Return the output from CreateUserRecharge, true for created, and nil error
	return createOutput, true, nil
}
func (s *sUserRecharges) UpdateRechargeStatus(ctx context.Context, tx gdb.TX, rechargeID uint64, state uint) error {
	updateData := g.Map{
		dao.UserRecharges.Columns().State:       state,
		dao.UserRecharges.Columns().CompletedAt: gtime.Now(),
	}

	_, err := dao.UserRecharges.Ctx(ctx).TX(tx).
		Data(updateData).
		Where(dao.UserRecharges.Columns().RechargesId, rechargeID).
		Update()

	if err != nil {
		return gerror.Wrapf(err, "failed to update recharge status for ID %d", rechargeID)
	}

	return nil
}
