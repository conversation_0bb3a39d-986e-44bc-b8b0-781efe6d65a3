package redis

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// HSet 哈希表设置字段。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) HSet(ctx context.Context, key string, field string, value interface{}) (int64, error) {
	// Assuming default instance if name is not provided in the interface signature
	// Note: gredis HSet expects a map.
	return s.Client().HSet(ctx, key, g.Map{field: value})
}
