package redis

import (
	"context"
	"time"
)

// Set 设置缓存，duration 为 0 表示永不过期。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) Set(ctx context.Context, key string, value interface{}, duration time.Duration, name ...string) error {
	if duration == 0 {
		_, err := s.Client(name...).Set(ctx, key, value)
		return err
	}
	return s.Client(name...).SetEX(ctx, key, value, int64(duration.Seconds()))
}
