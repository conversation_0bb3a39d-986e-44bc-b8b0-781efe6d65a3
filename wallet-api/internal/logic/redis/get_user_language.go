package redis

import (
	"context"

	"github.com/a19ba14d/tg-bot-common/consts"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetUserLanguage 获取用户语言缓存。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) GetUserLanguage(ctx context.Context, telegramID int64, name ...string) (string, error) {
	key := consts.UserLanguageKeyPrefix + gconv.String(telegramID)
	g.Log().Debugf(ctx, "Getting user language cache: key=%s", key)

	// Use the GetString method of this service
	lang, err := s.GetString(ctx, key, name...)
	if err != nil {
		// GetString handles key not found by returning "", nil. Log other errors.
		g.Log().Errorf(ctx, "Error getting user language from Redis for key %s: %v", key, err)
		return "", err
	}
	// Assuming language standardization is handled elsewhere if needed
	return lang, nil
}
