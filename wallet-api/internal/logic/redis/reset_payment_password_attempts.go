package redis

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// ResetPaymentPasswordAttempts 重置用户支付密码尝试次数和锁定状态。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) ResetPaymentPasswordAttempts(ctx context.Context, userID uint64) error {
	attemptsKey := getPaymentAttemptsKey(userID)
	lockKey := getPaymentLockKey(userID)
	g.Log().Debugf(ctx, "Resetting payment password attempts for user %d, keys=%s,%s", userID, attemptsKey, lockKey)

	// Use the Remove method of this service
	err1 := s.Remove(ctx, attemptsKey)
	err2 := s.Remove(ctx, lockKey)

	if err1 != nil {
		g.Log().Errorf(ctx, "Failed to delete payment attempts key %s: %v", attemptsKey, err1)
		// Decide if you want to return immediately or try deleting the other key
		// Returning the first error encountered for simplicity
		return err1
	}
	if err2 != nil {
		g.<PERSON>g().<PERSON><PERSON><PERSON>(ctx, "Failed to delete payment lock key %s: %v", lockKey, err2)
		return err2
	}
	return nil
}
