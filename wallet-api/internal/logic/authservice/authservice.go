package authservice

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/do"
	"wallet-api/internal/service"
	"wallet-api/internal/utility/jwtutil"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/golang-jwt/jwt/v4"
)

type authService struct {
	refreshTokenDAO dao.RefreshTokensDAO
	// jwtUtil 在此项目中是包级别函数，无需作为字段注入
}

// New 创建并返回 IAuth 服务的新实例
func New(refreshTokenDAO dao.RefreshTokensDAO) service.IAuth {
	return &authService{
		refreshTokenDAO: refreshTokenDAO,
	}
}

func init() {
	service.RegisterAuth(New)
}

// GetPublicWalletInitStatus 获取钱包初始化状态
func (s *authService) GetPublicWalletInitStatus(ctx context.Context, req *v1.GetPublicWalletInitStatusReq) (res *v1.GetPublicWalletInitStatusRes, err error) {
	wallet, err := dao.Wallets.GetWallet(ctx)
	if err != nil {
		return nil, err
	}

	return &v1.GetPublicWalletInitStatusRes{
		IsInitialized: wallet != nil,
	}, nil
}

// Login 处理用户登录，成功后返回访问令牌和刷新令牌
func (s *authService) Login(ctx context.Context, userID uint) (accessToken string, refreshToken string, err error) {
	userIDStr := strconv.FormatUint(uint64(userID), 10)

	accessToken, _, err = jwtutil.GenerateAccessToken(userIDStr)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, refreshJTI, err := jwtutil.GenerateRefreshToken(userIDStr)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate refresh token: %w", err)
	}

	var parsedTokenForClaims *jwt.Token // 用于获取claims，即使token验证失败（例如过期）
	var expiresAtFromClaims time.Time

	parsedTokenForClaims, err = jwt.ParseWithClaims(refreshToken, &jwtutil.CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		cfg := g.Cfg()
		secretVal, cfgErr := cfg.Get(context.Background(), "jwt.secret")
		if cfgErr != nil {
			return nil, fmt.Errorf("failed to get jwt.secret for parsing refresh token: %w", cfgErr)
		}
		secret := secretVal.String()
		if secret == "" {
			return nil, errors.New("jwt.secret is empty, cannot parse refresh token")
		}
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(secret), nil
	})

	// 不论解析是否成功(err != nil)，我们都尝试从 parsedTokenForClaims 中提取 claims
	if claims, ok := parsedTokenForClaims.Claims.(*jwtutil.CustomClaims); ok && claims.RegisteredClaims.ExpiresAt != nil {
		expiresAtFromClaims = claims.RegisteredClaims.ExpiresAt.Time
	} else {
		// 如果无法从claims中获取，则可能是token格式严重错误或非预期claims类型
		// 此时，如果jwt.ParseWithClaims本身也返回了错误，则优先返回那个错误
		if err != nil {
			return "", "", fmt.Errorf("failed to parse refresh token to get claims: %w", err)
		}
		// 如果jwt.ParseWithClaims没报错，但claims有问题，这也是一个错误
		return "", "", errors.New("could not extract valid claims or expiration from generated refresh token")
	}

	// 即使原始的 err 是 ValidationErrorExpired，我们也应该保存，因为JTI和ExpiresAt是有效的
	// 其他类型的解析错误 (如malformed) 则不应该继续保存
	if err != nil {
		var ve *jwt.ValidationError
		if !errors.As(err, &ve) || (ve.Errors&(jwt.ValidationErrorMalformed|jwt.ValidationErrorSignatureInvalid|jwt.ValidationErrorNotValidYet)) != 0 {
			// 如果不是ValidationError，或者是一些不能忽略的错误类型，则返回
			return "", "", fmt.Errorf("failed to parse refresh token: %w", err)
		}
		// 对于 ValidationErrorExpired 或其他可接受的错误，我们继续，因为expiresAtFromClaims已获取
	}

	refreshTokenDO := &do.RefreshTokens{
		UserId:    userID,                                 // Corrected field name
		Jti:       refreshJTI,                             // Corrected field name
		ExpiresAt: gtime.NewFromTime(expiresAtFromClaims), // Corrected type
		IsRevoked: false,
		CreatedAt: gtime.NewFromTime(time.Now()), // Corrected type
		UpdatedAt: gtime.NewFromTime(time.Now()), // Corrected type
	}

	if err := s.refreshTokenDAO.Save(ctx, refreshTokenDO); err != nil {
		return "", "", fmt.Errorf("failed to save refresh token: %w", err)
	}

	return accessToken, refreshToken, nil
}

// RefreshToken 使用旧的刷新令牌获取新的访问令牌和刷新令牌
func (s *authService) RefreshToken(ctx context.Context, req *v1.RefreshTokenReq) (res *v1.RefreshTokenRes, err error) {
	claims, err := jwtutil.VerifyToken(req.RefreshToken)
	if err != nil {
		errMsg := "failed to verify old refresh token: %w"
		if errors.Is(err, jwt.ErrTokenExpired) {
			errMsg = "old refresh token has expired: %w"
		} else if errors.Is(err, jwt.ErrTokenSignatureInvalid) {
			errMsg = "invalid signature for old refresh token: %w"
		}
		return nil, fmt.Errorf(errMsg, err)
	}

	oldRefreshJTI := claims.RegisteredClaims.ID
	userIDStr := claims.UserID
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse userID from refresh token claims: %w", err)
	}

	tokenInfo, err := s.refreshTokenDAO.GetByJTI(ctx, oldRefreshJTI)
	if err != nil {
		return nil, fmt.Errorf("failed to get refresh token from db by JTI: %w", err)
	}
	if tokenInfo == nil {
		return nil, errors.New("refresh token not found in db or already used/revoked (jti mismatch)")
	}

	// 检查 UserID 是否匹配，防止JTI碰撞且恰好未吊销的情况（虽然概率极低）
	if tokenInfo.UserId != uint(userID) { // Corrected field name
		// 理论上不应该发生，因为JTI应该是唯一的。但作为安全加固。
		// 也可以选择吊销这个有问题的 tokenInfo.JTI
		return nil, errors.New("refresh token user mismatch, potential security issue")
	}

	// 吊销旧的 Refresh Token
	if err := s.refreshTokenDAO.RevokeByJTI(ctx, oldRefreshJTI); err != nil {
		// 即使吊销失败，也应该阻止生成新token，或者记录严重错误
		return nil, fmt.Errorf("failed to revoke old refresh token: %w", err)
	}

	// 生成新的 Access Token 和 Refresh Token (复用 Login 的核心逻辑)
	// 注意：Login 方法内部包含了 Save Refresh Token 的逻辑，这里我们直接调用 Login。
	// 确保 Login 方法是幂等的，或者这里的调用是符合预期的。
	// 或者，将 Login 拆分为 "generateAndSaveTokens" 和 "loginOperation"
	accessToken, refreshToken, err := s.Login(ctx, uint(userID)) // 再次调用 Login，它会处理新 token 的生成和保存
	if err != nil {
		return nil, fmt.Errorf("failed to generate new tokens during refresh: %w", err)
	}

	return &v1.RefreshTokenRes{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	}, nil
}

// Logout 处理用户登出，使指定的刷新令牌失效
func (s *authService) Logout(ctx context.Context, req *v1.LogoutReq) (*v1.LogoutRes, error) {
	// 验证并获取 JTI，即使 token 过期也应该允许登出（吊销）
	claims, err := jwtutil.VerifyToken(req.RefreshToken)
	if err != nil {
		// 如果错误不是 "token expired"，则可能是其他问题（如格式错误、签名无效）
		// 如果是 "token expired"，我们依然可以尝试从claims中获取JTI（如果Parse能解析出来）
		// 尝试从原始错误中获取 token claims，即使 token 已过期
		// jwtutil.VerifyToken 内部的 ParseWithClaims 返回的 token 实例可能包含 claims
		// 但 VerifyToken 本身在出错时只返回 error。
		// 为了能从过期的token中获取JTI，我们需要一种方式在VerifyToken失败（特别是过期）时仍能访问claims。
		// 当前 jwtutil.VerifyToken 的设计使得这很困难。
		// 理想情况是 VerifyToken 在返回 ErrTokenExpired 时，也返回 claims。

		// 重新解析一次以尝试获取JTI，允许过期，但不允许其他错误
		parsedTokenForLogout, parseErr := jwt.ParseWithClaims(req.RefreshToken, &jwtutil.CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
			cfg := g.Cfg()
			secretVal, cfgErr := cfg.Get(context.Background(), "jwt.secret")
			if cfgErr != nil {
				return nil, fmt.Errorf("failed to get jwt.secret for logout token parsing: %w", cfgErr)
			}
			secret := secretVal.String()
			if secret == "" {
				return nil, errors.New("jwt.secret is empty, cannot parse token for logout")
			}
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(secret), nil
		})

		if parseErr != nil {
			var logoutVe *jwt.ValidationError
			if errors.As(parseErr, &logoutVe) {
				if logoutVe.Errors&jwt.ValidationErrorExpired != 0 {
					// Token 过期，尝试从解析出的 token (parsedTokenForLogout) 中获取 JTI
					if expiredClaims, ok := parsedTokenForLogout.Claims.(*jwtutil.CustomClaims); ok && expiredClaims.RegisteredClaims.ID != "" {
						if errRevoke := s.refreshTokenDAO.RevokeByJTI(ctx, expiredClaims.RegisteredClaims.ID); errRevoke != nil {
							return nil, fmt.Errorf("failed to revoke refresh token by JTI (from expired token): %w", errRevoke)
						}
						return &v1.LogoutRes{}, nil // 成功吊销过期的 token
					}
					return nil, fmt.Errorf("failed to get JTI from expired refresh token for logout: %w (original verify error: %v)", parseErr, err)
				}
			}
			// 对于其他解析错误 (malformed, signature invalid, etc.)
			return nil, fmt.Errorf("failed to parse refresh token for logout: %w (original verify error: %v)", parseErr, err)
		}
		// 如果 parseErr 为 nil，意味着 token 是有效的，可以从 parsedTokenForLogout.Claims 获取
		logoutClaims, ok := parsedTokenForLogout.Claims.(*jwtutil.CustomClaims)
		if !ok || !parsedTokenForLogout.Valid || logoutClaims.RegisteredClaims.ID == "" {
			return nil, errors.New("could not extract JTI from (parsed) refresh token for logout")
		}
		claims = logoutClaims // 使用新解析出的有效claims
	}

	// 如果 token 有效（未过期且签名正确），或者我们从过期token中成功提取了claims
	refreshJTI := claims.RegisteredClaims.ID
	if refreshJTI == "" { // 双重检查
		return nil, errors.New("could not extract JTI from refresh token for logout (after checks)")
	}

	if err := s.refreshTokenDAO.RevokeByJTI(ctx, refreshJTI); err != nil {
		return nil, fmt.Errorf("failed to revoke refresh token by JTI: %w", err)
	}
	return &v1.LogoutRes{}, nil
}

// ParseUserIDFromAccessToken 从访问令牌中解析出用户ID
func (s *authService) ParseUserIDFromAccessToken(ctx context.Context, accessTokenString string) (userID uint, err error) {
	claims, err := jwtutil.VerifyToken(accessTokenString)
	if err != nil {
		errMsg := "failed to verify access token: %w"
		if errors.Is(err, jwt.ErrTokenExpired) {
			errMsg = "access token has expired: %w"
		} else if errors.Is(err, jwt.ErrTokenSignatureInvalid) {
			errMsg = "invalid signature for access token: %w"
		}
		return 0, fmt.Errorf(errMsg, err)
	}

	userIDStr := claims.UserID
	parsedUserID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse userID from access token claims: %w", err)
	}
	return uint(parsedUserID), nil
}
