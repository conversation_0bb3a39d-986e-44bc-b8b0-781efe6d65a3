package ports

// DepositInfo 包含检测到的充值信息
// Moved from internal/logic/task/checker/interface.go to break import cycle
type DepositInfo struct {
	TxHash        string
	FromAddress   string
	ToAddress     string
	Amount        string // 使用字符串表示以避免精度问题，后续处理
	BlockNumber   uint64
	TokenSymbol   string // 例如 "ETH", "USDT"
	Chain         string // 例如 "ETH", "TRX"
	UserID        uint   // 关联的用户ID (从 UserAddress 获取)
	TokenID       uint   // 关联的TokenID (从 UserAddress 获取)
	Confirmations uint64 // 交易的当前确认数
}
