package repository

import (
	"context"
	"strings"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// withdrawRepository implements IWithdrawRepository interface.
type withdrawRepository struct{}

// NewWithdrawRepository creates and returns a new instance of IWithdrawRepository.
func NewWithdrawRepository() IWithdrawRepository {
	return &withdrawRepository{}
}

// buildWithdrawCondition builds a condition map for withdraw queries based on options.
func (r *withdrawRepository) buildWithdrawCondition(options WithdrawQueryOptions) g.Map {
	condition := g.Map{}

	// Add filters based on options
	if options.FromAddress != "" {
		condition[dao.UserWithdraws.Columns().FromAddress+" like ?"] = "%" + options.FromAddress + "%"
	}

	// Add filters based on options
	if options.ToAddress != "" {
		condition[dao.UserWithdraws.Columns().FromAddress+" like ?"] = "%" + options.ToAddress + "%"
	}

	if options.Chain != "" {
		condition[dao.UserWithdraws.Columns().Chan] = options.Chain
	}

	if options.Coin != "" {
		condition[dao.UserWithdraws.Columns().Name] = options.Coin
	}

	if options.Status != "" {
		// Map API status to database status
		var state uint
		switch options.Status {
		case "pending":
			state = 1 // Pending
		case "processing":
			state = 2 // Processing
		case "rejected":
			state = 3 // Rejected
		case "completed":
			state = 4 // Completed
		case "failed":
			state = 5 // Failed
		}
		condition[dao.UserWithdraws.Columns().State] = state
	}

	// Add date range filter if provided
	if options.DateStart != nil && options.DateEnd != nil {
		condition["where"] = g.Map{
			dao.UserWithdraws.Columns().CreatedAt + " BETWEEN ? AND ?": []interface{}{
				options.DateStart.Format("2006-01-02 15:04:05"),
				options.DateEnd.Format("2006-01-02 15:04:05"),
			},
		}
	}

	return condition
}

// ListWithdraws retrieves a paginated list of withdraws based on options.
func (r *withdrawRepository) ListWithdraws(ctx context.Context, options WithdrawQueryOptions) (list []*entity.UserWithdraws, total int, err error) {
	condition := r.buildWithdrawCondition(options)

	// Build order by clause
	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		// Map API field names to database column names if needed
		dbField := options.SortField
		if strings.HasPrefix(dbField, "user_withdraws_") {
			// Convert camelCase to snake_case if needed
			dbField = strings.Replace(dbField, "user_withdraws_", "", 1)
		}
		orderBy = dbField + " " + options.SortOrder
	} else {
		orderBy = "user_withdraws_id DESC" // Default sort
	}

	// Query with pagination
	model := dao.UserWithdraws.Ctx(ctx).Where(condition)

	// Get total count
	total, err = model.Count()
	if err != nil {
		return nil, 0, err
	}

	// Get paginated data
	err = model.Page(options.Page, options.Limit).Order(orderBy).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// FindAllWithdraws retrieves all withdraws based on options (for export).
func (r *withdrawRepository) FindAllWithdraws(ctx context.Context, options WithdrawQueryOptions) ([]*entity.UserWithdraws, error) {
	condition := r.buildWithdrawCondition(options)

	// Build order by clause
	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		// Map API field names to database column names if needed
		dbField := options.SortField
		if strings.HasPrefix(dbField, "user_withdraws_") {
			// Convert camelCase to snake_case if needed
			dbField = strings.Replace(dbField, "user_withdraws_", "", 1)
		}
		orderBy = dbField + " " + options.SortOrder
	} else {
		orderBy = "user_withdraws_id DESC" // Default sort
	}

	// Query all matching records
	var list []*entity.UserWithdraws
	err := dao.UserWithdraws.Ctx(ctx).Where(condition).Order(orderBy).Scan(&list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// GetWithdrawStatistics retrieves aggregated statistics about withdraws.
func (r *withdrawRepository) GetWithdrawStatistics(ctx context.Context) (*WithdrawStatistic, error) {
	var stats WithdrawStatistic
	var err error

	// Get total withdraw count
	stats.TotalWithdrawCount, err = dao.UserWithdraws.Ctx(ctx).Count()
	if err != nil {
		return nil, err
	}

	// Get pending count (state = 1)
	stats.PendingCount, err = dao.UserWithdraws.Ctx(ctx).Where(dao.UserWithdraws.Columns().State, 1).Count()
	if err != nil {
		return nil, err
	}

	// Get processing count (state = 2)
	stats.ProcessingCount, err = dao.UserWithdraws.Ctx(ctx).Where(dao.UserWithdraws.Columns().State, 2).Count()
	if err != nil {
		return nil, err
	}

	// Get rejected count (state = 3)
	stats.RejectedCount, err = dao.UserWithdraws.Ctx(ctx).Where(dao.UserWithdraws.Columns().State, 3).Count()
	if err != nil {
		return nil, err
	}

	// Get completed count (state = 4)
	stats.CompletedCount, err = dao.UserWithdraws.Ctx(ctx).Where(dao.UserWithdraws.Columns().State, 4).Count()
	if err != nil {
		return nil, err
	}

	// Get failed count (state = 5)
	stats.FailedCount, err = dao.UserWithdraws.Ctx(ctx).Where(dao.UserWithdraws.Columns().State, 5).Count()
	if err != nil {
		return nil, err
	}

	// Get total USDT amount
	result, err := dao.UserWithdraws.Ctx(ctx).
		Where(dao.UserWithdraws.Columns().Name, "USDT").
		Fields("SUM(" + dao.UserWithdraws.Columns().Amount + ") as total").
		Value()
	if err != nil {
		return nil, err
	}
	stats.TotalUsdtAmount = result.Float64()

	// Get total TRX amount
	result, err = dao.UserWithdraws.Ctx(ctx).
		Where(dao.UserWithdraws.Columns().Name, "TRX").
		Fields("SUM(" + dao.UserWithdraws.Columns().Amount + ") as total").
		Value()
	if err != nil {
		return nil, err
	}
	stats.TotalTrxAmount = result.Float64()

	// Get total ETH amount
	result, err = dao.UserWithdraws.Ctx(ctx).
		Where(dao.UserWithdraws.Columns().Name, "ETH").
		Fields("SUM(" + dao.UserWithdraws.Columns().Amount + ") as total").
		Value()
	if err != nil {
		return nil, err
	}
	stats.TotalEthAmount = result.Float64()

	return &stats, nil
}

// CreateWithdraw creates a new withdraw record.
func (r *withdrawRepository) CreateWithdraw(ctx context.Context, withdraw *entity.UserWithdraws) (int, error) {
	result, err := dao.UserWithdraws.Ctx(ctx).Data(withdraw).Insert()
	if err != nil {
		return 0, err
	}

	// Get the inserted ID
	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	return int(id), nil
}
