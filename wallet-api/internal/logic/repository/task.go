package repository

import (
	"context"
	"time"
	"wallet-api/internal/model/entity" // Import entity

	"github.com/gogf/gf/v2/frame/g"
)

// TaskQueryOptions holds options for querying tasks.
type TaskQueryOptions struct {
	Page        int
	Limit       int
	Type        string     // Filter by network_type (ETH/TRON)
	Status      string     // Filter by task_status
	DateStart   *time.Time // Filter by start date
	DateEnd     *time.Time // Filter by end date
	SortField   string
	SortOrder   string
	TaskType    string // Filter by task_type (transfer/collect)
	ExecuteType string // Filter by execute_type (manual/auto)
	// Add other filters as needed
}

// ITaskRepository defines the interface for task business data operations.
type ITaskRepository interface {
	// CreateTask creates a new task record and returns its ID.
	// Accepts g.Map for flexibility in creating tasks with various fields.
	CreateTask(ctx context.Context, taskData g.Map) (taskId int64, err error)

	// ListTasks retrieves a paginated list of tasks based on options.
	// Returns the list of tasks, total count, and error.
	ListTasks(ctx context.Context, options TaskQueryOptions) (list []*entity.Tasks, total int, err error)

	// FindAllTasks retrieves all tasks based on options.
	FindAllTasks(ctx context.Context, options TaskQueryOptions) ([]*entity.Tasks, error)

	// FindTaskByID retrieves a single task by its ID.
	// Returns nil, nil if not found.
	FindTaskByID(ctx context.Context, taskId int64) (*entity.Tasks, error)

	// UpdateTask updates specific fields of a task record identified by its ID.
	UpdateTask(ctx context.Context, taskId int64, data g.Map) error

	// UpdateTaskStatus updates the status of a task identified by its ID.
	UpdateTaskStatus(ctx context.Context, taskId int64, status string) error

	// Add other task-specific methods as needed...
}
