package repository

import (
	"context"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	// "github.com/gogf/gf/v2/errors/gerror" // Removed unused import

	"wallet-api/internal/codes" // Import codes package
)

// operationLogRepository implements IOperationLogRepository interface.
type operationLogRepository struct {
	opLogDao dao.IOperationLogsDao // Dependency on DAO interface
}

// NewOperationLogRepository creates and returns a new instance of IOperationLogRepository.
func NewOperationLogRepository(dao dao.IOperationLogsDao) IOperationLogRepository {
	return &operationLogRepository{
		opLogDao: dao,
	}
}

// AddLog adds a new operation log entry.
func (r *operationLogRepository) AddLog(ctx context.Context, logData *entity.OperationLogs) error {
	if logData == nil {
		return codes.NewError(codes.CodeRepositoryNilData)
	}

	// Directly pass the entity struct to the new DAO method
	err := r.opLogDao.InsertLog(ctx, logData) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return err
	}
	return nil
}

// ListLogs - Placeholder implementation if needed later
// func (r *operationLogRepository) ListLogs(ctx context.Context, options LogQueryOptions) (list []*entity.OperationLogs, total int, err error) {
// 	// Implementation would involve building query based on options and using DAO methods
// 	return nil, 0, codes.NewError(codes.CodeRepositoryNotImplemented)
// }
