package repository

import (
	"context"
	"strings"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// rechargeRepository implements IRechargeRepository interface.
type rechargeRepository struct{}

// NewRechargeRepository creates and returns a new instance of IRechargeRepository.
func NewRechargeRepository() IRechargeRepository {
	return &rechargeRepository{}
}

// buildRechargeCondition builds a condition map for recharge queries based on options.
func (r *rechargeRepository) buildRechargeCondition(options RechargeQueryOptions) g.Map {
	condition := g.Map{}

	// Add filters based on options
	if options.Address != "" {
		// Search in both from_address and to_address using direct SQL OR condition
		fromAddressCol := dao.UserRecharges.Columns().FromAddress
		toAddressCol := dao.UserRecharges.Columns().ToAddress
		condition[fromAddressCol+" LIKE ? OR "+toAddressCol+" LIKE ?"] = []interface{}{
			"%" + options.Address + "%",
			"%" + options.Address + "%",
		}
	}

	if options.Chain != "" {
		condition[dao.UserRecharges.Columns().Chan] = options.Chain
	}

	if options.Coin != "" {
		condition[dao.UserRecharges.Columns().Name] = options.Coin
	}

	if options.Status != "" {
		// Map API status to database status
		var state uint
		if options.Status == "pending" {
			state = 1 // Pending
		} else if options.Status == "completed" {
			state = 2 // Completed
		}
		condition[dao.UserRecharges.Columns().State] = state
	}

	// Add date range filter if provided
	if options.DateStart != nil && options.DateEnd != nil {
		condition[dao.UserRecharges.Columns().CreatedAt+" BETWEEN ? AND ?"] = []any{
			options.DateStart.Format("2006-01-02 15:04:05"),
			options.DateEnd.Format("2006-01-02 15:04:05"),
		}
	}

	return condition
}

// ListRecharges retrieves a paginated list of recharges based on options.
func (r *rechargeRepository) ListRecharges(ctx context.Context, options RechargeQueryOptions) (list []*entity.UserRecharges, total int, err error) {
	condition := r.buildRechargeCondition(options)

	// Build order by clause
	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		// Map API field names to database column names if needed
		dbField := options.SortField
		if strings.HasPrefix(dbField, "recharges_") {
			// Convert camelCase to snake_case if needed
			dbField = strings.Replace(dbField, "recharges_", "", 1)
		}
		orderBy = dbField + " " + options.SortOrder
	} else {
		orderBy = "recharges_id DESC" // Default sort
	}

	// Query with pagination
	model := dao.UserRecharges.Ctx(ctx).Where(condition)

	// Get total count
	total, err = model.Count()
	if err != nil {
		return nil, 0, err
	}

	// Get paginated data
	err = model.Page(options.Page, options.Limit).Order(orderBy).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// FindAllRecharges retrieves all recharges based on options (for export).
func (r *rechargeRepository) FindAllRecharges(ctx context.Context, options RechargeQueryOptions) ([]*entity.UserRecharges, error) {
	condition := r.buildRechargeCondition(options)

	// Build order by clause
	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		// Map API field names to database column names if needed
		dbField := options.SortField
		if strings.HasPrefix(dbField, "recharges_") {
			// Convert camelCase to snake_case if needed
			dbField = strings.Replace(dbField, "recharges_", "", 1)
		}
		orderBy = dbField + " " + options.SortOrder
	} else {
		orderBy = "recharges_id DESC" // Default sort
	}

	// Query without pagination
	var list []*entity.UserRecharges
	err := dao.UserRecharges.Ctx(ctx).Where(condition).Order(orderBy).Scan(&list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// GetRechargeStatistics retrieves aggregated statistics about recharges.
func (r *rechargeRepository) GetRechargeStatistics(ctx context.Context) (*RechargeStatistic, error) {
	var stats RechargeStatistic
	var err error

	// Get total recharge count
	stats.TotalRechargeCount, err = dao.UserRecharges.Ctx(ctx).Count()
	if err != nil {
		return nil, err
	}

	// Get pending count (state = 1)
	stats.PendingCount, err = dao.UserRecharges.Ctx(ctx).Where(dao.UserRecharges.Columns().State, 1).Count()
	if err != nil {
		return nil, err
	}

	// Get completed count (state = 2)
	stats.CompletedCount, err = dao.UserRecharges.Ctx(ctx).Where(dao.UserRecharges.Columns().State, 2).Count()
	if err != nil {
		return nil, err
	}

	// Get total USDT amount
	result, err := dao.UserRecharges.Ctx(ctx).
		Where(dao.UserRecharges.Columns().Name, "USDT").
		Fields("SUM(" + dao.UserRecharges.Columns().Amount + ") as total").
		Value()
	if err != nil {
		return nil, err
	}
	stats.TotalUsdtAmount = result.Float64()

	// Get total TRX amount
	result, err = dao.UserRecharges.Ctx(ctx).
		Where(dao.UserRecharges.Columns().Name, "TRX").
		Fields("SUM(" + dao.UserRecharges.Columns().Amount + ") as total").
		Value()
	if err != nil {
		return nil, err
	}
	stats.TotalTrxAmount = result.Float64()

	// Get total ETH amount
	result, err = dao.UserRecharges.Ctx(ctx).
		Where(dao.UserRecharges.Columns().Name, "ETH").
		Fields("SUM(" + dao.UserRecharges.Columns().Amount + ") as total").
		Value()
	if err != nil {
		return nil, err
	}
	stats.TotalEthAmount = result.Float64()

	return &stats, nil
}
