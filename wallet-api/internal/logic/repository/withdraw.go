package repository

import (
	"context"
	"time"
	"wallet-api/internal/model/entity"
)

// WithdrawQueryOptions holds options for querying withdraw records.
type WithdrawQueryOptions struct {
	Page        int
	Limit       int
	FromAddress string     // Filter by address (like)
	ToAddress   string     // Filter by address (like)
	Chain       string     // Filter by chain (ETH/TRON)
	Status      string     // Filter by status
	Coin        string     // Filter by name (token name)
	DateStart   *time.Time // Filter by start date (inclusive)
	DateEnd     *time.Time // Filter by end date (inclusive)
	SortField   string
	SortOrder   string
}

// WithdrawStatistic holds aggregated statistics about withdrawals.
type WithdrawStatistic struct {
	TotalWithdrawCount int
	PendingCount       int
	ProcessingCount    int
	RejectedCount      int
	CompletedCount     int
	FailedCount        int
	TotalUsdtAmount    float64
	TotalTrxAmount     float64
	TotalEthAmount     float64
}

// IWithdrawRepository defines the interface for withdraw business data operations.
type IWithdrawRepository interface {
	// ListWithdraws retrieves a paginated list of withdraws based on options.
	// Returns the list of withdraws, total count, and error.
	ListWithdraws(ctx context.Context, options WithdrawQueryOptions) (list []*entity.UserWithdraws, total int, err error)

	// FindAllWithdraws retrieves all withdraws based on options (for export).
	FindAllWithdraws(ctx context.Context, options WithdrawQueryOptions) ([]*entity.UserWithdraws, error)

	// GetWithdrawStatistics retrieves aggregated statistics about withdraws.
	GetWithdrawStatistics(ctx context.Context) (*WithdrawStatistic, error)

	// CreateWithdraw creates a new withdraw record.
	CreateWithdraw(ctx context.Context, withdraw *entity.UserWithdraws) (int, error)
}
