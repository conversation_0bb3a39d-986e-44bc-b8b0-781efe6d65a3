package repository

import (
	"context"
	"time"
	"wallet-api/internal/model/entity"
)

// RechargeQueryOptions holds options for querying recharge records.
type RechargeQueryOptions struct {
	Page      int
	Limit     int
	Address   string     // Filter by from_address or to_address (like)
	Chain     string     // Filter by chain (ETH/TRON)
	Status    string     // Filter by status
	Coin      string     // Filter by name (token name)
	DateStart *time.Time // Filter by start date (inclusive)
	DateEnd   *time.Time // Filter by end date (inclusive)
	SortField string
	SortOrder string
}

// RechargeStatistic holds aggregated recharge statistics.
type RechargeStatistic struct {
	TotalRechargeCount int
	PendingCount       int
	CompletedCount     int
	TotalUsdtAmount    float64
	TotalTrxAmount     float64
	TotalEthAmount     float64
}

// IRechargeRepository defines the interface for recharge business data operations.
type IRechargeRepository interface {
	// ListRecharges retrieves a paginated list of recharges based on options.
	// Returns the list of recharges, total count, and error.
	ListRecharges(ctx context.Context, options RechargeQueryOptions) (list []*entity.UserRecharges, total int, err error)

	// FindAllRecharges retrieves all recharges based on options (for export).
	FindAllRecharges(ctx context.Context, options RechargeQueryOptions) ([]*entity.UserRecharges, error)

	// GetRechargeStatistics retrieves aggregated statistics about recharges.
	GetRechargeStatistics(ctx context.Context) (*RechargeStatistic, error)
}
