package repository

import (
	"context"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv" // Needed for conversion
)

// taskRepository implements ITaskRepository interface.
type taskRepository struct {
	taskDao dao.ITasksDao // Dependency on DAO interface
}

// NewTaskRepository creates and returns a new instance of ITaskRepository.
func NewTaskRepository(dao dao.ITasksDao) ITaskRepository {
	return &taskRepository{
		taskDao: dao,
	}
}

// buildTaskCondition converts query options to a map condition for DAO methods.
func (r *taskRepository) buildTaskCondition(options TaskQueryOptions) map[string]interface{} {
	condition := g.Map{}

	if options.Type != "" {
		condition["network_type"] = options.Type
	}
	if options.Status != "" {
		condition["task_status"] = options.Status
	}
	if options.TaskType != "" {
		condition["task_type"] = options.TaskType
	}
	if options.ExecuteType != "" {
		condition["execute_type"] = options.ExecuteType
	}
	if options.DateStart != nil && options.DateEnd != nil {
		condition["create_at >= ? AND create_at <= ?"] = []interface{}{options.DateStart, options.DateEnd}
	} else if options.DateStart != nil {
		condition["create_at >= ?"] = options.DateStart
	} else if options.DateEnd != nil {
		condition["create_at <= ?"] = options.DateEnd
	}
	// Add other filters based on TaskQueryOptions if needed

	return condition
}

// CreateTask creates a new task record and returns its ID.
func (r *taskRepository) CreateTask(ctx context.Context, taskData g.Map) (taskId int64, err error) {
	// Remove ID field if present, as it's auto-increment
	delete(taskData, "id")

	// Convert g.Map to *entity.Tasks
	var taskEntity *entity.Tasks
	err = gconv.Struct(taskData, &taskEntity)
	if err != nil {
		return 0, gerror.Wrap(err, "repository: failed to convert task data to entity")
	}
	if taskEntity == nil {
		return 0, gerror.New("repository: converted task entity is nil")
	}

	taskId, err = r.taskDao.InsertTaskAndGetID(ctx, taskEntity) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return 0, err
	}
	return taskId, nil
}

// ListTasks retrieves a paginated list of tasks based on options.
func (r *taskRepository) ListTasks(ctx context.Context, options TaskQueryOptions) (list []*entity.Tasks, total int, err error) {
	condition := r.buildTaskCondition(options)
	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		orderBy = options.SortField + " " + options.SortOrder
	} else {
		orderBy = "id DESC" // Default sort
	}

	list, total, err = r.taskDao.ListTasks(ctx, condition, options.Page, options.Limit, orderBy) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return nil, 0, err
	}
	return list, total, nil
}

// FindAllTasks retrieves all tasks based on options.
func (r *taskRepository) FindAllTasks(ctx context.Context, options TaskQueryOptions) ([]*entity.Tasks, error) {
	condition := r.buildTaskCondition(options)
	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		orderBy = options.SortField + " " + options.SortOrder
	} else {
		orderBy = "id DESC" // Default sort
	}

	// Call ListTasks without pagination
	list, _, err := r.taskDao.ListTasks(ctx, condition, 0, 0, orderBy) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return nil, err
	}
	return list, nil
}

// FindTaskByID retrieves a single task by its ID.
func (r *taskRepository) FindTaskByID(ctx context.Context, taskId int64) (*entity.Tasks, error) {
	task, err := r.taskDao.FindTaskByID(ctx, taskId) // Use new DAO method
	if err != nil {
		// DAO handles wrapping, just return
		return nil, err
	}
	// DAO FindTaskByID should return nil, nil if not found
	return task, nil
}

// UpdateTask updates specific fields of a task record identified by its ID.
func (r *taskRepository) UpdateTask(ctx context.Context, taskId int64, data g.Map) error {
	if len(data) == 0 {
		return gerror.New("repository: update data cannot be empty for task")
	}
	// Remove potentially harmful fields from direct update map
	delete(data, "id")
	delete(data, "task_id") // Assuming task_id is the UUID string and shouldn't be updated this way

	// Convert g.Map to map[string]interface{} for DAO method
	updateData := map[string]interface{}(data)

	err := r.taskDao.UpdateTask(ctx, taskId, updateData) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return err
	}
	return nil
}

// UpdateTaskStatus updates the status of a task identified by its ID.
func (r *taskRepository) UpdateTaskStatus(ctx context.Context, taskId int64, status string) error {
	// Use the generic UpdateTask method
	return r.UpdateTask(ctx, taskId, g.Map{"task_status": status})
}

// Note: isGdbNoRowsError helper function is assumed to be in repository_utils.go
// If not, uncomment and include it here or ensure repository_utils.go is created.
/*
// Helper function to check for GoFrame's wrapped ErrNoRows
func isGdbNoRowsError(err error) bool {
	if err == nil { return false }
	if errors.Is(err, sql.ErrNoRows) { return true }
	// Add specific checks for gdb errors if needed
	return false
}
*/
