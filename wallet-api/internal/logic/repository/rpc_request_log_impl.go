package repository

import (
	"context"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	// "github.com/gogf/gf/v2/errors/gerror" // Removed unused import

	"wallet-api/internal/codes" // Import codes package
)

// rpcRequestLogRepository implements IRpcRequestLogRepository interface.
type rpcRequestLogRepository struct {
	rpcLogDao dao.IRpcRequestLogsDao // Dependency on DAO interface
}

// NewRpcRequestLogRepository creates and returns a new instance of IRpcRequestLogRepository.
func NewRpcRequestLogRepository(dao dao.IRpcRequestLogsDao) IRpcRequestLogRepository {
	return &rpcRequestLogRepository{
		rpcLogDao: dao,
	}
}

// AddLog adds a new RPC request log entry.
func (r *rpcRequestLogRepository) AddLog(ctx context.Context, logData *entity.RpcRequestLogs) error {
	if logData == nil {
		return codes.NewError(codes.CodeRepositoryNilData)
	}

	// Directly pass the entity struct to the new DAO method
	err := r.rpcLogDao.InsertLog(ctx, logData) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return err
	}
	return nil
}

// ListLogs - Placeholder implementation if needed later
// func (r *rpcRequestLogRepository) ListLogs(ctx context.Context, options RpcLogQueryOptions) (list []*entity.RpcRequestLogs, total int, err error) {
// 	// Implementation would involve building query based on options and using DAO methods
// 	return nil, 0, codes.NewError(codes.CodeRepositoryNotImplemented)
// }
