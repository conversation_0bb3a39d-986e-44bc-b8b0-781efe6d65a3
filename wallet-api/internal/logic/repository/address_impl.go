package repository

import (
	"context"
	"math"
	"wallet-api/internal/consts" // Needed for address types
	"wallet-api/internal/dao"    // Import DAO interface package
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv" // Needed for map conversion
)

// addressRepository implements IAddressRepository interface.
type addressRepository struct {
	addressDao dao.IAddressDao // Dependency on DAO interface
}

// NewAddressRepository creates and returns a new instance of IAddressRepository.
func NewAddressRepository(dao dao.IAddressDao) IAddressRepository {
	return &addressRepository{
		addressDao: dao,
	}
}

// BatchCreateAddresses creates multiple address records.
func (r *addressRepository) BatchCreateAddresses(ctx context.Context, addressesMap []g.Map) error {
	if len(addressesMap) == 0 {
		return nil // Nothing to insert
	}
	// Convert []g.Map to []*entity.Address
	addressesEntity := make([]*entity.Address, 0, len(addressesMap))
	err := gconv.Structs(addressesMap, &addressesEntity)
	if err != nil {
		return gerror.Wrap(err, "repository: failed to convert address map to struct")
	}

	err = r.addressDao.BatchInsertAddresses(ctx, addressesEntity) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return err
	}
	return nil
}

// ListAddresses retrieves a paginated list of addresses based on options.
func (r *addressRepository) ListAddresses(ctx context.Context, options AddressQueryOptions) (list []*entity.Address, total int, err error) {
	condition := g.Map{}
	// Apply filters
	if options.Address != "" {
		// Use map key for LIKE condition if DAO supports it, otherwise needs specific DAO method
		condition["address like ?"] = "%" + options.Address + "%"
	}
	if options.Type != "" {
		condition["type"] = options.Type
	}
	if options.HasBalance {
		// Complex OR condition - Removed for now, needs refinement (e.g., specific DAO method or post-filtering)
		g.Log().Warning(ctx, "ListAddresses: HasBalance filter currently not applied due to DAO interface change. Needs refinement.")
		// Example post-filtering (less efficient, total count inaccurate):
		// defer func() {
		// 	if err == nil && options.HasBalance {
		// 		filteredList := make([]*entity.Address, 0)
		// 		for _, addr := range list {
		// 			if addr.ChainCoinBalance > 0 || addr.ChainUsdtBalance > 0 {
		// 				filteredList = append(filteredList, addr)
		// 			}
		// 		}
		// 		list = filteredList
		// 	}
		// }()
	}
	if options.BindStatus != nil {
		condition["bind_status"] = *options.BindStatus
	}

	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		orderBy = options.SortField + " " + options.SortOrder
	} else {
		orderBy = "id DESC" // Default sort
	}

	list, total, err = r.addressDao.ListAddresses(ctx, condition, options.Page, options.Limit, orderBy) // Use new DAO method
	if err != nil {
		// Error wrapping is handled by the DAO layer now, just return
		return nil, 0, err
	}

	return list, total, nil
}

// GetAddressStatistics retrieves aggregated statistics about addresses.
func (r *addressRepository) GetAddressStatistics(ctx context.Context) (*AddressStatistic, error) {
	var stats AddressStatistic
	var err error

	// Get total count
	stats.TotalSize, err = r.addressDao.CountAddresses(ctx, nil) // Use new DAO method
	if err != nil {
		return nil, err // Error wrapping done in DAO
	}

	// Get active count (assuming status=2 means active)
	stats.ActiveAddress, err = r.addressDao.CountAddresses(ctx, g.Map{"status": 2}) // Use new DAO method
	if err != nil {
		return nil, err
	}

	// Get ETH balances sum
	ethCoinSum, ethUsdtSum, err := r.addressDao.SumBalances(ctx, g.Map{"type": consts.NetworkTypeETH}) // Use new DAO method
	if err != nil {
		return nil, err
	}
	stats.EthTotalBalance = ethCoinSum
	stats.Erc20UsdtTotalBalance = ethUsdtSum

	// Get TRX balances sum
	trxCoinSum, trxUsdtSum, err := r.addressDao.SumBalances(ctx, g.Map{"type": consts.NetworkTypeTRX}) // Use new DAO method
	if err != nil {
		return nil, err
	}
	stats.TrxTotalBalance = trxCoinSum
	stats.Trc20UsdtTotalBalance = trxUsdtSum

	// Get address counts by type
	stats.EthAddressCount, err = r.addressDao.CountAddresses(ctx, g.Map{"type": consts.NetworkTypeETH}) // Use new DAO method
	if err != nil {
		return nil, err
	}
	stats.TrxAddressCount, err = r.addressDao.CountAddresses(ctx, g.Map{"type": consts.NetworkTypeTRX}) // Use new DAO method
	if err != nil {
		return nil, err
	}

	return &stats, nil
}

// FindAddresses retrieves addresses based on filter criteria.
func (r *addressRepository) FindAddresses(ctx context.Context, filter g.Map) ([]*entity.Address, error) {
	// Use the more specific ListAddresses DAO method without pagination/ordering
	list, _, err := r.addressDao.ListAddresses(ctx, filter, 0, 0, "") // Use new DAO method
	if err != nil {
		return nil, err // Error wrapping done in DAO
	}
	return list, nil
}

// UpdateAddresses updates multiple addresses based on filter criteria.
func (r *addressRepository) UpdateAddresses(ctx context.Context, data g.Map, filter g.Map) error {
	if len(data) == 0 {
		return gerror.New("repository: update data cannot be empty")
	}
	if len(filter) == 0 {
		// Safety check: prevent updating all rows without a filter
		return gerror.New("repository: update filter cannot be empty for safety")
	}
	err := r.addressDao.UpdateAddresses(ctx, data, filter) // Use new DAO method
	if err != nil {
		return err // Error wrapping done in DAO
	}
	return nil
}

// FindByAddress retrieves a single address by its address string and type.
func (r *addressRepository) FindByAddress(ctx context.Context, address string, addressType string) (*entity.Address, error) {
	addr, err := r.addressDao.FindByAddress(ctx, address, addressType) // Use new DAO method
	if err != nil {
		// DAO handles wrapping, just return
		return nil, err
	}
	// DAO FindByAddress should return nil, nil if not found
	return addr, nil
}

// UpdateAddress updates a single address record identified by its address string and type.
// Note: Added addressType parameter to align with DAO method. Callers need to provide it.
func (r *addressRepository) UpdateAddress(ctx context.Context, address string, data g.Map) error {
	if len(data) == 0 {
		return gerror.New("repository: update data cannot be empty")
	}

	err := r.addressDao.UpdateAddress(ctx, address, data) // Use new DAO method
	if err != nil {
		// DAO handles wrapping, just return
		return err
	}
	return nil
}

// GetMaxDerivationPath retrieves the maximum derivation path index used for a given address type.
func (r *addressRepository) GetMaxDerivationPath(ctx context.Context, addressType string) (int, error) {
	maxPathVal, err := r.addressDao.GetMaxPath(ctx, addressType) // Use new DAO method
	if err != nil {
		// DAO handles wrapping, just return
		return 0, err
	}
	// Ensure non-negative result (DAO might return 0 if no records found)
	return int(math.Max(0, float64(maxPathVal))), nil
}

// isGdbNoRowsError is assumed to be defined in repository_utils.go
// func isGdbNoRowsError(err error) bool { ... }

// FindByMerchantAndAlias retrieves a single address by merchant ID, alias, and network type.
func (r *addressRepository) FindByMerchantAndAlias(ctx context.Context, merchantId int, alias string, networkType string) (*entity.Address, error) {
	condition := g.Map{
		dao.Address.Columns().MerchantId: merchantId,
		dao.Address.Columns().Alias:      alias,
		dao.Address.Columns().Type:       networkType,
	}
	// Assuming the DAO interface has a method like FindOneByCondition
	addr, err := r.addressDao.FindOneByCondition(ctx, condition)
	if err != nil {
		// DAO should handle specific errors like ErrNotFound and wrapping
		return nil, err
	}
	return addr, nil
}

// CreateAddress creates a single address record.
func (r *addressRepository) CreateAddress(ctx context.Context, addressData g.Map) error {
	// Convert g.Map to *entity.Address
	var addressEntity *entity.Address
	err := gconv.Struct(addressData, &addressEntity)
	if err != nil {
		return gerror.Wrap(err, "repository: failed to convert address map to struct for creation")
	}

	// Assuming the DAO interface has a method like InsertAddress
	err = r.addressDao.InsertAddress(ctx, addressEntity)
	if err != nil {
		// DAO should handle specific errors like duplicate keys and wrapping
		return err
	}
	return nil
}
