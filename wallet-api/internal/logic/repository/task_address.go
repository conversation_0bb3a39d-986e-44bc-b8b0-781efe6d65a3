package repository

import (
	"context"
	"wallet-api/internal/model/entity" // Import entity

	"github.com/gogf/gf/v2/frame/g"
)

// TaskAddressQueryOptions holds options for querying task addresses.
type TaskAddressQueryOptions struct {
	Page      int
	Limit     int
	TaskID    int64  // Filter by Task ID
	Address   string // Filter by sender or receiver address (like)
	Status    string // Filter by status
	SortField string
	SortOrder string
}

// TaskAddressCounts holds counts of task addresses by status for a given task ID.
type TaskAddressCounts struct {
	SuccessCount  int
	FailedCount   int
	PendingCount  int // Includes Pending and Processing
	CanceledCount int
}

// ITaskAddressRepository defines the interface for task_address business data operations.
type ITaskAddressRepository interface {
	// BatchCreateTaskAddresses creates multiple task address records.
	BatchCreateTaskAddresses(ctx context.Context, taskAddresses []g.Map) error

	// ListTaskAddresses retrieves a paginated list of task addresses based on options.
	ListTaskAddresses(ctx context.Context, options TaskAddressQueryOptions) (list []*entity.TaskAddress, total int, err error)

	// FindAllTaskAddresses retrieves all task addresses based on options.
	FindAllTaskAddresses(ctx context.Context, options TaskAddressQueryOptions) ([]*entity.TaskAddress, error)

	// GetTaskAddressCounts retrieves the counts of task addresses by status for a specific task ID.
	GetTaskAddressCounts(ctx context.Context, taskId int64) (*TaskAddressCounts, error)

	// FindByTaskID retrieves all task addresses associated with a specific task ID.
	FindByTaskID(ctx context.Context, taskId int64) ([]*entity.TaskAddress, error)

	// UpdateTaskAddress updates specific fields of a task address record identified by its ID.
	UpdateTaskAddress(ctx context.Context, taskAddressId int64, data g.Map) error

	// UpdateTaskAddressStatus updates the status, transaction hash, fee, and fail reason of a task address record identified by its ID.
	UpdateTaskAddressStatus(ctx context.Context, taskAddressId int64, status string, txHash string, fee float64, failReason string) error
}
