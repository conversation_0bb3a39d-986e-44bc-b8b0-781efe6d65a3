package repository

import (
	"context"
	"time"                             // Import time for optional query options
	"wallet-api/internal/model/entity" // Import entity
)

// IOperationLogRepository defines the interface for operation log business logic.
type IOperationLogRepository interface {
	// AddLog adds a new operation log entry.
	// It accepts the full entity struct for flexibility.
	AddLog(ctx context.Context, logData *entity.OperationLogs) error

	// ListLogs retrieves a paginated list of operation logs (optional, add if needed).
	// ListLogs(ctx context.Context, options LogQueryOptions) (list []*entity.OperationLogs, total int, err error)
}

// LogQueryOptions (optional, define if ListLogs is added)
type LogQueryOptions struct {
	Page      int
	Limit     int
	LogType   string
	LogStatus string
	DateStart *time.Time
	DateEnd   *time.Time
	SortField string
	SortOrder string
}
