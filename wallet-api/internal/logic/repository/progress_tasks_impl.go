package repository

import (
	"context"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// progressTasksRepository implements IProgressTasksRepository interface.
type progressTasksRepository struct {
	// No need to store a reference to the DAO
}

// NewProgressTasksRepository creates and returns a new instance of IProgressTasksRepository.
func NewProgressTasksRepository() IProgressTasksRepository {
	return &progressTasksRepository{}
}

// CreateProgressTask creates a new progress task record.
func (r *progressTasksRepository) CreateProgressTask(ctx context.Context, taskData g.Map) error {
	// Convert g.Map to entity.ProgressTasks
	var taskEntity *entity.ProgressTasks
	err := gconv.Struct(taskData, &taskEntity)
	if err != nil {
		return gerror.Wrap(err, "repository: failed to convert progress task data to entity")
	}
	if taskEntity == nil {
		return gerror.New("repository: converted progress task entity is nil")
	}

	// Insert the record using the model directly
	_, err = g.Model("progress_tasks").Ctx(ctx).Data(taskEntity).Insert()
	if err != nil {
		return gerror.Wrap(err, "repository: failed to insert progress task")
	}
	return nil
}

// GetProgressTask retrieves a progress task by its ID.
func (r *progressTasksRepository) GetProgressTask(ctx context.Context, taskId string) (*entity.ProgressTasks, error) {
	var task *entity.ProgressTasks
	err := g.Model("progress_tasks").Ctx(ctx).Where("task_id", taskId).Scan(&task)
	if err != nil {
		return nil, gerror.Wrap(err, "repository: failed to get progress task")
	}
	return task, nil
}

// UpdateProgressTask updates a progress task record.
func (r *progressTasksRepository) UpdateProgressTask(ctx context.Context, taskId string, data g.Map) error {
	if len(data) == 0 {
		return gerror.New("repository: update data cannot be empty for progress task")
	}

	// Update the record using the model directly
	_, err := g.Model("progress_tasks").Ctx(ctx).Where("task_id", taskId).Data(data).Update()
	if err != nil {
		return gerror.Wrap(err, "repository: failed to update progress task")
	}
	return nil
}
