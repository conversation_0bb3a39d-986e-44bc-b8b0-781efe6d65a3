package repository

import (
	"context"
)

// ISystemConfigRepository defines the interface for system configuration business data operations.
type ISystemConfigRepository interface {
	// GetConfigValue retrieves the configuration value for a given key.
	// Returns an empty string and nil error if the key is not found.
	GetConfigValue(ctx context.Context, key string) (value string, err error)

	// SetConfigValue sets (inserts or updates) the configuration value for a given key.
	SetConfigValue(ctx context.Context, key string, value string) error

	// DeleteConfig deletes a configuration entry by its key.
	DeleteConfig(ctx context.Context, key string) error
}
