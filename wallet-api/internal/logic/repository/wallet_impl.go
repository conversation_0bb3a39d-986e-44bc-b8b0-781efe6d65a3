package repository

import (
	"context"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime" // Needed for UpdateLastUnlockTime
)

// walletRepository implements IWalletRepository interface.
type walletRepository struct {
	walletDao dao.IWalletsDao
}

// NewWalletRepository creates and returns a new instance of IWalletRepository.
func NewWalletRepository(dao dao.IWalletsDao) IWalletRepository {
	return &walletRepository{
		walletDao: dao,
	}
}

// IsWalletExist checks if a wallet record already exists.
func (r *walletRepository) IsWalletExist(ctx context.Context) (bool, error) {
	wallet, err := r.walletDao.GetWallet(ctx) // Use new DAO method
	if err != nil {
		// DAO should handle specific not found errors, repo just checks general error
		// Assuming DAO returns nil, nil for not found
		// We only wrap unexpected errors
		// Note: isGdbNoRowsError might be needed if DAO error handling changes
		// if errors.Is(err, sql.ErrNoRows) || isGdbNoRowsError(err) {
		// 	return false, nil
		// }
		// If DAO returns error for not found, we need to check it here.
		// For now, assume DAO returns nil, nil for not found.
		// If err is not nil, it's an unexpected error.
		if err != nil {
			return false, gerror.Wrap(err, "repository: failed to check wallet existence")
		}
		// If err is nil but wallet is also nil (DAO indicated not found)
		if wallet == nil {
			return false, nil
		}
	}
	// If wallet is not nil, it exists
	return wallet != nil, nil
}

// CreateWallet creates a new wallet record with essential initial data.
func (r *walletRepository) CreateWallet(ctx context.Context,
	encryptedMnemonic string,
	passwordHash string, // This is the bcrypt hash
	encryptedGoogleCode string,
	mnemonicSalt []byte,
	mnemonicIterations int,
	googleSecretSalt []byte,
	googleSecretIterations int,
) error {
	// Check if wallet already exists
	exists, err := r.IsWalletExist(ctx)
	if err != nil {
		return gerror.Wrap(err, "repository: failed to check wallet existence before creation")
	}
	if exists {
		return gerror.New("repository: wallet already exists")
	}

	// Create wallet entity
	walletEntity := &entity.Wallets{
		Mnemonic:     encryptedMnemonic,
		PasswordHash: passwordHash, // Store bcrypt hash in PasswordHash field
		// Password:               "", // Explicitly set Password to empty or it will be handled by DB default if nullable
		GoogleCode:             encryptedGoogleCode,
		MnemonicSalt:           mnemonicSalt,
		MnemonicIterations:     mnemonicIterations,
		GoogleSecretSalt:       googleSecretSalt,
		GoogleSecretIterations: googleSecretIterations,
		// CreatedAt and UpdatedAt are typically handled by the database (e.g., DEFAULT CURRENT_TIMESTAMP)
		// or by gmeta.Meta if embedded in the entity.
		// Removed explicit setting:
		// CreatedAt:  gtime.Now(),
		// UpdatedAt:  gtime.Now(),
		// Initialize other fields if necessary, e.g., ID might be auto-increment or needs specific handling
		// If ID is auto-increment, it should not be set here.
		// If your table expects a specific ID (like 1 for a single wallet system), set it.
		// For now, assuming ID is auto-increment or handled by DB default.
		// Id: 1, // Example if ID needs to be explicitly set to 1 for a single-wallet system
	}

	// Use InsertWallet to create a new record
	// Ensure r.walletDao has an InsertWallet method matching this signature.
	// This assumes the dao.Wallets (which r.walletDao is an instance of) has InsertWallet.
	err = r.walletDao.InsertWallet(ctx, walletEntity)
	if err != nil {
		// Error wrapping should ideally be consistent (either here or in DAO)
		return gerror.Wrap(err, "repository: failed to insert wallet")
	}
	return nil
}

// GetWallet retrieves the single wallet record.
func (r *walletRepository) GetWallet(ctx context.Context) (*entity.Wallets, error) {
	wallet, err := r.walletDao.GetWallet(ctx) // Use new DAO method
	if err != nil {
		// DAO handles wrapping, just return
		return nil, err
	}
	// DAO GetWallet should return nil, nil if not found
	return wallet, nil
}

// DeleteWallet - Placeholder, uncomment if needed.
// func (r *walletRepository) DeleteWallet(ctx context.Context) error {
// 	// Needs corresponding DAO method
// 	// err := r.walletDao.DeleteWallet(ctx)
// 	// if err != nil { return err }
// 	// return nil
// 	return gerror.New("repository: DeleteWallet not implemented")
// }

// UpdateWalletFields updates specific fields of the wallet record.
func (r *walletRepository) UpdateWalletFields(ctx context.Context, data g.Map) error {
	err := r.walletDao.UpdateWallet(ctx, data) // Use new DAO method
	if err != nil {
		// DAO handles wrapping
		return err
	}
	return nil
}

// UpdateLastUnlockTime updates the last successful unlock timestamp.
func (r *walletRepository) UpdateLastUnlockTime(ctx context.Context, t *gtime.Time) error {
	// Create a data map with the field to update
	data := g.Map{
		"last_unlock_at": t,
		"updated_at":     gtime.Now(),
	}

	// Use the general UpdateWallet method available in the interface
	err := r.walletDao.UpdateWallet(ctx, data)
	if err != nil {
		// DAO handles wrapping
		return err
	}
	return nil
}

// IncrementUnlockErrorCount increments the unlock error counter.
func (r *walletRepository) IncrementUnlockErrorCount(ctx context.Context) error {
	// First get the current wallet to obtain the current error count
	wallet, err := r.GetWallet(ctx)
	if err != nil {
		return gerror.Wrap(err, "repository: failed to get wallet for increment unlock error count")
	}
	if wallet == nil {
		return gerror.New("repository: wallet not found when incrementing unlock error count")
	}

	// Create data map with incremented error count
	data := g.Map{
		"unlock_error_count": wallet.UnlockErrorCount + 1,
		"updated_at":         gtime.Now(),
	}

	// Use UpdateWallet that exists in the interface
	err = r.walletDao.UpdateWallet(ctx, data)
	if err != nil {
		// DAO handles wrapping
		return err
	}
	return nil
}

// ResetUnlockErrorCount resets the unlock error counter to zero.
func (r *walletRepository) ResetUnlockErrorCount(ctx context.Context) error {
	// Create data map to reset error count to zero
	data := g.Map{
		"unlock_error_count": 0,
		"updated_at":         gtime.Now(),
	}

	// Use UpdateWallet that exists in the interface
	err := r.walletDao.UpdateWallet(ctx, data)
	if err != nil {
		// DAO handles wrapping
		return err
	}
	return nil
}

// UpdatePasswordHash updates the stored password hash (bcrypt).
func (r *walletRepository) UpdatePasswordHash(ctx context.Context, newPasswordHash string) error {
	// Create data map with the updated password hash
	data := g.Map{
		// Use Columns() for type safety, assuming dao.Wallets is accessible and has Columns()
		// If dao.Wallets is not directly accessible here, use string literal "password_hash"
		// For now, using string literal as dao.Wallets might not be directly in scope for Columns()
		"password_hash": newPasswordHash, // Ensure this updates the correct column
		"updated_at":    gtime.Now(),
	}

	// Use the general UpdateWallet method available in the interface
	// This assumes UpdateWallet in DAO updates based on ID=1 or similar logic
	err := r.walletDao.UpdateWallet(ctx, data)
	if err != nil {
		// DAO handles wrapping
		return err
	}
	return nil
}

// UpdateGoogleSecret updates the Google Authenticator secret and status.
func (r *walletRepository) UpdateGoogleSecret(ctx context.Context, newEncryptedSecret string, enabled bool) error {
	// Create data map with the Google secret and enabled status
	data := g.Map{
		"google_code":    newEncryptedSecret,
		"google_enabled": enabled,
		"updated_at":     gtime.Now(),
	}

	// Use the general UpdateWallet method available in the interface
	err := r.walletDao.UpdateWallet(ctx, data)
	if err != nil {
		// DAO handles wrapping
		return err
	}
	return nil
}

// UpdatePasswordAndReencryptSensitiveData updates password and re-encrypts all sensitive data in a transaction.
func (r *walletRepository) UpdatePasswordAndReencryptSensitiveData(ctx context.Context, newPasswordHash string,
	newEncryptedMnemonic string, newMnemonicSalt []byte, newMnemonicIterations int,
	newEncryptedGoogleCode string, newGoogleSecretSalt []byte, newGoogleSecretIterations int,
	newEncryptedEthFeePrivateKey string, newEncryptedTrxFeePrivateKey string) error {

	// Use transaction to ensure atomicity
	err := r.walletDao.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// Prepare update data with all fields that need to be updated
		data := g.Map{
			"password_hash":            newPasswordHash,
			"mnemonic":                 newEncryptedMnemonic,
			"mnemonic_salt":            newMnemonicSalt,
			"mnemonic_iterations":      newMnemonicIterations,
			"google_code":              newEncryptedGoogleCode,
			"google_secret_salt":       newGoogleSecretSalt,
			"google_secret_iterations": newGoogleSecretIterations,
			"updated_at":               gtime.Now(),
		}

		// Only update fee private keys if they are not empty
		if newEncryptedEthFeePrivateKey != "" {
			data["eth_fee_private_key"] = newEncryptedEthFeePrivateKey
		}
		if newEncryptedTrxFeePrivateKey != "" {
			data["trx_fee_private_key"] = newEncryptedTrxFeePrivateKey
		}

		// Execute the update within the transaction
		// Note: We need to use the transaction context here
		// The DAO method should support transaction context
		_, err := tx.Model("wallets").Data(data).Update()
		if err != nil {
			return gerror.Wrap(err, "repository: failed to update wallet with re-encrypted data")
		}

		return nil
	})

	if err != nil {
		return gerror.Wrap(err, "repository: transaction failed for password and re-encryption update")
	}

	return nil
}

// Note: isGdbNoRowsError helper function is assumed to be in repository_utils.go
// func isGdbNoRowsError(err error) bool { ... }

// Removed duplicate PasswordAttemptInfo struct definition
