package repository

import (
	"context"
	"wallet-api/internal/model/entity" // Import entity

	"github.com/gogf/gf/v2/frame/g"
)

// AddressQueryOptions holds options for querying addresses.
type AddressQueryOptions struct {
	Page       int
	Limit      int
	Address    string // Filter by address (like)
	Type       string // Filter by type (ETH/TRON)
	HasBalance bool   // Filter for addresses with non-zero balance
	BindStatus *int   // Filter by bind status (pointer to allow filtering for 0)
	SortField  string
	SortOrder  string
}

// AddressStatistic holds aggregated address statistics.
type AddressStatistic struct {
	TotalSize             int
	ActiveAddress         int
	EthTotalBalance       float64
	Erc20UsdtTotalBalance float64
	TrxTotalBalance       float64
	Trc20UsdtTotalBalance float64
	EthAddressCount       int
	TrxAddressCount       int
}

// IAddressRepository defines the interface for address business data operations.
type IAddressRepository interface {
	// BatchCreateAddresses creates multiple address records.
	// Expects a slice of maps or structs containing address data.
	BatchCreateAddresses(ctx context.Context, addresses []g.Map) error

	// ListAddresses retrieves a paginated list of addresses based on options.
	// Returns the list of addresses, total count, and error.
	ListAddresses(ctx context.Context, options AddressQueryOptions) (list []*entity.Address, total int, err error)

	// GetAddressStatistics retrieves aggregated statistics about addresses.
	GetAddressStatistics(ctx context.Context) (*AddressStatistic, error)

	// FindAddresses retrieves addresses based on filter criteria (e.g., for export).
	// Use g.Map for flexible filtering for now.
	FindAddresses(ctx context.Context, filter g.Map) ([]*entity.Address, error)

	// UpdateAddresses updates multiple addresses based on filter criteria.
	// Use g.Map for flexible filtering and data.
	UpdateAddresses(ctx context.Context, data g.Map, filter g.Map) error

	// FindByAddress retrieves a single address by its address string and type.
	// Returns nil, nil if not found.
	FindByAddress(ctx context.Context, address string, addressType string) (*entity.Address, error)

	// UpdateAddress updates a single address record identified by its address string.
	UpdateAddress(ctx context.Context, address string, data g.Map) error

	// GetMaxDerivationPath retrieves the maximum derivation path index used for a given address type.
	GetMaxDerivationPath(ctx context.Context, addressType string) (int, error)

	// FindByMerchantAndAlias retrieves a single address by merchant ID, alias, and network type.
	// Returns nil, nil if not found.
	FindByMerchantAndAlias(ctx context.Context, merchantId int, alias string, networkType string) (*entity.Address, error)

	// CreateAddress creates a single address record.
	// Expects a map containing address data.
	CreateAddress(ctx context.Context, addressData g.Map) error
}
