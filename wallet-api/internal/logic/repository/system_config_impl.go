package repository

import (
	"context"
	"wallet-api/internal/dao"
)

// systemConfigRepository implements ISystemConfigRepository interface.
type systemConfigRepository struct {
	configDao dao.ISystemConfigDao // Dependency on DAO interface
}

// NewSystemConfigRepository creates and returns a new instance of ISystemConfigRepository.
func NewSystemConfigRepository(dao dao.ISystemConfigDao) ISystemConfigRepository {
	return &systemConfigRepository{
		configDao: dao,
	}
}

// GetConfigValue retrieves the configuration value for a given key.
func (r *systemConfigRepository) GetConfigValue(ctx context.Context, key string) (value string, err error) {
	value, err = r.configDao.GetValueByKey(ctx, key) // Use new DAO method
	if err != nil {
		// DAO method should handle wrapping, just return the error
		// DAO method should return "", nil if key not found
		return "", err
	}
	return value, nil
}

// SetConfigValue sets (inserts or updates) the configuration value for a given key.
func (r *systemConfigRepository) SetConfigValue(ctx context.Context, key string, value string) error {
	err := r.configDao.SetValueByKey(ctx, key, value) // Use new DAO method (handles upsert)
	if err != nil {
		// DAO method should handle wrapping, just return the error
		return err
	}
	return nil
}

// DeleteConfig deletes a configuration entry by its key.
func (r *systemConfigRepository) DeleteConfig(ctx context.Context, key string) error {
	err := r.configDao.DeleteByKey(ctx, key) // Use new DAO method
	if err != nil {
		// DAO method should handle wrapping, just return the error
		return err
	}
	return nil
}

// Note: isGdbNoRowsError helper function is assumed to be in repository_utils.go
// func isGdbNoRowsError(err error) bool { ... }
