package repository

import (
	"context"
	"time"                             // For date range filtering
	"wallet-api/internal/model/entity" // Import entity
)

// TransactionQueryOptions holds options for querying transactions.
type TransactionQueryOptions struct {
	Page      int
	Limit     int
	Address   string     // Filter by sender or receiver address (like)
	Type      string     // Filter by transaction_type
	Chain     string     // Filter by chain (ETH/TRON)
	Status    string     // Filter by status
	Coin      string     // Filter by token_name
	DateStart *time.Time // Filter by start date (inclusive)
	DateEnd   *time.Time // Filter by end date (inclusive)
	SortField string
	SortOrder string
}

// TransactionStatistic holds aggregated transaction statistics.
type TransactionStatistic struct {
	DepositOrderCount        int
	WithdrawOrderCount       int
	MinerFeeOrderCount       int
	PendingConfirmationCount int
	TotalUsdtDepositAmount   float64
	TotalUsdtWithdrawAmount  float64
	TotalTrxDepositAmount    float64
	TotalTrxWithdrawAmount   float64
	TotalEthDepositAmount    float64
	TotalEthWithdrawAmount   float64
}

// ITransactionRepository defines the interface for transaction business data operations.
type ITransactionRepository interface {
	// ListTransactions retrieves a paginated list of transactions based on options.
	// Returns the list of transactions, total count, and error.
	ListTransactions(ctx context.Context, options TransactionQueryOptions) (list []*entity.Transactions, total int, err error)

	// FindAllTransactions retrieves all transactions based on options (for export).
	FindAllTransactions(ctx context.Context, options TransactionQueryOptions) ([]*entity.Transactions, error)

	// GetTransactionStatistics retrieves aggregated statistics about transactions.
	GetTransactionStatistics(ctx context.Context) (*TransactionStatistic, error)

	// AddTransaction adds a new transaction record.
	// Consider accepting g.Map or specific fields instead of the full entity if needed.
	AddTransaction(ctx context.Context, txData *entity.Transactions) error

	// FindByHash retrieves a transaction by its hash.
	// Returns nil, nil if not found.
	FindByHash(ctx context.Context, txHash string) (*entity.Transactions, error)

	// UpdateTransactionStatus updates the status and potentially other fields of a transaction.
	// UpdateTransactionStatus(ctx context.Context, txHash string, status string, confirmations int, blockNumber int64, blockHash string, /* other fields */) error
}
