package repository

import (
	"context"
	"time"                             // Import time for optional query options
	"wallet-api/internal/model/entity" // Import entity
)

// IRpcRequestLogRepository defines the interface for RPC request log business logic.
type IRpcRequestLogRepository interface {
	// AddLog adds a new RPC request log entry.
	AddLog(ctx context.Context, logData *entity.RpcRequestLogs) error

	// ListLogs retrieves a paginated list of RPC request logs (optional, add if needed).
	// ListLogs(ctx context.Context, options RpcLogQueryOptions) (list []*entity.RpcRequestLogs, total int, err error)
}

// RpcLogQueryOptions (optional, define if ListL<PERSON><PERSON> is added)
type RpcLogQueryOptions struct {
	Page          int
	Limit         int
	RequestType   string
	RequestMethod string
	Address       string
	Coin          string
	RequestStatus string
	DateStart     *time.Time
	DateEnd       *time.Time
	SortField     string
	SortOrder     string
}
