package repository

import (
	"context"
	"time"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity" // Import entity for scanning

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

const (
	// Assuming the single record always has ID=1, based on boot_check.go logic
	passwordAttemptRecordID int64 = 1
)

// passwordAttemptRepository implements IPasswordAttemptRepository interface.
type passwordAttemptRepository struct {
	attemptDao dao.IPasswordAttemptsDao // Dependency on DAO interface
}

// NewPasswordAttemptRepository creates and returns a new instance of IPasswordAttemptRepository.
func NewPasswordAttemptRepository(dao dao.IPasswordAttemptsDao) IPasswordAttemptRepository {
	return &passwordAttemptRepository{
		attemptDao: dao,
	}
}

// ensureRecordExists checks if the attempt record exists and creates it if not.
// Returns the current attempt count (0 if just created) and error.
func (r *passwordAttemptRepository) ensureRecordExists(ctx context.Context) (currentCount int, err error) {
	count, err := r.attemptDao.CountByID(ctx, passwordAttemptRecordID) // Use new DAO method
	if err != nil {
		// DAO method handles wrapping
		return 0, err
	}

	if count == 0 {
		// Record does not exist, create it with default values
		attemptEntity := &entity.PasswordAttempts{
			Id:           int(passwordAttemptRecordID), // Convert int64 constant to int
			AttemptCount: 0,
			LastAttempt:  gtime.Now(),
		}
		err = r.attemptDao.Insert(ctx, attemptEntity) // Use new DAO method
		if err != nil {
			// Handle potential race condition if another process created it concurrently
			// A simple retry or more robust locking might be needed in high-concurrency scenarios.
			// For now, wrap the error.
			return 0, gerror.Wrap(err, "repository: failed to create initial password attempt record")
		}
		return 0, nil // Just created, count is 0
	}
	// Record exists, indicate count needs separate fetching if required by caller logic
	// Returning the actual count might be more useful if DAO's CountByID is reliable
	return count, nil // Return the count found
}

// GetAttempts retrieves the current attempt count and last attempt time.
func (r *passwordAttemptRepository) GetAttempts(ctx context.Context) (*PasswordAttemptInfo, error) {
	// Ensure the record exists first - this also returns the count now
	_, err := r.ensureRecordExists(ctx)
	if err != nil {
		return nil, err // Error during check/creation
	}

	attemptEntity, err := r.attemptDao.FindByID(ctx, passwordAttemptRecordID) // Use new DAO method
	if err != nil {
		// DAO handles wrapping
		return nil, err
	}
	if attemptEntity == nil {
		// This case should ideally be handled by ensureRecordExists, but handle defensively
		g.Log().Warning(ctx, "Password attempt record not found after ensuring existence, potential issue.")
		// Return default values
		return &PasswordAttemptInfo{AttemptCount: 0, LastAttempt: time.Time{}}, nil
	}

	// Convert gtime.Time to time.Time if necessary
	var lastAttemptTime time.Time
	if attemptEntity.LastAttempt != nil {
		lastAttemptTime = attemptEntity.LastAttempt.Time
	}

	return &PasswordAttemptInfo{
		AttemptCount: attemptEntity.AttemptCount,
		LastAttempt:  lastAttemptTime,
	}, nil
}

// IncrementAttempts increments the attempt count and updates the last attempt time.
func (r *passwordAttemptRepository) IncrementAttempts(ctx context.Context) (newAttemptCount int, err error) {
	// Ensure record exists first and get current count
	currentCount, err := r.ensureRecordExists(ctx)
	if err != nil {
		return -1, err
	}

	newAttemptCount = currentCount + 1
	now := gtime.Now()

	updateData := map[string]interface{}{
		"attempt_count": newAttemptCount,
		"last_attempt":  now,
	}

	err = r.attemptDao.UpdateByID(ctx, passwordAttemptRecordID, updateData) // Use new DAO method

	if err != nil {
		// DAO handles wrapping
		return -1, err
	}

	return newAttemptCount, nil
}

// ResetAttempts resets the attempt count to 0 and updates the last attempt time.
func (r *passwordAttemptRepository) ResetAttempts(ctx context.Context) error {
	// Ensure record exists first (optional, update might handle non-existent row depending on DB)
	_, err := r.ensureRecordExists(ctx)
	if err != nil {
		// Log warning but proceed, maybe update will succeed or fail gracefully
		g.Log().Warningf(ctx, "Failed to ensure password attempt record exists before reset: %v", err)
	}

	now := gtime.Now()
	updateData := map[string]interface{}{
		"attempt_count": 0,
		"last_attempt":  now,
	}
	err = r.attemptDao.UpdateByID(ctx, passwordAttemptRecordID, updateData) // Use new DAO method

	if err != nil {
		// DAO handles wrapping
		return err
	}
	return nil
}

// Note: isGdbNoRowsError helper function is assumed to be in repository_utils.go
// func isGdbNoRowsError(err error) bool { ... }
