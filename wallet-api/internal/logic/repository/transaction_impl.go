package repository

import (
	"context"
	"wallet-api/internal/consts" // Needed for statistic calculation logic
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g" // Needed for g.Map
	// "wallet-api/internal/codes" // Removed unused import
)

// transactionRepository implements ITransactionRepository interface.
type transactionRepository struct {
	txDao dao.ITransactionsDao // Dependency on DAO interface
}

// NewTransactionRepository creates and returns a new instance of ITransactionRepository.
func NewTransactionRepository(dao dao.ITransactionsDao) ITransactionRepository {
	return &transactionRepository{
		txDao: dao,
	}
}

// buildTransactionCondition converts query options to a map condition for DAO methods.
func (r *transactionRepository) buildTransactionCondition(options TransactionQueryOptions) map[string]interface{} {
	condition := g.Map{}

	if options.Address != "" {
		// DAO needs to handle OR condition if required, or use separate queries
		// For simplicity, let's assume DAO's List/Count can handle complex Where conditions if needed
		// or we adjust the query logic here based on DAO capabilities.
		// Using a simple AND for now, might need adjustment.
		// A better approach might be separate fields for sender/receiver in options.
		condition["sender_address = ? OR receiver_address = ?"] = []interface{}{options.Address, options.Address} // Example for OR
	}
	if options.Type != "" {
		condition["transaction_type"] = options.Type
	}
	if options.Chain != "" {
		condition["chain"] = options.Chain
	}
	if options.Status != "" {
		condition["status"] = options.Status // Assuming 'status' is the correct column name
	}
	if options.Coin != "" {
		// Assuming 'token_name' or 'token_symbol' stores the coin/token identifier
		condition["token_name"] = options.Coin // Adjust column name if necessary
	}
	if options.DateStart != nil && options.DateEnd != nil {
		condition["create_at >= ? AND create_at <= ?"] = []interface{}{options.DateStart, options.DateEnd} // Adjust column name if necessary
	} else if options.DateStart != nil {
		condition["create_at >= ?"] = options.DateStart
	} else if options.DateEnd != nil {
		condition["create_at <= ?"] = options.DateEnd
	}

	return condition
}

// ListTransactions retrieves a paginated list of transactions based on options.
func (r *transactionRepository) ListTransactions(ctx context.Context, options TransactionQueryOptions) (list []*entity.Transactions, total int, err error) {
	condition := r.buildTransactionCondition(options)
	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		orderBy = options.SortField + " " + options.SortOrder
	} else {
		orderBy = "id DESC" // Default sort
	}

	list, total, err = r.txDao.ListTransactions(ctx, condition, options.Page, options.Limit, orderBy) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return nil, 0, err
	}
	return list, total, nil
}

// FindAllTransactions retrieves all transactions based on options (for export).
func (r *transactionRepository) FindAllTransactions(ctx context.Context, options TransactionQueryOptions) ([]*entity.Transactions, error) {
	condition := r.buildTransactionCondition(options)
	orderBy := ""
	if options.SortField != "" && options.SortOrder != "" {
		orderBy = options.SortField + " " + options.SortOrder
	} else {
		orderBy = "id DESC" // Default sort
	}

	// Call ListTransactions without pagination
	list, _, err := r.txDao.ListTransactions(ctx, condition, 0, 0, orderBy) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return nil, err
	}
	return list, nil
}

// GetTransactionStatistics retrieves aggregated statistics about transactions.
func (r *transactionRepository) GetTransactionStatistics(ctx context.Context) (*TransactionStatistic, error) {
	var stats TransactionStatistic
	var err error

	// Define column names (replace with Columns struct if available)
	txTypeCol := "transaction_type"
	statusCol := "status"
	tokenNameCol := "token_name"
	// amountCol := "amount" // Used in SumAmount DAO method

	stats.DepositOrderCount, err = r.txDao.CountTransactions(ctx, g.Map{txTypeCol: consts.TransactionTypeDeposit})
	if err != nil {
		return nil, err
	}

	stats.WithdrawOrderCount, err = r.txDao.CountTransactions(ctx, g.Map{txTypeCol: consts.TransactionTypeWithdraw})
	if err != nil {
		return nil, err
	}

	stats.MinerFeeOrderCount, err = r.txDao.CountTransactions(ctx, g.Map{txTypeCol: consts.TransactionTypeMinerFee})
	if err != nil {
		return nil, err
	}

	stats.PendingConfirmationCount, err = r.txDao.CountTransactions(ctx, g.Map{statusCol: consts.TransactionStatusPending})
	if err != nil {
		return nil, err
	}

	// Sum amounts using the new DAO method
	stats.TotalUsdtDepositAmount, err = r.txDao.SumAmount(ctx, g.Map{txTypeCol: consts.TransactionTypeDeposit, tokenNameCol: consts.CoinTypeUSDT})
	if err != nil {
		return nil, err
	}

	stats.TotalUsdtWithdrawAmount, err = r.txDao.SumAmount(ctx, g.Map{txTypeCol: consts.TransactionTypeWithdraw, tokenNameCol: consts.CoinTypeUSDT})
	if err != nil {
		return nil, err
	}

	stats.TotalTrxDepositAmount, err = r.txDao.SumAmount(ctx, g.Map{txTypeCol: consts.TransactionTypeDeposit, tokenNameCol: consts.CoinTypeTRX})
	if err != nil {
		return nil, err
	}

	stats.TotalTrxWithdrawAmount, err = r.txDao.SumAmount(ctx, g.Map{txTypeCol: consts.TransactionTypeWithdraw, tokenNameCol: consts.CoinTypeTRX})
	if err != nil {
		return nil, err
	}

	stats.TotalEthDepositAmount, err = r.txDao.SumAmount(ctx, g.Map{txTypeCol: consts.TransactionTypeDeposit, tokenNameCol: consts.CoinTypeETH})
	if err != nil {
		return nil, err
	}

	stats.TotalEthWithdrawAmount, err = r.txDao.SumAmount(ctx, g.Map{txTypeCol: consts.TransactionTypeWithdraw, tokenNameCol: consts.CoinTypeETH})
	if err != nil {
		return nil, err
	}

	return &stats, nil
}

// AddTransaction adds a new transaction record.
func (r *transactionRepository) AddTransaction(ctx context.Context, txData *entity.Transactions) error {
	// Pass the entity struct directly to the new DAO method
	err := r.txDao.InsertTransaction(ctx, txData) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return err
	}
	return nil
}

// FindByHash retrieves a transaction by its hash.
func (r *transactionRepository) FindByHash(ctx context.Context, txHash string) (*entity.Transactions, error) {
	tx, err := r.txDao.FindTransactionByHash(ctx, txHash) // Use new DAO method
	if err != nil {
		// DAO handles wrapping, just return
		return nil, err
	}
	// DAO FindTransactionByHash should return nil, nil if not found
	return tx, nil
}

// UpdateTransactionStatus - Placeholder implementation
// Needs corresponding DAO method like UpdateTransaction(ctx, txHash, data)
// func (r *transactionRepository) UpdateTransactionStatus(ctx context.Context, txHash string, status string, confirmations int, blockNumber int64, blockHash string /* other fields */) error {
// 	data := g.Map{
// 		"transaction_status": status, // Adjust column name if needed
// 		"confirmations":      confirmations,
// 		"block_number":       blockNumber,
// 		"block_hash":         blockHash,
// 		// Add other fields to update
// 	}
// 	// err := r.txDao.UpdateTransaction(ctx, txHash, data) // Example call to a potential new DAO method
// 	// if err != nil {
// 	// 	return gerror.Wrapf(err, "repository: failed to update status for transaction %s", txHash)
// 	// }
// 	// return nil
// 	return codes.NewError(codes.CodeRepositoryNotImplemented)
// }

// isGdbNoRowsError checks if the error is sql.ErrNoRows or a similar DB-specific error.
// Assumed to be defined in repository_utils.go
// func isGdbNoRowsError(err error) bool { ... }
