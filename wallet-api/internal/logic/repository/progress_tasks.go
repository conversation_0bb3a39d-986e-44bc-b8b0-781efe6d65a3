package repository

import (
	"context"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// ProgressTasksQueryOptions defines options for querying progress tasks.
type ProgressTasksQueryOptions struct {
	TaskId string // Task ID to filter by
}

// IProgressTasksRepository defines the interface for progress_tasks business data operations.
type IProgressTasksRepository interface {
	// CreateProgressTask creates a new progress task record.
	CreateProgressTask(ctx context.Context, taskData g.Map) error

	// GetProgressTask retrieves a progress task by its ID.
	GetProgressTask(ctx context.Context, taskId string) (*entity.ProgressTasks, error)

	// UpdateProgressTask updates a progress task record.
	UpdateProgressTask(ctx context.Context, taskId string, data g.Map) error
}
