package repository

import (
	"context"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// IWalletRepository defines the interface for wallet business data operations.
type IWalletRepository interface {
	// IsWalletExist checks if a wallet record already exists.
	IsWalletExist(ctx context.Context) (bool, error)

	// CreateWallet creates a new wallet record with essential initial data.
	// It expects encrypted values as arguments, along with salt and iterations for PBKDF2.
	CreateWallet(ctx context.Context,
		encryptedMnemonic string,
		passwordHash string, // This should be the bcrypt hash
		encryptedGoogleCode string,
		mnemonicSalt []byte,
		mnemonicIterations int,
		googleSecretSalt []byte,
		googleSecretIterations int,
	) error

	// GetWallet retrieves the single wallet record.
	// Returns nil, nil if the wallet does not exist or an error occurs.
	GetWallet(ctx context.Context) (*entity.Wallets, error)

	// DeleteWallet deletes the wallet record(s). Use with caution.
	// Consider if this operation is truly needed or should be handled differently.
	// DeleteWallet(ctx context.Context) error // Uncomment if ResetWallet logic is restored

	// UpdateWalletFields updates specific fields of the wallet record.
	// Uses g.Map for flexibility, similar to current DAO usage.
	// It assumes the wallet record (ID=1 or similar logic) exists.
	UpdateWalletFields(ctx context.Context, data g.Map) error

	// --- Methods anticipated from other logic files (e.g., auth, setting) ---
	// These are placeholders and should be confirmed/refined when analyzing other logic files.

	// UpdateLastUnlockTime updates the last successful unlock timestamp.
	UpdateLastUnlockTime(ctx context.Context, t *gtime.Time) error

	// IncrementUnlockErrorCount increments the unlock error counter.
	IncrementUnlockErrorCount(ctx context.Context) error

	// ResetUnlockErrorCount resets the unlock error counter to zero.
	ResetUnlockErrorCount(ctx context.Context) error

	// UpdatePasswordHash updates the stored password hash (bcrypt).
	UpdatePasswordHash(ctx context.Context, newPasswordHash string) error

	// UpdateGoogleSecret updates the Google Authenticator secret and status.
	UpdateGoogleSecret(ctx context.Context, newEncryptedSecret string, enabled bool) error

	// UpdatePasswordAndReencryptSensitiveData updates password and re-encrypts all sensitive data in a transaction.
	// This method ensures that when password is changed, all encrypted data is re-encrypted with the new password.
	UpdatePasswordAndReencryptSensitiveData(ctx context.Context, newPasswordHash string,
		newEncryptedMnemonic string, newMnemonicSalt []byte, newMnemonicIterations int,
		newEncryptedGoogleCode string, newGoogleSecretSalt []byte, newGoogleSecretIterations int,
		newEncryptedEthFeePrivateKey string, newEncryptedTrxFeePrivateKey string) error
}
