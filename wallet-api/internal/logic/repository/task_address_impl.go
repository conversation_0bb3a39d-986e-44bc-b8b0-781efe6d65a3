package repository

import (
	"context"
	"strings" // Needed for Contains
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv" // Needed for conversion
)

// taskAddressRepository implements ITaskAddressRepository interface.
type taskAddressRepository struct {
	taskAddressDao dao.ITaskAddressDao // Dependency on DAO interface
}

// NewTaskAddressRepository creates and returns a new instance of ITaskAddressRepository.
func NewTaskAddressRepository(dao dao.ITaskAddressDao) ITaskAddressRepository {
	return &taskAddressRepository{
		taskAddressDao: dao,
	}
}

// buildTaskAddressCondition converts query options to a map condition for DAO methods.
// Note: This might be less used if DAO methods become more specific.
func (r *taskAddressRepository) buildTaskAddressCondition(options TaskAddressQueryOptions) map[string]interface{} {
	condition := g.Map{}

	// TaskID is usually handled by specific DAO methods like ListTaskAddressesByTaskID
	// condition["task_id"] = options.TaskID

	if options.Address != "" {
		// This complex OR condition might need a specific DAO method or post-filtering
		condition["sender_address LIKE ? OR receiver_address LIKE ?"] = []interface{}{"%" + options.Address + "%", "%" + options.Address + "%"}
	}
	if options.Status != "" {
		condition["status"] = options.Status
	}
	// Add other filters if needed

	return condition
}

// BatchCreateTaskAddresses creates multiple task address records.
func (r *taskAddressRepository) BatchCreateTaskAddresses(ctx context.Context, taskAddressesMap []g.Map) error {
	if len(taskAddressesMap) == 0 {
		return nil // Nothing to insert
	}
	// Convert []g.Map to []*entity.TaskAddress
	taskAddressesEntity := make([]*entity.TaskAddress, 0, len(taskAddressesMap))
	err := gconv.Structs(taskAddressesMap, &taskAddressesEntity)
	if err != nil {
		return gerror.Wrap(err, "repository: failed to convert task address map to struct")
	}

	err = r.taskAddressDao.BatchInsertTaskAddresses(ctx, taskAddressesEntity) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return err
	}
	return nil
}

// ListTaskAddresses retrieves a paginated list of task addresses based on options.
// Note: This implementation now relies on DAO providing ListTaskAddressesByTaskID
// and performs filtering/pagination in the repository layer, which might be inefficient.
// Consider adding a more flexible List method to DAO if performance is critical.
func (r *taskAddressRepository) ListTaskAddresses(ctx context.Context, options TaskAddressQueryOptions) (list []*entity.TaskAddress, total int, err error) {
	// 1. Fetch all addresses for the task ID first
	allAddresses, err := r.taskAddressDao.ListTaskAddressesByTaskID(ctx, options.TaskID)
	if err != nil {
		return nil, 0, err // Error wrapping done in DAO
	}

	// 2. Apply filtering in memory (less efficient)
	filteredList := make([]*entity.TaskAddress, 0)
	for _, addr := range allAddresses {
		match := true
		if options.Address != "" {
			// Simple string contains check, adjust if LIKE logic is needed
			if !strings.Contains(addr.SenderAddress, options.Address) && !strings.Contains(addr.ReceiverAddress, options.Address) {
				match = false
			}
		}
		if options.Status != "" && gconv.String(addr.Status) != options.Status {
			match = false
		}
		// Add other filters here

		if match {
			filteredList = append(filteredList, addr)
		}
	}

	total = len(filteredList)

	// 3. Apply sorting (if needed, requires custom sorting logic here)
	// Example: sort.SliceStable(...) based on options.SortField

	// 4. Apply pagination in memory
	start := (options.Page - 1) * options.Limit
	end := start + options.Limit
	if start < 0 {
		start = 0
	}
	if end > total {
		end = total
	}
	if start >= total {
		list = []*entity.TaskAddress{} // Return empty slice if page is out of bounds
	} else {
		list = filteredList[start:end]
	}

	return list, total, nil
}

// FindAllTaskAddresses retrieves all task addresses based on options.
// Similar to ListTaskAddresses but without pagination. Filtering is done in memory.
func (r *taskAddressRepository) FindAllTaskAddresses(ctx context.Context, options TaskAddressQueryOptions) ([]*entity.TaskAddress, error) {
	// 1. Fetch all addresses for the task ID first
	allAddresses, err := r.taskAddressDao.ListTaskAddressesByTaskID(ctx, options.TaskID)
	if err != nil {
		return nil, err // Error wrapping done in DAO
	}

	// 2. Apply filtering in memory (less efficient)
	filteredList := make([]*entity.TaskAddress, 0)
	for _, addr := range allAddresses {
		match := true
		if options.Address != "" {
			if !strings.Contains(addr.SenderAddress, options.Address) && !strings.Contains(addr.ReceiverAddress, options.Address) {
				match = false
			}
		}
		if options.Status != "" && gconv.String(addr.Status) != options.Status {
			match = false
		}
		// Add other filters here

		if match {
			filteredList = append(filteredList, addr)
		}
	}

	// 3. Apply sorting (if needed)

	return filteredList, nil
}

// GetTaskAddressCounts retrieves the counts of task addresses by status for a specific task ID.
func (r *taskAddressRepository) GetTaskAddressCounts(ctx context.Context, taskId int64) (*TaskAddressCounts, error) {
	total, success, fail, _, err := r.taskAddressDao.GetTaskAddressStatsByTaskID(ctx, taskId) // Use new DAO method
	if err != nil {
		return nil, err // Error wrapping done in DAO
	}

	// Calculate pending count (assuming pending = total - success - fail)
	// This might need adjustment based on actual status values and logic
	pending := total - success - fail
	if pending < 0 {
		pending = 0
	} // Ensure non-negative

	counts := &TaskAddressCounts{
		SuccessCount: success,
		FailedCount:  fail,
		PendingCount: pending,
		// CanceledCount needs to be handled if status exists and DAO provides it
	}

	return counts, nil
}

// FindByTaskID retrieves all task addresses associated with a specific task ID.
func (r *taskAddressRepository) FindByTaskID(ctx context.Context, taskId int64) ([]*entity.TaskAddress, error) {
	list, err := r.taskAddressDao.ListTaskAddressesByTaskID(ctx, taskId) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return nil, err
	}
	return list, nil
}

// UpdateTaskAddress updates specific fields of a task address record identified by its ID.
func (r *taskAddressRepository) UpdateTaskAddress(ctx context.Context, taskAddressId int64, data g.Map) error {
	if len(data) == 0 {
		return gerror.New("repository: update data cannot be empty for task address")
	}
	delete(data, "id")      // Prevent updating primary key
	delete(data, "task_id") // Prevent updating foreign key

	// Convert g.Map to map[string]interface{} for DAO method
	updateData := map[string]interface{}(data)

	err := r.taskAddressDao.UpdateTaskAddress(ctx, taskAddressId, updateData) // Use new DAO method
	if err != nil {
		// Error wrapping done in DAO
		return err
	}
	return nil
}

// UpdateTaskAddressStatus updates the status, transaction hash, fee, and fail reason of a task address record.
func (r *taskAddressRepository) UpdateTaskAddressStatus(ctx context.Context, taskAddressId int64, status string, txHash string, fee float64, failReason string) error {
	data := g.Map{
		"status":           status,
		"transaction_hash": txHash,
		"fee":              fee,
		"fail_reason":      failReason,
		// update_at will likely be handled by gdb automatic timestamping if configured
	}
	// Use the generic UpdateTaskAddress method
	return r.UpdateTaskAddress(ctx, taskAddressId, data)
}

// Note: isGdbNoRowsError helper function is assumed to be in repository_utils.go
// func isGdbNoRowsError(err error) bool { ... }
