package repository

import (
	"context"
	"time"
)

// PasswordAttemptInfo holds the attempt count and last attempt time.
type PasswordAttemptInfo struct {
	AttemptCount int
	LastAttempt  time.Time
}

// IPasswordAttemptRepository defines the interface for password attempt business logic.
// Assumes there is only one record (e.g., ID=1) tracking attempts.
type IPasswordAttemptRepository interface {
	// GetAttempts retrieves the current attempt count and last attempt time.
	// It ensures the record exists, creating it if necessary.
	GetAttempts(ctx context.Context) (*PasswordAttemptInfo, error)

	// IncrementAttempts increments the attempt count and updates the last attempt time.
	// Returns the new attempt count.
	IncrementAttempts(ctx context.Context) (newAttemptCount int, err error)

	// ResetAttempts resets the attempt count to 0 and updates the last attempt time.
	ResetAttempts(ctx context.Context) error

	// EnsureRecordExists checks if the attempt record exists and creates it if not.
	// This might be an internal detail handled by GetAttempts, or exposed if needed elsewhere.
	// EnsureRecordExists(ctx context.Context) error
}
