package repository

import (
	"database/sql"
	"errors"
	"strings"
)

// isGdbNoRowsError checks if an error signifies that no rows were found in the result set.
// It checks for standard sql.ErrNoRows and common GoFrame error message patterns.
func isGdbNoRowsError(err error) bool {
	if err == nil {
		return false
	}
	// Check standard sql.ErrNoRows first
	if errors.Is(err, sql.ErrNoRows) {
		return true
	}
	// Check if the error message contains the specific string GoFrame might use
	// This is less reliable and might need adjustment based on observed behavior
	return strings.Contains(strings.ToLower(err.Error()), "no rows in result set")
}
