package wallet

import (
	"context"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/codes"

	"github.com/gogf/gf/v2/frame/g"
)

// GetAddressTaskProgress 查询地址创建任务进度
func (s *sWallet) GetAddressTaskProgress(ctx context.Context, req *v1.GetAddressTaskProgressReq) (res *v1.GetAddressTaskProgressRes, err error) {
	// 检查钱包是否存在
	_, err = s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressCheckWalletExistsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressCheckWalletExistsFailed)
	}

	// 查询任务进度
	progressTask, err := s.progressTasksRepo.GetProgressTask(ctx, req.TaskId)
	if err != nil {
		g.Log().<PERSON>rrorf(ctx, "Failed to get progress task: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressGetTaskProgressFailed)
	}

	if progressTask == nil {
		return nil, codes.NewError(codes.CodeWalletAddressTaskNotFound)
	}
	//保留两位小数
	Progress := float64(int(progressTask.Progress*100)) / 100

	// 构建返回结果
	res = &v1.GetAddressTaskProgressRes{
		TaskId:        progressTask.TaskId,
		Status:        progressTask.Status,
		Progress:      Progress,
		ProcessedRows: progressTask.ProcessedRows,
		TotalRows:     progressTask.TotalRows,
		ErrorMessage:  progressTask.ErrorMessage,
	}

	return res, nil
}
