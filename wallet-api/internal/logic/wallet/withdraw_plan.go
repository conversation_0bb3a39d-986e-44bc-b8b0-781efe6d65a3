package wallet

import (
	"context"
	"strconv"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/codes"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 添加地址到归集计划
func (s *sWallet) AddAddressToWithdrawPlan(ctx context.Context, req *v1.AddAddressToWithdrawPlanReq) (res *v1.AddAddressToWithdrawPlanRes, err error) {
	// 检查钱包是否存在
	_, err = s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCheckWalletExistsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCheckWalletExistsFailed)
	}

	// 使用 Address Repository 查找地址
	foundAddresses, err := s.addressRepo.FindAddresses(ctx, g.Map{
		"address": req.Address,
		"type":    req.Chan,
	})
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressFindFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressFindFailed)
	}

	if len(foundAddresses) == 0 {
		return nil, codes.NewError(codes.CodeWalletAddressNotFound)
	}

	// 获取地址信息
	addressInfo := foundAddresses[0]
	
	// 初始化响应
	res = &v1.AddAddressToWithdrawPlanRes{}

	// 如果需要检查余额（默认为true）
	if req.CheckBalance || req.CheckBalance == false && req.MinBalanceAmount != "" {
		// 获取地址余额信息
		chainBalance := strconv.FormatFloat(addressInfo.ChainCoinBalance, 'f', -1, 64)
		usdtBalance := strconv.FormatFloat(addressInfo.ChainUsdtBalance, 'f', -1, 64)
		
		res.ChainBalance = chainBalance
		res.UsdtBalance = usdtBalance
		
		// 如果设置了最小余额要求，进行检查
		if req.MinBalanceAmount != "" {
			// 将字符串转换为浮点数进行比较
			minBalance, err := strconv.ParseFloat(req.MinBalanceAmount, 64)
			
			if err == nil && addressInfo.ChainUsdtBalance < minBalance {
				res.BalanceCheckInfo = g.I18n().Tf(ctx, "USDT余额不足，当前余额：%s，最小要求：%s", usdtBalance, req.MinBalanceAmount)
				return res, nil
			}
		}
		
		// 检查是否有余额
		if addressInfo.ChainCoinBalance == 0 && addressInfo.ChainUsdtBalance == 0 {
			res.BalanceCheckInfo = g.I18n().T(ctx, "地址余额为0，无需创建归集计划")
			return res, nil
		}
	}

	// 检查地址是否已经在归集计划中
	var existingPlan *entity.WithdrawPlan
	err = dao.WithdrawPlan.Ctx(ctx).
		Where(dao.WithdrawPlan.Columns().Address, req.Address).
		Where(dao.WithdrawPlan.Columns().Chan, req.Chan).
		Where(dao.WithdrawPlan.Columns().State, 1). // 只检查待处理的计划
		Scan(&existingPlan)

	if err != nil {
		g.Log().Errorf(ctx, "查询归集计划失败: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletWithdrawPlanQueryFailed)
	}

	// 如果已存在，则返回成功
	if existingPlan != nil {
		res.Success = true
		res.WithdrawPlanId = existingPlan.WithdrawPlanId
		res.BalanceCheckInfo = g.I18n().T(ctx, "地址已在归集计划中")
		return res, nil
	}

	// 创建新的归集计划
	id, err := dao.WithdrawPlan.Ctx(ctx).InsertAndGetId(g.Map{
		dao.WithdrawPlan.Columns().Address:      req.Address,
		dao.WithdrawPlan.Columns().Chan:         req.Chan,
		dao.WithdrawPlan.Columns().State:        1, // 1-待确认
		dao.WithdrawPlan.Columns().ErrorMessage: "[]",
		dao.WithdrawPlan.Columns().CreatedAt:    gtime.Now(),
		dao.WithdrawPlan.Columns().UpdatedAt:    gtime.Now(),
	})

	if err != nil {
		g.Log().Errorf(ctx, "创建归集计划失败: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletWithdrawPlanCreateFailed)
	}

	// 同时更新地址的绑定状态
	err = s.addressRepo.UpdateAddress(ctx, req.Address, g.Map{
		"bind_status": 1,
		"bind_at":     gtime.Now(),
	})
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressUpdateBindStatusFailed.Message(), err)
		// 不影响主流程，只记录错误
	}

	res.Success = true
	res.WithdrawPlanId = uint(id)
	return res, nil
}

// 获取归集计划列表
func (s *sWallet) GetWithdrawPlanList(ctx context.Context, req *v1.GetWithdrawPlanListReq) (res *v1.GetWithdrawPlanListRes, err error) {
	// 暂时跳过钱包检查，用于测试
	/*
		// 检查钱包是否存在
		_, err = s.walletRepo.GetWallet(ctx)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCheckWalletExistsFailed.Message(), err)
			return nil, codes.WrapError(err, codes.CodeWalletCheckWalletExistsFailed)
		}
	*/

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// 构建查询条件
	model := dao.WithdrawPlan.Ctx(ctx)

	// 添加筛选条件
	if req.Chan != "" {
		model = model.Where(dao.WithdrawPlan.Columns().Chan, req.Chan)
	}
	if req.State > 0 {
		model = model.Where(dao.WithdrawPlan.Columns().State, req.State)
	}

	// 获取总数
	total, err := model.Count()
	if err != nil {
		g.Log().Errorf(ctx, "获取归集计划总数失败: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletWithdrawPlanQueryFailed)
	}

	// 获取分页数据
	var plans []*entity.WithdrawPlan
	err = model.Page(req.Page, req.Limit).
		Order(dao.WithdrawPlan.Columns().CreatedAt + " DESC").
		Scan(&plans)

	if err != nil {
		g.Log().Errorf(ctx, "获取归集计划列表失败: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletWithdrawPlanQueryFailed)
	}

	// 构建响应
	list := make([]*v1.WithdrawPlanInfo, 0, len(plans))
	for _, plan := range plans {
		list = append(list, &v1.WithdrawPlanInfo{
			WithdrawPlanId: plan.WithdrawPlanId,
			Chan:           plan.Chan,
			Address:        plan.Address,
			State:          plan.State,
			ErrorMessage:   plan.ErrorMessage,
			CreatedAt:      plan.CreatedAt.String(),
			UpdatedAt:      plan.UpdatedAt.String(),
		})
	}

	return &v1.GetWithdrawPlanListRes{
		Page: v1.Page{
			Current:  req.Page,
			PageSize: req.Limit,
			Total:    total,
		},
		List:  list,
		Total: total,
	}, nil
}
