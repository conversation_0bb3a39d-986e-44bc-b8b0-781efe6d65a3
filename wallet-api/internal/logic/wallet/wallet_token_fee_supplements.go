package wallet

import (
	"context"
	"fmt"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetTokenFeeSupplements 获取代币费用补充记录列表
func (s *sWallet) GetTokenFeeSupplements(ctx context.Context, req *v1.GetTokenFeeSupplementsReq) (res *v1.GetTokenFeeSupplementsRes, err error) {
	res = &v1.GetTokenFeeSupplementsRes{}

	// 构建查询条件
	model := dao.TokenFeeSupplements.Ctx(ctx)

	// 添加过滤条件
	if req.Address != "" {
		model = model.Where(dao.TokenFeeSupplements.Columns().Address, req.Address)
	}
	if req.ChainType != "" {
		model = model.Where(dao.TokenFeeSupplements.Columns().ChainType, req.ChainType)
	}
	if req.TokenSymbol != "" {
		model = model.Where(dao.TokenFeeSupplements.Columns().TokenSymbol, req.TokenSymbol)
	}
	if req.FeeType != "" {
		model = model.Where(dao.TokenFeeSupplements.Columns().FeeType, req.FeeType)
	}
	if req.Status != "" {
		model = model.Where(dao.TokenFeeSupplements.Columns().Status, req.Status)
	}
	if req.TrxSupplementStatus != "" {
		model = model.Where(dao.TokenFeeSupplements.Columns().TrxSupplementStatus, req.TrxSupplementStatus)
	}

	// 获取总记录数
	count, err := model.Count()
	if err != nil {
		return nil, err
	}

	// 设置分页信息
	res.Page = v1.Page{
		Current:  req.Page,
		PageSize: req.Limit,
		Total:    int(count),
	}

	// 如果没有记录，直接返回
	if count == 0 {
		res.List = []*v1.TokenFeeSupplement{}
		res.Stats = s.calculateFeeStatistics(ctx, nil)
		return res, nil
	}

	// 设置排序
	if req.SortField != "" && req.SortOrder != "" {
		sortDirection := "ASC"
		if req.SortOrder == "desc" {
			sortDirection = "DESC"
		}
		model = model.Order(fmt.Sprintf("%s %s", req.SortField, sortDirection))
	} else {
		// 默认按创建时间倒序排序
		model = model.Order(dao.TokenFeeSupplements.Columns().CreatedAt + " DESC")
	}

	// 分页查询
	err = model.Page(req.Page, req.Limit).Scan(&res.List)
	if err != nil {
		return nil, err
	}

	// 获取统计信息
	res.Stats = s.calculateFeeStatistics(ctx, nil)

	return res, nil
}

// UpdateTokenFeeSupplementStatus 更新代币费用补充记录状态
func (s *sWallet) UpdateTokenFeeSupplementStatus(ctx context.Context, req *v1.UpdateTokenFeeSupplementStatusReq) (res *v1.UpdateTokenFeeSupplementStatusRes, err error) {
	res = &v1.UpdateTokenFeeSupplementStatusRes{Success: false}

	// 验证密码
	wallet, err := s.walletRepo.GetWallet(ctx)
	if err != nil {
		return nil, err
	}
	if wallet == nil {
		return nil, fmt.Errorf("钱包不存在")
	}

	// 验证谷歌验证码
	if err = s._validateGoogleCode(ctx, wallet, req.GoogleCode, req.Password); err != nil {
		return nil, err
	}

	// 验证密码 (简单比较)
	if err = s._validatePasswordSimple(ctx, wallet, req.Password); err != nil {
		return nil, err
	}

	// 查询记录是否存在
	var supplement *entity.TokenFeeSupplements
	err = dao.TokenFeeSupplements.Ctx(ctx).
		Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, req.TokenFeeSupplementId).
		Scan(&supplement)
	if err != nil {
		return nil, err
	}
	if supplement == nil {
		return nil, fmt.Errorf("记录不存在")
	}

	// 更新状态
	updateData := g.Map{
		dao.TokenFeeSupplements.Columns().Status:     req.Status,
		dao.TokenFeeSupplements.Columns().UpdatedAt:  gtime.Now(),
		dao.TokenFeeSupplements.Columns().RetryCount: 0,
	}

	// 如果提供了交易哈希，则更新
	if req.TransactionHash != "" {
		updateData[dao.TokenFeeSupplements.Columns().TransactionHash] = req.TransactionHash
	}

	// 如果提供了错误信息，则更新
	if req.ErrorMessage != "" {
		updateData[dao.TokenFeeSupplements.Columns().ErrorMessage] = req.ErrorMessage
	}

	// 执行更新
	_, err = dao.TokenFeeSupplements.Ctx(ctx).
		Where(dao.TokenFeeSupplements.Columns().TokenFeeSupplementId, req.TokenFeeSupplementId).
		Data(updateData).
		Update()
	if err != nil {
		return nil, err
	}

	res.Success = true
	return res, nil
}

// GetFeeStatistics 获取费用统计信息
func (s *sWallet) GetFeeStatistics(ctx context.Context, req *v1.GetFeeStatisticsReq) (res *v1.GetFeeStatisticsRes, err error) {
	res = &v1.GetFeeStatisticsRes{}

	// 获取统计信息
	res.Statistics = s.calculateFeeStatistics(ctx, nil)

	return res, nil
}

// calculateFeeStatistics 计算费用统计信息
func (s *sWallet) calculateFeeStatistics(ctx context.Context, tx gdb.TX) v1.FeeStatistics {
	var stats v1.FeeStatistics

	// 使用事务或上下文
	model := dao.TokenFeeSupplements.Ctx(ctx)
	if tx != nil {
		model = dao.TokenFeeSupplements.TX(tx)
	}

	// 获取总记录数
	totalCount, err := model.Count()
	if err != nil {
		g.Log().Error(ctx, "计算费用统计信息失败:", err)
		return stats
	}
	stats.TotalCount = int(totalCount)

	// 获取各状态记录数
	statusCounts := []struct {
		Status string
		Count  int
	}{}
	err = model.Fields("status, count(1) as count").
		Group("status").
		Scan(&statusCounts)
	if err != nil {
		g.Log().Error(ctx, "计算状态统计信息失败:", err)
		return stats
	}

	// 设置各状态记录数
	for _, sc := range statusCounts {
		switch sc.Status {
		case "pending":
			stats.PendingCount = sc.Count
		case "processing":
			stats.ProcessingCount = sc.Count
		case "success":
			stats.SuccessCount = sc.Count
		case "failed":
			stats.FailedCount = sc.Count
		case "partial_success":
			stats.PartialSuccessCount = sc.Count
		}
	}

	// 获取总需要费用数量
	var totalRequired struct {
		Total float64
	}
	err = model.Fields("sum(required_amount) as total").Scan(&totalRequired)
	if err != nil {
		g.Log().Error(ctx, "计算总需要费用数量失败:", err)
	} else {
		stats.TotalRequiredAmount = totalRequired.Total
	}

	// 获取总已补充费用数量
	var totalProvided struct {
		Total float64
	}
	err = model.Fields("sum(provided_amount) as total").Scan(&totalProvided)
	if err != nil {
		g.Log().Error(ctx, "计算总已补充费用数量失败:", err)
	} else {
		stats.TotalProvidedAmount = totalProvided.Total
	}

	return stats
}
