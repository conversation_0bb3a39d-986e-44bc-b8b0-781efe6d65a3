package wallet

import (
	"context"

	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/codes"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"
	util "wallet-api/internal/utility/utils" // Added for EncryptStringWithPBKDF2

	"github.com/gogf/gf/v2/errors/gerror" // Added for error wrapping
	"github.com/gogf/gf/v2/frame/g"
)

// WalletSetting 钱包设置 (合并所有设置)
func (s *sWallet) WalletSetting(ctx context.Context, req *v1.WalletSettingReq) (res *v1.WalletSettingRes, err error) {
	// 获取钱包实体
	walletEntity, err := s._getWalletEntity(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "获取钱包实体失败: %v", err)
		return nil, err
	}

	// 验证密码
	if err = s._validatePasswordSimple(ctx, walletEntity, req.Password); err != nil {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "密码验证失败: %v", err)
		return nil, err
	}

	// 验证谷歌验证码
	if err = s._validateGoogleCode(ctx, walletEntity, req.GoogleCode, req.Password); err != nil {
		g.Log().Errorf(ctx, "谷歌验证码验证失败: %v", err)
		return nil, err
	}

	g.Log().Info(ctx, "钱包设置请求参数:", req)

	// 初始化更新数据映射

	// 处理地址和私钥设置
	// Pass the validated password to processSettings for encryption
	update_data, err := s.processSettings(ctx, req, walletEntity, req.Password)
	if err != nil {
		g.Log().Errorf(ctx, "处理地址和私钥设置失败: %v", err)
		return nil, err
	}

	// 如果没有需要更新的数据,直接返回成功
	if len(update_data) == 0 {
		g.Log().Info(ctx, "没有需要更新的钱包设置")
		return &v1.WalletSettingRes{
			Success: true,
		}, nil
	}

	g.Log().Info(ctx, "更新钱包设置:", update_data)

	// Assuming wallet ID is always 1 for the single wallet
	_, err = dao.Wallets.Ctx(ctx).Where(dao.Wallets.Columns().Id, 1).Update(update_data)

	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingUpdateInfoFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletSettingUpdateInfoFailed)
	}

	return &v1.WalletSettingRes{
		Success: true,
	}, nil
}

// processSettings 处理地址和私钥设置，并对敏感信息进行加密
// userPassword 是经过验证的用户登录密码
func (s *sWallet) processSettings(ctx context.Context, req *v1.WalletSettingReq, walletEntity *entity.Wallets, userPassword string) (g.Map, error) {

	// // 策略归集设置
	// StrategyCollectSwitch string `json:"strategy_collect_switch" dc:"策略归集开关"`
	// EthCollectThreshold   string `json:"eth_collect_threshold" dc:"ETH归集阈值"`
	// TrxCollectThreshold   string `json:"trx_collect_threshold" dc:"TRX归集阈值"`
	// UsdtCollectThreshold  string `json:"usdt_collect_threshold" dc:"USDT归集阈值"`

	// // 定时归集设置
	// CronCollectSwitch string `json:"cron_collect_switch" dc:"定时归集开关"`
	// CronCollectTime   string `json:"cron_collect_time" dc:"定时归集时间"`

	// // 归集地址设置
	// TrxCollectAddress string `json:"trx_collect_address" dc:"TRX链特定归集地址"`
	// EthCollectAddress string `json:"eth_collect_address" dc:"ETH链特定归集地址"`

	// // 费用私钥设置
	// TrxFeePrivateKey string `json:"trx_fee_private_key" dc:"TRX费用支付私钥(请谨慎处理,加密)"`
	// EthFeePrivateKey string `json:"eth_fee_private_key" dc:"ETH费用支付私钥(请谨慎处理,加密)"`

	// // 费用地址设置
	// TrxFeeAddress string `json:"trx_fee_address" dc:"TRX费用资金持有地址"`
	// EthFeeAddress string `json:"eth_fee_address" dc:"ETH费用资金持有地址"`

	// // TRX激活金额
	// TrxActivateAmount string `json:"trx_activate_amount" dc:"TRX激活账户金额" v:"min:0#激活金额不能为负数"`

	// // 费用模式设置
	// EthFeeMode string `json:"eth_fee_mode" dc:"ETH矿工费模式 1自动 2手动" v:"in:1,2#ETH矿工费模式必须是1或2"`
	// TrxFeeMode string `json:"trx_fee_mode" dc:"TRX矿工费模式 1自动 2手动" v:"in:1,2#TRX矿工费模式必须是1或2"`

	// // 固定费用金额
	// EthFeeAmount string `json:"eth_fee_amount" dc:"ETH固定矿工费发送金额" v:"min:0#费用金额不能为负数"`
	// TrxFeeAmount string `json:"trx_fee_amount" dc:"TRX固定矿工费发送金额" v:"min:0#费用金额不能为负数"`

	// // 最大费用限制
	// EthFeeMax   string `json:"eth_fee_max" dc:"ETH最大矿工费" v:"min:0#最大费用不能为负数"`
	// Erc20FeeMax string `json:"erc20_fee_max" dc:"ERC20最大矿工费" v:"min:0#最大费用不能为负数"`
	// TrxFeeMax   string `json:"trx_fee_max" dc:"TRX交易最多支付手续费" v:"min:0#最大费用不能为负数"`

	// // Gas设置
	// EthGasPrice   string `json:"eth_gas_price" dc:"ETH Gas价格"`
	// EthGasLimit   string `json:"eth_gas_limit" dc:"ETH Gas限制"`
	// Erc20GasPrice string `json:"erc20_gas_price" dc:"ERC20 Gas价格"`
	// Erc20GasLimit string `json:"erc20_gas_limit" dc:"ERC20 Gas限制"`

	// // 保留金额设置
	// TrxKeepAmount string `json:"trx_keep_amount" dc:"归集保持账户内最低TRX余额,防止交易失败" v:"min:0#保留金额不能为负数"`
	// EthKeepAmount string `json:"eth_keep_amount" dc:"ETH归集时预留金额" v:"min:0#保留金额不能为负数"`

	// // TRC20能量和带宽设置
	// Trc20MinRequiredEnergy    string `json:"trc20_min_required_energy" dc:"转账USDT最低需要的能量,手动模式设置" v:"min:0#能量值不能为负数"`
	// Trc20MaxEnergyFee         string `json:"trc20_max_energy_fee" dc:"最大允许消耗的TRX能量费金额,用于购买TRX能量的费用设置" v:"min:0#能量费不能为负数"`
	// Trc20MinRequiredBandwidth string `json:"trc20_min_required_bandwidth" dc:"最低带宽" v:"min:0#带宽值不能为负数"`
	var update_data = g.Map{}
	if req.StrategyCollectSwitch != "" {
		if req.StrategyCollectSwitch == "true" {
			update_data[dao.Wallets.Columns().StrategyCollectSwitch] = 1
		} else if req.StrategyCollectSwitch == "false" {
			update_data[dao.Wallets.Columns().StrategyCollectSwitch] = 0
		}
		// 对于其他非空字符串值，当前不会更新该字段，这与之前的行为（仅在非空时更新）部分一致。
		// 如果需要更严格的错误处理（例如，记录无效值），可以在此处添加。
	}
	if req.EthCollectThreshold != "" {
		update_data[dao.Wallets.Columns().EthCollectThreshold] = req.EthCollectThreshold
	}
	if req.TrxCollectThreshold != "" {
		update_data[dao.Wallets.Columns().TrxCollectThreshold] = req.TrxCollectThreshold
	}
	if req.UsdtCollectThreshold != "" {
		update_data[dao.Wallets.Columns().UsdtCollectThreshold] = req.UsdtCollectThreshold
	}

	if req.CronCollectSwitch != "" {
		if req.CronCollectSwitch == "true" {
			update_data[dao.Wallets.Columns().CronCollectSwitch] = 1
		} else if req.CronCollectSwitch == "false" {
			update_data[dao.Wallets.Columns().CronCollectSwitch] = 0
		}
		// 对于其他非空字符串值，当前不会更新该字段。
	}
	if req.CronCollectTime != "" {
		update_data[dao.Wallets.Columns().CronCollectTime] = req.CronCollectTime
	}

	if req.TrxCollectAddress != "" {
		update_data[dao.Wallets.Columns().TrxCollectAddress] = req.TrxCollectAddress
	}
	if req.EthCollectAddress != "" {
		update_data[dao.Wallets.Columns().EthCollectAddress] = req.EthCollectAddress
	}

	// 加密存储 TrxFeePrivateKey
	if req.TrxFeePrivateKey != "" {
		if walletEntity.MnemonicSalt == nil || len(walletEntity.MnemonicSalt) == 0 || walletEntity.MnemonicIterations == 0 {
			g.Log().Errorf(ctx, "Mnemonic salt or iterations missing for TrxFeePrivateKey encryption. WalletID: %d", walletEntity.Id)
			return nil, gerror.New("加密TRX手续费私钥失败：缺少助记词加密参数")
		}

		encryptedTrxFeeKey, err := util.EncryptStringWithPBKDF2(ctx, req.TrxFeePrivateKey, userPassword, walletEntity.MnemonicSalt, walletEntity.MnemonicIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to encrypt TrxFeePrivateKey: %v. WalletID: %d", err, walletEntity.Id)
			return nil, gerror.Wrap(err, "加密TRX手续费私钥失败")
		}
		update_data[dao.Wallets.Columns().TrxFeePrivateKey] = encryptedTrxFeeKey
		g.Log().Infof(ctx, "TrxFeePrivateKey will be updated (encrypted). WalletID: %d", walletEntity.Id)
	}

	// 加密存储 EthFeePrivateKey
	if req.EthFeePrivateKey != "" {
		if walletEntity.MnemonicSalt == nil || len(walletEntity.MnemonicSalt) == 0 || walletEntity.MnemonicIterations == 0 {
			g.Log().Errorf(ctx, "Mnemonic salt or iterations missing for EthFeePrivateKey encryption. WalletID: %d", walletEntity.Id)
			return nil, gerror.New("加密ETH手续费私钥失败：缺少助记词加密参数")
		}

		encryptedEthFeeKey, err := util.EncryptStringWithPBKDF2(ctx, req.EthFeePrivateKey, userPassword, walletEntity.MnemonicSalt, walletEntity.MnemonicIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to encrypt EthFeePrivateKey: %v. WalletID: %d", err, walletEntity.Id)
			return nil, gerror.Wrap(err, "加密ETH手续费私钥失败")
		}
		update_data[dao.Wallets.Columns().EthFeePrivateKey] = encryptedEthFeeKey
		g.Log().Infof(ctx, "EthFeePrivateKey will be updated (encrypted). WalletID: %d", walletEntity.Id)
	}

	if req.TrxFeeAddress != "" {
		update_data[dao.Wallets.Columns().TrxFeeAddress] = req.TrxFeeAddress
	}
	if req.EthFeeAddress != "" {
		update_data[dao.Wallets.Columns().EthFeeAddress] = req.EthFeeAddress
	}

	if req.TrxActivateAmount != "" {
		update_data[dao.Wallets.Columns().TrxActivateAmount] = req.TrxActivateAmount
	}

	if req.EthFeeMode != "" {
		update_data[dao.Wallets.Columns().EthFeeMode] = req.EthFeeMode
	}
	if req.TrxFeeMode != "" {
		update_data[dao.Wallets.Columns().TrxFeeMode] = req.TrxFeeMode
	}

	if req.EthFeeAmount != "" {
		update_data[dao.Wallets.Columns().EthFeeAmount] = req.EthFeeAmount
	}

	if req.TrxFeeAmount != "" {
		update_data[dao.Wallets.Columns().TrxFeeAmount] = req.TrxFeeAmount
	}

	if req.EthFeeMax != "" {
		update_data[dao.Wallets.Columns().EthFeeMax] = req.EthFeeMax
	}

	if req.Erc20FeeMax != "" {
		update_data[dao.Wallets.Columns().Erc20FeeMax] = req.Erc20FeeMax
	}

	if req.TrxFeeMax != "" {
		update_data[dao.Wallets.Columns().TrxFeeMax] = req.TrxFeeMax
	}

	if req.EthGasPrice != "" {
		update_data[dao.Wallets.Columns().EthGasPrice] = req.EthGasPrice
	}

	if req.EthGasLimit != "" {
		update_data[dao.Wallets.Columns().EthGasLimit] = req.EthGasLimit
	}

	if req.Erc20GasPrice != "" {
		update_data[dao.Wallets.Columns().Erc20GasPrice] = req.Erc20GasPrice
	}

	if req.Erc20GasLimit != "" {
		update_data[dao.Wallets.Columns().Erc20GasLimit] = req.Erc20GasLimit
	}

	if req.TrxKeepAmount != "" {
		update_data[dao.Wallets.Columns().TrxKeepAmount] = req.TrxKeepAmount
	}

	if req.EthKeepAmount != "" {
		update_data[dao.Wallets.Columns().EthKeepAmount] = req.EthKeepAmount
	}

	if req.Trc20MinRequiredEnergy != "" {
		update_data[dao.Wallets.Columns().Trc20MinRequiredEnergy] = req.Trc20MinRequiredEnergy
	}
	if req.Trc20MaxEnergyFee != "" {
		update_data[dao.Wallets.Columns().Trc20MaxEnergyFee] = req.Trc20MaxEnergyFee
	}

	if req.Trc20MinRequiredBandwidth != "" {
		update_data[dao.Wallets.Columns().Trc20MinRequiredBandwidth] = req.Trc20MinRequiredBandwidth
	}

	if req.EthMinTakeAmount != "" {
		update_data[dao.Wallets.Columns().EthMinTakeAmount] = req.EthMinTakeAmount
	}
	if req.TrxMinTakeAmount != "" {
		update_data[dao.Wallets.Columns().TrxMinTakeAmount] = req.TrxMinTakeAmount
	}
	if req.UsdtMinTakeAmount != "" {
		update_data[dao.Wallets.Columns().UsdtMinTakeAmount] = req.UsdtMinTakeAmount
	}
	if req.Trc20TriggerFeeAmount != "" {
		update_data[dao.Wallets.Columns().Trc20TriggerFeeAmount] = req.Trc20TriggerFeeAmount
	}

	return update_data, nil
}
