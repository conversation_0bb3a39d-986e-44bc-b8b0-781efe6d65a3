package wallet

import (
	"context"
	"fmt"
	"strings"
	"time"

	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/codes" // Import codes package
	"wallet-api/internal/consts"
	"wallet-api/internal/dao"
	"wallet-api/internal/logic/repository" // 导入 repository 包
	"wallet-api/internal/security/credentialmanager"

	// 导入 task 包，用于 GetTokenAttributes
	// "wallet-api/internal/model/entity"                   // 导入 entity 包 - 由 repository 层处理
	"wallet-api/internal/service"
	"wallet-api/internal/utility"
	eth "wallet-api/internal/utility/crypto/eth"
	tronUtil "wallet-api/internal/utility/crypto/tron" // 导入 tron 包
	util "wallet-api/internal/utility/utils"

	"github.com/gogf/gf/v2/errors/gerror" // 导入 gerror 包
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-jwt/jwt/v4"
	"github.com/pquerna/otp/totp"
	"github.com/shopspring/decimal" // 导入 decimal 包
	"golang.org/x/crypto/bcrypt"
)

// 钱包服务实现
type sWallet struct {
	walletRepo        repository.IWalletRepository        // Wallet Repository
	addressRepo       repository.IAddressRepository       // Address Repository
	taskRepo          repository.ITaskRepository          // Task Repository
	taskAddressRepo   repository.ITaskAddressRepository   // TaskAddress Repository
	transactionRepo   repository.ITransactionRepository   // Transaction Repository
	progressTasksRepo repository.IProgressTasksRepository // ProgressTasks Repository
}

// 注册钱包服务
func init() {
	// 创建 Repository 实例 (依赖全局 DAO 变量)
	// 创建 Repository 实例 (依赖全局 DAO 变量 - 确保 DAO 变量仍然可用或调整 Repository 实现)
	// 创建 Repository 实例 (传递 DAO 实例的指针以满足接口要求)
	walletRepo := repository.NewWalletRepository(&dao.Wallets)
	addressRepo := repository.NewAddressRepository(&dao.Address)
	taskRepo := repository.NewTaskRepository(&dao.Tasks)
	taskAddressRepo := repository.NewTaskAddressRepository(&dao.TaskAddress)
	transactionRepo := repository.NewTransactionRepository(&dao.Transactions)
	progressTasksRepo := repository.NewProgressTasksRepository()
	// 将所有需要的 Repository 注入 Logic/Service
	service.RegisterWallet(New(walletRepo, addressRepo, taskRepo, taskAddressRepo, transactionRepo, progressTasksRepo))
}

// 创建新的钱包服务实例
func New(
	walletRepo repository.IWalletRepository,
	addressRepo repository.IAddressRepository,
	taskRepo repository.ITaskRepository,
	taskAddressRepo repository.ITaskAddressRepository,
	transactionRepo repository.ITransactionRepository,
	progressTasksRepo repository.IProgressTasksRepository,
) service.IWalletService { // 返回接口类型
	return &sWallet{
		walletRepo:        walletRepo,
		addressRepo:       addressRepo,
		taskRepo:          taskRepo,
		taskAddressRepo:   taskAddressRepo,
		transactionRepo:   transactionRepo,
		progressTasksRepo: progressTasksRepo,
	}
}

// 生成钱包助记词
func (s *sWallet) GenerateWallet(ctx context.Context, req *v1.GenerateWalletReq) (res *v1.GenerateWalletRes, err error) {

	// 检查是否已初始化钱包
	// 使用 Repository 检查钱包是否存在
	exist, err := s.walletRepo.IsWalletExist(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletQueryFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletQueryFailed)
	}
	if exist {
		return nil, codes.NewError(codes.CodeWalletAlreadyExists)
	}

	// 生成随机助记词（24个词）
	mnemonic, err := eth.GenerateMnemonic()
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletGenerateMnemonicFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletGenerateMnemonicFailed)
	}

	res = &v1.GenerateWalletRes{
		Mnemonic: mnemonic,
	}
	return
}

// 创建钱包
func (s *sWallet) CreateWallet(ctx context.Context, req *v1.CreateWalletReq) (res *v1.CreateWalletRes, err error) {
	// 使用 Repository 检查钱包是否存在
	exist, err := s.walletRepo.IsWalletExist(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletQueryFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletQueryFailed)
	}
	if exist {
		return nil, codes.NewError(codes.CodeWalletAlreadyExists)
	}

	//校验谷歌验证码
	valid := totp.Validate(req.GoogleCode, req.Secret)

	if !valid {
		return nil, codes.NewError(codes.CodeWalletInvalidGoogleCode)
	}

	// 检查助记词是否正确
	if !eth.ValidateMnemonic(req.Mnemonic) {
		return nil, codes.NewError(codes.CodeWalletInvalidMnemonic)
	}

	// 使用bcrypt对密码进行加密
	// 使用bcrypt对密码进行加密 (直接哈希原始密码)
	hashedPasswordBytes, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletEncryptPasswordFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletEncryptPasswordFailed)
	}
	passwordHash := string(hashedPasswordBytes) // This is the bcrypt hash for login

	// --- PBKDF2 Encryption for Mnemonic ---
	mnemonicSalt, err := util.GenerateSalt(util.DefaultSaltLength)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to generate salt for mnemonic: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletGenerateSaltFailed) // Define this error code
	}
	cfgMnemonicIterations, errCfg := g.Cfg().Get(ctx, "security.pbkdf2Iterations")
	if errCfg != nil || cfgMnemonicIterations.IsNil() {
		g.Log().Errorf(ctx, "Failed to get security.pbkdf2Iterations from config for mnemonic: %v", errCfg)
		return nil, codes.NewError(codes.CodeConfigError) // Define this error code
	}
	mnemonicIterations := cfgMnemonicIterations.Int()
	if mnemonicIterations <= 0 {
		g.Log().Errorf(ctx, "Invalid security.pbkdf2Iterations from config for mnemonic: %d", mnemonicIterations)
		return nil, codes.NewError(codes.CodeConfigError)
	}
	encryptedMnemonic, err := util.EncryptStringWithPBKDF2(ctx, req.Mnemonic, req.Password, mnemonicSalt, mnemonicIterations)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletEncryptMnemonicFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletEncryptMnemonicFailed)
	}

	// --- PBKDF2 Encryption for Google Secret ---
	googleSecretSalt, err := util.GenerateSalt(util.DefaultSaltLength)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to generate salt for google secret: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletGenerateSaltFailed) // Define this error code
	}
	// Iterations can be the same or configured separately
	cfgGoogleSecretIterations, errCfgGS := g.Cfg().Get(ctx, "security.pbkdf2Iterations") // Assuming same config for iterations
	if errCfgGS != nil || cfgGoogleSecretIterations.IsNil() {
		g.Log().Errorf(ctx, "Failed to get security.pbkdf2Iterations from config for google secret: %v", errCfgGS)
		return nil, codes.NewError(codes.CodeConfigError)
	}
	googleSecretIterations := cfgGoogleSecretIterations.Int()
	if googleSecretIterations <= 0 {
		g.Log().Errorf(ctx, "Invalid security.pbkdf2Iterations from config for google secret: %d", googleSecretIterations)
		return nil, codes.NewError(codes.CodeConfigError)
	}
	encryptedSecret, err := util.EncryptStringWithPBKDF2(ctx, req.Secret, req.Password, googleSecretSalt, googleSecretIterations)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletEncryptGoogleSecretFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletEncryptGoogleSecretFailed)
	}

	// 保存钱包信息
	// 使用 Repository 创建钱包 - Note: s.walletRepo.CreateWallet signature will need to be updated
	err = s.walletRepo.CreateWallet(ctx, encryptedMnemonic, passwordHash, encryptedSecret,
		mnemonicSalt, mnemonicIterations, googleSecretSalt, googleSecretIterations)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSaveFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletSaveFailed)
	}
	// 缓存密码和解密后的费用私钥
	// 在 CreateWallet 流程中，wallet 实体尚未保存到数据库并重新加载，
	// 因此这里传递的 wallet 实体中的费用私钥字段是空的。
	// SetPassword 方法内部会处理这种情况，不会尝试解密空字段。
	// 真正的费用私钥解密会在用户首次认证 (Auth) 时发生。
	credentialmanager.SetPassword(ctx, req.Password, nil) // 在创建钱包时，费用私钥尚未设置，传递 nil 或一个不包含私钥的 wallet 实体

	return &v1.CreateWalletRes{
		Success: true,
	}, nil
}

// 重置钱包
func (s *sWallet) ResetWallet(ctx context.Context, req *v1.ResetWalletReq) (res *v1.ResetWalletRes, err error) {
	// TODO: 恢复 ResetWallet 逻辑时，需要调用 s.walletRepo.DeleteWallet(ctx)
	// exist, err := s.walletRepo.IsWalletExist(ctx)
	// ...
	// err = s.walletRepo.DeleteWallet(ctx)
	// ...

	return &v1.ResetWalletRes{
		Success: true,
	}, nil
}

// 检查钱包状态
func (s *sWallet) CheckStatus(ctx context.Context, req *v1.CheckStatusReq) (res *v1.CheckStatusRes, err error) {
	res = &v1.CheckStatusRes{}

	// 获取请求
	r := g.RequestFromCtx(ctx)
	if r == nil {
		res.Message = codes.CodeWalletGetRequestInfoFailed.Message()
		res.IsUnlocked = false
		res.IsInitialized = false
		return
	}
	// 检查钱包是否存在
	// 使用 Repository 获取钱包信息
	walletEntity, err := s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletQueryFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletQueryFailed)
	}

	if walletEntity == nil {
		res.Message = codes.CodeWalletNotInitialized.Message()
		res.IsUnlocked = false
		res.IsInitialized = false
		return
	}

	// 检查最后解锁时间，超过10分钟视为过期
	// 从实体获取 lastUnlockAt
	lastUnlockAt := walletEntity.LastUnlockAt
	if lastUnlockAt == nil {
		res.Message = codes.CodeWalletNotUnlocked.Message()
		res.IsUnlocked = false
		res.IsInitialized = true
		return
	}

	// 如果最后解锁时间超过10分钟，视为过期
	if gtime.Now().After(lastUnlockAt.Add(10 * time.Minute)) {
		res.Message = codes.CodeWalletExpired.Message()
		res.IsUnlocked = false
		res.IsInitialized = true
		return
	}

	// 获取Authorization头
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		return nil, codes.NewError(codes.CodeWalletAuthTokenNotProvided) // Use specific code
	}

	// 提取token
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		return nil, codes.NewError(codes.CodeWalletAuthTokenFormatError) // Use specific code
	}
	tokenString := parts[1]

	secretKey, err := utility.GetTokenSecret()
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletGetTokenSecretFailed) // Use specific code
	}

	// 解析token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, codes.NewError(codes.CodeWalletInvalidSignAlgorithm) // Use specific code
		}
		return []byte(secretKey), nil
	})

	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletInvalidAuthToken) // Use specific code and wrap original error
	}

	// 验证token
	if _, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		res.Message = codes.CodeWalletStatusOk.Message() // Use specific code
		res.IsUnlocked = true
		res.IsInitialized = true
		return
	} else {
		res.Message = codes.CodeWalletExpired.Message()
		res.IsUnlocked = false
		res.IsInitialized = true
		return
	}

}

// 获取钱包信息
func (s *sWallet) GetWalletInfo(ctx context.Context, req *v1.GetWalletInfoReq) (res *v1.GetWalletInfoRes, err error) {
	// 将在后续实现
	// 使用 Repository 获取钱包信息
	walletEntity, err := s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletQueryFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletQueryFailed)
	}
	if walletEntity == nil {
		// 或者根据业务逻辑返回错误
		return nil, codes.NewError(codes.CodeWalletNotFound) // Use specific code
	}

	var WalletInfo v1.WalletInfo // Changed from var WalletInfo = v1.WalletInfo{} to allow passing its address

	// 使用 gconv.Struct 进行转换，注意参数类型
	// walletEntity 是 *entity.Wallets
	// &WalletInfo 是 *v1.WalletInfo
	convErr := gconv.Struct(walletEntity, &WalletInfo)
	if convErr != nil {
		g.Log().Warningf(ctx, "GetWalletInfo: gconv.Struct failed for WalletInfo: %v", convErr)
		// 根据业务需求决定是否因为转换失败而返回错误
		return nil, codes.WrapError(convErr, codes.CodeInternalError)
	}

	WalletInfo.TrxFeePrivateKeyIsSet = walletEntity.TrxFeePrivateKey != ""
	WalletInfo.EthFeePrivateKeyIsSet = walletEntity.EthFeePrivateKey != ""
	WalletInfo.GoogleCodeSwitch = true

	WalletInfo.EthMinTakeAmount = walletEntity.EthMinTakeAmount
	WalletInfo.TrxMinTakeAmount = walletEntity.TrxMinTakeAmount
	WalletInfo.UsdtMinTakeAmount = walletEntity.UsdtMinTakeAmount
	WalletInfo.Trc20TriggerFeeAmount = walletEntity.Trc20TriggerFeeAmount

	res = &v1.GetWalletInfoRes{
		WalletInfo: WalletInfo,
	}
	return
}

// 检查钱包是否存在
func (s *sWallet) IsWalletExist(ctx context.Context) (res bool, err error) {
	// 使用 Repository 检查钱包是否存在
	res, err = s.walletRepo.IsWalletExist(ctx)
	if err != nil {
		return false, err
	}
	return // 直接返回 Repository 的结果
}

// 获取仪表盘统计
func (s *sWallet) GetDashboardStatistic(ctx context.Context, req *v1.DashboardStatisticReq) (res *v1.DashboardStatisticRes, err error) {
	// 将在后续实现
	// 使用 Repository 获取钱包信息 (虽然这里没用到具体信息，但检查存在性)
	_, err = s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletQueryFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletQueryFailed)
	}
	// walletEntity can be nil if not found, handle if necessary

	// 获取TRC20 USDT余额
	trc20_usdt_balance, err := g.Model("address").Where("type", consts.NetworkTypeTRX).Sum("chain_usdt_balance")
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletGetTrc20UsdtFailed) // Use specific code
	}
	trx_balance, err := g.Model("address").Where("type", consts.NetworkTypeTRX).Sum("chain_coin_balance")
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletGetTrxFailed) // Use specific code
	}

	erc20_usdt_balance, err := g.Model("address").Where("type", consts.NetworkTypeETH).Sum("chain_usdt_balance")
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletGetErc20UsdtFailed) // Use specific code
	}

	eth_balance, err := g.Model("address").Where("type", consts.NetworkTypeETH).Sum("chain_coin_balance")
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletGetEthFailed) // Use specific code
	}

	total_balance := trc20_usdt_balance + erc20_usdt_balance + eth_balance + trx_balance

	return &v1.DashboardStatisticRes{
		Trc20UsdtTotalBalance: fmt.Sprintf("%f", trc20_usdt_balance),
		Erc20UsdtTotalBalance: fmt.Sprintf("%f", erc20_usdt_balance),
		EthTotalBalance:       fmt.Sprintf("%f", eth_balance),
		TrxTotalBalance:       fmt.Sprintf("%f", trx_balance),
		TotalBalance:          fmt.Sprintf("%f", total_balance),
	}, nil
}

// GetPublicInitializationStatus 检查钱包是否已经创建/初始化 (公共接口使用)
func (s *sWallet) GetPublicInitializationStatus(ctx context.Context) (isInitialized bool, err error) {
	// 直接调用已有的 IsWalletExist，该方法不包含认证逻辑，并最终检查 wallets 表
	// sWallet.IsWalletExist 方法内部会调用 s.walletRepo.IsWalletExist(ctx)
	exist, err := s.IsWalletExist(ctx)
	if err != nil {
		// IsWalletExist 内部已经记录了更具体的错误日志，这里可以不重复记录或记录一个更上层的错误
		g.Log().Warningf(ctx, "GetPublicInitializationStatus: calling IsWalletExist failed: %v", err)
		// 遵循项目已有的错误返回风格，这里假设 IsWalletExist 已经包装了错误
		// 或者如果需要，可以再次包装 codes.WrapError(err, codes.CodeWalletQueryFailed)
		return false, err
	}
	return exist, nil
}

// UpdateWalletBalancesByAddress 更新特定地址在特定链上的原生代币和USDT代币的余额
func (s *sWallet) UpdateWalletBalancesByAddress(ctx context.Context, chainSymbol string, targetAddress string, tokenSymbol string) error {
	logPrefix := fmt.Sprintf("[UpdateWalletBalancesByAddress][chain:%s, addr:%s, token:%s]", chainSymbol, targetAddress, tokenSymbol)
	g.Log().Infof(ctx, "%s Start processing", logPrefix)

	// 1. 获取代币属性
	// tokenContractAddress, _, tokenDecimals, err := task.GetTokenAttributes(ctx, chainSymbol, tokenSymbol) // tokenSymbol is used to determine if it's native or USDT
	// if err != nil {
	// 	g.Log().Errorf(ctx, "%s Failed to get token attributes for %s: %v", logPrefix, tokenSymbol, err)
	// 	return gerror.Wrapf(err, "%s: failed to get token attributes for chain %s, token %s", logPrefix, chainSymbol, tokenSymbol)
	// }
	// g.Log().Infof(ctx, "%s Token attributes for %s - Contract: '%s', Decimals: %d", logPrefix, tokenSymbol, tokenContractAddress, tokenDecimals)

	// 2. 初始化余额变量
	var nativeBalance decimal.Decimal
	var usdtBalance decimal.Decimal
	var nativeBalanceFetched bool
	var usdtBalanceFetched bool
	var generalErr error // To store non-critical errors

	// 3. 查询链上余额
	chainSymbolUpper := strings.ToUpper(chainSymbol)
	tokenSymbolUpper := strings.ToUpper(tokenSymbol) // This is the primary token we are interested in

	switch chainSymbolUpper {
	case consts.NetworkTypeETH:
		// 查询原生 ETH 余额 (总是尝试查询原生币，除非明确只查USDT且原生币查询失败)
		// eth.GetETHBalance returns (string, error)
		ethBalanceStr, ethErr := eth.GetETHBalance(targetAddress)
		if ethErr != nil {
			g.Log().Warningf(ctx, "%s Failed to get ETH balance from util for address %s: %v.", logPrefix, targetAddress, ethErr)
			if tokenSymbolUpper == consts.CoinTypeETH || tokenSymbolUpper == "" {
				if generalErr == nil {
					generalErr = gerror.Wrapf(ethErr, "%s: ETH balance query failed via util", logPrefix)
				} else {
					generalErr = gerror.Wrapf(generalErr, "additionally: ETH balance query failed via util: %v", ethErr)
				}
			}
			// nativeBalanceFetched remains false
		} else if ethBalanceStr == "" {
			errMsg := fmt.Sprintf("%s ETH balance string was empty for address %s (implies fetch error)", logPrefix, targetAddress)
			g.Log().Warning(ctx, errMsg)
			if tokenSymbolUpper == consts.CoinTypeETH || tokenSymbolUpper == "" {
				errToWrap := fmt.Errorf(errMsg)
				if generalErr == nil {
					generalErr = errToWrap
				} else {
					generalErr = gerror.Wrapf(generalErr, "additionally: %v", errToWrap)
				}
			}
			// nativeBalanceFetched remains false
		} else {
			var parseErr error
			nativeBalance, parseErr = decimal.NewFromString(ethBalanceStr)
			if parseErr != nil {
				g.Log().Warningf(ctx, "%s Failed to parse ETH balance string '%s' for address %s: %v.", logPrefix, ethBalanceStr, targetAddress, parseErr)
				if tokenSymbolUpper == consts.CoinTypeETH || tokenSymbolUpper == "" {
					errToWrap := gerror.Wrapf(parseErr, "%s: ETH balance string parse error", logPrefix)
					if generalErr == nil {
						generalErr = errToWrap
					} else {
						generalErr = gerror.Wrapf(generalErr, "additionally: %v", errToWrap)
					}
				}
				// nativeBalanceFetched remains false
			} else {
				g.Log().Infof(ctx, "%s Successfully fetched and parsed ETH balance for %s: %s", logPrefix, targetAddress, nativeBalance.String())
				nativeBalanceFetched = true
			}
		}

		// 查询 ERC20 USDT 余额 (仅当请求的是USDT时，或者如果也需要更新USDT)
		// eth.GetErc20UsdtBalance returns (string, error)
		if tokenSymbolUpper == consts.CoinTypeUSDT || tokenSymbolUpper == "" {
			erc20UsdtBalanceStr, erc20Err := eth.GetErc20UsdtBalance(targetAddress)
			if erc20Err != nil {
				g.Log().Warningf(ctx, "%s Failed to get ERC20 USDT balance from util for address %s: %v.", logPrefix, targetAddress, erc20Err)
				if generalErr == nil {
					generalErr = gerror.Wrapf(erc20Err, "%s: USDT (ETH) balance query failed via util", logPrefix)
				} else {
					generalErr = gerror.Wrapf(generalErr, "additionally: USDT (ETH) balance query failed via util: %v", erc20Err)
				}
				// usdtBalanceFetched remains false
			} else if erc20UsdtBalanceStr == "" {
				errMsg := fmt.Sprintf("%s ERC20 USDT balance string was empty for address %s (implies fetch error)", logPrefix, targetAddress)
				g.Log().Warning(ctx, errMsg)
				if tokenSymbolUpper == consts.CoinTypeUSDT || tokenSymbolUpper == "" {
					errToWrap := fmt.Errorf(errMsg)
					if generalErr == nil {
						generalErr = errToWrap
					} else {
						generalErr = gerror.Wrapf(generalErr, "additionally: %v", errToWrap)
					}
				}
				// usdtBalanceFetched remains false
			} else {
				var parseErr error
				usdtBalance, parseErr = decimal.NewFromString(erc20UsdtBalanceStr)
				if parseErr != nil {
					g.Log().Warningf(ctx, "%s Failed to parse ERC20 USDT balance string '%s' for address %s: %v.", logPrefix, erc20UsdtBalanceStr, targetAddress, parseErr)
					if tokenSymbolUpper == consts.CoinTypeUSDT || tokenSymbolUpper == "" {
						errToWrap := gerror.Wrapf(parseErr, "%s: USDT (ETH) balance string parse error", logPrefix)
						if generalErr == nil {
							generalErr = errToWrap
						} else {
							generalErr = gerror.Wrapf(generalErr, "additionally: %v", errToWrap)
						}
					}
					// usdtBalanceFetched remains false
				} else {
					g.Log().Infof(ctx, "%s Successfully fetched and parsed ERC20 USDT balance for %s: %s", logPrefix, targetAddress, usdtBalance.String())
					usdtBalanceFetched = true
				}
			}
		}

	case consts.NetworkTypeTRX:
		// 查询 TRX 和 TRC20 USDT 余额
		trxDecimalBalance, trxErr := tronUtil.GetTRXBalance(ctx, targetAddress)
		if trxErr != nil {
			g.Log().Warningf(ctx, "%s Failed to get TRX and TRC20 balances from util for address %s: %v.", logPrefix, targetAddress, trxErr)
			if tokenSymbolUpper == consts.CoinTypeTRX || tokenSymbolUpper == consts.CoinTypeUSDT || tokenSymbolUpper == "" {
				if generalErr == nil {
					generalErr = gerror.Wrapf(trxErr, "%s: TRX/USDT balance query failed via util", logPrefix)
				} else {
					generalErr = gerror.Wrapf(generalErr, "additionally: TRX/USDT balance query failed via util: %v", trxErr)
				}
			}
			// nativeBalanceFetched and usdtBalanceFetched remain false
		} else {
			// Process TRX balance
			if trxDecimalBalance.IsZero() {
				g.Log().Warningf(ctx, "%s TRX balance is zero or empty for address %s", logPrefix, targetAddress)
				// If we specifically requested TRX, consider this a problem
				if tokenSymbolUpper == consts.CoinTypeTRX {
					errMsg := fmt.Sprintf("%s TRX balance string was empty for address %s", logPrefix, targetAddress)
					errToWrap := fmt.Errorf(errMsg)
					if generalErr == nil {
						generalErr = errToWrap
					} else {
						generalErr = gerror.Wrapf(generalErr, "additionally: %v", errToWrap)
					}
				}
				// Set a default zero balance
				nativeBalance = decimal.Zero
				// Only mark as fetched if we didn't specifically request TRX
				if tokenSymbolUpper != consts.CoinTypeTRX {
					nativeBalanceFetched = true
				}
			} else {
				// Use the decimal value directly
				nativeBalance = trxDecimalBalance
				g.Log().Infof(ctx, "%s Successfully fetched and parsed TRX balance for %s: %s", logPrefix, targetAddress, nativeBalance.String())
				nativeBalanceFetched = true
			}

			// Process USDT balance if needed
			if tokenSymbolUpper == consts.CoinTypeUSDT || tokenSymbolUpper == "" {

				// Check if we have a balance for this contract
				usdtDecimalBalance, err := tronUtil.GetTRC20UsdtTokenBalance(ctx, targetAddress)

				if err != nil { // Changed condition: only check for actual error
					g.Log().Warningf(ctx, "%s Failed to get TRC20 USDT balance for address %s: %v", logPrefix, targetAddress, err) // Updated log message
					// If we specifically requested USDT, consider this a problem
					if tokenSymbolUpper == consts.CoinTypeUSDT {
						errToWrap := gerror.Wrapf(err, "%s: USDT (TRON) balance query failed", logPrefix) // More accurate error wrapping
						if generalErr == nil {
							generalErr = errToWrap
						} else {
							generalErr = gerror.Wrapf(generalErr, "additionally: %v", errToWrap)
						}
					}
					// Set a default zero balance
					usdtBalance = decimal.Zero
					// usdtBalanceFetched remains false because the query failed
				} else { // No error (err == nil), means query was successful, even if balance is 0
					usdtBalance = usdtDecimalBalance
					g.Log().Infof(ctx, "%s Successfully fetched TRC20 USDT balance for %s: %s", logPrefix, targetAddress, usdtBalance.String())
					usdtBalanceFetched = true // Mark as fetched because query was successful
				}
			}
		}
	default:
		errMsg := fmt.Sprintf("%s Unsupported chain symbol: %s", logPrefix, chainSymbol)
		g.Log().Error(ctx, errMsg)
		return gerror.New(errMsg) // Critical error, return immediately
	}

	// If the primary token query failed and it was critical, return now.
	// Or if tokenSymbolUpper is empty (meaning update all available), and all fetches failed, return error.
	criticalQueryFailed := false
	if tokenSymbolUpper == consts.CoinTypeETH && !nativeBalanceFetched {
		criticalQueryFailed = true
	} else if tokenSymbolUpper == consts.CoinTypeTRX && !nativeBalanceFetched {
		criticalQueryFailed = true
	} else if tokenSymbolUpper == consts.CoinTypeUSDT && !usdtBalanceFetched {
		criticalQueryFailed = true
	} else if tokenSymbolUpper == "" && !nativeBalanceFetched && !usdtBalanceFetched { // Case for "update all", if nothing was fetched
		criticalQueryFailed = true
		if generalErr == nil { // If no specific error was set, create a generic one
			generalErr = gerror.Newf("%s: Failed to fetch any balance for address %s when attempting to update all", logPrefix, targetAddress)
		}
	}

	if criticalQueryFailed && generalErr != nil {
		g.Log().Errorf(ctx, "%s Critical balance query failed for token %s (or all): %v", logPrefix, tokenSymbolUpper, generalErr)
		return generalErr
	}

	// 4. 从数据库获取当前地址信息
	addressEntity, err := s.addressRepo.FindByAddress(ctx, targetAddress, chainSymbolUpper)
	if err != nil {
		g.Log().Errorf(ctx, "%s Failed to get address info from DB for %s: %v", logPrefix, targetAddress, err)
		return gerror.Wrapf(err, "%s: DB query for address info failed", logPrefix)
	}
	if addressEntity == nil {
		g.Log().Warningf(ctx, "%s Address info not found for address %s", codes.CodeTaskAddressInfoNotFound.Message(), targetAddress) // Re-use task code
		// This might be a new address not yet in DB, or an issue.
		// Depending on policy, we might want to create it or return an error.
		// For now, log and proceed if any balance was fetched. If not, it's an issue.
		if !nativeBalanceFetched && !usdtBalanceFetched {
			return gerror.Newf("%s: Address %s not found in DB and no balance could be fetched", logPrefix, targetAddress)
		}
		// If we proceed, updates will be skipped as current balances are unknown.
		g.Log().Infof(ctx, "%s Processing finished successfully (address not in DB, no updates performed).", logPrefix)
		return nil
	}

	// 5. 比较并更新余额
	updateData := g.Map{
		dao.Address.Columns().UpdateAt:         gtime.Now(),
		dao.Address.Columns().ChainCoinBalance: nativeBalance.String(),
		dao.Address.Columns().ChainUsdtBalance: usdtBalance.String(),
	}

	dbErr := s.addressRepo.UpdateAddress(ctx, targetAddress, updateData)
	if dbErr != nil {
		g.Log().Errorf(ctx, "%s Failed to update address balances in DB for address ID %d: %v", logPrefix, addressEntity.Id, dbErr)
		return gerror.Wrapf(dbErr, "%s: DB update failed for address ID %d", logPrefix, addressEntity.Id)
	}

	g.Log().Infof(ctx, "%s Processing finished successfully.", logPrefix)
	return nil
}
