package wallet

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"time"

	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/logic/repository" // Import repository
	"wallet-api/internal/model/entity"     // Import entity

	"github.com/gogf/gf/v2/database/gdb" // Keep gdb for Transaction
	// "github.com/gogf/gf/v2/errors/gcode" // Removed unused import
	// "github.com/gogf/gf/v2/errors/gerror" // Removed unused import
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"

	"wallet-api/internal/codes" // Import codes package
	"wallet-api/internal/consts"
)

// 获取矿工费地址
func (s *sWallet) GetFeeAddress(ctx context.Context, req *v1.GetFeeAddressReq) (res *v1.GetFeeAddressRes, err error) {
	// Use Wallet Repository
	walletEntity, err := s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingGetInfoFailed.Message(), err) // Re-use code
		return nil, codes.WrapError(err, codes.CodeWalletSettingGetInfoFailed)
	}
	if walletEntity == nil {
		return nil, codes.NewError(codes.CodeWalletNotFound) // Re-use code
	}

	res = &v1.GetFeeAddressRes{
		EthFeeAddress: walletEntity.EthFeeAddress,
		TrxFeeAddress: walletEntity.TrxFeeAddress,
	}
	return
}

// 获取归集地址
func (s *sWallet) GetCollectAddress(ctx context.Context, req *v1.GetCollectAddressReq) (res *v1.GetCollectAddressRes, err error) {
	// Use Wallet Repository
	walletEntity, err := s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingGetInfoFailed.Message(), err) // Re-use code
		return nil, codes.WrapError(err, codes.CodeWalletSettingGetInfoFailed)
	}
	if walletEntity == nil {
		return nil, codes.NewError(codes.CodeWalletNotFound) // Re-use code
	}

	trx_collect_address := walletEntity.TrxCollectAddress
	eth_collect_address := walletEntity.EthCollectAddress

	res = &v1.GetCollectAddressRes{
		EthCollectAddress: eth_collect_address,
		TrxCollectAddress: trx_collect_address,
	}
	return
}

// 提交归集任务
func (s *sWallet) SubmitCollectTask(ctx context.Context, req *v1.SubmitCollectTaskReq) (res *v1.SubmitCollectTaskRes, err error) {
	// 参数验证
	if err = s.validateCollectTaskRequest(ctx, req); err != nil {
		return nil, err
	}

	// Use Wallet Repository
	walletEntity, err := s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingGetInfoFailed.Message(), err) // Re-use code
		return nil, codes.WrapError(err, codes.CodeWalletSettingGetInfoFailed)
	}
	if walletEntity == nil {
		return nil, codes.NewError(codes.CodeWalletNotFound) // Re-use code
	}

	// 创建任务ID
	taskIdStr := uuid.New().String()
	var taskId int64 = 0

	// 开启事务
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// Use Task Repository to create task (within transaction)
		taskData := g.Map{
			"network_type":  req.Type,
			"token_type":    req.Coin,
			"task_type":     req.TransType,
			"execute_type":  consts.ExecuteTypeManual,
			"task_id":       taskIdStr,
			"task_name":     "Manual Collection", // Use English
			"is_all_amount": req.IsAllAmount,
			"amount":        req.Amount,
			"to_address":    req.ToAddress,
			"from_address":  req.FromAddress,
			"gas_limit":     req.GasLimit,
			"gas_price":     req.GasPrice,
			"fee":           0,
			"total_amount":  0,
			"total_fee":     0,
			"task_status":   consts.TaskStatusPending,
			"fee_limit":     req.FeeLimit,
		}
		// Note: CreateTask in repository might need adjustment if it doesn't handle transactions automatically.
		// Assuming the repository implementation handles the transaction context correctly.
		id, err := s.taskRepo.CreateTask(ctx, taskData) // Use Task Repository
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectCreateTaskFailed.Message(), err)
			return codes.WrapError(err, codes.CodeWalletCollectCreateTaskFailed)
		}

		taskId = id

		// 创建任务地址数据
		taskAddressData, err := s.createTaskAddressData(ctx, id, req, walletEntity) // Pass entity
		if err != nil {
			return err
		}

		if len(taskAddressData) > 0 {
			// 批量插入任务地址数据
			// Use TaskAddress Repository (within transaction)
			// Assuming the repository implementation handles the transaction context correctly.
			err = s.taskAddressRepo.BatchCreateTaskAddresses(ctx, taskAddressData) // Use TaskAddress Repo
			if err != nil {
				g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectCreateTaskAddressFailed.Message(), err)
				return codes.WrapError(err, codes.CodeWalletCollectCreateTaskAddressFailed)
			}
		}

		// Keeping original message structure but using English.
		g.Log().Infof(ctx, "%s: %v", codes.CodeWalletCollectCreateTaskSuccessLog.Message(), g.Map{
			"taskId": taskIdStr,
			"id":     id,
			"type":   req.TransType,
		})

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &v1.SubmitCollectTaskRes{
		Success: true,
		TaskID:  taskId,
	}, nil
}

// 验证归集任务请求参数
func (s *sWallet) validateCollectTaskRequest(_ context.Context, req *v1.SubmitCollectTaskReq) error {
	// 验证交易类型
	if req.TransType != consts.TaskTypeMany2One && req.TransType != consts.TaskTypeOne2Many {
		return codes.NewErrorf(codes.CodeWalletCollectInvalidTransferType, "Invalid transfer type: %s", req.TransType)
	}

	// 验证网络类型
	if req.Type != consts.NetworkTypeETH && req.Type != consts.NetworkTypeTRX {
		return codes.NewErrorf(codes.CodeWalletCollectInvalidNetworkType, "Invalid network type: %s", req.Type)
	}

	// 验证一对多转账不支持全部金额
	if req.TransType == consts.TaskTypeOne2Many && req.IsAllAmount {
		return codes.NewError(codes.CodeWalletCollectOneToManyNoAllAmount)
	}

	// 验证金额
	if !req.IsAllAmount && (req.Amount == "" || req.Amount == "0") {
		return codes.NewError(codes.CodeWalletCollectInvalidAmount)
	}

	// 验证地址
	if req.TransType == consts.TaskTypeMany2One {
		if len(req.ToAddress) == 0 {
			return codes.NewError(codes.CodeWalletCollectMissingToAddress)
		}
	} else {
		if len(req.FromAddress) == 0 {
			return codes.NewError(codes.CodeWalletCollectMissingFromAddress)
		}
	}

	// ETH网络需要验证Gas参数
	if req.Type == consts.NetworkTypeETH {
		if req.GasLimit != "" {
			gasLimit, err := strconv.ParseInt(req.GasLimit, 10, 64)
			if err != nil || gasLimit <= 0 {
				return codes.NewError(codes.CodeWalletCollectInvalidGasLimit)
			}
		}

		if req.GasPrice != "" {
			gasPrice, err := strconv.ParseFloat(req.GasPrice, 64)
			if err != nil || gasPrice <= 0 {
				return codes.NewError(codes.CodeWalletCollectInvalidGasPrice)
			}
		}
	}

	return nil
}

// 创建任务地址数据
func (s *sWallet) createTaskAddressData(_ context.Context, taskId int64, req *v1.SubmitCollectTaskReq, wallet *entity.Wallets) ([]g.Map, error) { // Use entity type
	var taskAddressData = []g.Map{}

	// 确定金额
	amount := req.Amount
	if req.IsAllAmount {
		amount = "0" // 使用0表示全部金额
	}

	g.Log().Debug(context.Background(), "createTaskAddressData", g.Map{
		"wallet": wallet,
	})
	// 多对一归集
	if req.TransType == consts.TaskTypeMany2One {
		var collectAddress string

		// 获取归集地址
		if req.Type == consts.NetworkTypeETH {
			collectAddress = wallet.EthCollectAddress
		} else {
			collectAddress = wallet.TrxCollectAddress
		}

		if collectAddress == "" {
			return nil, codes.NewErrorf(codes.CodeWalletCollectAddressNotSet, "Collection address not set for %s network", req.Type)
		}

		// 创建多对一任务地址数据
		for _, fromAddress := range req.FromAddress {
			if fromAddress == "" {
				continue // 跳过空地址
			}
			taskAddressData = append(taskAddressData, g.Map{
				"task_id":          taskId,                          // 任务ID
				"sender_address":   fromAddress,                     // 发送地址
				"address_type":     consts.AddressTypeTo,            // 地址类型 转入
				"amount":           amount,                          // 金额
				"receiver_address": collectAddress,                  // 接收地址
				"network":          req.Type,                        // 网络
				"coin":             req.Coin,                        // 币种
				"fee":              0,                               // 手续费
				"status":           consts.TaskAddressStatusPending, // 状态 进行中
			})
		}
	} else {
		// 一对多矿工费
		var feeAddress string

		// 获取矿工费地址
		if req.Type == consts.NetworkTypeETH {
			feeAddress = wallet.EthFeeAddress
		} else {
			feeAddress = wallet.TrxFeeAddress
		}

		if feeAddress == "" {
			return nil, codes.NewErrorf(codes.CodeWalletCollectFeeAddressNotSet, "Fee address not set for %s network", req.Type)
		}

		// 创建一对多任务地址数据
		for _, toAddress := range req.ToAddress {
			if toAddress == "" {
				continue // 跳过空地址
			}
			taskAddressData = append(taskAddressData, g.Map{
				"task_id":          taskId,                          // 任务ID
				"receiver_address": toAddress,                       // 地址
				"address_type":     consts.AddressTypeFrom,          // 地址类型 转出
				"sender_address":   feeAddress,                      // 发送地址
				"amount":           amount,                          // 金额
				"network":          req.Type,                        // 网络
				"coin":             req.Coin,                        // 币种
				"fee":              0,                               // 手续费
				"status":           consts.TaskAddressStatusPending, // 状态 进行中
			})
		}
	}

	return taskAddressData, nil
}

// 归集任务列表
func (s *sWallet) CollectTaskList(ctx context.Context, req *v1.CollectTaskListReq) (res *v1.CollectTaskListRes, err error) {
	return s.collectTaskListInternal(ctx, req, false)
}

// 带导出标志的内部归集任务列表方法
func (s *sWallet) collectTaskListInternal(ctx context.Context, req *v1.CollectTaskListReq, isExport bool) (res *v1.CollectTaskListRes, err error) {
	// Use Task Repository to list tasks
	options := repository.TaskQueryOptions{
		Page:        req.Page,
		Limit:       req.Limit,
		Type:        req.Type,
		Status:      req.Status,
		SortField:   req.SortField,
		SortOrder:   req.SortOrder,
		TaskType:    req.TaskType,
		ExecuteType: req.ExecuteType,
	}

	// Parse date range
	var startTime, endTime *time.Time
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			st, err1 := time.ParseInLocation("2006-01-02", dateRange[0], time.Local)
			et, err2 := time.ParseInLocation("2006-01-02", dateRange[1], time.Local)
			if err1 != nil || err2 != nil {
				g.Log().Errorf(ctx, "%s: %v, %v", codes.CodeWalletCollectTimeFormatFailed.Message(), err1, err2)
				return nil, codes.WrapError(err1, codes.CodeWalletCollectTimeFormatFailed) // Wrap the first error
			}
			// Adjust end time to include the whole day
			et = et.Add(24*time.Hour - 1*time.Nanosecond)
			startTime = &st
			endTime = &et
			options.DateStart = startTime
			options.DateEnd = endTime
		}
	}

	var taskEntities []*entity.Tasks
	var total_size int

	if isExport {
		taskEntities, err = s.taskRepo.FindAllTasks(ctx, options)
		total_size = len(taskEntities) // For export, total size is the number found
	} else {
		taskEntities, total_size, err = s.taskRepo.ListTasks(ctx, options)
	}

	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectGetTaskListFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectGetTaskListFailed)
	}

	// Convert []*entity.Tasks to []v1.TaskList
	taskList := make([]v1.TaskList, 0, len(taskEntities))
	taskIds := make([]int64, 0, len(taskEntities)) // Use int64 slice
	for _, taskEntity := range taskEntities {
		taskIds = append(taskIds, int64(taskEntity.Id)) // Convert int to int64 for append
		// Manual conversion or gconv.Struct
		info := v1.TaskList{
			Id:          int64(taskEntity.Id), // Convert int to int64
			NetworkType: taskEntity.NetworkType,
			TokenType:   taskEntity.TokenType,
			TaskType:    taskEntity.TaskType,
			ExecuteType: taskEntity.ExecuteType,
			TaskName:    taskEntity.TaskName,
			// TaskID:       taskEntity.TaskId, // Mismatch: entity.TaskId is string, v1.TaskList.TaskID is int64. Skip for now.
			Amount:      fmt.Sprintf("%f", taskEntity.Amount),      // Convert float64
			IsAllAmount: taskEntity.IsAllAmount == 1,               // Convert int to bool
			TotalAmount: fmt.Sprintf("%f", taskEntity.TotalAmount), // Convert float64
			TotalFee:    fmt.Sprintf("%f", taskEntity.TotalFee),    // Convert float64
			TaskStatus:  taskEntity.TaskStatus,
			CreateAt:    formatGtimePtr(taskEntity.CreateAt),
			UpdateAt:    formatGtimePtr(taskEntity.UpdateAt),
			GasLimit:    strconv.Itoa(taskEntity.GasLimit),      // Convert int to string
			GasPrice:    fmt.Sprintf("%f", taskEntity.GasPrice), // Convert float64 to string
			FromAddress: taskEntity.FromAddress,
			ToAddress:   taskEntity.ToAddress,
			// FeeLimit:     taskEntity.FeeLimit, // Field does not exist in entity.Tasks or v1.TaskList
			// Counts will be filled below
		}
		taskList = append(taskList, info)
	}

	// Get address counts for each task
	if len(taskIds) > 0 {
		// Use TaskAddress Repository to get counts for multiple tasks efficiently
		// This might require a new method in the repository or iterate GetTaskAddressCounts
		// For simplicity, let's iterate for now, but a batch method is better.
		for i := range taskList {
			taskId := taskList[i].Id
			counts, err := s.taskAddressRepo.GetTaskAddressCounts(ctx, taskId)
			if err != nil {
				g.Log().Warningf(ctx, "%s for task %d: %v", codes.CodeWalletCollectGetTaskStatsFailedLog.Message(), taskId, err)
				// Continue processing other tasks, maybe set counts to -1 or 0
				continue
			}
			if counts != nil {
				taskList[i].SuccessAddressCount = counts.SuccessCount
				taskList[i].FailedAddressCount = counts.FailedCount
				taskList[i].PendingAddressCount = counts.PendingCount
				taskList[i].CanceledAddressCount = counts.CanceledCount
			}
		}
	}

	// 构建返回结果
	res = &v1.CollectTaskListRes{
		Page: v1.Page{
			Current:  req.Page,
			PageSize: req.Limit,
			Total:    total_size,
		},
		List: taskList,
	}
	return res, nil
}

// 获取任务记录统计
func (s *sWallet) CollectRecordStatistic(ctx context.Context, req *v1.CollectRecordStatisticReq) (res *v1.CollectRecordStatisticRes, err error) {

	// Use Task Repository to find all relevant tasks for statistics
	options := repository.TaskQueryOptions{
		Type:        req.Type,
		Status:      req.Status,
		SortField:   req.SortField, // Sorting might not be necessary for stats
		SortOrder:   req.SortOrder,
		TaskType:    req.TaskType,
		ExecuteType: req.ExecuteType,
	}
	// Parse date range (similar to CollectTaskList)
	var startTime, endTime *time.Time
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			st, err1 := time.ParseInLocation("2006-01-02", dateRange[0], time.Local)
			et, err2 := time.ParseInLocation("2006-01-02", dateRange[1], time.Local)
			if err1 != nil || err2 != nil {
				g.Log().Errorf(ctx, "%s: %v, %v", codes.CodeWalletCollectTimeFormatFailed.Message(), err1, err2)
				return nil, codes.WrapError(err1, codes.CodeWalletCollectTimeFormatFailed) // Wrap the first error
			}
			et = et.Add(24*time.Hour - 1*time.Nanosecond)
			startTime = &st
			endTime = &et
			options.DateStart = startTime
			options.DateEnd = endTime
		}
	}

	taskEntities, err := s.taskRepo.FindAllTasks(ctx, options)
	if err != nil {
		g.Log().Errorf(ctx, "%s (for stats): %v", codes.CodeWalletCollectGetTaskListFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectGetTaskListFailed)
	}

	// Aggregate counts from all found tasks
	taskIds := make([]int64, 0, len(taskEntities))
	for _, taskEntity := range taskEntities { // Renamed variable for clarity
		taskIds = append(taskIds, int64(taskEntity.Id)) // Cast int to int64
	}

	completedTaskCount := 0
	processingTaskCount := 0 // Note: GetTaskAddressCounts combines pending and processing
	failedTaskCount := 0
	pendingTaskCount := 0
	canceledTaskCount := 0

	if len(taskIds) > 0 {
		// Ideally, have a repository method to get counts for multiple task IDs at once.
		// Simulating by iterating for now.
		for _, taskId := range taskIds {
			counts, err := s.taskAddressRepo.GetTaskAddressCounts(ctx, taskId)
			if err != nil {
				g.Log().Warningf(ctx, "%s for task %d (for overall stats): %v", codes.CodeWalletCollectGetTaskStatsFailedLog.Message(), taskId, err)
				continue // Skip this task's counts if error occurs
			}
			if counts != nil {
				completedTaskCount += counts.SuccessCount
				failedTaskCount += counts.FailedCount
				pendingTaskCount += counts.PendingCount // Includes processing
				canceledTaskCount += counts.CanceledCount
			}
		}
	}

	return &v1.CollectRecordStatisticRes{
		CompletedTaskCount:  completedTaskCount,
		ProcessingTaskCount: processingTaskCount,
		FailedTaskCount:     failedTaskCount,
		PendingTaskCount:    pendingTaskCount,
		CanceledTaskCount:   canceledTaskCount,
	}, nil
}

// 导出任务记录
func (s *sWallet) ExportTaskRecord(ctx context.Context, req *v1.ExportTaskRecordReq) (res *v1.ExportTaskRecordRes, err error) {
	// 使用TaskRecordReq结构体传递参数
	taskListReq, err := s.collectTaskListInternal(ctx, &v1.CollectTaskListReq{
		TaskRecordReq: v1.TaskRecordReq{
			// Page:        req.Page, // Export all, so no pagination needed here
			// Limit:       req.Limit,
			// Address:     req.Address,
			Type:        req.Type,
			SortField:   req.SortField,
			SortOrder:   req.SortOrder,
			Status:      req.Status,
			Coin:        req.Coin,
			DateRange:   req.DateRange,
			TaskType:    req.TaskType,
			ExecuteType: req.ExecuteType,
		},
	}, true) // Set isExport to true
	if err != nil {
		return nil, err
	}
	// Keeping original message structure but using English.
	g.Log().Info(ctx, "Exporting task records:", taskListReq)

	result := taskListReq.List

	// 生成CSV数据
	buffer := bytes.NewBuffer(nil)
	writer := csv.NewWriter(buffer)

	// type TaskList struct { ... } // Schema definition commented out for brevity

	if err := writer.Write([]string{"ID", "网络", "币种", "任务类型", "执行类型", "任务名称", "金额", "是否", "总金额", "总手续费", "状态", "矿工费限制", "矿工费价格", "转出地址", "转入地址", "成功数量", "失败数量", "待执行数量", "取消数量", "创建时间", "更新时间"}); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvHeaderFailed)
	}

	for _, address := range result {
		if err := writer.Write([]string{
			strconv.FormatInt(address.Id, 10),
			address.NetworkType,
			address.TokenType,
			address.TaskType,
			address.ExecuteType,
			address.TaskName,
			address.Amount,
			strconv.FormatBool(address.IsAllAmount),
			address.TotalAmount,
			address.TotalFee,
			address.TaskStatus,
			address.GasLimit,
			address.GasPrice,
			address.FromAddress,
			address.ToAddress,
			strconv.Itoa(address.SuccessAddressCount),
			strconv.Itoa(address.FailedAddressCount),
			strconv.Itoa(address.PendingAddressCount),
			strconv.Itoa(address.CanceledAddressCount),
			address.CreateAt,
			address.UpdateAt,
		}); err != nil {
			// 记录错误并继续尝试写入其他行，或者直接返回错误
			g.Log().Warningf(ctx, "%s: %v, Task ID: %d", codes.CodeWalletCollectCsvRowFailedLog.Message(), err, address.Id)
			// return nil, gerror.Wrapf(err, "写入CSV行失败: 任务ID %d", address.Id)
		}
	}

	if err := writer.Error(); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvProcessError)
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		// Flush之后也可能报告错误
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvFlushFailed)
	}

	//生成文件名根据币种
	fileName := "task_record_" + time.Now().Format("2006-01-02_15-04-05") + ".csv" // Use underscore for better compatibility

	// 设置响应头
	r := g.RequestFromCtx(ctx)
	r.Response.Header().Set("Content-Type", "text/csv")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+fileName)

	// 写入CSV数据并退出
	r.Response.Write(buffer.Bytes())
	r.Response.Request.ExitAll()

	return nil, nil // Return nil as response is handled via ExitAll
}

// 获取任务地址记录 内部函数
func (s *sWallet) collectTaskAddressInternal(ctx context.Context, req *v1.TaskAddressRecordReq, isExport bool) (res *v1.TaskAddressRecordRes, err error) {

	taskid := req.TaskID

	//查询task表
	// Use Task Repository
	taskEntity, err := s.taskRepo.FindTaskByID(ctx, taskid)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectGetTaskFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectGetTaskFailed)
	}
	if taskEntity == nil {
		return nil, codes.NewError(codes.CodeWalletCollectTaskNotFound)
	}
	address := req.Address
	sortField := req.SortField
	sortOrder := req.SortOrder
	status := req.Status

	// Use TaskAddress Repository
	options := repository.TaskAddressQueryOptions{
		Page:      req.Page,
		Limit:     req.Limit,
		TaskID:    taskid,
		Address:   address,
		Status:    status,
		SortField: sortField,
		SortOrder: sortOrder,
	}

	var taskAddressEntities []*entity.TaskAddress
	var total_size int

	if isExport {
		taskAddressEntities, err = s.taskAddressRepo.FindAllTaskAddresses(ctx, options)
		total_size = len(taskAddressEntities)
	} else {
		taskAddressEntities, total_size, err = s.taskAddressRepo.ListTaskAddresses(ctx, options)
	}

	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectGetTaskAddressListFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectGetTaskAddressListFailed)
	}

	// 将结果转换为TaskList类型
	// Convert []*entity.TaskAddress to []v1.TaskAddressRecord
	taskAddressList := make([]v1.TaskAddressRecord, 0, len(taskAddressEntities))
	for _, taskAddrEntity := range taskAddressEntities {
		// Manual conversion or gconv.Struct
		record := v1.TaskAddressRecord{
			Id:              int64(taskAddrEntity.Id),     // Convert int to int64
			TaskID:          int64(taskAddrEntity.TaskId), // Convert int to int64
			SenderAddress:   taskAddrEntity.SenderAddress,
			ReceiverAddress: taskAddrEntity.ReceiverAddress,
			// AddressType:     taskAddrEntity.AddressType, // Field does not exist
			Amount:  fmt.Sprintf("%f", taskAddrEntity.Amount), // Convert float64
			Fee:     fmt.Sprintf("%f", taskAddrEntity.Fee),    // Convert float64
			Network: taskAddrEntity.Network,
			// Coin:            taskAddrEntity.Coin, // Field does not exist
			Status:          taskAddrEntity.Status,
			FailReason:      taskAddrEntity.FailReason,
			TransactionHash: taskAddrEntity.TransactionHash,
			CreateAt:        formatGtimePtr(taskAddrEntity.CreateAt),
			UpdateAt:        formatGtimePtr(taskAddrEntity.UpdateAt),
			// TaskInfo will be filled below
		}
		taskAddressList = append(taskAddressList, record)
	}

	//获取taskAddressList 所有id
	taskAddressIds := make([]interface{}, 0, len(taskAddressList))
	existingIds := make(map[int64]bool)
	for _, item := range taskAddressList {
		if !existingIds[item.TaskID] {
			taskAddressIds = append(taskAddressIds, item.TaskID)
			existingIds[item.TaskID] = true
		}
	}

	//查询task 信息
	// Use Task Repository to get task info (already fetched above, reuse taskEntity)
	// No need to query again if taskid is the primary filter
	// If filtering by other criteria, might need a FindTasksByIds method
	// For now, assume we only need the single taskEntity fetched earlier

	//将taskInfo 添加到taskAddressList中
	// Add task info to each record
	// Add task info to each record
	for i := range taskAddressList {
		if taskAddressList[i].TaskID == int64(taskEntity.Id) { // Convert taskEntity.Id to int64 for comparison
			taskAddressList[i].TaskInfo = v1.TaskList{
				Id:          int64(taskEntity.Id), // Convert int to int64
				NetworkType: taskEntity.NetworkType,
				TokenType:   taskEntity.TokenType,
				TaskType:    taskEntity.TaskType,
				TaskName:    taskEntity.TaskName,
				// Add other relevant fields from taskEntity if needed in TaskInfo
			}
		}
	}

	return &v1.TaskAddressRecordRes{
		Page: v1.Page{
			Current:  req.Page,
			PageSize: req.Limit,
			Total:    total_size,
		},
		List: taskAddressList,
	}, nil
}

// 获取任务地址记录
func (s *sWallet) TaskAddressRecord(ctx context.Context, req *v1.TaskAddressRecordReq) (res *v1.TaskAddressRecordRes, err error) {
	// 空实现
	return s.collectTaskAddressInternal(ctx, req, false)
}

// 导出任务地址记录
func (s *sWallet) ExportTaskAddressRecord(ctx context.Context, req *v1.ExportTaskAddressRecordReq) (res *v1.ExportTaskAddressRecordRes, err error) {
	taskAddressRes, err := s.collectTaskAddressInternal(ctx, &v1.TaskAddressRecordReq{
		TaskAddress: v1.TaskAddress{
			TaskID:    req.TaskID,
			Page:      1,
			Limit:     10000, // Export all matching records
			Address:   req.Address,
			SortField: req.SortField,
			SortOrder: req.SortOrder,
			Status:    req.Status,
		},
	}, true) // Set isExport to true
	if err != nil {
		return nil, err
	}

	// 生成CSV数据
	buffer := bytes.NewBuffer(nil)
	writer := csv.NewWriter(buffer)

	// 写入表头
	if err := writer.Write([]string{"ID", "任务ID", "转出地址", "转入地址", "金额", "手续费", "网络", "状态", "失败原因", "交易哈希", "创建时间", "更新时间"}); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvHeaderFailed) // Use specific code
	}

	// 写入数据
	for _, item := range taskAddressRes.List {
		if err := writer.Write([]string{
			strconv.FormatInt(item.Id, 10),
			strconv.FormatInt(item.TaskID, 10),
			item.SenderAddress,
			item.ReceiverAddress,
			item.Amount,
			item.Fee,
			item.Network,
			item.Status,
			item.FailReason,
			item.TransactionHash,
			item.CreateAt,
			item.UpdateAt,
		}); err != nil { // 添加缺失的错误检查
			// 记录错误并继续尝试写入其他行，或者直接返回错误
			g.Log().Warningf(ctx, "%s: %v, Address ID: %d", codes.CodeWalletCollectCsvRowFailedLog.Message(), err, item.Id) // Use specific code
			// return nil, gerror.Wrapf(err, "写入CSV行失败 (ExportTaskAddressRecord): 地址ID %d", item.Id)
		}
	}

	if err := writer.Error(); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvProcessError) // Use specific code
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvFlushFailed) // Use specific code
	}

	// 生成文件名
	fileName := fmt.Sprintf("task_address_record_%d_%s.csv", req.TaskID, time.Now().Format("20060102150405"))

	// 设置响应头
	r := g.RequestFromCtx(ctx)
	r.Response.Header().Set("Content-Type", "text/csv")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+fileName)

	// 写入CSV数据并退出
	r.Response.Write(buffer.Bytes())
	r.Response.Request.ExitAll()

	return &v1.ExportTaskAddressRecordRes{Success: true}, nil
}

// 获取任务地址统计
func (s *sWallet) TaskAddressStatistic(ctx context.Context, req *v1.TaskAddressStatisticReq) (res *v1.TaskAddressStatisticRes, err error) {

	// type TaskAddressStatisticRes struct { ... } // Schema definition commented out

	// Use Task Repository
	taskEntity, err := s.taskRepo.FindTaskByID(ctx, req.TaskID)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectGetTaskFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectGetTaskFailed)
	}
	if taskEntity == nil {
		return nil, codes.NewError(codes.CodeWalletCollectTaskNotFound)
	}

	// Use TaskAddress Repository
	taskAddressEntities, err := s.taskAddressRepo.FindByTaskID(ctx, req.TaskID)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectGetTaskAddressListFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectGetTaskAddressListFailed)
	}
	// No need to check for nil slice, len(taskAddressEntities) == 0 is sufficient

	var totalAmount = big.NewFloat(0)
	var totalFee = big.NewFloat(0)
	var transferCount = len(taskAddressEntities)
	var completedCount = 0
	var processingCount = 0
	var failedCount = 0
	var pendingCount = 0
	var canceledCount = 0

	for _, item := range taskAddressEntities {
		// Use entity fields directly
		amountFloat, _ := new(big.Float).SetString(fmt.Sprintf("%f", item.Amount)) // Convert float64 to big.Float
		feeFloat, _ := new(big.Float).SetString(fmt.Sprintf("%f", item.Fee))       // Convert float64 to big.Float
		if amountFloat != nil {
			totalAmount = totalAmount.Add(totalAmount, amountFloat)
		}
		if feeFloat != nil {
			totalFee = totalFee.Add(totalFee, feeFloat)
		}

		switch item.Status {
		case consts.TaskAddressStatusCompleted:
			completedCount++
		case consts.TaskAddressStatusProcessing:
			processingCount++
		case consts.TaskAddressStatusFailed:
			failedCount++
		case consts.TaskAddressStatusPending:
			pendingCount++
		case consts.TaskAddressStatusCanceled:
			canceledCount++
		}
	}

	// 空实现
	return &v1.TaskAddressStatisticRes{
		TotalAmount:     totalAmount.String(),
		TotalFee:        totalFee.String(),
		NetworkType:     taskEntity.NetworkType,
		CoinType:        taskEntity.TokenType,
		TransferCount:   transferCount,
		CompletedCount:  completedCount,
		ProcessingCount: processingCount,
		FailedCount:     failedCount,
		PendingCount:    pendingCount,
		CanceledCount:   canceledCount,
	}, nil
}

// 获取交易记录
func (s *sWallet) GetTransactionRecord(ctx context.Context, req *v1.GetTransactionRecordReq) (res *v1.GetTransactionRecordRes, err error) {
	// 空实现
	return s.getTransactionRecordInternal(ctx, req, false)
}

// 交易记录内部函数
func (s *sWallet) getTransactionRecordInternal(ctx context.Context, req *v1.GetTransactionRecordReq, isExport bool) (res *v1.GetTransactionRecordRes, err error) {

	// Use Transaction Repository
	options := repository.TransactionQueryOptions{
		Page:      req.Page,
		Limit:     req.Limit,
		Address:   req.Address,
		Type:      req.Type,
		Chain:     req.Chain,
		Status:    req.Status,
		Coin:      req.Coin,
		SortField: req.SortField,
		SortOrder: req.SortOrder,
	}

	// Parse date range
	var startTime, endTime *time.Time
	if req.DateRange != "" {
		dateRange := strings.Split(req.DateRange, ",")
		if len(dateRange) == 2 {
			st, err1 := time.ParseInLocation("2006-01-02", dateRange[0], time.Local)
			et, err2 := time.ParseInLocation("2006-01-02", dateRange[1], time.Local)
			if err1 != nil || err2 != nil {
				g.Log().Errorf(ctx, "%s: %v, %v", codes.CodeWalletCollectTimeFormatFailed.Message(), err1, err2)
				return nil, codes.WrapError(err1, codes.CodeWalletCollectTimeFormatFailed) // Wrap the first error
			}
			et = et.Add(24*time.Hour - 1*time.Nanosecond)
			startTime = &st
			endTime = &et
			options.DateStart = startTime
			options.DateEnd = endTime
		}
	}
	var transactionEntities []*entity.Transactions
	var total_size int

	if isExport {
		transactionEntities, err = s.transactionRepo.FindAllTransactions(ctx, options)
		total_size = len(transactionEntities)
	} else {
		transactionEntities, total_size, err = s.transactionRepo.ListTransactions(ctx, options)
	}

	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectGetTxRecordFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectGetTxRecordFailed)
	}

	// Convert []*entity.Transactions to []v1.Transaction
	transactionRecordList := make([]v1.Transaction, 0, len(transactionEntities))
	for _, txEntity := range transactionEntities {
		// Manual conversion or gconv.Struct
		record := v1.Transaction{
			Id:                txEntity.Id, // Type matches (int)
			TransactionStatus: txEntity.TransactionStatus,
			Chain:             txEntity.Chain,
			IsToken:           txEntity.IsToken == 1, // Convert int to bool
			ContractAddress:   txEntity.ContractAddress,
			TokenName:         txEntity.TokenName, // Use TokenName from entity
			SenderAddress:     txEntity.SenderAddress,
			ReceiverAddress:   txEntity.ReceiverAddress,
			TransactionTime:   formatGtimePtr(txEntity.TransactionTime), // Convert *gtime.Time
			Amount:            txEntity.Amount,                          // Type matches (string)
			TransactionType:   txEntity.TransactionType,
			TransactionFee:    txEntity.TransactionFee,   // Type matches (string)
			BlockNumber:       int(txEntity.BlockNumber), // Convert int64 to int
			BlockHash:         txEntity.BlockHash,
			Confirmations:     txEntity.Confirmations, // Type matches (int)
			Notes:             txEntity.Notes,
			CreateAt:          formatGtimePtr(txEntity.CreateAt),
			UpdateAt:          formatGtimePtr(txEntity.UpdateAt),
			NetFee:            txEntity.NetFee,    // Type matches (string)
			EnergyFee:         txEntity.EnergyFee, // Type matches (string)
			EffectiveGasPrice: txEntity.EffectiveGasPrice,
			GasUsed:           txEntity.GasUsed,
			CumulativeGasUsed: txEntity.CumulativeGasUsed,
			TransactionHash:   txEntity.TransactionHash,
			// TokenSymbol is in entity but not in v1.Transaction
		}
		transactionRecordList = append(transactionRecordList, record)
	}

	return &v1.GetTransactionRecordRes{
		Page: v1.Page{
			Current:  req.Page,
			PageSize: req.Limit,
			Total:    total_size,
		},
		List: transactionRecordList,
	}, nil

}

// 导出交易记录
func (s *sWallet) ExportTransactionRecord(ctx context.Context, req *v1.ExportTransactionRecordReq) (res *v1.ExportTransactionRecordRes, err error) {
	// 空实现
	transactionRecordList, err := s.getTransactionRecordInternal(ctx,
		&v1.GetTransactionRecordReq{
			TransactionType: v1.TransactionType{
				Page:      1,     // Export all, so page 1
				Limit:     10000, // Set a large limit for export
				Address:   req.Address,
				Type:      req.Type,
				Chain:     req.Chain,
				SortField: req.SortField,
				SortOrder: req.SortOrder,
				Status:    req.Status,
				Coin:      req.Coin,
				DateRange: req.DateRange,
				// TransactionType: req.TransactionType,
			},
		},
		true, // Set isExport to true
	)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectExportTxRecordFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectExportTxRecordFailed)
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "%s: %v", codes.CodeWalletCollectExportTxRecordSuccessLog.Message(), transactionRecordList)

	result := transactionRecordList.List

	// 生成CSV数据
	buffer := bytes.NewBuffer(nil)
	writer := csv.NewWriter(buffer)

	if err := writer.Write([]string{"ID", "交易状态", "交易哈希", "链", "是否代币", "合约地址", "代币名字", "发送方地址", "接收方地址",
		"交易时间", "交易金额",
		"交易类型", "交易手续费（eth链）", "区块号",
		"创建时间", "更新时间"}); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvHeaderFailed) // Use specific code
	}

	//闭包函数
	formatTransactionType := func(transactionType string) string {
		switch transactionType {
		case consts.TransactionTypeDeposit:
			return "Deposit" // Use English
		case consts.TransactionTypeWithdraw:
			return "Withdraw" // Use English
		case consts.TransactionTypeTransfer:
			return "Transfer" // Use English
		case consts.TransactionTypeMinerFee:
			return "Miner Fee" // Use English
		default:
			return "Unknown" // Use English
		}
	}

	for _, transaction := range result {
		if err := writer.Write([]string{ // 添加缺失的错误检查
			strconv.FormatInt(int64(transaction.Id), 10),
			transaction.TransactionStatus,
			transaction.TransactionHash,
			transaction.Chain,
			strconv.FormatBool(transaction.IsToken),
			transaction.ContractAddress,
			transaction.TokenName,
			transaction.SenderAddress,
			transaction.ReceiverAddress,
			transaction.TransactionTime,
			transaction.Amount,
			formatTransactionType(transaction.TransactionType),
			transaction.TransactionFee,
			strconv.FormatInt(int64(transaction.BlockNumber), 10),
			transaction.CreateAt,
			transaction.UpdateAt,
		}); err != nil {
			// 记录错误并继续尝试写入其他行，或者直接返回错误
			g.Log().Warningf(ctx, "%s: %v, Transaction ID: %d", codes.CodeWalletCollectCsvRowFailedLog.Message(), err, transaction.Id) // Use specific code
			// return nil, gerror.Wrapf(err, "写入CSV行失败 (ExportTransactionRecord): 交易ID %d", transaction.Id)
		}
	}

	if err := writer.Error(); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvProcessError) // Use specific code
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvFlushFailed) // Use specific code
	}

	//生成文件名根据币种
	fileName := "transaction_record_" + time.Now().Format("2006-01-02_15-04-05") + ".csv" // Use underscore

	// 设置响应头
	r := g.RequestFromCtx(ctx)
	r.Response.Header().Set("Content-Type", "text/csv")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+fileName)

	// 写入CSV数据并退出
	r.Response.Write(buffer.Bytes())
	r.Response.Request.ExitAll()

	return nil, nil
}

// 获取交易记录统计
func (s *sWallet) GetTransactionRecordStatistic(ctx context.Context, req *v1.GetTransactionRecordStatisticReq) (res *v1.GetTransactionRecordStatisticRes, err error) {

	// Use Transaction Repository to get statistics
	stats, err := s.transactionRepo.GetTransactionStatistics(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCollectGetTxStatsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectGetTxStatsFailed)
	}
	if stats == nil {
		// Handle case where stats might be nil (e.g., no transactions)
		stats = &repository.TransactionStatistic{} // Initialize with zero values
	}

	// 空实现
	return &v1.GetTransactionRecordStatisticRes{
		DepositOrderCount:        stats.DepositOrderCount,
		WithdrawOrderCount:       stats.WithdrawOrderCount,
		MinerFeeOrderCount:       stats.MinerFeeOrderCount,
		PendingConfirmationCount: stats.PendingConfirmationCount,

		TotalUsdtDepositAmount:  fmt.Sprintf("%.2f", stats.TotalUsdtDepositAmount),
		TotalUsdtWithdrawAmount: fmt.Sprintf("%.2f", stats.TotalUsdtWithdrawAmount),

		TotalTrxDepositAmount:  fmt.Sprintf("%.2f", stats.TotalTrxDepositAmount),
		TotalTrxWithdrawAmount: fmt.Sprintf("%.2f", stats.TotalTrxWithdrawAmount),

		TotalEthDepositAmount:  fmt.Sprintf("%.2f", stats.TotalEthDepositAmount),
		TotalEthWithdrawAmount: fmt.Sprintf("%.2f", stats.TotalEthWithdrawAmount),
	}, nil
}
