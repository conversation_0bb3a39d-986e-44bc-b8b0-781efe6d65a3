package wallet

import (
	"context"

	v1 "wallet-api/api/wallet/v1"
	// "wallet-api/internal/model/entity" // Import entity
	eth "wallet-api/internal/utility/crypto/eth"
	"wallet-api/internal/utility/crypto/tron"

	// "github.com/gogf/gf/v2/errors/gcode" // Removed unused import
	// "github.com/gogf/gf/v2/errors/gerror" // Removed unused import
	"github.com/gogf/gf/v2/frame/g"

	"wallet-api/internal/codes" // Import codes package
)

// 基本设置
func (s *sWallet) BaseSetting(ctx context.Context, req *v1.BaseSettingReq) (res *v1.BaseSettingRes, err error) {
	// 获取钱包实体
	walletEntity, err := s._getWalletEntity(ctx)
	if err != nil {
		return nil, err
	}
	// 验证密码 (简单比较)
	if err = s._validatePasswordSimple(ctx, walletEntity, req.Password); err != nil {
		return nil, err
	}

	// 验证谷歌验证码
	if err = s._validateGoogleCode(ctx, walletEntity, req.GoogleCode, req.Password); err != nil {
		return nil, err
	}

	// Keeping original message structure but using English.
	g.Log().Info(ctx, "Request parameters:", req)
	trx_collect_address := req.TrxCollectAddress
	if trx_collect_address != "" {
		if !tron.ValidateAddress(trx_collect_address) {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrxAddressFormat)
		}
	}
	eth_collect_address := req.EthCollectAddress
	if eth_collect_address != "" {
		if !eth.ValidateAddress(eth_collect_address) {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidEthAddressFormat)
		}
	}
	trx_fee_private_key := req.TrxFeePrivateKey
	var trx_fee_address string
	if trx_fee_private_key != "" {
		if !tron.ValidatePrivateKey(trx_fee_private_key) {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrxKeyFormat)
		}
		trx_fee_address, err = tron.GetTronFeeAddress(trx_fee_private_key)
		if err != nil {
			return nil, codes.WrapError(err, codes.CodeWalletSettingTrxAddressConvertFailed)
		}

	}
	eth_fee_private_key := req.EthFeePrivateKey
	var eth_fee_address string
	if eth_fee_private_key != "" {
		if !eth.ValidatePrivateKey(eth_fee_private_key) {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidEthKeyFormat)
		}
		eth_fee_address, err = eth.GetEthFeeAddress(eth_fee_private_key)
		if err != nil {
			return nil, codes.WrapError(err, codes.CodeWalletSettingEthAddressConvertFailed)
		}
	}

	update_data := g.Map{}

	old_trx_collect_address := walletEntity.TrxCollectAddress
	old_eth_collect_address := walletEntity.EthCollectAddress
	old_trx_fee_private_key := walletEntity.TrxFeePrivateKey // Assuming field exists
	old_eth_fee_private_key := walletEntity.EthFeePrivateKey // Assuming field exists

	if trx_collect_address != old_trx_collect_address && trx_collect_address != "" {
		update_data["trx_collect_address"] = trx_collect_address
	}
	if eth_collect_address != old_eth_collect_address && eth_collect_address != "" {
		update_data["eth_collect_address"] = eth_collect_address
	}
	if trx_fee_private_key != old_trx_fee_private_key && trx_fee_private_key != "" {
		update_data["trx_fee_private_key"] = trx_fee_private_key
		update_data["trx_fee_address"] = trx_fee_address
	}
	if eth_fee_private_key != old_eth_fee_private_key && eth_fee_private_key != "" {
		update_data["eth_fee_private_key"] = eth_fee_private_key
		update_data["eth_fee_address"] = eth_fee_address
	}

	// eth矿工费 模式 自动 ，手动
	// EthFeeMode int `json:"eth_fee_mode" dc:"eth矿工费模式"`

	// //eth 矿工费最大值
	// EthFeeMax float64 `json:"eth_fee_max" dc:"eth 矿工费最大值"`
	// //gas price
	// EthGasPrice int64 `json:"eth_gas_price" dc:"eth gas price"`
	// //gas limit
	// EthGasLimit int64 `json:"eth_gas_limit" dc:"eth gas limit"`

	// //trx 矿工费最大值
	// TrxFeeMax int64 `json:"trx_fee_max" dc:"trx 矿工费最大值"`

	if req.EthFeeMode != walletEntity.EthFeeMode && req.EthFeeMode != 0 {
		if req.EthFeeMode != 1 && req.EthFeeMode != 2 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidEthFeeMode)
		}
		update_data["eth_fee_mode"] = req.EthFeeMode
	}
	if req.EthFeeMax != walletEntity.EthFeeMax && req.EthFeeMax != 0 {
		if req.EthFeeMax < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidEthFeeMax)
		}
		update_data["eth_fee_max"] = req.EthFeeMax
	}
	if req.EthGasPrice != walletEntity.EthGasPrice && req.EthGasPrice != 0 {
		if req.EthGasPrice < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidEthGasPrice)
		}
		update_data["eth_gas_price"] = req.EthGasPrice
	}
	if req.EthGasLimit != walletEntity.EthGasLimit && req.EthGasLimit != 0 {
		if req.EthGasLimit < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidEthGasLimit)
		}
		update_data["eth_gas_limit"] = req.EthGasLimit
	}
	if req.TrxFeeMax != walletEntity.TrxFeeMax && req.TrxFeeMax != 0 {
		if req.TrxFeeMax < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrxFeeMax)
		}
		update_data["trx_fee_max"] = req.TrxFeeMax
	}

	if req.TrxKeepAmount != walletEntity.TrxKeepAmount && req.TrxKeepAmount != 0 {
		if req.TrxKeepAmount < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrxKeepAmount)
		}
		update_data["trx_keep_amount"] = req.TrxKeepAmount
	}
	if req.EthKeepAmount != walletEntity.EthKeepAmount && req.EthKeepAmount != 0 {
		if req.EthKeepAmount < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidEthKeepAmount)
		}
		update_data["eth_keep_amount"] = req.EthKeepAmount
	}
	if req.Trc20MinRequiredEnergy != walletEntity.Trc20MinRequiredEnergy && req.Trc20MinRequiredEnergy != 0 {
		if req.Trc20MinRequiredEnergy < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrc20MinRequiredEnergy)
		}
		update_data["trc20_min_required_energy"] = req.Trc20MinRequiredEnergy
	}
	if req.Trc20MaxEnergyFee != walletEntity.Trc20MaxEnergyFee && req.Trc20MaxEnergyFee != 0 {
		if req.Trc20MaxEnergyFee < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrc20MaxEnergyFee)
		}
		update_data["trc20_max_energy_fee"] = req.Trc20MaxEnergyFee
	}
	if req.Trc20MinRequiredBandwidth != walletEntity.Trc20MinRequiredBandwidth && req.Trc20MinRequiredBandwidth != 0 {
		if req.Trc20MinRequiredBandwidth < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrc20MinRequiredBandwidth)
		}
		update_data["trc20_min_required_bandwidth"] = req.Trc20MinRequiredBandwidth
	}
	if req.TrxFeeMode != walletEntity.TrxFeeMode && req.TrxFeeMode != 0 {
		if req.TrxFeeMode != 1 && req.TrxFeeMode != 2 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrxFeeMode)
		}
		update_data["trx_fee_mode"] = req.TrxFeeMode
	}
	if req.Trc20TriggerFeeAmount != walletEntity.Trc20TriggerFeeAmount && req.Trc20TriggerFeeAmount != 0 {
		if req.Trc20TriggerFeeAmount < 0 {
			return nil, codes.NewError(codes.CodeWalletSettingInvalidTrc20TriggerFeeAmount)
		}
		update_data["trc20_trigger_fee_amount"] = req.Trc20TriggerFeeAmount
	}

	if len(update_data) == 0 {
		return &v1.BaseSettingRes{
			Success: true,
		}, nil
	}
	// Keeping original message structure but using English.
	g.Log().Info(ctx, "Updating wallet info:", update_data)
	// 更新钱包信息
	// Use Wallet Repository to update fields
	err = s.walletRepo.UpdateWalletFields(ctx, update_data)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingUpdateInfoFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletSettingUpdateInfoFailed)
	}
	return &v1.BaseSettingRes{
		Success: true,
	}, nil
}

// // 定时归集设置
// func (s *sWallet) CronCollectSetting(ctx context.Context, req *v1.CronCollectSettingReq) (res *v1.CronCollectSettingRes, err error) {
// 	// Use Wallet Repository
// 	var walletEntity *entity.Wallets                // Explicitly declare type
// 	walletEntity, err = s.walletRepo.GetWallet(ctx) // Use = instead of :=
// 	if err != nil {
// 		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingGetInfoFailed.Message(), err)
// 		return nil, codes.WrapError(err, codes.CodeWalletSettingGetInfoFailed)
// 	}
// 	if walletEntity == nil {
// 		return nil, codes.NewError(codes.CodeWalletNotFound) // Re-use code
// 	}
// 	update_data := g.Map{}

// 	old_switch := walletEntity.CronCollectSwitch == 1 // Assuming field exists and is int
// 	old_time := walletEntity.CronCollectTime          // Assuming field exists and is string

// 	if req.Switch != old_switch {
// 		if req.Switch {
// 			update_data["cron_collect_switch"] = 1
// 		} else {
// 			update_data["cron_collect_switch"] = 0
// 		}
// 	}
// 	if req.Time != old_time {
// 		update_data["cron_collect_time"] = req.Time
// 	}

// 	// 更新钱包信息
// 	// Use Wallet Repository to update fields
// 	err = s.walletRepo.UpdateWalletFields(ctx, update_data)
// 	if err != nil {
// 		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingUpdateCronCollectFailed.Message(), err)
// 		return nil, codes.WrapError(err, codes.CodeWalletSettingUpdateCronCollectFailed)
// 	}
// 	return &v1.CronCollectSettingRes{
// 		Success: true,
// 	}, nil
// }

// // 策略归集设置
// func (s *sWallet) StrategyCollectSetting(ctx context.Context, req *v1.StrategyCollectSettingReq) (res *v1.StrategyCollectSettingRes, err error) {
// 	//
// 	// Use Wallet Repository
// 	var walletEntity *entity.Wallets                // Explicitly declare type
// 	walletEntity, err = s.walletRepo.GetWallet(ctx) // Use = instead of :=
// 	if err != nil {
// 		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingGetInfoFailed.Message(), err)
// 		return nil, codes.WrapError(err, codes.CodeWalletSettingGetInfoFailed)
// 	}
// 	if walletEntity == nil {
// 		return nil, codes.NewError(codes.CodeWalletNotFound) // Re-use code
// 	}
// 	update_data := g.Map{
// 		"eth_collect_threshold":  req.EthCollectThreshold,
// 		"trx_collect_threshold":  req.TrxCollectThreshold,
// 		"usdt_collect_threshold": req.UsdtCollectThreshold,
// 	}

// 	old_switch := walletEntity.StrategyCollectSwitch == 1 // Assuming field exists and is int

// 	if req.Switch != old_switch {
// 		if req.Switch {
// 			update_data["strategy_collect_switch"] = 1
// 		} else {
// 			update_data["strategy_collect_switch"] = 0
// 		}
// 	}
// 	// 更新钱包信息
// 	// Use Wallet Repository to update fields
// 	err = s.walletRepo.UpdateWalletFields(ctx, update_data)
// 	if err != nil {
// 		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingUpdateStrategyCollectFailed.Message(), err)
// 		return nil, codes.WrapError(err, codes.CodeWalletSettingUpdateStrategyCollectFailed)
// 	}
// 	return &v1.StrategyCollectSettingRes{
// 		Success: true,
// 	}, nil
// }
