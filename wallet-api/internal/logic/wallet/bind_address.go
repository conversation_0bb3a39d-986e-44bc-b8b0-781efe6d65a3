package wallet

import (
	"context"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/codes"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 绑定地址
func (s *sWallet) BindAddress(ctx context.Context, req *v1.BindAddressReq) (res *v1.BindAddressRes, err error) {
	// 检查钱包是否存在
	_, err = s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressCheckWalletExistsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressCheckWalletExistsFailed)
	}

	// 使用 Address Repository 查找地址
	foundAddresses, err := s.addressRepo.FindAddresses(ctx, g.Map{
		"address": req.Address,
		"type":    req.Type,
	})
	if err != nil {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "%s: %v", codes.CodeWalletAddressFindFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressFindFailed)
	}

	if len(foundAddresses) == 0 {
		return nil, codes.NewError(codes.CodeWalletAddressNotFound)
	}

	// 获取第一个匹配的地址
	addressEntity := foundAddresses[0]

	// 如果地址已经绑定，则返回成功
	if addressEntity.BindStatus == 1 {
		return &v1.BindAddressRes{
			Success: true,
		}, nil
	}

	// 更新地址绑定状态
	err = s.addressRepo.UpdateAddress(ctx, addressEntity.Address, g.Map{
		"bind_status": 1,
		"bind_at":     gtime.Now(),
	})
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressUpdateBindStatusFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressUpdateBindStatusFailed)
	}

	return &v1.BindAddressRes{
		Success: true,
	}, nil
}
