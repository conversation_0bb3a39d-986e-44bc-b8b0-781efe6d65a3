package wallet

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/codes"
	"wallet-api/internal/logic/repository"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// 获取充值记录
func (s *sWallet) GetRechargeRecord(ctx context.Context, req *v1.GetRechargeRecordReq) (res *v1.GetRechargeRecordRes, err error) {
	return s.getRechargeRecordInternal(ctx, req, false)
}

// 充值记录内部函数
func (s *sWallet) getRechargeRecordInternal(ctx context.Context, req *v1.GetRechargeRecordReq, isExport bool) (res *v1.GetRechargeRecordRes, err error) {
	// Parse date range if provided
	var dateStart, dateEnd *time.Time
	if req.DateRange != "" {
		parts := strings.Split(req.DateRange, ",")
		if len(parts) == 2 {
			start, err := time.Parse("2006-01-02", parts[0])
			if err == nil {
				dateStart = &start
			}
			end, err := time.Parse("2006-01-02", parts[1])
			if err == nil {
				// Set end time to end of day
				end = end.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
				dateEnd = &end
			}
		}
	}

	// Use Recharge Repository
	options := repository.RechargeQueryOptions{
		Page:      req.Page,
		Limit:     req.Limit,
		Address:   req.Address,
		Chain:     req.Chain,
		Status:    req.Status,
		Coin:      req.Coin,
		SortField: req.SortField,
		SortOrder: req.SortOrder,
		DateStart: dateStart,
		DateEnd:   dateEnd,
	}

	var rechargeEntities []*entity.UserRecharges
	var total int

	// Get recharge repository from wallet service
	rechargeRepo := repository.NewRechargeRepository()

	if isExport {
		rechargeEntities, err = rechargeRepo.FindAllRecharges(ctx, options)
		total = len(rechargeEntities)
	} else {
		rechargeEntities, total, err = rechargeRepo.ListRecharges(ctx, options)
	}

	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletGetRechargeRecordFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletGetRechargeRecordFailed)
	}

	// Convert entity records to API response format
	rechargeRecordList := make([]v1.RechargeRecord, 0, len(rechargeEntities))
	for _, entity := range rechargeEntities {
		record := v1.RechargeRecord{
			RechargesId:          int(entity.RechargesId),
			TokenId:              int(entity.TokenId),
			Name:                 entity.Name,
			Chain:                entity.Chan,
			TokenContractAddress: entity.TokenContractAddress,
			FromAddress:          entity.FromAddress,
			ToAddress:            entity.ToAddress,
			TxHash:               entity.TxHash,
			Error:                entity.Error,
			Amount:               strconv.FormatFloat(entity.Amount, 'f', -1, 64),
			State:                int(entity.State),
			Confirmations:        int(entity.Confirmations),
			CreatedAt:            entity.CreatedAt.String(),
			UpdatedAt:            entity.UpdatedAt.String(),
		}

		// Handle CompletedAt which might be nil
		if entity.CompletedAt != nil {
			record.CompletedAt = entity.CompletedAt.String()
		}

		rechargeRecordList = append(rechargeRecordList, record)
	}

	return &v1.GetRechargeRecordRes{
		Page: v1.Page{
			Current:  req.Page,
			PageSize: req.Limit,
			Total:    total,
		},
		List: rechargeRecordList,
	}, nil
}

// 导出充值记录
func (s *sWallet) ExportRechargeRecord(ctx context.Context, req *v1.ExportRechargeRecordReq) (res *v1.ExportRechargeRecordRes, err error) {
	// Convert ExportRechargeRecordReq to GetRechargeRecordReq for internal processing
	_, err = s.getRechargeRecordInternal(ctx,
		&v1.GetRechargeRecordReq{
			RechargeType: v1.RechargeType{
				Page:      1,     // Export all, so page 1
				Limit:     10000, // Set a large limit for export
				Address:   req.Address,
				Chain:     req.Chain,
				SortField: req.SortField,
				SortOrder: req.SortOrder,
				Status:    req.Status,
				Coin:      req.Coin,
				DateRange: req.DateRange,
			},
		},
		true, // Set isExport to true
	)

	if err != nil {
		return nil, err
	}

	// TODO: Implement actual export functionality (e.g., generate CSV file)
	// For now, just return success
	return &v1.ExportRechargeRecordRes{
		Success: true,
	}, nil
}

// 获取充值记录统计
func (s *sWallet) GetRechargeRecordStatistic(ctx context.Context, req *v1.GetRechargeRecordStatisticReq) (res *v1.GetRechargeRecordStatisticRes, err error) {
	// Get recharge repository from wallet service
	rechargeRepo := repository.NewRechargeRepository()

	// Use Recharge Repository to get statistics
	stats, err := rechargeRepo.GetRechargeStatistics(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletGetRechargeStatsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletGetRechargeStatsFailed)
	}

	if stats == nil {
		// Handle case where stats might be nil (e.g., no recharges)
		stats = &repository.RechargeStatistic{} // Initialize with zero values
	}

	return &v1.GetRechargeRecordStatisticRes{
		TotalRechargeCount: stats.TotalRechargeCount,
		PendingCount:       stats.PendingCount,
		CompletedCount:     stats.CompletedCount,
		TotalUsdtAmount:    fmt.Sprintf("%.2f", stats.TotalUsdtAmount),
		TotalTrxAmount:     fmt.Sprintf("%.2f", stats.TotalTrxAmount),
		TotalEthAmount:     fmt.Sprintf("%.2f", stats.TotalEthAmount),
	}, nil
}
