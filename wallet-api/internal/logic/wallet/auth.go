package wallet

import (
	"context"
	// "time" // 暂时注释掉，看是否需要

	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/model/entity" // Re-enable import
	"wallet-api/internal/security/credentialmanager"
	"wallet-api/internal/service" // Re-enable import

	// "wallet-api/internal/utility" //不再需要
	util "wallet-api/internal/utility/utils"

	// "github.com/gogf/gf/v2/errors/gcode" // Removed unused import
	// "github.com/gogf/gf/v2/errors/gerror" // Removed unused import
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	// "github.com/golang-jwt/jwt/v4" //不再需要
	"github.com/pquerna/otp/totp"
	"golang.org/x/crypto/bcrypt"

	"wallet-api/internal/codes" // Import codes package
)

// 钱包认证
func (s *sWallet) Auth(ctx context.Context, req *v1.AuthReq) (res *v1.AuthRes, err error) {

	// 获取钱包实体
	wallet, err := s._getWalletEntity(ctx)
	if err != nil {
		return nil, err // _getWalletEntity handles logging and error codes
	}

	// 验证密码 (包含渐进式迁移)
	if err = s._validatePasswordWithMigration(ctx, wallet, req.Password); err != nil {
		return nil, err
	}

	// 验证谷歌验证码
	// Pass req.Password to _validateGoogleCode as it's needed for PBKDF2 decryption
	if err = s._validateGoogleCode(ctx, wallet, req.GoogleCode, req.Password); err != nil {
		return nil, err
	}

	// 调用新的认证服务登录方法
	accessToken, refreshToken, err := service.Auth().Login(ctx, uint(wallet.Id))
	if err != nil {
		g.Log().Errorf(ctx, "service.Auth().Login failed for userID %d: %v", wallet.Id, err)
		return nil, codes.WrapError(err, codes.CodeLoginFailed)
	}

	currentTime := gtime.Now() // 保留用于更新最后解锁时间

	// 更新钱包最后解锁时间
	err = s.walletRepo.UpdateLastUnlockTime(ctx, currentTime) // Use repository method
	if err != nil {
		g.Log().Errorf(ctx, "%s for userID %d: %v", codes.CodeWalletAuthUpdateLastUnlockFailed.Message(), wallet.Id, err)
		// 即使这里失败，token也已经生成，可以考虑是否返回token
		// 但为了数据一致性，这里选择返回错误
		return nil, codes.WrapError(err, codes.CodeWalletAuthUpdateLastUnlockFailed)
	}
	// 缓存密码和解密后的费用私钥
	credentialmanager.SetPassword(ctx, req.Password, wallet)

	res = &v1.AuthRes{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	}

	return
}

// 修改密码
func (s *sWallet) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error) {
	// 获取钱包实体
	wallet, err := s._getWalletEntity(ctx)
	if err != nil {
		return nil, err
	}
	// 验证谷歌验证码
	// Pass req.GoogleCode and req.OldPassword (or a verified current password if ChangePassword requires re-auth)
	// For ChangePassword, OldPassword is being validated, so it's the "current" password for this operation.
	if err = s._validateGoogleCode(ctx, wallet, req.GoogleCode, req.OldPassword); err != nil {
		return nil, err
	}

	// 验证旧密码 (简单比较)
	if err = s._validatePasswordSimple(ctx, wallet, req.OldPassword); err != nil {
		// 返回特定的错误信息可能更好
		return nil, codes.NewError(codes.CodeWalletAuthOldPasswordIncorrect)
	}

	// 检查新密码是否与旧密码相同
	if req.NewPassword == req.OldPassword {
		return nil, codes.NewError(codes.CodeWalletAuthNewPasswordSameAsOld)
	}

	// 生成新的密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletEncryptPasswordFailed)
	}

	// 重新加密所有敏感数据
	err = s._reencryptSensitiveDataWithNewPassword(ctx, wallet, req.OldPassword, req.NewPassword, string(hashedPassword))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to re-encrypt sensitive data with new password: %v", err)
		return nil, err
	}

	// 更新内存中的密码缓存
	credentialmanager.SetPassword(ctx, req.NewPassword, wallet)

	return &v1.ChangePasswordRes{
		Success: true,
	}, nil
}

// 生成谷歌验证码
func (s *sWallet) GenerateGoogleCode(ctx context.Context, req *v1.GenerateGoogleCodeReq) (res *v1.GenerateGoogleCodeRes, err error) {

	// 生成TOTP密钥
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      "xpay",
		AccountName: "wallet",
	})
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAuthGenerateGoogleKeyFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAuthGenerateGoogleKeyFailed)
	}

	// 获取二维码
	qrCode := key.URL()

	//todo 上线要放开
	res = &v1.GenerateGoogleCodeRes{
		// Secret: key.Secret(),
		Secret: "********************************",
		QrCode: qrCode,
	}
	return
}

// 重新绑定谷歌验证码
func (s *sWallet) ReBindGoogleCode(ctx context.Context, req *v1.ReBindGoogleCodeReq) (res *v1.ReBindGoogleCodeRes, err error) {
	// 获取钱包实体
	wallet, err := s._getWalletEntity(ctx)
	if err != nil {
		return nil, err
	}

	// 验证当前谷歌验证码
	// Pass req.Password as it's required for this operation
	if err = s._validateGoogleCode(ctx, wallet, req.Code, req.Password); err != nil {
		return nil, err
	}

	// 验证密码 (简单比较)
	if err = s._validatePasswordSimple(ctx, wallet, req.Password); err != nil {
		return nil, err
	}

	// 加密谷歌验证码
	// For ReBindGoogleCode, new salt and iterations should be generated and stored.
	newGoogleSecretSalt, err := util.GenerateSalt(util.DefaultSaltLength)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to generate salt for new google secret: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletGenerateSaltFailed)
	}
	cfgGoogleSecretIterations, errCfgGS := g.Cfg().Get(ctx, "security.pbkdf2Iterations")
	if errCfgGS != nil || cfgGoogleSecretIterations.IsNil() {
		g.Log().Errorf(ctx, "Failed to get security.pbkdf2Iterations for new google secret: %v", errCfgGS)
		return nil, codes.NewError(codes.CodeConfigError)
	}
	newGoogleSecretIterations := cfgGoogleSecretIterations.Int()
	if newGoogleSecretIterations <= 0 {
		g.Log().Errorf(ctx, "Invalid security.pbkdf2Iterations for new google secret: %d", newGoogleSecretIterations)
		return nil, codes.NewError(codes.CodeConfigError)
	}

	encryptedSecret, err := util.EncryptStringWithPBKDF2(ctx, req.NewSecret, req.Password, newGoogleSecretSalt, newGoogleSecretIterations)
	if err != nil {
		g.Log().Errorf(ctx, "%s (PBKDF2): %v", codes.CodeWalletAuthEncryptGoogleKeyFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAuthEncryptGoogleKeyFailed)
	}

	// 更新谷歌验证码 using specific repository method
	// UpdateGoogleSecret in repository and DAO needs to be adjusted to also save new salt and iterations.
	// For now, we assume UpdateGoogleSecret will be modified or we use UpdateWalletFields.
	// Let's prepare data for UpdateWalletFields as it's more flexible for now.
	updateData := g.Map{
		// dao.Wallets.Columns().GoogleCode:             encryptedSecret, // Use string literal if Columns not available
		// dao.Wallets.Columns().GoogleSecretSalt:       newGoogleSecretSalt,
		// dao.Wallets.Columns().GoogleSecretIterations: newGoogleSecretIterations,
		// dao.Wallets.Columns().GoogleCodeSwitch:       1, // true
		// dao.Wallets.Columns().UpdateAt:               gtime.Now(),
		"google_code":              encryptedSecret,
		"google_secret_salt":       newGoogleSecretSalt,
		"google_secret_iterations": newGoogleSecretIterations,
		"google_code_switch":       1, // true
		"updated_at":               gtime.Now(),
	}
	err = s.walletRepo.UpdateWalletFields(ctx, updateData) // Using UpdateWalletFields
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAuthUpdateGoogleKeyFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAuthUpdateGoogleKeyFailed)
	}
	return &v1.ReBindGoogleCodeRes{
		Success: true,
	}, nil
}

// 校验google 验证码是否正确
func (s *sWallet) VerifyGoogleCode(ctx context.Context, req *service.VerifyGoogleCodeReq) (res bool, err error) {
	var secret string

	// 获取密钥
	if req.Secret != nil {
		secret = *req.Secret
	} else {
		// 如果没有传入密钥，则从钱包中获取
		var wallet *entity.Wallets                // Explicitly declare type
		wallet, err = s.walletRepo.GetWallet(ctx) // Use repository method
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingGetInfoFailed.Message(), err) // Re-use code
			return false, codes.WrapError(err, codes.CodeWalletSettingGetInfoFailed)
		}
		if wallet == nil {
			return false, codes.NewError(codes.CodeWalletNotFound) // Re-use code
		}
		// secret = wallet.GoogleCode // This is the encrypted one

		// 解密谷歌验证码
		// VerifyGoogleCode is a public service method, it might not have direct access to user's password.
		// This implies that for this specific VerifyGoogleCode service method to work standalone
		// without a prior full Auth (which would cache the key), it CANNOT use PBKDF2 if password is not available.
		// This is a design consideration. If VerifyGoogleCode is always called in a context
		// where the wallet is already "unlocked" (i.e., AES key cached in memory), then it can use AESDecrypt.
		// If it's called with req.Secret (which is plaintext), then no decryption is needed from DB.
		// The current logic path (else block) implies we need to decrypt from DB.
		// This is problematic if password is not available.
		// For now, assuming this function will be refactored or password will be made available.
		// Let's assume for now this specific path (decrypting from DB) is for an internal check
		// where password *could* be retrieved or is part of a larger flow.
		// This highlights a need to review the call sites of VerifyGoogleCode.
		// If we proceed with PBKDF2, this function needs the user's current password.
		// For the purpose of this diff, we'll assume password must be obtained if salt/iterations are present.
		// This function's direct utility is reduced if it can't get the password.
		// A temporary placeholder or a more significant refactor is needed here.
		// For now, let's assume it cannot proceed if it has to decrypt using PBKDF2 without a password.
		if wallet.GoogleSecretSalt != nil && len(wallet.GoogleSecretSalt) > 0 {
			// This indicates PBKDF2 is used. This function, as is, cannot get the password.
			g.Log().Errorf(ctx, "VerifyGoogleCode (service): Attempting to decrypt PBKDF2-encrypted secret without password.")
			return false, codes.NewError(codes.CodeWalletAuthPasswordRequiredForDecryption) // New error code
		}
		// Fallback to old decryption if no salt (should not happen for new/migrated data)
		// This part should ideally be removed after migration.
		// For now, if old decryption was needed and DecryptString is removed, this path is broken.
		// It's better to explicitly state that this path is not supported with the new encryption.
		g.Log().Errorf(ctx, "VerifyGoogleCode: Attempting to use old decryption path for GoogleCode, but util.DecryptString is removed. Wallet ID: %d. This indicates an unmigrated or problematic state.", wallet.Id)
		return false, codes.NewError(codes.CodeUtilityFunctionRemoved) // New error code for removed utility

		// Original problematic lines:
		// secret, err = util.DecryptString(ctx, wallet.GoogleCode)
		// if err != nil {
		// 	g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAuthDecryptGoogleKeyFailed.Message(), err)
		// 	return false, codes.WrapError(err, codes.CodeWalletAuthDecryptGoogleKeyFailed)
		// }
		// The above return was part of the original problematic lines.
		// The corrected logic for this 'else' branch (where req.Secret is nil) is:
		// 1. Check if PBKDF2 is used (salt exists).
		// 2. If yes, and no password, return error.
		// 3. If no, and old DecryptString is removed, return error.
		// The 'return false, codes.WrapError(err, codes.CodeWalletAuthDecryptGoogleKeyFailed)' at line 277
		// was likely a leftover from the merge. The if block for 'err != nil' at 273-276 is also part of that.
		// Let's ensure the structure is correct for the intended logic for this 'else' block.
		// The 'if err != nil' at 273 seems to be a general error check for 'wallet, err = s.walletRepo.GetWallet(ctx)'
		// which is already handled by _getWalletEntity.
		// The return at 277 is inside the 'else' of 'if req.Secret != nil'.
		// The syntax error at 282 'if !totp.Validate' means the 'else' block starting at 228 was not properly closed
		// before the 'if !totp.Validate'.

		// Corrected structure for the 'else' block of 'if req.Secret != nil':
		// This entire block handles the case where req.Secret is NOT provided, so we try to use wallet.GoogleCode
	} // This closes the 'else' from 'if req.Secret != nil'

	// If secret is still empty here, it means req.Secret was nil AND we couldn't (or shouldn't) decrypt from DB.
	if secret == "" {
		// This case should ideally be covered by the logic within the 'else' block if req.Secret was nil.
		// If req.Secret was provided, 'secret' would not be empty.
		// If req.Secret was nil, and DB decryption failed or was not possible, an error should have been returned already.
		// This check is a safeguard.
		g.Log().Error(ctx, "VerifyGoogleCode: Secret remains empty after attempting to fetch/decrypt.")
		return false, codes.NewError(codes.CodeWalletInvalidGoogleCode) // Or a more specific error
	}

	// 校验谷歌验证码
	if !totp.Validate(req.GoogleCode, secret) {
		return false, codes.NewError(codes.CodeWalletInvalidGoogleCode) // Re-use code
	}
	return true, nil
}

// --- Private Helper Methods for Validation ---

// _getWalletEntity retrieves the wallet entity and handles common errors.
func (s *sWallet) _getWalletEntity(ctx context.Context) (*entity.Wallets, error) {
	wallet, err := s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletSettingGetInfoFailed.Message(), err) // Re-use code
		return nil, codes.WrapError(err, codes.CodeWalletSettingGetInfoFailed)
	}
	if wallet == nil {
		return nil, codes.NewError(codes.CodeWalletNotFound) // Re-use code
	}
	return wallet, nil
}

// _validateGoogleCode decrypts the stored secret and validates the provided Google code.
// This function is typically called when the user has just provided their password (e.g., during Auth).
func (s *sWallet) _validateGoogleCode(ctx context.Context, wallet *entity.Wallets, googleCode string, passwordForDecryption string) error {
	if wallet.GoogleSecretSalt == nil || len(wallet.GoogleSecretSalt) == 0 || wallet.GoogleSecretIterations <= 0 {
		g.Log().Errorf(ctx, "Google secret salt or iterations not set for wallet ID %d. Cannot decrypt Google Code.", wallet.Id)
		// This might indicate a state where Google Auth was set up before PBKDF2 migration
		// Or an incompletely configured new wallet.
		// For now, treat as a decryption failure. A more specific error code might be needed.
		return codes.NewError(codes.CodeWalletAuthDecryptGoogleKeyFailed)
	}

	decryptedSecret, err := util.DecryptStringWithPBKDF2(ctx, wallet.GoogleCode, passwordForDecryption, wallet.GoogleSecretSalt, wallet.GoogleSecretIterations)
	if err != nil {
		g.Log().Errorf(ctx, "%s (PBKDF2): %v", codes.CodeWalletAuthDecryptGoogleKeyFailed.Message(), err)
		// This error could mean wrong password if passwordForDecryption was incorrect.
		return codes.WrapError(err, codes.CodeWalletAuthDecryptGoogleKeyFailed)
	}
	if !totp.Validate(googleCode, decryptedSecret) {
		return codes.NewError(codes.CodeWalletInvalidGoogleCode) // Re-use code
	}
	return nil
}

// _validatePasswordWithMigration validates the password (now only uses the new method).
// Old migration logic has been removed as per user request.
func (s *sWallet) _validatePasswordWithMigration(ctx context.Context, wallet *entity.Wallets, password string) error {
	// Validate with the new method (direct compare with PasswordHash)
	errCompare := bcrypt.CompareHashAndPassword([]byte(wallet.PasswordHash), []byte(password))
	if errCompare == nil {
		g.Log().Debugf(ctx, "Wallet password verified successfully (ID: %d)", wallet.Id)
		return nil // Success with new hash format
	}

	g.Log().Warningf(ctx, "Wallet password verification failed (ID: %d)", wallet.Id)
	return codes.NewError(codes.CodeWalletAuthPasswordVerifyFailed) // Use specific code
}

// _validatePasswordSimple performs a simple password comparison using the new hash format.
func (s *sWallet) _validatePasswordSimple(ctx context.Context, wallet *entity.Wallets, password string) error {
	// This function should also use PasswordHash, as it's for validating against the current, presumably new, hash.
	if err := bcrypt.CompareHashAndPassword([]byte(wallet.PasswordHash), []byte(password)); err != nil {
		return codes.NewError(codes.CodeWalletAuthPasswordVerifyFailed) // Use specific code
	}
	return nil
}

// _reencryptSensitiveDataWithNewPassword decrypts all sensitive data with old password and re-encrypts with new password.
// This ensures that when password is changed, all encrypted data is re-encrypted with the new password.
func (s *sWallet) _reencryptSensitiveDataWithNewPassword(ctx context.Context, wallet *entity.Wallets, oldPassword, newPassword, newPasswordHash string) error {
	g.Log().Infof(ctx, "Starting re-encryption of sensitive data for wallet ID: %d", wallet.Id)

	// 1. 解密助记词
	var decryptedMnemonic string
	if wallet.Mnemonic != "" && wallet.MnemonicSalt != nil && len(wallet.MnemonicSalt) > 0 && wallet.MnemonicIterations > 0 {
		var err error
		decryptedMnemonic, err = util.DecryptStringWithPBKDF2(ctx, wallet.Mnemonic, oldPassword, wallet.MnemonicSalt, wallet.MnemonicIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to decrypt mnemonic with old password: %v", err)
			return codes.WrapError(err, codes.CodeUtilityDecryptFailed)
		}
		g.Log().Debugf(ctx, "Successfully decrypted mnemonic")
	} else {
		g.Log().Warningf(ctx, "Mnemonic encryption parameters missing for wallet ID: %d", wallet.Id)
		return codes.NewError(codes.CodeWalletEncryptMnemonicFailed)
	}

	// 2. 解密谷歌验证码密钥
	var decryptedGoogleCode string
	if wallet.GoogleCode != "" && wallet.GoogleSecretSalt != nil && len(wallet.GoogleSecretSalt) > 0 && wallet.GoogleSecretIterations > 0 {
		var err error
		decryptedGoogleCode, err = util.DecryptStringWithPBKDF2(ctx, wallet.GoogleCode, oldPassword, wallet.GoogleSecretSalt, wallet.GoogleSecretIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to decrypt Google code with old password: %v", err)
			return codes.WrapError(err, codes.CodeWalletAuthDecryptGoogleKeyFailed)
		}
		g.Log().Debugf(ctx, "Successfully decrypted Google code")
	} else {
		g.Log().Warningf(ctx, "Google code encryption parameters missing for wallet ID: %d", wallet.Id)
		// Google code might not be set, so this is not necessarily an error
		decryptedGoogleCode = ""
	}

	// 3. 解密手续费私钥
	var decryptedEthFeePrivateKey, decryptedTrxFeePrivateKey string

	// 解密 ETH 手续费私钥
	if wallet.EthFeePrivateKey != "" {
		var err error
		decryptedEthFeePrivateKey, err = util.DecryptStringWithPBKDF2(ctx, wallet.EthFeePrivateKey, oldPassword, wallet.MnemonicSalt, wallet.MnemonicIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to decrypt ETH fee private key with old password: %v", err)
			return codes.WrapError(err, codes.CodeUtilityDecryptFailed)
		}
		g.Log().Debugf(ctx, "Successfully decrypted ETH fee private key")
	}

	// 解密 TRX 手续费私钥
	if wallet.TrxFeePrivateKey != "" {
		var err error
		decryptedTrxFeePrivateKey, err = util.DecryptStringWithPBKDF2(ctx, wallet.TrxFeePrivateKey, oldPassword, wallet.MnemonicSalt, wallet.MnemonicIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to decrypt TRX fee private key with old password: %v", err)
			return codes.WrapError(err, codes.CodeUtilityDecryptFailed)
		}
		g.Log().Debugf(ctx, "Successfully decrypted TRX fee private key")
	}

	// 4. 生成新的盐值和迭代次数
	newMnemonicSalt, err := util.GenerateSalt(util.DefaultSaltLength)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to generate new mnemonic salt: %v", err)
		return codes.WrapError(err, codes.CodeWalletGenerateSaltFailed)
	}

	cfgMnemonicIterations, errCfgM := g.Cfg().Get(ctx, "security.pbkdf2Iterations")
	if errCfgM != nil || cfgMnemonicIterations.IsNil() {
		g.Log().Errorf(ctx, "Failed to get security.pbkdf2Iterations for new mnemonic: %v", errCfgM)
		return codes.NewError(codes.CodeConfigError)
	}
	newMnemonicIterations := cfgMnemonicIterations.Int()
	if newMnemonicIterations <= 0 {
		g.Log().Errorf(ctx, "Invalid security.pbkdf2Iterations for new mnemonic: %d", newMnemonicIterations)
		return codes.NewError(codes.CodeConfigError)
	}

	newGoogleSecretSalt, err := util.GenerateSalt(util.DefaultSaltLength)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to generate new Google secret salt: %v", err)
		return codes.WrapError(err, codes.CodeWalletGenerateSaltFailed)
	}

	cfgGoogleSecretIterations, errCfgGS := g.Cfg().Get(ctx, "security.pbkdf2Iterations")
	if errCfgGS != nil || cfgGoogleSecretIterations.IsNil() {
		g.Log().Errorf(ctx, "Failed to get security.pbkdf2Iterations for new Google secret: %v", errCfgGS)
		return codes.NewError(codes.CodeConfigError)
	}
	newGoogleSecretIterations := cfgGoogleSecretIterations.Int()
	if newGoogleSecretIterations <= 0 {
		g.Log().Errorf(ctx, "Invalid security.pbkdf2Iterations for new Google secret: %d", newGoogleSecretIterations)
		return codes.NewError(codes.CodeConfigError)
	}

	// 5. 使用新密码重新加密所有敏感数据
	newEncryptedMnemonic, err := util.EncryptStringWithPBKDF2(ctx, decryptedMnemonic, newPassword, newMnemonicSalt, newMnemonicIterations)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to encrypt mnemonic with new password: %v", err)
		return codes.WrapError(err, codes.CodeWalletEncryptMnemonicFailed)
	}

	var newEncryptedGoogleCode string
	if decryptedGoogleCode != "" {
		newEncryptedGoogleCode, err = util.EncryptStringWithPBKDF2(ctx, decryptedGoogleCode, newPassword, newGoogleSecretSalt, newGoogleSecretIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to encrypt Google code with new password: %v", err)
			return codes.WrapError(err, codes.CodeWalletEncryptGoogleSecretFailed)
		}
	}

	var newEncryptedEthFeePrivateKey, newEncryptedTrxFeePrivateKey string
	if decryptedEthFeePrivateKey != "" {
		newEncryptedEthFeePrivateKey, err = util.EncryptStringWithPBKDF2(ctx, decryptedEthFeePrivateKey, newPassword, newMnemonicSalt, newMnemonicIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to encrypt ETH fee private key with new password: %v", err)
			return codes.WrapError(err, codes.CodeUtilityEncryptFailed)
		}
	}

	if decryptedTrxFeePrivateKey != "" {
		newEncryptedTrxFeePrivateKey, err = util.EncryptStringWithPBKDF2(ctx, decryptedTrxFeePrivateKey, newPassword, newMnemonicSalt, newMnemonicIterations)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to encrypt TRX fee private key with new password: %v", err)
			return codes.WrapError(err, codes.CodeUtilityEncryptFailed)
		}
	}

	// 6. 在数据库事务中更新所有加密数据
	err = s.walletRepo.UpdatePasswordAndReencryptSensitiveData(ctx, newPasswordHash,
		newEncryptedMnemonic, newMnemonicSalt, newMnemonicIterations,
		newEncryptedGoogleCode, newGoogleSecretSalt, newGoogleSecretIterations,
		newEncryptedEthFeePrivateKey, newEncryptedTrxFeePrivateKey)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to update wallet with re-encrypted data: %v", err)
		return codes.WrapError(err, codes.CodeWalletAuthUpdatePasswordFailed)
	}

	g.Log().Infof(ctx, "Successfully re-encrypted all sensitive data for wallet ID: %d", wallet.Id)
	return nil
}
