package wallet

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"
	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/codes"
	"wallet-api/internal/dao"
	"wallet-api/internal/logic/repository"
	"wallet-api/internal/model/entity"
	eth "wallet-api/internal/utility/crypto/eth"
	"wallet-api/internal/utility/crypto/tron"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// 获取提现记录
func (s *sWallet) GetWithdrawRecord(ctx context.Context, req *v1.GetWithdrawRecordReq) (res *v1.GetWithdrawRecordRes, err error) {
	return s.getWithdrawRecordInternal(ctx, req, false)
}

// 提现记录内部函数
func (s *sWallet) getWithdrawRecordInternal(ctx context.Context, req *v1.GetWithdrawRecordReq, isExport bool) (res *v1.GetWithdrawRecordRes, err error) {
	// Parse date range if provided
	var dateStart, dateEnd *time.Time
	if req.DateRange != "" {
		parts := strings.Split(req.DateRange, ",")
		if len(parts) == 2 {
			start, err := time.Parse("2006-01-02", parts[0])
			if err == nil {
				dateStart = &start
			}
			end, err := time.Parse("2006-01-02", parts[1])
			if err == nil {
				// Set end time to end of day
				end = end.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
				dateEnd = &end
			}
		}
	}

	// Create withdraw repository
	withdrawRepo := repository.NewWithdrawRepository()

	// Prepare query options
	options := repository.WithdrawQueryOptions{
		Page:        req.Page,
		Limit:       req.Limit,
		ToAddress:   req.ToAddress,
		FromAddress: req.FromAddress,
		Chain:       req.Chain,
		Status:      req.Status,
		Coin:        req.Coin,
		DateStart:   dateStart,
		DateEnd:     dateEnd,
		SortField:   req.SortField,
		SortOrder:   req.SortOrder,
	}

	// Get withdraw records
	var withdrawList []*entity.UserWithdraws
	var total int

	if isExport {
		// For export, get all records
		withdrawList, err = withdrawRepo.FindAllWithdraws(ctx, options)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletGetWithdrawsFailed.Message(), err)
			return nil, codes.WrapError(err, codes.CodeWalletGetWithdrawsFailed)
		}
		total = len(withdrawList)
	} else {
		// For normal list, get paginated records
		withdrawList, total, err = withdrawRepo.ListWithdraws(ctx, options)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletGetWithdrawsFailed.Message(), err)
			return nil, codes.WrapError(err, codes.CodeWalletGetWithdrawsFailed)
		}
	}

	withdrawRecordList := make([]v1.UserWithdraws, 0, len(withdrawList))
	gconv.Struct(withdrawList, &withdrawRecordList)

	return &v1.GetWithdrawRecordRes{
		Page: v1.Page{
			Current:  req.Page,
			PageSize: req.Limit,
			Total:    total,
		},
		List: withdrawRecordList,
	}, nil
}

// 导出提现记录
func (s *sWallet) ExportWithdrawRecord(ctx context.Context, req *v1.ExportWithdrawRecordReq) (res *v1.ExportWithdrawRecordRes, err error) {
	// Convert ExportWithdrawRecordReq to GetWithdrawRecordReq for internal processing
	_, err = s.getWithdrawRecordInternal(ctx,
		&v1.GetWithdrawRecordReq{
			WithdrawType: v1.WithdrawType{
				Page:        1,     // Export all, so page 1
				Limit:       10000, // Set a large limit for export
				FromAddress: req.FromAddress,
				ToAddress:   req.ToAddress,
				Chain:       req.Chain,
				SortField:   req.SortField,
				SortOrder:   req.SortOrder,
				Status:      req.Status,
				Coin:        req.Coin,
				DateRange:   req.DateRange,
			},
		},
		true, // Set isExport to true
	)

	if err != nil {
		return nil, err
	}

	// TODO: Implement actual export functionality (e.g., generate CSV file)
	// For now, just return success
	return &v1.ExportWithdrawRecordRes{
		Success: true,
	}, nil
}

// 获取提现记录统计
func (s *sWallet) GetWithdrawRecordStatistic(ctx context.Context, req *v1.GetWithdrawRecordStatisticReq) (res *v1.GetWithdrawRecordStatisticRes, err error) {
	// Create withdraw repository
	withdrawRepo := repository.NewWithdrawRepository()

	// Use Withdraw Repository to get statistics
	stats, err := withdrawRepo.GetWithdrawStatistics(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletGetWithdrawStatsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletGetWithdrawStatsFailed)
	}

	if stats == nil {
		// Handle case where stats might be nil (e.g., no withdraws)
		stats = &repository.WithdrawStatistic{} // Initialize with zero values
	}

	return &v1.GetWithdrawRecordStatisticRes{
		TotalWithdrawCount: stats.TotalWithdrawCount,
		PendingCount:       stats.PendingCount,
		ProcessingCount:    stats.ProcessingCount,
		RejectedCount:      stats.RejectedCount,
		CompletedCount:     stats.CompletedCount,
		FailedCount:        stats.FailedCount,
		TotalUsdtAmount:    fmt.Sprintf("%.2f", stats.TotalUsdtAmount),
		TotalTrxAmount:     fmt.Sprintf("%.2f", stats.TotalTrxAmount),
		TotalEthAmount:     fmt.Sprintf("%.2f", stats.TotalEthAmount),
	}, nil
}

// 创建提现记录
func (s *sWallet) CreateWithdraw(ctx context.Context, req *v1.CreateWithdrawReq) (res *v1.CreateWithdrawRes, err error) {

	var address *entity.Address
	//查找地址
	err = dao.Address.Ctx(ctx).Where("address", req.Address).Scan(&address)

	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressFindFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressFindFailed)
	}
	if address == nil {
		return nil, codes.NewError(codes.CodeWalletCreateWithdrawFailed)
	}

	var UsdtBalance string
	var TokenBalance string

	switch address.Type {
	case "ETH":
		UsdtBalance, err = eth.GetErc20UsdtBalance(req.Address)
		if err != nil {
			// 考虑是否需要返回错误，或者仅记录并继续获取ETH余额
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletNodeGetErc20UsdtFailedLog.Message(), err)
			// return nil, err // 如果需要严格处理错误，取消注释此行
			UsdtBalance = "0" // 假设获取失败则余额为0
		}
		TokenBalance, err = eth.GetETHBalance(req.Address)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletNodeGetEthFailedLog.Message(), err)
			return nil, err // 获取主币余额失败通常需要返回错误
		}
	case "TRON":
		trxDecimalBalance, err := tron.GetTRXBalance(ctx, req.Address)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletNodeGetTronBalanceFailedLog.Message(), err)
			return nil, err
		}
		TokenBalance = trxDecimalBalance.String()

		usdtBalance, err := tron.GetTRC20UsdtTokenBalance(ctx, req.Address)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", "获取TRC20 USDT余额失败", err)
			return nil, err
		}

		UsdtBalance = usdtBalance.String()
	default:
		return nil, codes.NewErrorf(codes.CodeWalletNodeUnsupportedType, "Unsupported type: %s", address.Type)
	}

	UsdtBalanceFloat, err := strconv.ParseFloat(UsdtBalance, 64)
	if err != nil {
		return nil, err
	}
	TokenBalanceFloat, err := strconv.ParseFloat(TokenBalance, 64)
	if err != nil {
		return nil, err
	}

	// 创建一个map来存储非零余额
	balanceData := make(map[string]float64)

	// 只有当余额不为0时才添加到map中
	if UsdtBalanceFloat > 0 {
		balanceData["USDT"] = UsdtBalanceFloat
	}

	if TokenBalanceFloat > 0 {
		// 根据地址类型存储对应的Token名称
		var tokenName string
		switch address.Type {
		case "ETH":
			tokenName = "ETH"
		case "TRON":
			tokenName = "TRX"
		default:
			tokenName = address.Type
		}
		balanceData[tokenName] = TokenBalanceFloat
	}

	// Create withdraw repository
	withdrawRepo := repository.NewWithdrawRepository()

	// 记录所有创建的记录ID
	var withdrawIds []int

	// 遍历map，对每个非零余额创建记录
	for coin, amount := range balanceData {
		withdraw := &entity.UserWithdraws{
			Name:         coin,
			Chan:         address.Type, // 使用地址类型作为链
			FromAddress:  address.Address,
			Amount:       amount,
			HandlingFee:  0,           // 默认手续费为0
			ActualAmount: amount,      // 默认实际金额与金额相同
			State:        1,           // 1 = 待审核
			TxHash:       "",          // 新记录的交易哈希为空
			ErrorMessage: "[]",        // 新记录的错误信息为空
			CreatedAt:    gtime.Now(), // 当前时间
			UpdatedAt:    gtime.Now(), // 当前时间
			Retries:      0,           // 默认重试次数为0
			NergyState:   0,           // 默认nergy状态为0
		}

		// 创建提现记录
		id, err := withdrawRepo.CreateWithdraw(ctx, withdraw)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletCreateWithdrawFailed.Message(), err)
			return nil, codes.WrapError(err, codes.CodeWalletCreateWithdrawFailed)
		}

		// 将ID添加到列表中
		withdrawIds = append(withdrawIds, id)
	}

	// 如果没有创建任何记录，返回错误
	if len(withdrawIds) == 0 {
		return nil, codes.NewError(codes.CodeWalletCreateWithdrawFailed)
	}

	// 返回第一个创建的ID
	return &v1.CreateWithdrawRes{
		UserWithdrawsId: withdrawIds[0],
	}, nil
}
