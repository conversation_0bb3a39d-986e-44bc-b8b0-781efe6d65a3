package wallet

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"time"

	v1 "wallet-api/api/wallet/v1"
	"wallet-api/internal/codes" // Import codes package
	"wallet-api/internal/consts"
	"wallet-api/internal/logic/repository" // Import repository
	"wallet-api/internal/model/entity"     // Import entity
	eth "wallet-api/internal/utility/crypto/eth"
	"wallet-api/internal/utility/crypto/tron"
	util "wallet-api/internal/utility/utils"

	// "github.com/gogf/gf/v2/errors/gcode" // Removed unused import
	// "github.com/gogf/gf/v2/errors/gerror" // Removed unused import
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
	"wallet-api/internal/utility/utils/bip39" // Added for NewSeed
)

// 批量创建地址
func (s *sWallet) BatchCreateAddress(ctx context.Context, req *v1.BatchCreateAddressReq) (res *v1.BatchCreateAddressRes, err error) {
	// 获取钱包实体
	walletEntity, err := s._getWalletEntity(ctx)
	if err != nil {
		return nil, err
	}

	// 验证谷歌验证码
	if err = s._validateGoogleCode(ctx, walletEntity, req.GoogleCode, req.Password); err != nil {
		return nil, err
	}

	// 验证密码 (简单比较)
	if err = s._validatePasswordSimple(ctx, walletEntity, req.Password); err != nil {
		return nil, err
	}

	if req.Count > 10000 {
		return nil, codes.NewError(codes.CodeWalletAddressCreateTooMany)
	}

	// 生成唯一的任务ID
	taskId := fmt.Sprintf("addr_%s", gtime.Now().Format("YmdHis_")+grand.S(8))

	// 创建任务进度记录
	err = s.progressTasksRepo.CreateProgressTask(ctx, g.Map{
		"task_id":        taskId,
		"status":         consts.TaskStatusPending,
		"progress":       0,
		"processed_rows": 0,
		"total_rows":     req.Count * 2, // TRON和ETH地址各一个
		"error_message":  "",
		"created_at":     gtime.Now(),
		"updated_at":     gtime.Now(),
	})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to create progress task: %v", err)
		return nil, codes.WrapError(err, codes.CodeWalletCollectCreateTaskFailed)
	}

	go func() {
		ctx := context.Background()
		// 更新任务状态为处理中
		err := s.progressTasksRepo.UpdateProgressTask(ctx, taskId, g.Map{
			"status":     consts.TaskStatusProcessing,
			"updated_at": gtime.Now(),
		})
		if err != nil {
			g.Log().Errorf(ctx, "Failed to update task status to processing: %v", err)
			return
		}

		// 批量创建地址
		tron_address_list := make([]g.Map, 0, req.Count)
		eth_address_list := make([]g.Map, 0, req.Count)
		processedCount := 0

		// Decrypt mnemonic to get seed ONCE before the loop
		if walletEntity.Mnemonic == "" || walletEntity.MnemonicSalt == nil || walletEntity.MnemonicIterations == 0 {
			errMsg := "BatchCreateAddress: Encrypted mnemonic, salt, or iterations missing in wallet entity."
			g.Log().Errorf(ctx, errMsg+" TaskID: %s", taskId)
			s.progressTasksRepo.UpdateProgressTask(ctx, taskId, g.Map{
				"status": consts.TaskStatusFailed, "error_message": errMsg, "updated_at": gtime.Now(),
			})
			return // Exit goroutine as we cannot proceed
		}

		decryptedMnemonic, err := util.DecryptStringWithPBKDF2(ctx, walletEntity.Mnemonic, req.Password, walletEntity.MnemonicSalt, walletEntity.MnemonicIterations)
		if err != nil {
			errMsg := fmt.Sprintf("BatchCreateAddress: Failed to decrypt mnemonic: %v", err)
			g.Log().Errorf(ctx, errMsg+" TaskID: %s", taskId)
			s.progressTasksRepo.UpdateProgressTask(ctx, taskId, g.Map{
				"status": consts.TaskStatusFailed, "error_message": errMsg, "updated_at": gtime.Now(),
			})
			return // Exit goroutine
		}
		seed := bip39.NewSeed(decryptedMnemonic, "") // Standard BIP39 seed generation, password for seed is empty

		for i := 1; i <= req.Count; i++ {
			// 每处理10个地址更新一次进度
			if i%10 == 0 || i == req.Count {
				progress := float64(processedCount) / float64(req.Count*2) * 100
				err := s.progressTasksRepo.UpdateProgressTask(ctx, taskId, g.Map{
					"progress":       progress,
					"processed_rows": processedCount,
					"updated_at":     gtime.Now(),
				})
				if err != nil {
					g.Log().Warningf(ctx, "Failed to update task progress: %v", err)
				}
			}

			// Derive TRON key using the decrypted seed
			tron_derived_private_key_str, tron_derived_index, err := tron.GetTronDerivedPrivateKey(ctx, seed, i)
			if err != nil {
				g.Log().Errorf(ctx, "TRON Key Derivation: %s: %v", codes.CodeWalletAddressGetDerivedKeyFailedLog.Message(), err) // Log with chain type
				continue
			}
			tron_ecdsaPrivKey, err := eth.PrivateKeyFromString(tron_derived_private_key_str) // Re-use eth's converter
			if err != nil {
				g.Log().Errorf(ctx, "TRON Key Conversion: %s: %v", codes.CodeWalletAddressConvertKeyFailedLog.Message(), err)
				continue
			}
			// Use PBKDF2 for encrypting derived private key, using main wallet's salt/iterations for mnemonic
			// and the password provided for this batch operation.
			if walletEntity.MnemonicSalt == nil || walletEntity.MnemonicIterations == 0 {
				g.Log().Errorf(ctx, "TRON Key Encryption: Mnemonic salt or iterations not set in wallet entity. Cannot encrypt derived key. TaskID: %s", taskId)
				// Update task to failed and exit goroutine
				s.progressTasksRepo.UpdateProgressTask(ctx, taskId, g.Map{
					"status":        consts.TaskStatusFailed,
					"error_message": "Mnemonic salt or iterations not set for PBKDF2 encryption of derived keys.",
					"updated_at":    gtime.Now(),
				})
				return // Exit goroutine
			}
			encrypted_tron_derived_private_key, err := util.EncryptStringWithPBKDF2(ctx, tron_derived_private_key_str, req.Password, walletEntity.MnemonicSalt, walletEntity.MnemonicIterations)
			if err != nil {
				g.Log().Errorf(ctx, "TRON Key Encryption (PBKDF2): %s: %v", codes.CodeWalletAddressEncryptKeyFailedLog.Message(), err)
				continue
			}

			// 生成TRON地址
			tron_addr_map := tron.GenerateAddress(tron_ecdsaPrivKey)
			tron_address_list = append(tron_address_list, g.Map{
				"address":            tron_addr_map["address"],
				"private_key":        encrypted_tron_derived_private_key,
				"type":               "TRON",
				"create_time":        gtime.Now(),
				"wallet_id":          walletEntity.Id,
				"bind_status":        0,
				"bind_at":            nil,
				"last_query_at":      nil,
				"status":             1,
				"chain_coin_balance": 0,
				"chain_usdt_balance": 0,
				"path":               tron_derived_index,
			})
			processedCount++

			// Derive ETH key using the decrypted seed
			eth_derived_private_key_str, eth_derived_index, err := eth.GetDerivedPrivateKey(ctx, seed, i)
			if err != nil {
				g.Log().Errorf(ctx, "ETH Key Derivation: %s: %v", codes.CodeWalletAddressGetDerivedKeyFailedLog.Message(), err)
				continue
			}
			eth_ecdsaPrivKey, err := eth.PrivateKeyFromString(eth_derived_private_key_str)
			if err != nil {
				g.Log().Errorf(ctx, "ETH Key Conversion: %s: %v", codes.CodeWalletAddressConvertKeyFailedLog.Message(), err)
				continue
			}
			// Use PBKDF2 for encrypting derived private key, using main wallet's salt/iterations for mnemonic
			if walletEntity.MnemonicSalt == nil || walletEntity.MnemonicIterations == 0 {
				g.Log().Errorf(ctx, "ETH Key Encryption: Mnemonic salt or iterations not set in wallet entity. Cannot encrypt derived key. TaskID: %s", taskId)
				// Update task to failed and exit goroutine
				s.progressTasksRepo.UpdateProgressTask(ctx, taskId, g.Map{
					"status":        consts.TaskStatusFailed,
					"error_message": "Mnemonic salt or iterations not set for PBKDF2 encryption of derived keys.",
					"updated_at":    gtime.Now(),
				})
				return // Exit goroutine
			}
			encrypted_eth_derived_private_key, err := util.EncryptStringWithPBKDF2(ctx, eth_derived_private_key_str, req.Password, walletEntity.MnemonicSalt, walletEntity.MnemonicIterations)
			if err != nil {
				g.Log().Errorf(ctx, "ETH Key Encryption (PBKDF2): %s: %v", codes.CodeWalletAddressEncryptKeyFailedLog.Message(), err)
				continue
			}

			// 生成ETH地址
			eth_address_str := eth.GenerateAddress(eth_ecdsaPrivKey)
			eth_address_list = append(eth_address_list, g.Map{
				"address":            eth_address_str,
				"private_key":        encrypted_eth_derived_private_key,
				"type":               "ETH",
				"create_time":        gtime.Now(),
				"wallet_id":          walletEntity.Id,
				"bind_status":        0,
				"bind_at":            nil,
				"last_query_at":      nil,
				"status":             1,
				"chain_coin_balance": 0,
				"chain_usdt_balance": 0,
				"path":               eth_derived_index,
			})
			processedCount++
		}

		address_list := append(tron_address_list, eth_address_list...)

		// 使用 Address Repository 批量创建地址
		err = s.addressRepo.BatchCreateAddresses(ctx, address_list)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressBatchCreateFailedLog.Message(), err)
			// 更新任务状态为失败
			updateErr := s.progressTasksRepo.UpdateProgressTask(ctx, taskId, g.Map{
				"status":        consts.TaskStatusFailed,
				"error_message": err.Error(),
				"updated_at":    gtime.Now(),
			})
			if updateErr != nil {
				g.Log().Errorf(ctx, "Failed to update task status to failed: %v", updateErr)
			}
		} else {
			g.Log().Infof(ctx, "%s: %d", codes.CodeWalletAddressBatchCreateSuccessLog.Message(), len(address_list))
			// 更新任务状态为完成
			updateErr := s.progressTasksRepo.UpdateProgressTask(ctx, taskId, g.Map{
				"status":         consts.TaskStatusCompleted,
				"progress":       100,
				"processed_rows": len(address_list),
				"updated_at":     gtime.Now(),
			})
			if updateErr != nil {
				g.Log().Errorf(ctx, "Failed to update task status to completed: %v", updateErr)
			}
		}
	}()

	return &v1.BatchCreateAddressRes{
		Success: true,
		TaskId:  taskId,
	}, nil
}

// 获取地址列表
func (s *sWallet) GetAddressList(ctx context.Context, req *v1.GetAddressListReq) (res *v1.GetAddressListRes, err error) {
	// 检查钱包是否存在 (虽然未使用 walletEntity 的字段，但保持检查)
	_, err = s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressCheckWalletExistsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressCheckWalletExistsFailed)
	}
	// Repository 的 GetWallet 会在找不到时返回 nil, nil，所以不需要显式检查 nil

	// 使用 Address Repository 获取地址列表
	options := repository.AddressQueryOptions{
		Page:       req.Page,
		Limit:      req.Limit,
		Address:    req.Address,
		Type:       req.Type,
		HasBalance: req.HasBalance,
		SortField:  req.SortField,
		SortOrder:  req.SortOrder,
	}
	// 如果 SortField 为空，Repository 实现应默认排序 (e.g., id DESC)

	addressEntities, total_size, err := s.addressRepo.ListAddresses(ctx, options)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressGetListFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressGetListFailed)
	}

	// 将 []*entity.Address 转换为 []v1.AddressInfo
	addressList := make([]v1.AddressInfo, 0, len(addressEntities))
	for _, addressEntity := range addressEntities {
		// Convert entity.Address to v1.AddressInfo
		info := v1.AddressInfo{
			Id:         addressEntity.Id,
			WalletId:   addressEntity.WalletId,
			Address:    addressEntity.Address,
			Type:       addressEntity.Type,
			Label:      addressEntity.Label, // Add Label mapping
			BindStatus: addressEntity.BindStatus,
			Status:     addressEntity.Status,
			// Convert *gtime.Time to string, handling nil
			BindAt:      formatGtimePtr(addressEntity.BindAt),
			LastQueryAt: formatGtimePtr(addressEntity.LastQueryAt),
			CreateAt:    formatGtimePtr(addressEntity.CreateAt), // Map CreateAt
			UpdateAt:    formatGtimePtr(addressEntity.UpdateAt), // Map UpdateAt
			// Convert float64 to string
			ChainCoinBalance: fmt.Sprintf("%f", addressEntity.ChainCoinBalance),
			ChainUsdtBalance: fmt.Sprintf("%f", addressEntity.ChainUsdtBalance),
			// Fields like PrivateKey, Path, LastCollect* are not in v1.AddressInfo or not mapped
		}
		addressList = append(addressList, info)
	}

	// 构建返回结果
	res = &v1.GetAddressListRes{
		Page: v1.Page{
			Current:  req.Page,
			PageSize: req.Limit,
			Total:    total_size,
		},
		List: addressList,
	}
	return res, nil
}

// 获取地址页面统计信息
func (s *sWallet) GetAddressStatistic(ctx context.Context, req *v1.GetAddressStatisticReq) (res *v1.GetAddressStatisticRes, err error) {
	// 检查钱包是否存在 (虽然未使用 walletEntity 的字段，但保持检查)
	_, err = s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressCheckWalletExistsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressCheckWalletExistsFailed)
	}

	// 使用 Address Repository 获取统计信息
	stats, err := s.addressRepo.GetAddressStatistics(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressGetStatsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressGetStatsFailed)
	}
	if stats == nil {
		// Handle case where stats might be nil (e.g., no addresses)
		stats = &repository.AddressStatistic{} // Initialize with zero values
	}
	res = &v1.GetAddressStatisticRes{
		TotalSize:             stats.TotalSize,
		ActiveAddress:         stats.ActiveAddress,
		EthTotalBalance:       fmt.Sprintf("%.2f", stats.EthTotalBalance),
		Erc20UsdtTotalBalance: fmt.Sprintf("%.2f", stats.Erc20UsdtTotalBalance),
		TrxTotalBalance:       fmt.Sprintf("%.2f", stats.TrxTotalBalance),
		Trc20UsdtTotalBalance: fmt.Sprintf("%.2f", stats.Trc20UsdtTotalBalance),
		EthAddressCount:       stats.EthAddressCount,
		TrxAddressCount:       stats.TrxAddressCount,
	}
	return
}

// 导出地址
func (s *sWallet) ExportAddress(ctx context.Context, req *v1.ExportAddressReq) (res *v1.ExportAddressRes, err error) {
	// 获取钱包实体
	walletEntity, err := s._getWalletEntity(ctx)
	if err != nil {
		return nil, err
	}

	// 验证谷歌验证码
	if err = s._validateGoogleCode(ctx, walletEntity, req.GoogleCode, req.Password); err != nil {
		return nil, err
	}

	// 验证密码 (简单比较)
	if err = s._validatePasswordSimple(ctx, walletEntity, req.Password); err != nil {
		return nil, err
	}

	// 使用 Address Repository 查找地址
	var addressEntities []*entity.Address
	filter := g.Map{}
	updateFilter := g.Map{}
	updateData := g.Map{}

	switch req.Type {
	case "all":
		// No specific filter needed for all
	case "unbound":
		filter["bind_status"] = 0
		updateFilter["bind_status"] = 0 // Filter for update
		updateData["bind_status"] = 1
		updateData["bind_at"] = gtime.Now()
	case "bound":
		filter["bind_status"] = 1
	default:
		return nil, codes.NewErrorf(codes.CodeWalletAddressInvalidExportType, "Invalid export type: %s", req.Type)
	}

	addressEntities, err = s.addressRepo.FindAddresses(ctx, filter)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressFindFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressFindFailed)
	}

	// 如果是导出未绑定地址，则更新状态
	if req.Type == "unbound" && len(addressEntities) > 0 {
		err = s.addressRepo.UpdateAddresses(ctx, updateData, updateFilter)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressUpdateBindStatusFailed.Message(), err)
			// Decide whether to proceed with export despite update failure
			return nil, codes.WrapError(err, codes.CodeWalletAddressUpdateBindStatusFailed)
		}
	}
	if len(addressEntities) == 0 {
		return nil, codes.NewError(codes.CodeWalletAddressNoAddressToExport)
	}

	// 生成CSV数据
	buffer := bytes.NewBuffer(nil)
	writer := csv.NewWriter(buffer)

	// 写入表头
	if err := writer.Write([]string{"chan", "address"}); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvHeaderFailed) // Re-use code
	}

	for _, addressEntity := range addressEntities {
		if err := writer.Write([]string{
			addressEntity.Type,
			addressEntity.Address,
		}); err != nil {
			// 记录错误并继续尝试写入其他行，或者直接返回错误
			g.Log().Warningf(ctx, "%s: %v, Address: %s", codes.CodeWalletCollectCsvRowFailedLog.Message(), err, addressEntity.Address) // Re-use code
			// return nil, gerror.Wrapf(err, "写入CSV行失败: %s", addressEntity.Address)
		}
	}

	if err := writer.Error(); err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvProcessError) // Re-use code
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		// Flush之后也可能报告错误
		return nil, codes.WrapError(err, codes.CodeWalletCollectCsvFlushFailed) // Re-use code
	}

	//生成文件名根据币种
	fileName := "address_" + time.Now().Format("2006-01-02 15:04:05") + ".csv"

	// 设置响应头
	r := g.RequestFromCtx(ctx)
	r.Response.Header().Set("Content-Type", "text/csv")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+fileName)

	// 写入CSV数据并退出
	r.Response.Write(buffer.Bytes())
	r.Response.Request.ExitAll()

	return nil, nil
}

// 刷新地址
func (s *sWallet) RefreshAddress(ctx context.Context, req *v1.RefreshAddressReq) (res *v1.RefreshAddressRes, err error) {
	// 检查钱包是否存在 (虽然未使用 walletEntity 的字段，但保持检查)
	_, err = s.walletRepo.GetWallet(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressCheckWalletExistsFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressCheckWalletExistsFailed)
	}

	// 使用 Address Repository 查找地址 (先用 FindAddresses 获取记录，再判断类型)
	foundAddresses, err := s.addressRepo.FindAddresses(ctx, g.Map{"address": req.Address})
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletAddressFindFailed.Message(), err)
		return nil, codes.WrapError(err, codes.CodeWalletAddressFindFailed)
	}
	if len(foundAddresses) == 0 {
		return nil, codes.NewError(codes.CodeWalletAddressNotFound)
	}
	addressEntity := foundAddresses[0] // Get the first (and should be only) result

	go func() {
		// 创建新的上下文，不依赖原始请求的ctx
		bgCtx := context.Background()
		var balance string
		var usdt_balance string
		//判断地址类型
		if addressEntity.Type == consts.NetworkTypeETH { // Use consts

			//获取eth余额
			balance, err = eth.GetETHBalance(addressEntity.Address)
			if err != nil {
				g.Log().Errorf(bgCtx, "%s: %v", codes.CodeWalletAddressGetEthBalanceFailedLog.Message(), err)
				// balance = "0"
				return
			}

			//获取erc20Usdt余额
			usdt_balance, err = eth.GetErc20UsdtBalance(addressEntity.Address)
			if err != nil {
				g.Log().Errorf(bgCtx, "%s: %v", codes.CodeWalletAddressGetErc20UsdtFailedLog.Message(), err)
				// usdt_balance = "0" // 无效赋值，移除
				return
			}

		} else if addressEntity.Type == consts.NetworkTypeTRX { // Use consts

			trxDecimalBalance, err := tron.GetTRXBalance(bgCtx, req.Address)
			if err != nil {
				g.Log().Errorf(bgCtx, "%s: %v", codes.CodeWalletAddressGetTrxBalanceFailedLog.Message(), err)
				// balance = "0" // 无效赋值，移除
				// usdt_balance = "0" // 无效赋值，移除
				return
			}
			balance = trxDecimalBalance.String()

			usdtBalance, err := tron.GetTRC20UsdtTokenBalance(bgCtx, req.Address)
			if err != nil {
				g.Log().Errorf(bgCtx, "%s: %v", "获取TRC20 USDT余额失败", err)
				// usdt_balance = "0" // 无效赋值，移除
				return
			}
			usdt_balance = usdtBalance.String()

		}

		// 使用 Address Repository 更新地址余额
		updateData := g.Map{
			"chain_coin_balance": balance,
			"chain_usdt_balance": usdt_balance,
			"last_query_at":      gtime.Now(),
		}
		err = s.addressRepo.UpdateAddress(bgCtx, addressEntity.Address, updateData)
		if err != nil {
			g.Log().Errorf(bgCtx, "%s: %v", codes.CodeWalletAddressUpdateBalanceFailedLog.Message(), err)
		}
	}()

	return &v1.RefreshAddressRes{
		Success: true,
	}, nil
}

// formatGtimePtr safely converts *gtime.Time to a formatted string.
// Returns an empty string if the pointer is nil.
func formatGtimePtr(t *gtime.Time) string {
	if t == nil {
		return ""
	}
	// You can choose a different format if needed, e.g., t.Layout("2006-01-02 15:04:05")
	return t.String()
}
