package wallet

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"

	v1 "wallet-api/api/wallet/v1"

	// "github.com/gogf/gf/v2/errors/gcode" // Removed unused import
	// "github.com/gogf/gf/v2/errors/gerror" // Removed unused import
	"github.com/gogf/gf/v2/frame/g"

	"wallet-api/internal/utility/crypto/eth"
	"wallet-api/internal/utility/crypto/tron"

	"wallet-api/internal/codes" // Import codes package
)

// 定义响应数据的结构体 (根据 API 返回的 JSON 结构)
type GasOracleResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Result  struct {
		LastBlock       string `json:"LastBlock"`
		SafeGasPrice    string `json:"SafeGasPrice"`
		ProposeGasPrice string `json:"ProposeGasPrice"`
		FastGasPrice    string `json:"FastGasPrice"`
		SuggestBaseFee  string `json:"SuggestBaseFee"`
		GasUsedRatio    string `json:"GasUsedRatio"`
	} `json:"result"`
}

// 获取gastracker
func (s *sWallet) GetGastracker(ctx context.Context, req *v1.GetGastrackerReq) (res *v1.GetGastrackerRes, err error) {

	apiKey := g.Cfg().MustGet(ctx, "ethscan.api_key").String()

	if apiKey == "" {
		return nil, codes.NewError(codes.CodeWalletNodeApiKeyNotConfigured)
	}

	// 构建请求URL
	url := fmt.Sprintf("https://api.etherscan.io/api?module=gastracker&action=gasoracle&apikey=%s", apiKey)

	// 发送HTTP请求
	resp, err := http.Get(url)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletNodeHttpRequestFailed)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, codes.NewErrorf(codes.CodeWalletNodeApiRequestFailed, "ethscan API request failed with status: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletNodeReadResponseFailed)
	}
	g.Log().Info(ctx, "gasOracleResponse", string(body))

	var gasOracleResponse GasOracleResponse
	err = json.Unmarshal(body, &gasOracleResponse)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletNodeParseResponseFailed)
	}
	g.Log().Info(ctx, "gasOracleResponse", gasOracleResponse)

	// 将字符串转换为float64
	safeGasPrice, err := strconv.ParseFloat(gasOracleResponse.Result.SafeGasPrice, 64)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletNodeParseSafeGasFailed)
	}

	proposeGasPrice, err := strconv.ParseFloat(gasOracleResponse.Result.ProposeGasPrice, 64)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletNodeParseProposeGasFailed)
	}

	fastGasPrice, err := strconv.ParseFloat(gasOracleResponse.Result.FastGasPrice, 64)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletNodeParseFastGasFailed)
	}

	suggestBaseFee, err := strconv.ParseFloat(gasOracleResponse.Result.SuggestBaseFee, 64)
	if err != nil {
		return nil, codes.WrapError(err, codes.CodeWalletNodeParseSuggestBaseFeeFailed)
	}

	// 计算并保留四位小数
	safeGasPrice = math.Round(safeGasPrice*1.8*10000) / 10000
	proposeGasPrice = math.Round(proposeGasPrice*1.8*10000) / 10000
	fastGasPrice = math.Round(fastGasPrice*1.8*10000) / 10000
	suggestBaseFee = math.Round(suggestBaseFee*1.8*10000) / 10000

	return &v1.GetGastrackerRes{
		LastBlock:       gasOracleResponse.Result.LastBlock,
		SafeGasPrice:    safeGasPrice,
		ProposeGasPrice: proposeGasPrice,
		FastGasPrice:    fastGasPrice,
		SuggestBaseFee:  suggestBaseFee,
		GasUsedRatio:    gasOracleResponse.Result.GasUsedRatio,
	}, nil
}

// 获取代币和usdt余额
func (s *sWallet) GetTokenBalance(ctx context.Context, req *v1.GetTokenBalanceReq) (res *v1.GetTokenBalanceRes, err error) {

	Type := req.Type
	var UsdtBalance string
	var TokenBalance string

	switch Type {
	case "ETH":
		UsdtBalance, err = eth.GetErc20UsdtBalance(req.Address)
		if err != nil {
			// 考虑是否需要返回错误，或者仅记录并继续获取ETH余额
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletNodeGetErc20UsdtFailedLog.Message(), err)
			// return nil, err // 如果需要严格处理错误，取消注释此行
			UsdtBalance = "0" // 假设获取失败则余额为0
		}
		TokenBalance, err = eth.GetETHBalance(req.Address)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletNodeGetEthFailedLog.Message(), err)
			return nil, err // 获取主币余额失败通常需要返回错误
		}
	case "TRON":
		trxDecimalBalance, err := tron.GetTRXBalance(ctx, req.Address)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", codes.CodeWalletNodeGetTronBalanceFailedLog.Message(), err)
			return nil, err
		}
		TokenBalance = trxDecimalBalance.String()

		usdtBalance, err := tron.GetTRC20UsdtTokenBalance(ctx, req.Address)
		if err != nil {
			g.Log().Errorf(ctx, "%s: %v", "获取TRC20 USDT余额失败", err)
			return nil, err
		}

		UsdtBalance = usdtBalance.String()
	default:
		return nil, codes.NewErrorf(codes.CodeWalletNodeUnsupportedType, "Unsupported type: %s", Type)
	}

	UsdtBalanceFloat, err := strconv.ParseFloat(UsdtBalance, 64)
	if err != nil {
		return nil, err
	}
	TokenBalanceFloat, err := strconv.ParseFloat(TokenBalance, 64)
	if err != nil {
		return nil, err
	}

	//截取4位小数
	UsdtBalance = fmt.Sprintf("%.4f", UsdtBalanceFloat)
	TokenBalance = fmt.Sprintf("%.4f", TokenBalanceFloat)

	return &v1.GetTokenBalanceRes{
		TokenBalance: TokenBalance,
		UsdtBalance:  UsdtBalance,
	}, nil
}

// // 获取tron hash 交易手续费详细信息
// func (s *sWallet) GetTronTransactionFee(ctx context.Context, req *v1.GetTronTransactionFeeReq) (res *v1.GetTronTransactionFeeRes, err error) {
// 	if cost, err := tron.GetTronTransactionsByHash(req.Hash); err != nil {
// 		return nil, err
// 	} else {
// 		return &v1.GetTronTransactionFeeRes{
// 			Cost: cost,
// 		}, nil
// 	}
// }
