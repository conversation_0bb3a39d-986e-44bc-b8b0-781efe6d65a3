package utility

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"

	"wallet-api/internal/codes" // 导入错误码包
)

// InitializeSystem 系统初始化主函数 (旧密码逻辑已移除)
// 注意：此函数现在为空，可以考虑移除或赋予新的初始化职责
func InitializeSystem(ctx context.Context) error {
	// 移除对旧密码初始化和验证函数的调用
	// 移除对 InitTokenSecret 的调用，因为密钥现在从配置读取

	return nil
}

// GetTokenSecret 返回用于JWT签名的tokenSecret
// 从配置中读取静态密钥
func GetTokenSecret() (string, error) {
	ctx := gctx.New()

	// 从配置中读取 jwt.secret
	secretKeyValue := g.Cfg().MustGet(ctx, "jwt.secret") // 使用 MustGet，如果未配置则会 panic

	secretKey := secretKeyValue.String()

	// 检查密钥是否为空
	if secretKey == "" {
		// 使用错误码记录日志和返回错误
		err := codes.NewError(codes.CodeUtilityTokenSecretNotConfigured)
		g.Log().Errorf(ctx, "Failed to get JWT secret: %v", err) // Log the error directly
		return "", err
	}
	return secretKey, nil
}

// 移除旧的 InitSystemPassword 和 ValidateStartupPassword 函数
// 移除旧的 init() 和 InitTokenSecret() 函数
