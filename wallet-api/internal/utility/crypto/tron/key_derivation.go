package tron

// import (
// 	"context"
// 	"crypto/ecdsa"
// 	"fmt"
// 	"wallet-api/internal/dao"
// 	"wallet-api/internal/model/entity"
// 	tronwallet "wallet-api/internal/utility/crypto/tron/tron-wallet"

// 	"github.com/btcsuite/btcd/btcutil/hdkeychain"
// 	"github.com/btcsuite/btcd/chaincfg"
// 	"github.com/ethereum/go-ethereum/common/hexutil"
// 	"github.com/ethereum/go-ethereum/crypto"
// 	"github.com/gogf/gf/v2/errors/gerror"
// 	"github.com/gogf/gf/v2/frame/g"
// 	"github.com/gogf/gf/v2/os/glog"
// "wallet-api/internal/utility/utils/bip39" // Added for NewSeed

// )

// // GetTronDerivedPrivateKey derives a private key from the wallet mnemonic and path
// func GetTronDerivedPrivateKey(ctx context.Context, addressPath int) (string, string, error) {
// 	// Get wallet mnemonic from config
// 	mnemonic, err := getWalletMnemonic(ctx)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to get wallet mnemonic")
// 	}

// 	// Validate mnemonic
// 	if !bip39.IsMnemonicValid(mnemonic) {
// 		return "", "", gerror.New("invalid mnemonic")
// 	}

// 	// Generate seed from mnemonic
// 	seed := bip39.NewSeed(mnemonic, "")

// 	// Create master key from seed
// 	masterKey, err := hdkeychain.NewMaster(seed, &chaincfg.MainNetParams)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to create master key from seed")
// 	}

// 	// Derive child key using path
// 	// For TRON, the path is m/44'/195'/0'/0/i where i is the account index
// 	// Derive purpose
// 	purpose, err := masterKey.Derive(hdkeychain.HardenedKeyStart + 44)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive purpose")
// 	}

// 	// Derive coin type (195' for TRON)
// 	coinType, err := purpose.Derive(hdkeychain.HardenedKeyStart + 195)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive coin type")
// 	}

// 	// Derive account (0')
// 	account, err := coinType.Derive(hdkeychain.HardenedKeyStart + 0)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive account")
// 	}

// 	// Derive change (0 for external)
// 	change, err := account.Derive(0)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive change")
// 	}

// 	// Derive address index
// 	addressKey, err := change.Derive(uint32(addressPath))
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to derive address index")
// 	}

// 	// Get private key
// 	privateKeyECDSA, err := addressKey.ECPrivKey()
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to get private key")
// 	}

// 	// Convert to Ethereum private key
// 	privateKey := privateKeyECDSA.ToECDSA()

// 	// Convert private key to string
// 	privateKeyBytes := crypto.FromECDSA(privateKey)
// 	privateKeyHex := hexutil.Encode(privateKeyBytes)[2:] // Remove 0x prefix

// 	// Get ETH address from private key
// 	publicKey := privateKey.Public()
// 	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
// 	if !ok {
// 		return "", "", gerror.New("failed to cast public key to ECDSA")
// 	}
// 	ethAddress := crypto.PubkeyToAddress(*publicKeyECDSA).Hex()

// 	// Convert ETH address to TRON address
// 	tronAddress, err := tronwallet.EthAddressToTronAddress(ethAddress)
// 	if err != nil {
// 		return "", "", gerror.Wrapf(err, "failed to convert ETH address %s to TRON address", ethAddress)
// 	}

// 	derivationPath := fmt.Sprintf("m/44'/195'/0'/0/%d", addressPath)
// 	glog.Infof(ctx, "Derived TRON address %s for path %s", tronAddress, derivationPath)

// 	return privateKeyHex, tronAddress, nil
// }

// // getWalletMnemonic gets the wallet mnemonic from the database
// func getWalletMnemonic(ctx context.Context) (string, error) {
// 	// Get wallet ID from config
// 	walletID := g.Cfg().MustGet(ctx, "wallet.id", 1).Int()

// 	// Get wallet from database
// 	var wallet entity.Wallets
// 	err := dao.Wallets.Ctx(ctx).Where(dao.Wallets.Columns().Id, walletID).Scan(&wallet)
// 	if err != nil {
// 		return "", gerror.Wrapf(err, "failed to get wallet with ID %d", walletID)
// 	}

// 	// Get mnemonic
// 	mnemonic := wallet.Mnemonic
// 	if mnemonic == "" {
// 		return "", gerror.Newf("wallet with ID %d has empty mnemonic", walletID)
// 	}

// 	return mnemonic, nil
// }

// // GetTronAddressForPath gets the TRON address for a given path
// func GetTronAddressForPath(ctx context.Context, path int) (string, error) {
// 	_, address, err := GetTronDerivedPrivateKey(ctx, path)
// 	if err != nil {
// 		return "", gerror.Wrapf(err, "failed to derive private key for path %d", path)
// 	}
// 	return address, nil
// }

// // GetPathForAddress gets the path for a given TRON address
// func GetPathForAddress(ctx context.Context, address string) (int, error) {
// 	// Query the address table
// 	var addressEntity entity.Address
// 	err := dao.Address.Ctx(ctx).
// 		Where(dao.Address.Columns().Address, address).
// 		Where(dao.Address.Columns().Type, "TRON").
// 		Scan(&addressEntity)
// 	if err != nil {
// 		return 0, gerror.Wrapf(err, "failed to get address entity for address %s", address)
// 	}

// 	return addressEntity.Path, nil
// }
