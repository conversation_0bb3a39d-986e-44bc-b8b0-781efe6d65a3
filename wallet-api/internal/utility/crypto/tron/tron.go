package tron

import (
	"bytes"
	"context"
	"crypto/ecdsa"
	"crypto/sha256"
	"encoding/hex"

	// "errors" // Removed unused import
	"fmt"
	"math/big"
	"strings"

	"wallet-api/internal/codes" // Import codes package
	"wallet-api/internal/dao"   // New import for TRON derivation

	// New import for TRON derivation

	bip32 "wallet-api/internal/utility/crypto/bip32"

	"github.com/ethereum/go-ethereum/crypto"
)

// Base58解码实现
var b58Alphabet = []byte("**********************************************************")

// DecodeBase58 将Base58字符串解码为字节数组
func DecodeBase58(input string) ([]byte, error) {
	result := big.NewInt(0)
	zeroBytes := 0

	for _, r := range input {
		if r == '1' {
			zeroBytes++
		} else {
			break
		}
	}

	payload := input[zeroBytes:]
	for _, r := range payload {
		charIndex := strings.IndexByte(string(b58Alphabet), byte(r))
		if charIndex < 0 {
			return nil, codes.NewError(codes.CodeUtilityInvalidBase58Character)
		}
		result.Mul(result, big.NewInt(58))
		result.Add(result, big.NewInt(int64(charIndex)))
	}

	decoded := result.Bytes()
	combined := make([]byte, zeroBytes+len(decoded))
	copy(combined[zeroBytes:], decoded)

	return combined, nil
}

func ValidatePrivateKey(privateKey string) bool {
	// 验证私钥是否有效

	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKey, "0x")

	// 私钥必须是64个十六进制字符（32字节）
	if len(cleanPrivateKey) != 64 {
		return false
	}

	// 检查是否是有效的十六进制字符串
	_, err := hex.DecodeString(cleanPrivateKey)
	if err != nil {
		return false
	}

	// 检查私钥是否在有效范围内（小于secp256k1曲线的阶）
	n := new(big.Int)
	n, ok := n.SetString(cleanPrivateKey, 16)
	if !ok {
		return false
	}

	// secp256k1曲线的阶
	// N = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
	curveOrder, _ := new(big.Int).SetString("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141", 16)

	// 私钥必须大于0且小于曲线的阶
	if n.Cmp(big.NewInt(0)) <= 0 || n.Cmp(curveOrder) >= 0 {
		return false
	}

	// 尝试从私钥导出公钥，如果成功则私钥有效
	_, err = crypto.HexToECDSA(cleanPrivateKey)
	return err == nil
}

func ValidateAddress(address string) bool {
	// TRON地址验证

	// 检查地址是否以'T'开头
	if len(address) == 0 || address[0] != 'T' {
		return false
	}

	// TRON地址应该是34个字符
	if len(address) != 34 {
		return false
	}

	// 尝试对Base58地址进行解码
	decoded, err := DecodeBase58(address)
	if err != nil {
		return false
	}

	// 解码后应该是25个字节
	// 格式: [前缀(1字节)][地址(20字节)][校验和(4字节)]
	if len(decoded) != 25 {
		return false
	}

	// 检查前缀，TRON使用0x41(65)作为地址前缀
	if decoded[0] != 0x41 {
		return false
	}

	// 验证校验和
	// 取前21个字节进行两次SHA256哈希，结果的前4个字节应该与最后4个字节相等
	addressBytes := decoded[:21]
	checksum := decoded[21:]

	h := sha256.Sum256(addressBytes)
	h2 := sha256.Sum256(h[:])
	calculatedChecksum := h2[:4]

	return bytes.Equal(checksum, calculatedChecksum)
}

// GenerateAddress 生成TRON地址
func GenerateAddress(privateKey *ecdsa.PrivateKey) map[string]string {
	// 生成随机的私钥
	// privateKey, _ := crypto.GenerateKey()

	// 将私钥转换为十六进制字符串
	privateKeyBytes := crypto.FromECDSA(privateKey)
	privateKeyHex := hex.EncodeToString(privateKeyBytes)

	// 从私钥获取公钥并直接获取地址
	ethAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	ethAddressBytes := ethAddress.Bytes()

	// 转换为TRON格式地址
	// TRON地址前缀为0x41
	tronAddressBytes := append([]byte{0x41}, ethAddressBytes...)

	// 计算校验和
	h := sha256.Sum256(tronAddressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// 合并地址和校验和
	fullAddressBytes := append(tronAddressBytes, checksum...)

	// Base58编码
	address := encodeBase58(fullAddressBytes)

	return map[string]string{
		"address":     address,
		"private_key": privateKeyHex,
	}
}

// encodeBase58 将字节数组编码为Base58字符串
func encodeBase58(input []byte) string {
	// Base58编码实现
	bigInt := new(big.Int).SetBytes(input)
	var result []byte

	base := big.NewInt(58)
	zero := big.NewInt(0)

	for bigInt.Cmp(zero) > 0 {
		mod := new(big.Int)
		bigInt.DivMod(bigInt, base, mod)
		result = append(result, b58Alphabet[mod.Int64()])
	}

	// 反转结果
	for i, j := 0, len(result)-1; i < j; i, j = i+1, j-1 {
		result[i], result[j] = result[j], result[i]
	}

	return string(result)
}

// PublicKeyToTronAddress converts an ECDSA public key to a TRON Base58 address string.
func PublicKeyToTronAddress(publicKey *ecdsa.PublicKey) string {
	ethAddress := crypto.PubkeyToAddress(*publicKey)
	ethAddressBytes := ethAddress.Bytes()

	// TRON address prefix is 0x41
	tronAddressBytes := append([]byte{0x41}, ethAddressBytes...)

	// Calculate checksum
	h := sha256.Sum256(tronAddressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// Append checksum
	fullAddressBytes := append(tronAddressBytes, checksum...)

	// Base58 encode
	return encodeBase58(fullAddressBytes)
}

// GetTronDerivedPath returns the derivation path for TRON.
func GetTronDerivedPath(index int) string {
	return fmt.Sprintf("m/44'/195'/0'/0/%d", index) // TRON's coin_type is 195
}

// GetTronDerivedPrivateKey derives a TRON private key.
// Returns the private key as a hex string, the derived index, and an error if any.
// Changed wallet *entity.Wallets to seed []byte
func GetTronDerivedPrivateKey(ctx context.Context, seed []byte, loopIndex int) (string, int, error) {
	// Get seed from wallet
	// seed, err := dao.Wallets.GetSeed(ctx) // Removed DAO call
	if len(seed) == 0 {
		return "", 0, codes.NewError(codes.CodeWalletAddressGetSeedFailed) // Or a more generic "seed empty" error
	}
	// No error to handle if seed is passed directly

	// Get max path for TRON.
	// The loopIndex in BatchCreateAddress starts from 1.
	// If maxId is the highest current index (e.g., 5), and loopIndex is 1,
	// the new derivedIndex should be maxId + 1 = 6.
	// So, derivedIndex = maxPath + loopIndex.
	maxPath, err := dao.Address.GetMaxPath(ctx, "TRON")
	if err != nil {
		// It's common for GetMaxPath to return an error (e.g., sql.ErrNoRows) if no records exist.
		// In such a case, maxPath should effectively be -1 so that the first index is 0.
		// Or, if GetMaxPath returns 0 and no error for no records, then maxPath = 0.
		// Let's assume GetMaxPath returns 0 and no error if no "TRON" type addresses exist yet.
		// If it returns a specific "not found" error, that should be handled to set maxPath appropriately.
		// For now, if there's an error, we return it. A more robust solution would check the error type.
		return "", 0, codes.WrapError(err, codes.CodeWalletAddressGetMaxPathFailed)
	}

	// derivedIndex will be the actual index stored in the 'path' column.
	// If maxPath is 0 (no TRON addresses yet) and loopIndex is 1, derivedIndex will be 1.
	// If maxPath is 5 and loopIndex is 1, derivedIndex will be 6.
	derivedIndex := maxPath + loopIndex

	hdPath := GetTronDerivedPath(derivedIndex)

	masterKey, err := bip32.NewMasterKey(seed)
	if err != nil {
		return "", 0, codes.WrapError(err, codes.CodeWalletAddressNewMasterKeyFailed)
	}
	childKey, err := masterKey.NewChildKeyByPathString(hdPath)
	if err != nil {
		return "", 0, codes.WrapError(err, codes.CodeWalletAddressNewChildKeyFailed)
	}
	childPrivateKey := hex.EncodeToString(childKey.Key.Key)
	return childPrivateKey, derivedIndex, nil
}

// DeriveTronPrivateKeyForKnownPath derives a TRON private key for a known derivation path index.
// knownPathIndex is the exact index (e.g., from address.Path) to be used.
// Changed wallet *entity.Wallets to seed []byte
func DeriveTronPrivateKeyForKnownPath(ctx context.Context, seed []byte, knownPathIndex int) (string, error) {
	// 1. Get seed from wallet
	// seed, err := dao.Wallets.GetSeed(ctx) // Removed DAO call
	if len(seed) == 0 {
		return "", codes.NewError(codes.CodeWalletAddressGetSeedFailed) // Or a more generic "seed empty" error
	}
	// No error to handle if seed is passed directly

	// 2. Construct the HD path using the knownPathIndex directly
	hdPath := GetTronDerivedPath(knownPathIndex) // GetTronDerivedPath formats "m/44'/195'/0'/0/%d"

	// 3. Create master key
	masterKey, err := bip32.NewMasterKey(seed)
	if err != nil {
		return "", codes.WrapError(err, codes.CodeWalletAddressNewMasterKeyFailed) // Use appropriate error code
	}

	// 4. Derive child key using the exact hdPath
	childKey, err := masterKey.NewChildKeyByPathString(hdPath)
	if err != nil {
		return "", codes.WrapError(err, codes.CodeWalletAddressNewChildKeyFailed) // Use appropriate error code
	}

	// 5. Return private key as hex string
	childPrivateKey := hex.EncodeToString(childKey.Key.Key)
	return childPrivateKey, nil
}
func GetTronFeeAddress(privateKey string) (string, error) {
	// 获取TRON矿工费地址

	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKey, "0x")

	// 将私钥转换为ECDSA私钥对象
	privateKeyECDSA, err := crypto.HexToECDSA(cleanPrivateKey)
	if err != nil {
		return "", err
	}

	// 从私钥获取公钥并生成以太坊格式地址
	ethAddress := crypto.PubkeyToAddress(privateKeyECDSA.PublicKey)
	ethAddressBytes := ethAddress.Bytes()

	// 转换为TRON格式地址
	// TRON地址前缀为0x41
	tronAddressBytes := append([]byte{0x41}, ethAddressBytes...)

	// 计算校验和
	h := sha256.Sum256(tronAddressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// 合并地址和校验和
	fullAddressBytes := append(tronAddressBytes, checksum...)

	// Base58编码
	address := encodeBase58(fullAddressBytes)

	return address, nil
}

func GetAddressFromPrivateKey(privateKeyStr string) (string, error) {
	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKeyStr, "0x")

	// 将私钥转换为ECDSA私钥对象
	privateKeyECDSA, err := crypto.HexToECDSA(cleanPrivateKey)
	if err != nil {
		return "", fmt.Errorf("无效的私钥: %v", err)
	}

	// 从私钥获取公钥并生成以太坊格式地址
	ethAddress := crypto.PubkeyToAddress(privateKeyECDSA.PublicKey)
	ethAddressBytes := ethAddress.Bytes()

	// 转换为TRON格式地址
	// TRON地址前缀为0x41
	tronAddressBytes := append([]byte{0x41}, ethAddressBytes...)

	// 计算校验和
	h := sha256.Sum256(tronAddressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// 合并地址和校验和
	fullAddressBytes := append(tronAddressBytes, checksum...)

	// Base58编码
	address := encodeBase58(fullAddressBytes)

	return address, nil
}
