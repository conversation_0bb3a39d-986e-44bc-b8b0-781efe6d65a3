package tron

import (
	"fmt"
	"math/big"
	"strings"

	"wallet-api/internal/codes" // Import codes package

	"github.com/shopspring/decimal"
)

// SunToTRX 将 SUN (波场的最小单位) 数量的字符串转换为 TRX 数量的字符串。
// 1 TRX = 10^tokenDecimals SUN。
// sun: SUN 数量的字符串形式。
// tokenDecimals: TRX 的小数位数（通常为 6）。
// 返回格式化后的 TRX 数量字符串和可能的错误。
func SunToTRX(sun string, tokenDecimals int) (string, error) {
	// 将 SUN 字符串转换为 big.Int
	sunValue := new(big.Int)
	_, success := sunValue.SetString(sun, 10)
	if !success {
		return "0", codes.NewErrorf(codes.CodeUtilityInvalidSunValue, "Invalid SUN value: %s", sun)
	}

	// 创建 10^tokenDecimals 的 big.Int 作为除数
	decimalFactor := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(tokenDecimals)), nil)

	// 使用 big.Float 进行精确的浮点数除法
	sunFloat := new(big.Float).SetInt(sunValue)
	decimalFloat := new(big.Float).SetInt(decimalFactor)
	trxFloat := new(big.Float).Quo(sunFloat, decimalFloat)

	// 将 big.Float 转换为字符串，使用 %f 格式化符
	trxStr := fmt.Sprintf("%f", trxFloat)

	// 移除末尾多余的零和小数点
	trxStr = strings.TrimRight(strings.TrimRight(trxStr, "0"), ".")
	if trxStr == "" { // 如果移除后为空（例如输入为 "0"），则返回 "0"
		return "0", nil
	}

	return trxStr, nil
}

// TrxtoSun 将 TRX 数量的字符串转换为 SUN 数量的字符串。
// trx: TRX 数量的字符串形式。
// tokenDecimals: TRX 的小数位数（通常为 6）。
// 返回 SUN 数量的字符串形式和可能的错误。
func TrxtoSun(trx string, tokenDecimals int) (string, error) {
	// 将 TRX 字符串转换为 big.Float
	trxValue := new(big.Float)
	_, success := trxValue.SetString(trx)
	if !success {
		return "0", codes.NewErrorf(codes.CodeUtilityInvalidTrxValue, "Invalid TRX value: %s", trx)
	}

	// 创建 10^tokenDecimals 的 big.Int 作为乘数
	decimalFactor := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(tokenDecimals)), nil)
	decimalFloat := new(big.Float).SetInt(decimalFactor)

	// 使用 big.Float 进行精确的浮点数乘法
	sunFloat := new(big.Float).Mul(trxValue, decimalFloat)

	// 将结果转换为 big.Int（取整数部分，因为 SUN 是最小单位）
	sunInt := new(big.Int)
	sunFloat.Int(sunInt) // .Int() 会截断小数部分

	// 返回 SUN 数量的字符串形式
	return sunInt.String(), nil
}

// FormatTokenValue 格式化 TRC20 代币值。
// 将表示最小单位代币数量的字符串和代币的小数位数，转换为带小数点的代币数量字符串。
// value: 最小单位代币数量的字符串形式。
// decimal: 代币的小数位数。
// 返回格式化后的代币数量字符串和可能的错误。
func FormatTokenValue(value string, decimal int) (string, error) {
	// 将最小单位字符串转换为 big.Int
	valueInt := new(big.Int)
	_, success := valueInt.SetString(value, 10)
	if !success {
		return "0", codes.NewErrorf(codes.CodeUtilityInvalidTokenValue, "Invalid Token value: %s", value)
	}

	// 创建 10^decimal 的 big.Int 作为除数
	decimalInt := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimal)), nil)

	// 使用 big.Float 进行精确的浮点数除法
	valueFloat := new(big.Float).SetInt(valueInt)
	decimalFloat := new(big.Float).SetInt(decimalInt)
	tokenFloat := new(big.Float).Quo(valueFloat, decimalFloat)

	// 将 big.Float 转换为字符串，使用 %f 格式化符
	tokenStr := fmt.Sprintf("%f", tokenFloat)

	// 移除末尾多余的零和小数点
	tokenStr = strings.TrimRight(strings.TrimRight(tokenStr, "0"), ".")
	if tokenStr == "" { // 如果移除后为空（例如输入为 "0"），则返回 "0"
		return "0", nil
	}

	return tokenStr, nil
}

func TrxtoSunDecimal(trx decimal.Decimal, tokenDecimals int) (decimal.Decimal, error) {
	// 将 TRX 转换为 SUN
	sun := trx.Mul(decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(tokenDecimals))))
	return sun, nil
}

func SunToTrxDecimal(sun decimal.Decimal, tokenDecimals int) (decimal.Decimal, error) {
	// 将 SUN 转换为 TRX
	trx := sun.Div(decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(tokenDecimals))))
	return trx, nil
}
