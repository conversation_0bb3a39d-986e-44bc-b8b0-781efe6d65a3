package tron

type Cost struct {
	NetFeeCost         int64 `json:"net_fee_cost"`
	Fee                int64 `json:"fee"`
	EnergyFeeCost      int64 `json:"energy_fee_cost"`
	NetUsage           int64 `json:"net_usage"`
	MultiSignFee       int64 `json:"multi_sign_fee"`
	NetFee             int64 `json:"net_fee"`
	EnergyPenaltyTotal int64 `json:"energy_penalty_total"`
	EnergyUsage        int64 `json:"energy_usage"`
	EnergyFee          int64 `json:"energy_fee"`
	EnergyUsageTotal   int64 `json:"energy_usage_total"`
	MemoFee            int64 `json:"memoFee"`
	OriginEnergyUsage  int64 `json:"origin_energy_usage"`
	AccountCreateFee   int64 `json:"account_create_fee"`
}
