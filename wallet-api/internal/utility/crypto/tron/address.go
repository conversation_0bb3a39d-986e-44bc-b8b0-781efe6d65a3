package tron

import (
	"context"
	"encoding/hex"
	"fmt"
	"wallet-api/internal/dao"
	entity "wallet-api/internal/model/entity"

	bip32 "wallet-api/internal/utility/crypto/bip32"

	"github.com/gogf/gf/v2/errors/gerror"
)

// 从地址中获取私钥
// Changed walletId int to seed []byte
func GetPrivateKeyByAddress(ctx context.Context, seed []byte, address string) (string, error) {

	logprefix := fmt.Sprintf("[TronSender] address: %s", address)

	var addressEntity *entity.Address
	err := dao.Address.Ctx(ctx).Where("address", address).Scan(&addressEntity)
	if err != nil {
		return "", gerror.Wrap(err, logprefix+" dao: failed to get address path")
	}
	if addressEntity == nil {
		return "", gerror.New(logprefix + " dao: address entity is nil")
	}

	// 1. Seed is now passed as a parameter
	// seed, err := dao.Wallets.GetSeed(ctx) // Removed DAO call
	if len(seed) == 0 {
		return "", gerror.New(logprefix + " seed cannot be empty")
	}
	// No error to wrap if seed is passed directly

	hdPath := GetTronDerivedPath(addressEntity.Path)

	masterKey, err := bip32.NewMasterKey(seed)
	if err != nil {
		return "", gerror.New(logprefix + " dao: failed to create master key")
	}
	childKey, err := masterKey.NewChildKeyByPathString(hdPath)
	if err != nil {
		return "", gerror.Wrap(err, logprefix+" dao: failed to create child key for address %s")
	}
	childPrivateKey := hex.EncodeToString(childKey.Key.Key)

	return childPrivateKey, nil
}
