package tron

import (
	"context"
	"encoding/hex"
	"fmt"
	"math/big"

	// Standard Tron SDK and Ethereum crypto libraries
	// For ABI encoding (alternative to go-ethereum for TRC20 specific encoding if preferred)
	// Renamed to ethcommon to avoid conflict if common is used elsewhere
	"github.com/ethereum/go-ethereum/crypto"
	// For TRON address manipulation
	// For hex utility / base58check from gotron-sdk
	"github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"google.golang.org/protobuf/proto" // For marshalling transaction RawData

	// Project-specific imports (from user's original code)
	cryptoUtil "wallet-api/internal/utility/crypto"
	tronwallet "wallet-api/internal/utility/crypto/tron/tron-wallet" // Assuming this provides SignTransaction and potentially address utils
	util "wallet-api/internal/utility/utils"

	// GoFrame and Shopspring Decimal
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

const (
	// DEFAULT_SUN_PER_BANDWIDTH_POINT is the typical cost in SUN for one bandwidth point if not covered by frozen TRX.
	// This value can fluctuate and ideally should be fetched from network parameters for highest accuracy.
	// As of recent observations, 1 TRX = 1000 BP, so 1 BP = 1000 SUN.
	DEFAULT_SUN_PER_BANDWIDTH_POINT int64 = 1000

	// TRX_SIGNATURE_BYTES_ESTIMATE is an approximate size of a transaction signature.
	// ECDSA signatures are typically 65 bytes. TRON uses a slightly different scheme sometimes, 67 is safer.
	TRX_SIGNATURE_BYTES_ESTIMATE int64 = 67

	// TRC20_TRANSFER_FUNCTION_SELECTOR is "transfer(address,uint256)"
	TRC20_TRANSFER_FUNCTION_SELECTOR = "a9059cbb"

	// DEFAULT_SUN_PER_ENERGY is the typical cost in SUN for one unit of energy.
	// This value can fluctuate. Current common rate is around 420 SUN/energy.
	// Should ideally be fetched from network parameters (e.g., GetChainParameters -> getEnergyFee).
	DEFAULT_SUN_PER_ENERGY int64 = 420
)

func SendTrxTransaction(ctx context.Context, privateKey string, fromAddress string, toAddress string, amount decimal.Decimal, logPrefix string) (string, error) {
	clientManager := cryptoUtil.GetInstance()
	tronClient, err := clientManager.GetDefaultTronClient(ctx)
	logger := g.Log()

	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to connect to TRON node", logPrefix)
	}

	if len(privateKey) <= 0 {
		errMsg := fmt.Sprintf("TRON private key not configured, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	if len(fromAddress) <= 0 {
		errMsg := fmt.Sprintf("TRON fee address not configured, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	addressFromPrivateKey, err := GetAddressFromPrivateKey(privateKey)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get address from private key: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return "", fmt.Errorf(errMsg)
	}
	if addressFromPrivateKey != fromAddress {
		errMsg := fmt.Sprintf("TRON fee address mismatch: %s (from private key) != %s (from config), logPrefix: %s", addressFromPrivateKey, fromAddress, logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	var asset *core.Account
	asset, err = tronClient.GetAccount(fromAddress)
	if err != nil {
		// Try failover on error
		tronClient, failoverErr := clientManager.HandleTronError(ctx, err)
		if failoverErr != nil {
			errMsg := fmt.Sprintf("Failed to get TRX balance for fromAddress %s: %v (failover also failed: %v)", fromAddress, err, failoverErr)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return "", fmt.Errorf(errMsg)
		}
		
		// Retry with failover client
		asset, err = tronClient.GetAccount(fromAddress)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to get TRX balance for fromAddress %s: %v", fromAddress, err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return "", fmt.Errorf(errMsg)
		}
	}
	if asset == nil { // Should not happen if GetAccount returns no error, but good to check
		errMsg := fmt.Sprintf("Failed to get TRX balance: account is nil for address %s", fromAddress)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return "", fmt.Errorf(errMsg)
	}

	decimalAmountSUN, err := TrxtoSunDecimal(amount, 6) // TRX has 6 decimal places to SUN
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, err))
		return "", fmt.Errorf("Failed to convert TRX to SUN: %v", err)
	}
	if decimalAmountSUN.LessThanOrEqual(decimal.Zero) {
		errMsg := fmt.Sprintf("TRON transfer amount must be greater than 0, got %s SUN, logPrefix: %s", decimalAmountSUN.String(), logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	decimalAssetBalance := decimal.NewFromInt(asset.Balance) // Balance is in SUN

	logger.Infof(ctx, "%s TRX Transfer Details: From=%s, To=%s, Amount=%s TRX (%s SUN), Balance=%s SUN",
		logPrefix, fromAddress, toAddress, amount.String(), decimalAmountSUN.String(), decimalAssetBalance.String())

	if decimalAssetBalance.LessThan(decimalAmountSUN) {
		errMsg := fmt.Sprintf("Insufficient TRX balance: %s SUN < %s SUN, logPrefix: %s", decimalAssetBalance.String(), decimalAmountSUN.String(), logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	tx, err := tronClient.Transfer(fromAddress, toAddress, decimalAmountSUN.IntPart())
	if err != nil {
		// Try failover on error
		tronClient, failoverErr := clientManager.HandleTronError(ctx, err)
		if failoverErr != nil {
			errMsg := fmt.Sprintf("Failed to create TRX transfer transaction: %v (failover also failed: %v)", err, failoverErr)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return "", fmt.Errorf(errMsg)
		}
		
		// Retry with failover client
		tx, err = tronClient.Transfer(fromAddress, toAddress, decimalAmountSUN.IntPart())
		if err != nil {
			errMsg := fmt.Sprintf("Failed to create TRX transfer transaction: %v", err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return "", fmt.Errorf(errMsg)
		}
	}
	logger.Infof(ctx, "%s Successfully created TRX transfer: %v", logPrefix, tx.Txid) // tx.Txid might be empty before signing

	privateKeyEcd, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to convert private key to ECDSA", logPrefix)
	}

	// Assuming tronwallet.SignTransaction can handle *api.TransactionExtention directly
	// and correctly signs tx.Transaction within it.
	signedTxExt, err := tronwallet.SignTransaction(tx, privateKeyEcd)
	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to sign TRX transaction", logPrefix)
	}
	if signedTxExt == nil || signedTxExt.Transaction == nil {
		return "", gerror.Newf("%s signing TRX transaction returned nil transaction data", logPrefix)
	}

	ret, err := tronClient.Broadcast(signedTxExt.Transaction)
	if err != nil {
		// Try failover on error
		tronClient, failoverErr := clientManager.HandleTronError(ctx, err)
		if failoverErr != nil {
			return "", gerror.Wrapf(err, "%s failed to broadcast TRX transaction (failover also failed: %v)", logPrefix, failoverErr)
		}
		
		// Retry with failover client
		ret, err = tronClient.Broadcast(signedTxExt.Transaction)
		if err != nil {
			return "", gerror.Wrapf(err, "%s failed to broadcast TRX transaction", logPrefix)
		}
	}

	if !ret.GetResult() {
		logger.Errorf(ctx, "%s Failed to broadcast TRX transaction: Code %s, Message: %s", logPrefix, ret.GetCode().String(), string(ret.GetMessage()))
		return "", fmt.Errorf("Failed to broadcast TRX transaction: Code %s, Message: %s", ret.GetCode().String(), string(ret.GetMessage()))
	}
	if ret.GetCode() != api.Return_SUCCESS { // Double check, though GetResult() should cover this
		logger.Errorf(ctx, "%s Failed to broadcast TRX transaction (API code not SUCCESS): Code %s, Message: %s", logPrefix, ret.GetCode().String(), string(ret.GetMessage()))
		return "", fmt.Errorf("Failed to broadcast TRX transaction (API code not SUCCESS): Code %s, Message: %s", ret.GetCode().String(), string(ret.GetMessage()))
	}

	// Txid should be populated in signedTxExt after signing
	txIDBytes := signedTxExt.GetTxid()
	if len(txIDBytes) == 0 {
		// Fallback to txid from the original tx object if signing didn't populate it, though it should.
		// The txid from `tronClient.Transfer` is usually the hash of the raw_data, which becomes the txid after signing.
		txIDBytes = tx.GetTxid()
		if len(txIDBytes) == 0 {
			return "", gerror.Newf("%s broadcast successful but TxID is empty", logPrefix)
		}
	}
	txIDHex := hex.EncodeToString(txIDBytes)
	logger.Infof(ctx, "%s Successfully broadcast TRX transaction: %s", logPrefix, txIDHex)
	return txIDHex, nil
}

// Helper function to convert a decimal amount to its smallest unit as *big.Int
// considering the token's decimals.
func amountToSmallestUnit(amount decimal.Decimal, decimals int32) (*big.Int, error) {
	if decimals < 0 {
		return nil, fmt.Errorf("decimals cannot be negative: %d", decimals)
	}
	scaleFactor := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(decimals)))
	smallestUnitAmountDecimal := amount.Mul(scaleFactor)

	// Ensure it's an integer value after scaling
	if !smallestUnitAmountDecimal.Equals(smallestUnitAmountDecimal.Truncate(0)) {
		return nil, fmt.Errorf("amount %s with %d decimals results in non-integer smallest unit: %s", amount.String(), decimals, smallestUnitAmountDecimal.String())
	}
	return smallestUnitAmountDecimal.BigInt(), nil
}

func SendTrc20Transaction(ctx context.Context, privateKey string, fromAddress string, toAddress string, amount decimal.Decimal, feeLimit int64, logPrefix string) (string, error) {
	return sendTrc20TransactionWithRetry(ctx, privateKey, fromAddress, toAddress, amount, feeLimit, logPrefix)
}

// sendTrc20TransactionWithRetry implements the actual TRC20 transaction sending with failover logic
func sendTrc20TransactionWithRetry(ctx context.Context, privateKey string, fromAddress string, toAddress string, amount decimal.Decimal, feeLimit int64, logPrefix string) (string, error) {
	clientManager := cryptoUtil.GetInstance()
	tronGRPCClient, err := clientManager.GetDefaultTronClient(ctx)
	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to connect to TRON node", logPrefix)
	}

	contractAddress, err := cryptoUtil.GetTrc20ContractAddress(ctx) // Assuming this gets the specific TRC20 contract you intend to use
	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to get TRC20 contract address", logPrefix)
	}
	if contractAddress == "" { // Ensure contract address is not empty after fetching
		return "", gerror.Newf("%s TRC20 contract address is empty", logPrefix)
	}

	logger := g.Log()

	if len(privateKey) <= 0 {
		errMsg := fmt.Sprintf("TRON private key not configured, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	if len(fromAddress) <= 0 {
		errMsg := fmt.Sprintf("TRON fromAddress not provided, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	if len(toAddress) <= 0 {
		errMsg := fmt.Sprintf("TRON toAddress not provided, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	if feeLimit <= 0 {
		errMsg := fmt.Sprintf("TRON TRC20 feeLimit must be greater than 0, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	addressFromPrivateKey, err := GetAddressFromPrivateKey(privateKey)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get address from private key: %v", err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return "", fmt.Errorf(errMsg)
	}
	if addressFromPrivateKey != fromAddress {
		errMsg := fmt.Sprintf("TRON address mismatch: %s (from private key) != %s (from input), logPrefix: %s", addressFromPrivateKey, fromAddress, logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	account, err := tronGRPCClient.GetAccount(fromAddress)
	if err != nil {
		// Try failover on error
		tronGRPCClient, failoverErr := clientManager.HandleTronError(ctx, err)
		if failoverErr != nil {
			errMsg := fmt.Sprintf("Failed to get TRX balance for gas for address %s: %v (failover also failed: %v)", fromAddress, err, failoverErr)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return "", fmt.Errorf(errMsg)
		}
		
		// Retry with failover client
		account, err = tronGRPCClient.GetAccount(fromAddress)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to get TRX balance for gas for address %s: %v", fromAddress, err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return "", fmt.Errorf(errMsg)
		}
	}
	if account == nil {
		errMsg := fmt.Sprintf("Failed to get TRX balance for gas: account is nil for address %s", fromAddress)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return "", fmt.Errorf(errMsg)
	}
	// Check if account has sufficient TRX balance for potential transaction fees
	// Note: feeLimit is the maximum allowed fee, not the required balance
	// We need to estimate actual fee requirements based on energy and bandwidth

	currentBalance := account.GetBalance()
	logger.Debugf(ctx, "%s Current TRX balance: %d SUN (%.6f TRX), Fee limit: %d SUN (%.1f TRX)",
		logPrefix, currentBalance, float64(currentBalance)/1_000_000, feeLimit, float64(feeLimit)/1_000_000)

	// Estimate minimum TRX needed for TRC20 transaction
	// This is a conservative estimate - actual fees are usually much lower
	// minRequiredTRX := int64(2_000_000) // 2 TRX minimum for TRC20 transactions

	// if currentBalance < minRequiredTRX {
	// 	errMsg := fmt.Sprintf("Insufficient TRX balance for TRC20 transaction: %d SUN available < %d SUN minimum required, logPrefix: %s", currentBalance, minRequiredTRX, logPrefix)
	// 	logger.Error(ctx, errMsg)
	// 	return "", fmt.Errorf(errMsg)
	// }

	// logger.Infof(ctx, "%s TRX balance check passed: %d SUN available >= %d SUN minimum required",
	// 	logPrefix, currentBalance, minRequiredTRX)

	// tokenDecimalsBigInt, err := tronGRPCClient.TRC20GetDecimals(contractAddress)
	tokenDecimals, err := util.GetTokenDecimals(ctx, "TRC20USDT")
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get TRC20 token decimals for contract %s: %v", contractAddress, err)
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return "", fmt.Errorf(errMsg)
	}

	amountInSmallestUnit, err := amountToSmallestUnit(amount, int32(tokenDecimals))
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s Failed to convert TRC20 amount to smallest unit: %v", logPrefix, err))
		return "", fmt.Errorf("Failed to convert TRC20 amount to smallest unit: %v", err)
	}

	if amountInSmallestUnit.Cmp(big.NewInt(0)) <= 0 {
		errMsg := fmt.Sprintf("TRC20 transfer amount must be greater than 0, logPrefix: %s", logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	currentTrc20Balance, err := tronGRPCClient.TRC20ContractBalance(fromAddress, contractAddress)
	if err != nil {
		// Try failover on error
		tronGRPCClient, failoverErr := clientManager.HandleTronError(ctx, err)
		if failoverErr != nil {
			errMsg := fmt.Sprintf("Failed to get TRC20 balance for %s from contract %s: %v (failover also failed: %v)", fromAddress, contractAddress, err, failoverErr)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return "", fmt.Errorf(errMsg)
		}
		
		// Retry with failover client
		currentTrc20Balance, err = tronGRPCClient.TRC20ContractBalance(fromAddress, contractAddress)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to get TRC20 balance for %s from contract %s: %v", fromAddress, contractAddress, err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
			return "", fmt.Errorf(errMsg)
		}
	}

	if currentTrc20Balance.Cmp(amountInSmallestUnit) < 0 {
		errMsg := fmt.Sprintf("Insufficient TRC20 token balance: %s (available) < %s (required), logPrefix: %s", currentTrc20Balance.String(), amountInSmallestUnit.String(), logPrefix)
		logger.Error(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	logger.Infof(ctx, "%s From: %s, To: %s, Amount: %s (smallest unit: %s), Contract: %s, FeeLimit: %d",
		logPrefix, fromAddress, toAddress, amount.String(), amountInSmallestUnit.String(), contractAddress, feeLimit)

	txExt, err := tronGRPCClient.TRC20Send(fromAddress, toAddress, contractAddress, amountInSmallestUnit, feeLimit)
	if err != nil {
		// Try failover on error
		tronGRPCClient, failoverErr := clientManager.HandleTronError(ctx, err)
		if failoverErr != nil {
			if txExt != nil && txExt.Result != nil && len(txExt.Result.Message) > 0 {
				errMsgDetail := fmt.Sprintf("Failed to create TRC20 transfer transaction: %v, SDK Message: %s (failover also failed: %v)", err, string(txExt.Result.Message), failoverErr)
				logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsgDetail))
				return "", fmt.Errorf(errMsgDetail)
			}
			errMsgBasic := fmt.Sprintf("Failed to create TRC20 transfer transaction: %v (failover also failed: %v)", err, failoverErr)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsgBasic))
			return "", fmt.Errorf(errMsgBasic)
		}
		
		// Retry with failover client
		txExt, err = tronGRPCClient.TRC20Send(fromAddress, toAddress, contractAddress, amountInSmallestUnit, feeLimit)
		if err != nil {
			if txExt != nil && txExt.Result != nil && len(txExt.Result.Message) > 0 {
				errMsgDetail := fmt.Sprintf("Failed to create TRC20 transfer transaction: %v, SDK Message: %s", err, string(txExt.Result.Message))
				logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsgDetail))
				return "", fmt.Errorf(errMsgDetail)
			}
			errMsgBasic := fmt.Sprintf("Failed to create TRC20 transfer transaction: %v", err)
			logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsgBasic))
			return "", fmt.Errorf(errMsgBasic)
		}
	}
	if txExt == nil || txExt.Transaction == nil {
		errMsg := "Failed to create TRC20 transfer: TRC20Send returned nil transaction extension or core transaction"
		logger.Error(ctx, fmt.Sprintf("%s %s", logPrefix, errMsg))
		return "", fmt.Errorf(errMsg)
	}

	privateKeyEcd, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to convert private key to ECDSA", logPrefix)
	}

	signedTxExt, err := tronwallet.SignTransaction(txExt, privateKeyEcd)
	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to sign TRC20 transaction", logPrefix)
	}
	if signedTxExt == nil || signedTxExt.Transaction == nil {
		return "", gerror.Newf("%s signing TRC20 transaction returned nil transaction data", logPrefix)
	}

	ret, err := tronGRPCClient.Broadcast(signedTxExt.Transaction)
	if err != nil {
		// Try failover on error
		tronGRPCClient, failoverErr := clientManager.HandleTronError(ctx, err)
		if failoverErr != nil {
			return "", gerror.Wrapf(err, "%s failed to broadcast TRC20 transaction (failover also failed: %v)", logPrefix, failoverErr)
		}
		
		// Retry with failover client
		ret, err = tronGRPCClient.Broadcast(signedTxExt.Transaction)
		if err != nil {
			return "", gerror.Wrapf(err, "%s failed to broadcast TRC20 transaction", logPrefix)
		}
	}

	if !ret.GetResult() {
		errMsg := fmt.Sprintf("Failed to broadcast TRC20 transaction: Code %s, Message: %s", ret.GetCode().String(), string(ret.GetMessage()))
		logger.Errorf(ctx, "%s %s", logPrefix, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	if ret.GetCode() != api.Return_SUCCESS {
		errMsg := fmt.Sprintf("Failed to broadcast TRC20 transaction after initial success check: Code %s, Message: %s", ret.GetCode().String(), string(ret.GetMessage()))
		logger.Errorf(ctx, "%s %s", logPrefix, errMsg)
		return "", fmt.Errorf(errMsg)
	}

	txIDBytes := signedTxExt.GetTxid()
	if len(txIDBytes) == 0 {
		// Fallback to txid from pre-signed extension if signed one is empty
		txIDBytes = txExt.GetTxid()
		if len(txIDBytes) == 0 {
			logger.Info(ctx, "%s TRC20 broadcast reported success but TxID is empty. Response: %s, %s", logPrefix, ret.GetCode().String(), string(ret.GetMessage()))
			return "", gerror.Newf("%s TRC20 broadcast successful but TxID is empty", logPrefix)
		}
		txIDHex := hex.EncodeToString(txIDBytes)
		logger.Infof(ctx, "%s Using TxID from pre-signed extension due to empty signed TxID: %s", logPrefix, txIDHex)
		return txIDHex, nil
	}

	txIDHex := hex.EncodeToString(txIDBytes)
	logger.Infof(ctx, "%s Successfully broadcast TRC20 transaction: %s. To: %s, Amount: %s, Contract: %s", logPrefix, txIDHex, toAddress, amount.String(), contractAddress)
	return txIDHex, nil
}

// EstimateTrxFee estimates the fee for a TRX transfer in SUN.
// TRX transfers primarily consume bandwidth. If the sender has enough frozen TRX for bandwidth,
// the direct TRX cost can be 0. This function estimates the cost if bandwidth is paid for with TRX.
func EstimateTrxFee(ctx context.Context, fromAddress string, toAddress string, amount decimal.Decimal, logPrefix string) (estimatedFeeSUN int64, err error) {
	logger := g.Log()
	clientManager := cryptoUtil.GetInstance()
	tronClient, err := clientManager.GetDefaultTronClient(ctx)
	if err != nil {
		return 0, gerror.Wrapf(err, "%s failed to connect to TRON node for fee estimation", logPrefix)
	}

	if len(fromAddress) == 0 || len(toAddress) == 0 {
		return 0, fmt.Errorf("%s fromAddress and toAddress must be provided", logPrefix)
	}
	if amount.LessThanOrEqual(decimal.Zero) {
		return 0, fmt.Errorf("%s transfer amount must be positive", logPrefix)
	}

	sunAmountDecimal, err := TrxtoSunDecimal(amount, 6) // TRX has 6 decimal places to SUN
	if err != nil {
		return 0, gerror.Wrapf(err, "%s failed to convert TRX amount to SUN for fee estimation", logPrefix)
	}

	// Get available bandwidth for the account
	availableBandwidth, err := GetAccountBandwidth(ctx, fromAddress, "", "")
	if err != nil {
		logger.Warningf(ctx, "%s Failed to get account bandwidth: %v. Using default estimate.", logPrefix, err)
		availableBandwidth = 0 // Assume no bandwidth available
	}
	logger.Debugf(ctx, "%s Available bandwidth for address %s: %d points", logPrefix, fromAddress, availableBandwidth)

	// Create an unsigned transaction to estimate its size.
	txExt, err := tronClient.Transfer(fromAddress, toAddress, sunAmountDecimal.IntPart())
	var estimatedBandwidthPoints int64

	if err != nil {
		logger.Warningf(ctx, "%s Failed to create dummy TRX transfer for fee estimation (this might be ok if addresses are new or inactive): %v. Using default estimate.", logPrefix, err)
		// Fallback to a general typical estimate if transaction creation fails
		estimatedBandwidthPoints = 250 + TRX_SIGNATURE_BYTES_ESTIMATE // Approx 250 bytes raw + signature
	} else if txExt == nil || txExt.Transaction == nil || txExt.Transaction.GetRawData() == nil {
		logger.Warningf(ctx, "%s Dummy TRX transaction for fee estimation was nil or had no RawData. Using default estimate.", logPrefix)
		estimatedBandwidthPoints = 250 + TRX_SIGNATURE_BYTES_ESTIMATE // Default fallback
	} else {
		rawDataBytes, err := proto.Marshal(txExt.Transaction.GetRawData())
		if err != nil {
			return 0, gerror.Wrapf(err, "%s failed to marshal RawData for fee estimation", logPrefix)
		}
		estimatedBandwidthPoints = int64(len(rawDataBytes)) + TRX_SIGNATURE_BYTES_ESTIMATE
		logger.Debugf(ctx, "%s Actual transaction size: %d raw data bytes + %d signature bytes = %d total bandwidth points",
			logPrefix, len(rawDataBytes), TRX_SIGNATURE_BYTES_ESTIMATE, estimatedBandwidthPoints)
	}

	// Calculate how much bandwidth needs to be paid for with TRX
	bandwidthShortfall := estimatedBandwidthPoints - availableBandwidth
	if bandwidthShortfall <= 0 {
		// Even with sufficient bandwidth, TRON may still charge fees for account activation or other reasons
		// Let's add a small buffer for potential additional costs
		logger.Infof(ctx, "%s Sufficient bandwidth available (%d >= %d), but adding safety buffer for potential fees",
			logPrefix, availableBandwidth, estimatedBandwidthPoints)

		// Add a small safety buffer (0.1 TRX = 100,000 SUN) for potential account activation or other fees
		safetyBufferSUN := int64(100000) // 0.1 TRX
		logger.Infof(ctx, "%s Adding safety buffer: %d SUN (0.1 TRX) for potential additional costs", logPrefix, safetyBufferSUN)
		return safetyBufferSUN, nil
	}

	// Calculate TRX cost for the bandwidth shortfall
	estimatedFeeSUN = bandwidthShortfall * DEFAULT_SUN_PER_BANDWIDTH_POINT

	logger.Infof(ctx, "%s Bandwidth shortfall: %d points (need %d, have %d), estimated fee: %d SUN",
		logPrefix, bandwidthShortfall, estimatedBandwidthPoints, availableBandwidth, estimatedFeeSUN)

	return estimatedFeeSUN, nil
}

