package eth

import (
	"context"
	"encoding/hex"
	"fmt"
	"wallet-api/internal/dao"
	entity "wallet-api/internal/model/entity"
	bip32 "wallet-api/internal/utility/crypto/bip32"

	"github.com/gogf/gf/v2/errors/gerror"
)

// 从地址中获取私钥
// The walletId parameter is removed as seed is now passed directly.
func GetPrivateKeyByAddress(ctx context.Context, seed []byte, address string) (string, error) {

	logprefix := fmt.Sprintf("[ETHSender] address: %s", address)
	var addressEntity *entity.Address
	err := dao.Address.Ctx(ctx).Where("address", address).Scan(&addressEntity)
	if err != nil {
		return "", gerror.Wrap(err, logprefix+" dao: failed to get address path")
	}
	if addressEntity == nil {
		return "", gerror.New(logprefix + " dao: address entity is nil")
	}

	// 1. Seed is now passed as a parameter
	// seed, err := dao.Wallets.GetSeed(ctx) // Removed DAO call
	if len(seed) == 0 {
		return "", gerror.New(logprefix + " seed cannot be empty")
	}
	// No error to wrap if seed is passed directly

	// 2. Get HD path for ETH
	hdPath := GetDerivedPath(addressEntity.Path)
	if hdPath == "" {
		return "", gerror.Newf(logprefix+" dao: failed to get HD path for address %s", address)
	}

	rp, err := bip32.NewMasterKey(seed)
	if err != nil {
		return "", gerror.Newf(logprefix+" dao: failed to create master key for address %s", address)
	}
	c, err := rp.NewChildKeyByPathString(hdPath)
	if err != nil {
		return "", gerror.Newf(logprefix+" dao: failed to create child key for address %s", address)
	}
	childPrivateKey := hex.EncodeToString(c.Key.Key)

	return childPrivateKey, nil
}
