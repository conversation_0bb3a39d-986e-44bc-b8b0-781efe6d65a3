package eth

import (
	"context"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"wallet-api/internal/utility/crypto" // Added import for crypto package

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/gogf/gf/v2/frame/g"
)

// BroadcastETH broadcasts an ETH transfer transaction
func BroadcastETH(ctx context.Context, fromAddress, toAddress, privateKeyHex string, amount string, gasPrice string, gasLimit uint64) (bool, string, error) {
	// Convert ETH amount to Wei
	weiAmount, ok := new(big.Int).SetString(amount, 10)
	if !ok {
		return false, "", fmt.Errorf("invalid amount: %s", amount)
	}

	// Get Ethereum client from ClientManager
	client, err := crypto.GetInstance().GetDefaultEthClient(ctx)
	if err != nil {
		return false, "", fmt.Errorf("failed to get Ethereum client: %w", err)
	}

	// Create, sign, and broadcast the transaction
	return TransferETH(ctx, client, fromAddress, toAddress, privateKeyHex, weiAmount, gasPrice, gasLimit)
}

// BroadcastERC20 broadcasts an ERC20 token transfer transaction
func BroadcastERC20(ctx context.Context, fromAddress, toAddress, privateKeyHex, contractAddress, amount string, gasPrice string, gasLimit uint64) (bool, string, error) {
	// Convert token amount to smallest unit
	tokenAmount, ok := new(big.Int).SetString(amount, 10)
	if !ok {
		return false, "", fmt.Errorf("invalid amount: %s", amount)
	}

	// Get Ethereum client from ClientManager
	client, err := crypto.GetInstance().GetDefaultEthClient(ctx)
	if err != nil {
		return false, "", fmt.Errorf("failed to get Ethereum client: %w", err)
	}

	// Create, sign, and broadcast the transaction
	return TransferERC20(ctx, client, fromAddress, toAddress, privateKeyHex, contractAddress, tokenAmount, gasPrice, gasLimit)
}

// TransferETH performs a complete ETH transfer: creates, signs, and broadcasts the transaction
func TransferETH(ctx context.Context, client *ethclient.Client, fromAddress, toAddress, privateKeyHex string, amount *big.Int, gasPrice string, gasLimit uint64) (bool, string, error) {
	// Validate addresses
	if !common.IsHexAddress(fromAddress) {
		return false, "", fmt.Errorf("invalid from address: %s", fromAddress)
	}
	if !common.IsHexAddress(toAddress) {
		return false, "", fmt.Errorf("invalid to address: %s", toAddress)
	}

	// Convert addresses to checksum format
	fromAddr := common.HexToAddress(fromAddress)
	toAddr := common.HexToAddress(toAddress)

	// Get private key
	privateKey, err := PrivateKeyFromString(privateKeyHex)
	if err != nil {
		g.Log().Error(ctx, "Failed to convert private key:", err)
		return false, "", fmt.Errorf("failed to convert private key: %w", err)
	}

	// Get nonce
	nonce, err := client.PendingNonceAt(ctx, fromAddr)
	if err != nil {
		g.Log().Error(ctx, "Failed to get nonce:", err)
		return false, "", fmt.Errorf("failed to get nonce: %w", err)
	}

	// Get gas price if not provided
	var gasPriceBig *big.Int
	if gasPrice == "" {
		gasPriceBig, err = client.SuggestGasPrice(ctx)
		if err != nil {
			g.Log().Error(ctx, "Failed to get gas price:", err)
			return false, "", fmt.Errorf("failed to get gas price: %w", err)
		}
	} else {
		var ok bool
		gasPriceBig, ok = new(big.Int).SetString(gasPrice, 10)
		if !ok {
			return false, "", fmt.Errorf("invalid gas price: %s", gasPrice)
		}
	}

	// Get gas limit if not provided
	if gasLimit == 0 {
		gasLimit = 21000 // Default gas limit for ETH transfers
	}

	// Create transaction
	tx := types.NewTransaction(nonce, toAddr, amount, gasLimit, gasPriceBig, nil)

	// Get chain ID
	chainID, err := client.NetworkID(ctx)
	if err != nil {
		g.Log().Error(ctx, "Failed to get chain ID:", err)
		return false, "", fmt.Errorf("failed to get chain ID: %w", err)
	}

	// Sign transaction
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		g.Log().Error(ctx, "Failed to sign transaction:", err)
		return false, "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	// Broadcast transaction
	err = client.SendTransaction(ctx, signedTx)
	if err != nil {
		g.Log().Error(ctx, "Failed to broadcast transaction:", err)
		return false, signedTx.Hash().Hex(), fmt.Errorf("failed to broadcast transaction: %w", err)
	}

	g.Log().Infof(ctx, "ETH transaction sent successfully, TxID: %s", signedTx.Hash().Hex())
	return true, signedTx.Hash().Hex(), nil
}

// TransferERC20 performs a complete ERC20 token transfer: creates, signs, and broadcasts the transaction
func TransferERC20(ctx context.Context, client *ethclient.Client, fromAddress, toAddress, privateKeyHex, contractAddress string, amount *big.Int, gasPrice string, gasLimit uint64) (bool, string, error) {
	// Validate addresses
	if !common.IsHexAddress(fromAddress) {
		return false, "", fmt.Errorf("invalid from address: %s", fromAddress)
	}
	if !common.IsHexAddress(toAddress) {
		return false, "", fmt.Errorf("invalid to address: %s", toAddress)
	}
	if !common.IsHexAddress(contractAddress) {
		return false, "", fmt.Errorf("invalid contract address: %s", contractAddress)
	}

	// Convert addresses to checksum format
	fromAddr := common.HexToAddress(fromAddress)
	toAddr := common.HexToAddress(toAddress)
	contractAddr := common.HexToAddress(contractAddress)

	// Get private key
	privateKey, err := PrivateKeyFromString(privateKeyHex)
	if err != nil {
		g.Log().Error(ctx, "Failed to convert private key:", err)
		return false, "", fmt.Errorf("failed to convert private key: %w", err)
	}

	// Get nonce
	nonce, err := client.PendingNonceAt(ctx, fromAddr)
	if err != nil {
		g.Log().Error(ctx, "Failed to get nonce:", err)
		return false, "", fmt.Errorf("failed to get nonce: %w", err)
	}

	// Get gas price if not provided
	var gasPriceBig *big.Int
	if gasPrice == "" {
		gasPriceBig, err = client.SuggestGasPrice(ctx)
		if err != nil {
			g.Log().Error(ctx, "Failed to get gas price:", err)
			return false, "", fmt.Errorf("failed to get gas price: %w", err)
		}
	} else {
		var ok bool
		gasPriceBig, ok = new(big.Int).SetString(gasPrice, 10)
		if !ok {
			return false, "", fmt.Errorf("invalid gas price: %s", gasPrice)
		}
	}

	// Create ERC20 transfer function call data
	// transfer function signature: 0xa9059cbb
	// Pad address to 32 bytes (64 hex chars) for ABI encoding
	addrWithoutPrefix := strings.TrimPrefix(toAddr.Hex(), "0x")
	paddedAddr := fmt.Sprintf("%064s", addrWithoutPrefix)

	// Pad amount to 32 bytes (64 hex chars) for ABI encoding
	amountHex := fmt.Sprintf("%064s", amount.Text(16))

	// Combine function signature and parameters
	data := "0xa9059cbb" + paddedAddr + amountHex

	// Convert data to bytes
	dataBytes, err := hex.DecodeString(data[2:]) // Remove 0x prefix
	if err != nil {
		g.Log().Error(ctx, "Failed to decode data:", err)
		return false, "", fmt.Errorf("failed to decode data: %w", err)
	}

	// Get gas limit if not provided
	if gasLimit == 0 {
		// Estimate gas for ERC20 transfer
		gasLimit, err = client.EstimateGas(ctx, ethereum.CallMsg{
			From:     fromAddr,
			To:       &contractAddr,
			GasPrice: gasPriceBig,
			Data:     dataBytes,
		})
		if err != nil {
			g.Log().Error(ctx, "Failed to estimate gas:", err)
			// Use default gas limit for ERC20 transfers if estimation fails
			gasLimit = 100000
		}

		// Add some buffer to the gas limit
		gasLimit = uint64(float64(gasLimit) * 1.2)
	}

	// Create transaction
	tx := types.NewTransaction(nonce, contractAddr, big.NewInt(0), gasLimit, gasPriceBig, dataBytes)

	// Get chain ID
	chainID, err := client.NetworkID(ctx)
	if err != nil {
		g.Log().Error(ctx, "Failed to get chain ID:", err)
		return false, "", fmt.Errorf("failed to get chain ID: %w", err)
	}

	// Sign transaction
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		g.Log().Error(ctx, "Failed to sign transaction:", err)
		return false, "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	// Broadcast transaction
	err = client.SendTransaction(ctx, signedTx)
	if err != nil {
		g.Log().Error(ctx, "Failed to broadcast transaction:", err)
		return false, signedTx.Hash().Hex(), fmt.Errorf("failed to broadcast transaction: %w", err)
	}

	g.Log().Infof(ctx, "ERC20 transaction sent successfully, TxID: %s", signedTx.Hash().Hex())
	return true, signedTx.Hash().Hex(), nil
}
