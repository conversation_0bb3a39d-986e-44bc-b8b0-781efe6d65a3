package eth

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"wallet-api/internal/dao"
	"wallet-api/internal/model/entity"

	bip32 "wallet-api/internal/utility/crypto/bip32"

	"wallet-api/internal/utility/utils/bip39" // Added for NewSeed

	"github.com/ethereum/go-ethereum/accounts"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// GetDerivedPrivateKeyHD derives a private key from the wallet mnemonic and path using HD wallet
func GetDerivedPrivateKeyHD(ctx context.Context, addressPath int) (string, string, error) {
	// Get wallet mnemonic from config
	mnemonic, err := getWalletMnemonicHD(ctx)
	if err != nil {
		return "", "", gerror.Wrapf(err, "failed to get wallet mnemonic")
	}

	// Validate mnemonic
	if !bip39.IsMnemonicValid(mnemonic) {
		return "", "", gerror.New("invalid mnemonic")
	}

	// Generate seed from mnemonic
	seed := bip39.NewSeed(mnemonic, "")

	// Derive path
	derivationPath := fmt.Sprintf("m/44'/60'/0'/0/%d", addressPath)

	// Create master key and derive child key
	masterKey, err := bip32.NewMasterKey(seed)
	if err != nil {
		return "", "", gerror.Wrapf(err, "failed to create master key from seed")
	}

	childKey, err := masterKey.NewChildKeyByPathString(derivationPath)
	if err != nil {
		return "", "", gerror.Wrapf(err, "failed to derive child key for path %s", derivationPath)
	}

	// Get private key
	privateKeyHex := hexutil.Encode(childKey.Key.Key)[2:] // Remove 0x prefix

	// Convert to ECDSA private key
	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return "", "", gerror.Wrapf(err, "failed to convert private key to ECDSA")
	}

	// Get address from private key
	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", "", gerror.New("failed to cast public key to ECDSA")
	}
	address := crypto.PubkeyToAddress(*publicKeyECDSA).Hex()

	glog.Infof(ctx, "Derived ETH address %s for path %s", address, derivationPath)

	return privateKeyHex, address, nil
}

// getWalletMnemonicHD gets the wallet mnemonic from the database
func getWalletMnemonicHD(ctx context.Context) (string, error) {
	// Get wallet ID from config
	walletID := g.Cfg().MustGet(ctx, "wallet.id", 1).Int()

	// Get wallet from database
	var wallet entity.Wallets
	err := dao.Wallets.Ctx(ctx).Where(dao.Wallets.Columns().Id, walletID).Scan(&wallet)
	if err != nil {
		return "", gerror.Wrapf(err, "failed to get wallet with ID %d", walletID)
	}

	// Get mnemonic
	mnemonic := wallet.Mnemonic
	if mnemonic == "" {
		return "", gerror.Newf("wallet with ID %d has empty mnemonic", walletID)
	}

	return mnemonic, nil
}

// DeriveAddressFromPathHD derives an Ethereum address from a derivation path using HD wallet
func DeriveAddressFromPathHD(mnemonic string, path accounts.DerivationPath) (string, error) {
	// Validate mnemonic
	if !bip39.IsMnemonicValid(mnemonic) {
		return "", gerror.New("invalid mnemonic")
	}

	// Generate seed from mnemonic
	seed := bip39.NewSeed(mnemonic, "")

	// Convert accounts.DerivationPath to string path
	pathStr := path.String()

	// Create master key and derive child key
	masterKey, err := bip32.NewMasterKey(seed)
	if err != nil {
		return "", gerror.Wrapf(err, "failed to create master key from seed")
	}

	childKey, err := masterKey.NewChildKeyByPathString(pathStr)
	if err != nil {
		return "", gerror.Wrapf(err, "failed to derive child key for path %s", pathStr)
	}

	// Get private key
	privateKeyHex := hexutil.Encode(childKey.Key.Key)[2:] // Remove 0x prefix

	// Convert to ECDSA private key
	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return "", gerror.Wrapf(err, "failed to convert private key to ECDSA")
	}

	// Get address from private key
	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", gerror.New("failed to cast public key to ECDSA")
	}
	address := crypto.PubkeyToAddress(*publicKeyECDSA).Hex()

	return address, nil
}
