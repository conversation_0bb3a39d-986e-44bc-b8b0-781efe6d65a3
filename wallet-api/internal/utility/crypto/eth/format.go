package eth

import (
	"math/big"
	// Import codes package
	"github.com/shopspring/decimal"
)

// FormatTokenValue 格式化代币值，基于小数位数。
// 它将一个表示最小单位代币数量的字符串和一个整数表示的小数位数，
// 转换为一个格式化后的、带小数点的代币数量字符串。
// valueStr: 最小单位代币数量的字符串形式。
// decimal: 代币的小数位数。
// 返回格式化后的代币数量字符串，保留最多6位小数（根据原始代码逻辑）。
func FormatTokenValue(valueStr string, decimal int) string {
	// 将大整数字符串转为 big.Int
	value, ok := new(big.Int).SetString(valueStr, 10)
	if !ok {
		// 如果转换失败，返回 "0"
		return "0"
	}

	// 计算除数 (10^decimal)
	divisor := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimal)), nil)

	// 使用 big.Float 进行精确的浮点数除法
	floatValue := new(big.Float).Quo(
		new(big.Float).SetInt(value),   // 被除数：代币数量 (big.Int)
		new(big.Float).SetInt(divisor), // 除数：10^decimal (big.Int)
	)

	// 将 big.Float 转换为字符串，格式为 'f' (普通浮点数表示法)，保留 6 位小数。
	// 注意：这里固定保留6位小数，可能与某些代币的实际精度不符，但遵循了原始代码逻辑。
	// 如果需要根据 decimal 参数动态调整保留位数，可以修改这里的 '6'。
	return floatValue.Text('f', 6)
}

func EthToWei(eth decimal.Decimal) (decimal.Decimal, error) {

	// 创建一个 big.Float 表示 1 ETH (10^18 Wei)
	etherDivisor := decimal.NewFromInt(1e18) // 1e18 = 1 * 10^18

	// 使用 decimal.Decimal 进行精确的浮点数乘法，将 ETH 转换为 Wei
	wei := eth.Mul(etherDivisor)

	return wei, nil
}
func WeiToETH(wei decimal.Decimal) (decimal.Decimal, error) {
	// 创建一个 big.Float 表示 1 ETH (10^18 Wei)
	etherDivisor := decimal.NewFromInt(1e18) // 1e18 = 1 * 10^18

	// 使用 decimal.Decimal 进行精确的浮点数除法，将 Wei 转换为 ETH
	eth := wei.Div(etherDivisor)

	return eth, nil
}
