package crypto

import (
	"context"
	"strings"
	"sync"

	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

var (
	// Instance is the singleton instance of ClientManager
	instance *ClientManager
	once     sync.Once
)

// ClientManager manages blockchain clients as singletons
type ClientManager struct {
	ethClients        map[string]*ethclient.Client
	tronClients       map[string]*client.GrpcClient
	currentTronClient *client.GrpcClient
	ethMutex          sync.RWMutex
	tronMutex         sync.RWMutex
}

// GetInstance returns the singleton instance of ClientManager
func GetInstance() *ClientManager {
	once.Do(func() {
		instance = &ClientManager{
			ethClients:  make(map[string]*ethclient.Client),
			tronClients: make(map[string]*client.GrpcClient),
		}
	})
	return instance
}

// GetEthClient returns an ETH client for the given RPC URL
func (cm *ClientManager) GetEthClient(ctx context.Context, rpcURL string) (*ethclient.Client, error) {
	// Check if client already exists
	cm.ethMutex.RLock()
	client, exists := cm.ethClients[rpcURL]
	cm.ethMutex.RUnlock()

	if exists {
		return client, nil
	}

	// Create new client
	cm.ethMutex.Lock()
	defer cm.ethMutex.Unlock()

	// Check again in case another goroutine created it while we were waiting for the lock
	client, exists = cm.ethClients[rpcURL]
	if exists {
		return client, nil
	}

	// Create new client
	newClient, err := ethclient.Dial(rpcURL)
	if err != nil {
		return nil, err
	}

	// Store client
	cm.ethClients[rpcURL] = newClient
	glog.Infof(ctx, "Created new ETH client for RPC URL: %s", rpcURL)

	return newClient, nil
}

// GetTronClient returns a TRON client for the given RPC URL
func (cm *ClientManager) GetTronClient(ctx context.Context, rpcURL string, apiKey string) (*client.GrpcClient, error) {
	// Check if client already exists
	cm.tronMutex.RLock()
	tronClient, exists := cm.tronClients[rpcURL]
	cm.tronMutex.RUnlock()

	if exists {
		return tronClient, nil
	}

	// Create new client
	cm.tronMutex.Lock()
	defer cm.tronMutex.Unlock()

	// Check again in case another goroutine created it while we were waiting for the lock
	tronClient, exists = cm.tronClients[rpcURL]
	if exists {
		return tronClient, nil
	}

	// Create new client
	newClient := client.NewGrpcClient(rpcURL)
	// Set API Key if provided
	if apiKey != "" {
		if err := newClient.SetAPIKey(apiKey); err != nil {
			// 虽然SetAPIKey目前返回nil, 但良好实践是检查错误
			return nil, gerror.Wrapf(err, "failed to set API key for TRON client at %s", rpcURL)
		}
	}
	// Explicitly use insecure transport credentials
	dialOptions := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	}
	if err := newClient.Start(dialOptions...); err != nil {
		return nil, err
	}

	// Store client
	cm.tronClients[rpcURL] = newClient
	glog.Infof(ctx, "Created new TRON client for RPC URL: %s", rpcURL)

	return newClient, nil
}

// GetDefaultEthClient returns the default ETH client using the RPC URL from config
func (cm *ClientManager) GetDefaultEthClient(ctx context.Context) (*ethclient.Client, error) {
	rpcURL, err := GetEthRpcUrl(ctx)
	if err != nil {
		return nil, gerror.Wrap(err, "GetDefaultEthClient failed to get ETH RPC URL")
	}
	return cm.GetEthClient(ctx, rpcURL)
}

// GetDefaultTronClient returns the default TRON client using the RPC URL from config with fallback support
func (cm *ClientManager) GetDefaultTronClient(ctx context.Context) (*client.GrpcClient, error) {
	// Get primary endpoint from config
	primaryURL, _ := GetTronRpcUrl(ctx)
	primaryAPIKey, _ := GetTronRpcApikey(ctx) // Don't fail if API key is missing

	// Build list of endpoints to try
	var fallbackEndpoints []struct {
		url    string
		apiKey string
		name   string
	}

	// Add primary endpoint if available
	if primaryURL != "" {
		fallbackEndpoints = append(fallbackEndpoints, struct {
			url    string
			apiKey string
			name   string
		}{
			url:    primaryURL,
			apiKey: primaryAPIKey,
			name:   "Primary (Config)",
		})
	}

	// Backup endpoints have been removed from configuration
	// Only primary endpoint is used now

	// Add hardcoded fallbacks if no config backups
	// if len(fallbackEndpoints) <= 1 {
	// 	hardcodedBackups := []string{
	// 		"3.225.171.164:50051",
	// 		"52.53.189.99:50051",
	// 		"18.196.99.16:50051",
	// 		"47.252.19.181:50051",
	// 		"47.252.3.238:50051",
	// 	}
	// 	for i, backupURL := range hardcodedBackups {
	// 		if backupURL != primaryURL {
	// 			fallbackEndpoints = append(fallbackEndpoints, struct {
	// 				url    string
	// 				apiKey string
	// 				name   string
	// 			}{
	// 				url:    backupURL,
	// 				apiKey: "", // No API key for hardcoded backups
	// 				name:   fmt.Sprintf("Hardcoded-%d", i+1),
	// 			})
	// 		}
	// 	}
	// }

	// Try each endpoint until one works
	var lastErr error
	for _, endpoint := range fallbackEndpoints {
		if endpoint.url == "" {
			continue
		}

		glog.Infof(ctx, "Attempting to connect to TRON endpoint: %s (%s)", endpoint.url, endpoint.name)

		tronClient, err := cm.GetTronClient(ctx, endpoint.url, endpoint.apiKey)
		if err != nil {
			glog.Warningf(ctx, "Failed to connect to TRON endpoint %s (%s): %v", endpoint.url, endpoint.name, err)
			lastErr = err
			continue
		}

		// Test the connection by making a simple call
		if cm.testTronConnection(ctx, tronClient) {
			glog.Infof(ctx, "Successfully connected to TRON endpoint: %s (%s)", endpoint.url, endpoint.name)
			return tronClient, nil
		} else {
			glog.Warningf(ctx, "TRON endpoint %s (%s) connection test failed", endpoint.url, endpoint.name)
			// Close the failed client
			cm.CloseTronClient(ctx, endpoint.url)
			lastErr = gerror.New("connection test failed")
		}
	}

	if lastErr != nil {
		return nil, gerror.Wrapf(lastErr, "all TRON endpoints failed")
	}
	return nil, gerror.New("no TRON endpoints configured")
}

// testTronConnection tests if a TRON client connection is working with comprehensive checks
func (cm *ClientManager) testTronConnection(ctx context.Context, tronClient *client.GrpcClient) bool {
	// Test 1: Get latest block
	_, err := tronClient.GetNowBlock()
	if err != nil {
		glog.Debugf(ctx, "TRON connection test failed (GetNowBlock): %v", err)
		return false
	}

	// Test 2: Try to get node info (more comprehensive test)
	_, err = tronClient.GetNodeInfo()
	if err != nil {
		glog.Debugf(ctx, "TRON connection test failed (GetNodeInfo): %v", err)
		return false
	}

	// Test 3: Try to get a known account (TRON Foundation account)
	testAddress := "TLyqzVGLV1srkB7dToTAEqgDSfPtXRJZYH" // TRON Foundation address
	_, err = tronClient.GetAccount(testAddress)
	if err != nil {
		glog.Debugf(ctx, "TRON connection test failed (GetAccount): %v", err)
		return false
	}

	glog.Debugf(ctx, "TRON connection test passed all checks")
	return true
}

// GetTronClientWithFailover returns a TRON client with automatic failover on errors
func (cm *ClientManager) GetTronClientWithFailover(ctx context.Context) (*client.GrpcClient, error) {
	// Try to get the default client first
	tronClient, err := cm.GetDefaultTronClient(ctx)
	if err != nil {
		return nil, err
	}

	// Store the current client for potential failover
	cm.currentTronClient = tronClient
	return tronClient, nil
}

// HandleTronError handles TRON API errors and performs failover if needed
func (cm *ClientManager) HandleTronError(ctx context.Context, err error) (*client.GrpcClient, error) {
	if err == nil {
		return cm.currentTronClient, nil
	}

	// Check if this is a retryable error
	if !isRetryableError(err) {
		return cm.currentTronClient, err
	}

	glog.Warningf(ctx, "TRON API error detected, attempting failover: %v", err)

	// Close all current TRON clients to force reconnection
	cm.tronMutex.Lock()
	for url, tronClient := range cm.tronClients {
		tronClient.Stop()
		glog.Infof(ctx, "Closed TRON client for RPC URL during failover: %s", url)
	}
	cm.tronClients = make(map[string]*client.GrpcClient)
	cm.tronMutex.Unlock()

	// Try to get a new client with different endpoint
	newClient, newErr := cm.GetDefaultTronClient(ctx)
	if newErr != nil {
		glog.Errorf(ctx, "TRON failover failed: %v", newErr)
		return nil, newErr
	}

	cm.currentTronClient = newClient
	glog.Infof(ctx, "TRON failover successful")
	return newClient, nil
}

// isRetryableError checks if an error is retryable (network connectivity issues)
func isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// Check for common network connectivity errors
	retryablePatterns := []string{
		"503", // Service Unavailable
		"502", // Bad Gateway
		"504", // Gateway Timeout
		"connection refused",
		"connection reset",
		"connection timeout",
		"network is unreachable",
		"no such host",
		"temporary failure",
		"transport: received unexpected content-type",
		"rpc error: code = Unavailable",
		"rpc error: code = DeadlineExceeded",
		"rpc error: code = Internal",
	}

	for _, pattern := range retryablePatterns {
		if strings.Contains(strings.ToLower(errStr), strings.ToLower(pattern)) {
			return true
		}
	}

	return false
}

// CloseEthClient closes an ETH client
func (cm *ClientManager) CloseEthClient(ctx context.Context, rpcURL string) {
	cm.ethMutex.Lock()
	defer cm.ethMutex.Unlock()

	client, exists := cm.ethClients[rpcURL]
	if exists {
		client.Close()
		delete(cm.ethClients, rpcURL)
		glog.Infof(ctx, "Closed ETH client for RPC URL: %s", rpcURL)
	}
}

// CloseTronClient closes a TRON client
func (cm *ClientManager) CloseTronClient(ctx context.Context, rpcURL string) {
	cm.tronMutex.Lock()
	defer cm.tronMutex.Unlock()

	tronClient, exists := cm.tronClients[rpcURL]
	if exists {
		tronClient.Stop()
		delete(cm.tronClients, rpcURL)
		glog.Infof(ctx, "Closed TRON client for RPC URL: %s", rpcURL)
	}
}

// CloseAllClients closes all clients
func (cm *ClientManager) CloseAllClients(ctx context.Context) {
	// Close ETH clients
	cm.ethMutex.Lock()
	for url, ethClient := range cm.ethClients {
		ethClient.Close()
		glog.Infof(ctx, "Closed ETH client for RPC URL: %s", url)
	}
	cm.ethClients = make(map[string]*ethclient.Client)
	cm.ethMutex.Unlock()

	// Close TRON clients
	cm.tronMutex.Lock()
	for url, tronClient := range cm.tronClients {
		tronClient.Stop()
		glog.Infof(ctx, "Closed TRON client for RPC URL: %s", url)
	}
	cm.tronClients = make(map[string]*client.GrpcClient)
	cm.tronMutex.Unlock()
}

// GetEthRpcUrl returns the ETH RPC URL from config
func GetEthRpcUrl(ctx context.Context) (string, error) {
	rpcUrl := g.Cfg().MustGet(ctx, "blockchain.ETH.rpcUrl", "").String()
	if rpcUrl == "" {
		return "", gerror.New("ETH RPC URL not configured")
	}
	return rpcUrl, nil
}

// GetTronRpcUrl returns the TRON RPC URL from config
func GetTronRpcUrl(ctx context.Context) (string, error) {
	rpcUrl := g.Cfg().MustGet(ctx, "blockchain.TRON.rpcUrl", "").String()
	if rpcUrl == "" {
		return "", gerror.New("TRON RPC URL not configured")
	}
	return rpcUrl, nil
}

// GetTronRpcApikey
func GetTronRpcApikey(ctx context.Context) (string, error) {
	rpcUrl := g.Cfg().MustGet(ctx, "blockchain.TRON.apiKey", "").String()
	if rpcUrl == "" {
		return "", gerror.New("TRON RPC Apikey not configured")
	}
	return rpcUrl, nil
}

// 获取erc20合约地址
func GetErc20ContractAddress(ctx context.Context) (string, error) {
	erc20ContractAddress := g.Cfg().MustGet(ctx, "blockchain.ETH.erc20ContractAddress", "").String()
	if erc20ContractAddress == "" {
		return "", gerror.New("ERC20 contract address not configured")
	}
	return erc20ContractAddress, nil
}

// 获取trc20合约地址
func GetTrc20ContractAddress(ctx context.Context) (string, error) {
	trc20ContractAddress := g.Cfg().MustGet(ctx, "blockchain.TRON.trc20ContractAddress", "").String()
	if trc20ContractAddress == "" {
		return "", gerror.New("TRC20 contract address not configured")
	}
	return trc20ContractAddress, nil
}
