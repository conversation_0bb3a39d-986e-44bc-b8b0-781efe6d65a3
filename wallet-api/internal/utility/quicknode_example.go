package utility

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

// 演示如何使用QuickNode客户端
func QuickNodeExample(ctx context.Context) error {
	// 初始化QuickNode客户端
	qn, err := NewQuickNode(ctx)
	if err != nil {
		return err
	}

	// ====== ETH示例 ======

	// 获取ETH余额
	ethAddr := "******************************************" // 示例地址
	balance, err := qn.GetEthBalance(ethAddr, "latest")
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "ETH balance for address %s: %s", ethAddr, balance.String())

	// 获取最新区块号
	blockNumber, err := qn.GetEthBlockNumber()
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "Latest ETH block number: %d", blockNumber)

	// 获取账户交易次数（nonce）
	nonce, err := qn.GetEthTransactionCount(ethAddr, "latest")
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "Nonce for ETH address %s: %d", ethAddr, nonce)

	// 获取Gas价格
	gasPrice, err := qn.GetEthGasPrice()
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "Current ETH gas price: %s", gasPrice.String())

	// 获取ERC20代币余额
	tokenAddr := "******************************************" // USDT合约地址
	tokenBalance, err := qn.GetEthTokenBalance(tokenAddr, ethAddr)
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "USDT balance for address %s: %s", ethAddr, tokenBalance.String())

	// ====== TRON示例 ======

	// 获取TRON账户信息
	tronAddr := "TDGy2M9qWBepSHDEutWWxWd1JZfmAed3BP" // 示例地址
	accountInfo, err := qn.GetTronAccountInfo(tronAddr)
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "TRON account info: %v", accountInfo)

	// 获取TRON余额
	tronBalance, err := qn.GetTronBalance(tronAddr)
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "TRON balance for address %s: %s", tronAddr, tronBalance.String())

	// 获取最新区块号
	tronBlockNumber, err := qn.GetTronBlockNumber()
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "Latest TRON block number: %d", tronBlockNumber)

	// 获取TRC20代币余额
	tronTokenAddr := "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t" // USDT合约地址
	tronTokenBalance, err := qn.GetTronTokenBalance(tronTokenAddr, tronAddr)
	if err != nil {
		return err
	}
	// Keeping original message structure but using English.
	g.Log().Infof(ctx, "USDT balance for address %s: %s", tronAddr, tronTokenBalance.String())

	return nil
}

// 示例：如何发送ETH交易
func SendEthTransactionExample(privateKey string, to string, amount string) {
	ctx := context.Background()
	_, err := NewQuickNode(ctx)
	if err != nil {
		// Keeping original message structure but using English.
		fmt.Printf("Failed to initialize QuickNode: %v\n", err)
		return
	}

	// Keeping original message structure but using English.
	g.Log().Info(ctx, "Note: This is just an example. Actual transactions require signing using a third-party library.")
	g.Log().Info(ctx, "Recommended library for signing: github.com/ethereum/go-ethereum")
	g.Log().Info(ctx, "Signed transactions can be sent via the SendEthRawTransaction() method.")
}

// 示例：如何发送TRON交易
func SendTronTransactionExample(privateKey string, to string, amount string) {
	ctx := context.Background()
	_, err := NewQuickNode(ctx)
	if err != nil {
		// Keeping original message structure but using English.
		fmt.Printf("Failed to initialize QuickNode: %v\n", err)
		return
	}

	// Keeping original message structure but using English.
	g.Log().Info(ctx, "Note: This is just an example. Actual transactions require signing using a third-party library.")
	g.Log().Info(ctx, "Recommended library for signing: github.com/fbsobreira/gotron-sdk")
	g.Log().Info(ctx, "Signed transactions can be sent via the BroadcastTronTransaction() method.")
}
