package jwtutil

import (
	"context"
	"errors"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/guid"
	"github.com/golang-jwt/jwt/v4"
)

// CustomClaims 定义了 JWT 中的自定义载荷
type CustomClaims struct {
	UserID string `json:"userId"` // 用户ID
	jwt.RegisteredClaims
}

// GenerateAccessToken 生成 Access Token
// 返回 token 字符串, JTI 和错误
func GenerateAccessToken(userID string) (tokenString string, jti string, err error) {
	cfg := g.Cfg()
	secret, err := cfg.Get(context.Background(), "jwt.secret")
	if err != nil {
		g.Log().Error(context.Background(), "Failed to get jwt.secret from config", err)
		return "", "", errors.New("failed to read jwt secret configuration")
	}
	if secret.String() == "" {
		g.Log().Error(context.Background(), "jwt.secret is empty in config")
		return "", "", errors.New("jwt secret is not configured")
	}

	expireSeconds, err := cfg.Get(context.Background(), "auth.expireSeconds")
	if err != nil {
		g.Log().Error(context.Background(), "Failed to get auth.expireSeconds from config", err)
		return "", "", errors.New("failed to read token expiration configuration")
	}

	jti = guid.S()
	now := time.Now()
	expiresAt := now.Add(time.Duration(expireSeconds.Int64()) * time.Second)

	claims := CustomClaims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			ID:        jti,
			Issuer:    "wallet-api",
			Subject:   userID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err = token.SignedString([]byte(secret.String()))
	if err != nil {
		g.Log().Error(context.Background(), "Failed to sign access token", err)
		return "", "", errors.New("failed to generate access token")
	}

	return tokenString, jti, nil
}

// GenerateRefreshToken 生成 Refresh Token
// 返回 token 字符串, JTI 和错误
func GenerateRefreshToken(userID string) (tokenString string, jti string, err error) {
	cfg := g.Cfg()
	secret, err := cfg.Get(context.Background(), "jwt.secret")
	if err != nil {
		g.Log().Error(context.Background(), "Failed to get jwt.secret from config", err)
		return "", "", errors.New("failed to read jwt secret configuration")
	}
	if secret.String() == "" {
		g.Log().Error(context.Background(), "jwt.secret is empty in config")
		return "", "", errors.New("jwt secret is not configured")
	}

	refreshTokenExpireSeconds, err := cfg.Get(context.Background(), "auth.refreshTokenExpireSeconds")
	if err != nil {
		g.Log().Error(context.Background(), "Failed to get auth.refreshTokenExpireSeconds from config", err)
		return "", "", errors.New("failed to read refresh token expiration configuration")
	}

	jti = guid.S()
	now := time.Now()
	expiresAt := now.Add(time.Duration(refreshTokenExpireSeconds.Int64()) * time.Second)

	claims := CustomClaims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			ID:        jti,
			Issuer:    "wallet-api",
			Subject:   userID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err = token.SignedString([]byte(secret.String()))
	if err != nil {
		g.Log().Error(context.Background(), "Failed to sign refresh token", err)
		return "", "", errors.New("failed to generate refresh token")
	}

	return tokenString, jti, nil
}

// VerifyToken 验证 Token
// 返回 CustomClaims 指针和错误
func VerifyToken(tokenString string) (*CustomClaims, error) {
	cfg := g.Cfg()
	secret, err := cfg.Get(context.Background(), "jwt.secret")
	if err != nil {
		g.Log().Error(context.Background(), "Failed to get jwt.secret from config", err)
		return nil, errors.New("failed to read jwt secret configuration")
	}
	if secret.String() == "" {
		g.Log().Error(context.Background(), "jwt.secret is empty in config")
		return nil, errors.New("jwt secret is not configured")
	}

	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 确保签名算法是 HS256
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.NewValidationError("unexpected signing method", jwt.ValidationErrorSignatureInvalid)
		}
		return []byte(secret.String()), nil
	})

	if err != nil {
		// 检查特定错误类型
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, errors.New("token is malformed")
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				// Token 已过期
				return nil, jwt.ErrTokenExpired
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, errors.New("token not active yet")
			} else if ve.Errors&jwt.ValidationErrorSignatureInvalid != 0 {
				return nil, jwt.ErrTokenSignatureInvalid
			} else {
				return nil, errors.New("couldn't handle this token")
			}
		}
		return nil, errors.New("couldn't handle this token")
	}

	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("token is invalid")
}