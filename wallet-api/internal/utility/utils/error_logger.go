package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ErrorLogEntry defines the structure for a single error log.
type ErrorLogEntry struct {
	Timestamp string `json:"timestamp"`
	Message   string `json:"message"`
}

// BuildErrorMessageJson formats an error message into a JSON array string.
// It appends the newError to a list of existing errors (if any) in existingErrorJson.
// If existingErrorJson is an empty string or "[]", it initializes a new list.
// If existingErrorJson is not a valid JSON array of ErrorLogEntry, it logs a warning
// and prepends an entry indicating the unparsable original message.
func BuildErrorMessageJson(ctx context.Context, existingErrorJson string, newErrorMessage string) string {
	logger := g.Log()
	var errorLogs []ErrorLogEntry

	if existingErrorJson != "" && existingErrorJson != "[]" {
		if err := json.Unmarshal([]byte(existingErrorJson), &errorLogs); err != nil {
			logger.Warningf(ctx, "Failed to unmarshal existing ErrorMessage JSON '%s': %v. Initializing new log array and prepending original message.", existingErrorJson, err)
			// Prepend the unparsable message as the first entry
			errorLogs = append(errorLogs, ErrorLogEntry{
				Timestamp: gtime.Now().Format("2006-01-02 15:04:05"), // Or a fixed "unknown" timestamp
				Message:   fmt.Sprintf("Unparsable existing error: %s", existingErrorJson),
			})
		}
	}

	newErrorEntry := ErrorLogEntry{
		Timestamp: gtime.Now().Format("2006-01-02 15:04:05"),
		Message:   newErrorMessage,
	}
	errorLogs = append(errorLogs, newErrorEntry)

	updatedErrorMsgBytes, marshalErr := json.Marshal(errorLogs)
	if marshalErr != nil {
		logger.Errorf(ctx, "Failed to marshal error messages: %v. Falling back to a JSON-formatted error entry for this failure. Original new error: %s", marshalErr, newErrorMessage)
		// Create a fallback entry describing this marshalling failure
		fallbackEntry := ErrorLogEntry{
			Timestamp: gtime.Now().Format("2006-01-02 15:04:05"),
			Message:   fmt.Sprintf("Internal error: Failed to marshal detailed error log. Current error intended: %s. Marshalling issue: %s", newErrorMessage, marshalErr.Error()),
		}
		// Attempt to marshal this single fallback entry
		fallbackJsonBytes, finalMarshalErr := json.Marshal([]ErrorLogEntry{fallbackEntry})
		if finalMarshalErr != nil {
			// This is highly unlikely, but as a last resort, return a hardcoded valid JSON error string
			logger.Errorf(ctx, "FATAL: Failed to marshal even the fallback error entry: %v. Original new error: %s", finalMarshalErr, newErrorMessage)
			return fmt.Sprintf(`[{"timestamp":"%s","message":"FATAL: System error in logging facility. Original error: %s"}]`, gtime.Now().Format("2006-01-02 15:04:05"), newErrorMessage)
		}
		return string(fallbackJsonBytes)
	}
	return string(updatedErrorMsgBytes)
}