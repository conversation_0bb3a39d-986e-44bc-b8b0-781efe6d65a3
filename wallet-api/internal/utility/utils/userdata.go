package utils

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"

	"wallet-api/internal/codes" // Import codes package
)

const (
	// 应用名称，用于创建用户数据目录
	AppName = "wallet-api"
	// 数据库驱动名称
	DBDriverName = "sqlite" // 统一使用 sqlite3 作为驱动名称
)

// GetUserDataDir 获取用户数据目录
// 不同操作系统返回不同的目录：
// - macOS: ~/Library/Application Support/[AppName]
// - Windows: %APPDATA%\[AppName]
// - Linux: ~/.local/share/[AppName]
func GetUserDataDir() string {
	var dir string

	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		// 如果无法获取用户主目录，则使用当前目录
		// Keeping original message structure but using English.
		g.Log().Warningf(gctx.GetInitCtx(), "Unable to get user home directory, using current directory: %v", err)
		wd, _ := os.Getwd()
		return filepath.Join(wd, ".userdata")
	}

	// 根据操作系统选择合适的用户数据目录
	switch runtime.GOOS {
	case "darwin": // macOS
		dir = filepath.Join(homeDir, "Library", "Application Support", AppName)
	case "windows": // Windows
		dir = filepath.Join(homeDir, "AppData", "Roaming", AppName)
	default: // Linux 和其他
		dir = filepath.Join(homeDir, ".local", "share", AppName)
	}

	return dir
}

// GetUserDatabaseDir 获取用户数据库目录
func GetUserDatabaseDir() string {
	baseDir := GetUserDataDir()
	dbDir := filepath.Join(baseDir, "database")
	return dbDir
}

// GetUserDatabaseFilePath 获取SQLite数据库文件的完整路径
func GetUserDatabaseFilePath() string {
	dbDir := GetUserDatabaseDir()
	return filepath.Join(dbDir, "database.sqlite")
}

// EnsureUserDataDirExists 确保用户数据目录存在
func EnsureUserDataDirExists() error {
	// 确保基础用户数据目录存在
	baseDir := GetUserDataDir()
	if !gfile.Exists(baseDir) {
		if err := gfile.Mkdir(baseDir); err != nil {
			return err // Keep original error for directory creation failure
		}
	}

	// 确保数据库目录存在
	dbDir := GetUserDatabaseDir()
	if !gfile.Exists(dbDir) {
		if err := gfile.Mkdir(dbDir); err != nil {
			return err // Keep original error for directory creation failure
		}
	}

	return nil
}

// GetGoframeDbConnectionString 构建GoFrame连接字符串
func GetGoframeDbConnectionString(dbPath string) string {
	return fmt.Sprintf("sqlite::@file(%s)", dbPath)
}

// GetDbConnectionString 构建数据库连接字符串
func GetDbConnectionString(dbPath string) string {
	// 不再使用加密，直接返回文件路径
	return fmt.Sprintf("file:%s", dbPath)
}

// InitUserDatabase 初始化用户数据库
// 如果数据库文件不存在，则直接创建一个新数据库
func InitUserDatabase() error {
	// 确保目录存在
	if err := EnsureUserDataDirExists(); err != nil {
		// Log the specific error for directory creation failure if needed,
		// but return the original error for proper handling upstream.
		// g.Log().Warningf(gctx.GetInitCtx(), "Failed to ensure user data directory exists: %v", err)
		return err
	}

	// 获取目标数据库文件路径
	userDbPath := GetUserDatabaseFilePath()

	ctx := gctx.GetInitCtx()

	// 如果用户数据库文件不存在，直接创建新的数据库
	if !gfile.Exists(userDbPath) {
		// 连接到新创建的数据库文件
		// 注意：不需要先创建空文件，sql.Open会自动创建
		connStr := GetDbConnectionString(userDbPath)
		sqlDB, err := sql.Open(DBDriverName, connStr)
		if err != nil {
			g.Log().Warningf(ctx, "%s: %v", codes.CodeUtilityDbOpenFailed.Message(), err)
			return codes.WrapError(err, codes.CodeUtilityDbOpenFailed) // Return coded error
		}
		defer func() {
			if err := sqlDB.Close(); err != nil {
				g.Log().Warningf(ctx, "%s (InitUserDatabase): %v", codes.CodeUtilityDbCloseConnFailed.Message(), err)
			}
		}()

		// 确保数据库连接有效
		if err := sqlDB.Ping(); err != nil {
			g.Log().Warningf(ctx, "%s: %v", codes.CodeUtilityDbPingFailed.Message(), err)
			return codes.WrapError(err, codes.CodeUtilityDbPingFailed) // Return coded error
		}

		// 创建基本表结构
		if err := createBasicDatabaseStructure(sqlDB); err != nil {
			// createBasicDatabaseStructure already returns a coded error
			g.Log().Warningf(ctx, "%s: %v", codes.CodeUtilityDbInitSchemaFailed.Message(), err)
			return err
		}
		// Keeping original message structure but using English.
		g.Log().Info(ctx, "Database schema initialized")
	}

	return nil
}

// createBasicDatabaseStructure 创建基本的数据库表结构
func createBasicDatabaseStructure(db *sql.DB) error {
	// 创建基本表结构
	// 这里定义应用所需的基本表结构
	createTableStatements := []string{
		`CREATE TABLE IF NOT EXISTS system_config (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			key TEXT NOT NULL UNIQUE,
			value TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS user (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username TEXT NOT NULL UNIQUE,
			password TEXT NOT NULL,
			status INTEGER DEFAULT 1,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS migration (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			version TEXT NOT NULL UNIQUE,
			name TEXT NOT NULL,
			created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
		)`,
		// 可以根据需要添加更多表
	}

	// 执行创建表语句
	for _, stmt := range createTableStatements {
		if _, err := db.Exec(stmt); err != nil {
			// Use NewErrorf and include original error in the formatted message
			return codes.NewErrorf(codes.CodeUtilityDbCreateTableFailed, "Original error: %v - SQL: %s", err, stmt)
		}
	}

	return nil
}

// ContainsString 检查字符串是否包含子串
func ContainsString(s, substr string) bool {
	return strings.Contains(s, substr)
}

// JoinStrings 连接字符串列表
func JoinStrings(items []string, sep string) string {
	return strings.Join(items, sep)
}

// IsDatabaseValid 检查数据库是否有效
func IsDatabaseValid(dbPath string) bool {
	// 尝试打开数据库
	db, err := sql.Open(DBDriverName, "file:"+dbPath)
	if err != nil {
		// Log if needed, but return false directly
		// g.Log().Warningf(gctx.New(), "%s: %v", codes.CodeUtilityDbOpenFailed.Message(), err)
		return false
	}
	defer func() {
		if err := db.Close(); err != nil {
			// 在 IsDatabaseValid 中，可以只记录日志，因为函数目的是检查有效性
			g.Log().Warningf(gctx.New(), "%s (IsDatabaseValid): %v", codes.CodeUtilityDbCloseConnCheckFailed.Message(), err)
		}
	}()

	// 尝试执行一个简单查询
	_, err = db.Exec("SELECT 1 FROM sqlite_master LIMIT 1")
	if err != nil {
		// Log if needed, but return false directly
		// g.Log().Warningf(gctx.New(), "Database validation query failed: %v", err)
		return false
	}

	// 如果没有错误，则数据库有效
	return true
}
