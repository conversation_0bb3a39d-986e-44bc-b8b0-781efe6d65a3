package utility

import (
	"context"
	"encoding/json"

	// "errors" // Removed unused import
	"io"
	"math/big"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"

	"wallet-api/internal/codes" // Import codes package
)

// QuickNode 结构体，用于与QuickNode API交互
type QuickNode struct {
	g.Meta `json:"quicknode"`

	EthEndpoint  string // 以太坊节点端点
	TronEndpoint string // 波场节点端点
	HttpClient   *http.Client
	Logger       *glog.Logger
}

// 创建新的QuickNode客户端
func NewQuickNode(ctx context.Context) (*QuickNode, error) {
	cfg := gcfg.Instance()
	ethEndpoint := cfg.MustGet(ctx, "quicknode.eth_endpoint").String()
	tronEndpoint := cfg.MustGet(ctx, "quicknode.tron_endpoint").String()

	if ethEndpoint == "" || tronEndpoint == "" {
		return nil, codes.NewError(codes.CodeUtilityQuickNodeEndpointMissing)
	}

	return &QuickNode{
		EthEndpoint:  ethEndpoint,
		TronEndpoint: tronEndpoint,
		HttpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		Logger: g.Log(),
	}, nil
}

// RPC请求结构
type jsonRPCRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params"`
	ID      int         `json:"id"`
}

// RPC响应结构
type jsonRPCResponse struct {
	JSONRPC string          `json:"jsonrpc"`
	Result  json.RawMessage `json:"result"`
	Error   *jsonRPCError   `json:"error,omitempty"`
	ID      int             `json:"id"`
}

// RPC错误结构
type jsonRPCError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// 发送RPC请求
func (q *QuickNode) sendRequest(endpoint string, method string, params interface{}) (json.RawMessage, error) {
	ctx := gctx.New()
	reqBody, err := json.Marshal(jsonRPCRequest{
		JSONRPC: "2.0",
		Method:  method,
		Params:  params,
		ID:      1,
	})
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, strings.NewReader(string(reqBody)))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := q.HttpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() {
		// Use code for logging
		if err := resp.Body.Close(); err != nil {
			q.Logger.Warningf(gctx.New(), "%s: %v", codes.CodeUtilityQuickNodeCloseRespBodyFailed.Message(), err)
		}
	}()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var rpcResponse jsonRPCResponse
	err = json.Unmarshal(body, &rpcResponse)
	if err != nil {
		return nil, err
	}

	if rpcResponse.Error != nil {
		// Wrap the original JSON-RPC error message with a standard code
		return nil, codes.NewErrorf(codes.CodeUtilityJsonRpcReturnedError, "%s", rpcResponse.Error.Message)
	}

	return rpcResponse.Result, nil
}

// ========================
// ETH 相关方法
// ========================

// GetEthBalance 获取ETH余额
func (q *QuickNode) GetEthBalance(address string, blockNumber string) (*big.Int, error) {
	if blockNumber == "" {
		blockNumber = "latest"
	}

	result, err := q.sendRequest(q.EthEndpoint, "eth_getBalance", []interface{}{address, blockNumber})
	if err != nil {
		return nil, err
	}

	var balanceHex string
	err = json.Unmarshal(result, &balanceHex)
	if err != nil {
		return nil, err
	}

	balance := new(big.Int)
	balance.SetString(strings.TrimPrefix(balanceHex, "0x"), 16)
	return balance, nil
}

// GetEthBlockNumber 获取最新区块号
func (q *QuickNode) GetEthBlockNumber() (uint64, error) {
	result, err := q.sendRequest(q.EthEndpoint, "eth_blockNumber", []interface{}{})
	if err != nil {
		return 0, err
	}

	var blockNumberHex string
	err = json.Unmarshal(result, &blockNumberHex)
	if err != nil {
		return 0, err
	}

	return strconv.ParseUint(strings.TrimPrefix(blockNumberHex, "0x"), 16, 64)
}

// GetEthTransactionCount 获取账户交易次数（nonce）
func (q *QuickNode) GetEthTransactionCount(address string, blockNumber string) (uint64, error) {
	if blockNumber == "" {
		blockNumber = "latest"
	}

	result, err := q.sendRequest(q.EthEndpoint, "eth_getTransactionCount", []interface{}{address, blockNumber})
	if err != nil {
		return 0, err
	}

	var countHex string
	err = json.Unmarshal(result, &countHex)
	if err != nil {
		return 0, err
	}

	return strconv.ParseUint(strings.TrimPrefix(countHex, "0x"), 16, 64)
}

// GetEthGasPrice 获取当前Gas价格
func (q *QuickNode) GetEthGasPrice() (*big.Int, error) {
	result, err := q.sendRequest(q.EthEndpoint, "eth_gasPrice", []interface{}{})
	if err != nil {
		return nil, err
	}

	var gasPriceHex string
	err = json.Unmarshal(result, &gasPriceHex)
	if err != nil {
		return nil, err
	}

	gasPrice := new(big.Int)
	gasPrice.SetString(strings.TrimPrefix(gasPriceHex, "0x"), 16)
	return gasPrice, nil
}

// SendEthRawTransaction 发送已签名的交易
func (q *QuickNode) SendEthRawTransaction(signedTxHex string) (string, error) {
	if !strings.HasPrefix(signedTxHex, "0x") {
		signedTxHex = "0x" + signedTxHex
	}

	result, err := q.sendRequest(q.EthEndpoint, "eth_sendRawTransaction", []interface{}{signedTxHex})
	if err != nil {
		return "", err
	}

	var txHash string
	err = json.Unmarshal(result, &txHash)
	if err != nil {
		return "", err
	}

	return txHash, nil
}

// GetEthTokenBalance 获取ERC20代币余额
func (q *QuickNode) GetEthTokenBalance(tokenAddress, ownerAddress string) (*big.Int, error) {
	// ERC20 balanceOf方法选择器: 0x70a08231
	data := "0x70a08231000000000000000000000000" + strings.TrimPrefix(ownerAddress, "0x")

	params := map[string]interface{}{
		"to":   tokenAddress,
		"data": data,
	}

	result, err := q.sendRequest(q.EthEndpoint, "eth_call", []interface{}{params, "latest"})
	if err != nil {
		return nil, err
	}

	var balanceHex string
	err = json.Unmarshal(result, &balanceHex)
	if err != nil {
		return nil, err
	}

	balance := new(big.Int)
	balance.SetString(strings.TrimPrefix(balanceHex, "0x"), 16)
	return balance, nil
}

// ========================
// TRON 相关方法
// ========================

// GetTronAccountInfo 获取TRON账户信息
func (q *QuickNode) GetTronAccountInfo(address string) (map[string]interface{}, error) {
	result, err := q.sendRequest(q.TronEndpoint, "wallet/getaccount", map[string]interface{}{
		"address": address,
		"visible": true,
	})
	if err != nil {
		return nil, err
	}

	var accountInfo map[string]interface{}
	err = json.Unmarshal(result, &accountInfo)
	if err != nil {
		return nil, err
	}

	return accountInfo, nil
}

// GetTronBalance 获取TRON余额
func (q *QuickNode) GetTronBalance(address string) (*big.Int, error) {
	accountInfo, err := q.GetTronAccountInfo(address)
	if err != nil {
		return nil, err
	}

	balance := new(big.Int)
	if b, ok := accountInfo["balance"]; ok {
		balanceFloat, ok := b.(float64)
		if ok {
			balance.SetInt64(int64(balanceFloat))
		}
	}

	return balance, nil
}

// GetTronBlockNumber 获取最新区块号
func (q *QuickNode) GetTronBlockNumber() (int64, error) {
	result, err := q.sendRequest(q.TronEndpoint, "wallet/getnowblock", map[string]interface{}{})
	if err != nil {
		return 0, err
	}

	var blockInfo map[string]interface{}
	err = json.Unmarshal(result, &blockInfo)
	if err != nil {
		return 0, err
	}

	blockHeader, ok := blockInfo["block_header"].(map[string]interface{})
	if !ok {
		return 0, codes.NewError(codes.CodeUtilityQuickNodeParseBlockHeaderFailed)
	}

	rawData, ok := blockHeader["raw_data"].(map[string]interface{})
	if !ok {
		return 0, codes.NewError(codes.CodeUtilityQuickNodeParseBlockRawFailed)
	}

	number, ok := rawData["number"].(float64)
	if !ok {
		return 0, codes.NewError(codes.CodeUtilityQuickNodeParseBlockNumberFailed)
	}

	return int64(number), nil
}

// BroadcastTronTransaction 广播TRON交易
func (q *QuickNode) BroadcastTronTransaction(signedTxHex string) (string, error) {
	result, err := q.sendRequest(q.TronEndpoint, "wallet/broadcasttransaction", map[string]interface{}{
		"raw_data_hex": signedTxHex,
	})
	if err != nil {
		return "", err
	}

	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return "", err
	}

	if success, ok := response["result"].(bool); ok && success {
		if txid, ok := response["txid"].(string); ok {
			return txid, nil
		}
	}

	// Use the specific broadcast failed code
	return "", codes.NewError(codes.CodeUtilityQuickNodeBroadcastFailed)
}

// GetTronTokenBalance 获取TRC20代币余额
func (q *QuickNode) GetTronTokenBalance(tokenAddress, ownerAddress string) (*big.Int, error) {
	params := map[string]interface{}{
		"contract_address":  tokenAddress,
		"function_selector": "balanceOf(address)",
		"parameter":         ownerAddress,
		"visible":           true,
	}

	result, err := q.sendRequest(q.TronEndpoint, "wallet/triggerconstantcontract", params)
	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return nil, err
	}

	constantResult, ok := response["constant_result"].([]interface{})
	if !ok || len(constantResult) == 0 {
		return nil, codes.NewError(codes.CodeUtilityQuickNodeGetTokenBalanceFailed)
	}

	balanceHex, ok := constantResult[0].(string)
	if !ok {
		return nil, codes.NewError(codes.CodeUtilityQuickNodeParseTokenBalanceFailed)
	}

	balance := new(big.Int)
	balance.SetString(strings.TrimPrefix(balanceHex, "0x"), 16)
	return balance, nil
}
