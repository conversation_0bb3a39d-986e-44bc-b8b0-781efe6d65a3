// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/entity" // Added import

	"github.com/gogf/gf/v2/errors/gerror" // Added import
)

// tasksDao is the data access object for the table tasks.
// You can define custom methods on it to extend its functionality as needed.
type tasksDao struct {
	*internal.TasksDao
}

var (
	// Tasks is a globally accessible object for table tasks operations.
	Tasks = tasksDao{internal.NewTasksDao()}
)

// Add your custom methods and functionality below.

// --- ITasksDao Interface Implementation ---

// InsertTaskAndGetID inserts a new task record and returns its ID.
func (d *tasksDao) InsertTaskAndGetID(ctx context.Context, task *entity.Tasks) (int64, error) {
	result, err := d.Ctx(ctx).Data(task).Insert() // Uses Ctx().Data().Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "dao: failed to insert task")
	}
	id, err := result.LastInsertId()
	if err != nil {
		// Fallback or error handling if LastInsertId is not supported/fails
		return 0, gerror.Wrap(err, "dao: failed to get last insert ID for task")
	}
	return id, nil
}

// FindTaskByID finds a single task by its ID.
func (d *tasksDao) FindTaskByID(ctx context.Context, taskID int64) (*entity.Tasks, error) {
	var task *entity.Tasks
	err := d.Ctx(ctx).Where("id", taskID).Scan(&task) // Uses Ctx().Where().Scan()
	if err != nil {
		return nil, gerror.Wrapf(err, "dao: failed to find task by ID %d", taskID)
	}
	if task == nil {
		return nil, nil // Not found
	}
	return task, nil
}

// ListTasks retrieves a list of tasks based on conditions, pagination, and ordering.
func (d *tasksDao) ListTasks(ctx context.Context, condition map[string]interface{}, page, size int, orderBy string) (list []*entity.Tasks, total int, err error) {
	model := d.Ctx(ctx) // Uses Ctx()
	if condition != nil {
		model = model.Where(condition)
	}

	total, err = model.Count() // Uses Count()
	if err != nil {
		err = gerror.Wrap(err, "dao: failed to count tasks")
		return
	}

	if orderBy != "" {
		model = model.Order(orderBy)
	}

	if page > 0 && size > 0 {
		model = model.Page(page, size)
	}

	err = model.Scan(&list) // Uses Scan()
	if err != nil {
		err = gerror.Wrap(err, "dao: failed to list tasks")
	}
	return
}

// CountTasks counts tasks based on conditions.
func (d *tasksDao) CountTasks(ctx context.Context, condition map[string]interface{}) (int, error) {
	model := d.Ctx(ctx) // Uses Ctx()
	if condition != nil {
		model = model.Where(condition)
	}
	count, err := model.Count() // Uses Count()
	if err != nil {
		return 0, gerror.Wrap(err, "dao: failed to count tasks")
	}
	return count, nil
}

// UpdateTask updates task data by ID.
func (d *tasksDao) UpdateTask(ctx context.Context, taskID int64, data map[string]interface{}) error {
	if len(data) == 0 {
		return gerror.New("dao: update data cannot be empty")
	}
	_, err := d.Ctx(ctx).Data(data).Where("id", taskID).Update() // Uses Ctx().Data().Where().Update()
	if err != nil {
		return gerror.Wrapf(err, "dao: failed to update task with ID %d", taskID)
	}
	return nil
}
