// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"strings" // Needed for SumAmount error check
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/entity" // Added import

	"github.com/gogf/gf/v2/errors/gerror" // Added import
)

// transactionsDao is the data access object for the table transactions.
// You can define custom methods on it to extend its functionality as needed.
type transactionsDao struct {
	*internal.TransactionsDao
}

var (
	// Transactions is a globally accessible object for table transactions operations.
	Transactions = transactionsDao{internal.NewTransactionsDao()}
)

// Add your custom methods and functionality below.

// --- ITransactionsDao Interface Implementation ---

// InsertTransaction inserts a new transaction record.
func (d *transactionsDao) InsertTransaction(ctx context.Context, tx *entity.Transactions) error {
	_, err := d.Ctx(ctx).Data(tx).Insert() // Uses Ctx().Data().Insert()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to insert transaction")
	}
	return nil
}

// FindTransactionByHash finds a single transaction by its hash.
func (d *transactionsDao) FindTransactionByHash(ctx context.Context, txHash string) (*entity.Transactions, error) {
	var tx *entity.Transactions
	err := d.Ctx(ctx).Where("transaction_hash", txHash).Scan(&tx) // Uses Ctx().Where().Scan()
	if err != nil {
		return nil, gerror.Wrapf(err, "dao: failed to find transaction by hash %s", txHash)
	}
	if tx == nil {
		return nil, nil // Not found
	}
	return tx, nil
}

// ListTransactions retrieves a list of transactions based on conditions, pagination, and ordering.
func (d *transactionsDao) ListTransactions(ctx context.Context, condition map[string]interface{}, page, size int, orderBy string) (list []*entity.Transactions, total int, err error) {
	model := d.Ctx(ctx) // Uses Ctx()
	if condition != nil {
		model = model.Where(condition)
	}

	total, err = model.Count() // Uses Count()
	if err != nil {
		err = gerror.Wrap(err, "dao: failed to count transactions")
		return
	}

	if orderBy != "" {
		model = model.Order(orderBy)
	}

	if page > 0 && size > 0 {
		model = model.Page(page, size)
	}

	err = model.Scan(&list) // Uses Scan()
	if err != nil {
		err = gerror.Wrap(err, "dao: failed to list transactions")
	}
	return
}

// CountTransactions counts transactions based on conditions.
func (d *transactionsDao) CountTransactions(ctx context.Context, condition map[string]interface{}) (int, error) {
	model := d.Ctx(ctx) // Uses Ctx()
	if condition != nil {
		model = model.Where(condition)
	}
	count, err := model.Count() // Uses Count()
	if err != nil {
		return 0, gerror.Wrap(err, "dao: failed to count transactions")
	}
	return count, nil
}

// SumAmount calculates the sum of the 'amount' field based on conditions.
// Note: Assumes 'amount' column can be summed directly (e.g., numeric type).
func (d *transactionsDao) SumAmount(ctx context.Context, condition map[string]interface{}) (float64, error) {
	model := d.Ctx(ctx) // Uses Ctx()
	if condition != nil {
		model = model.Where(condition)
	}
	sum, err := model.Sum("amount") // Uses Sum()
	if err != nil {
		// Handle potential "no rows" error for Sum if necessary
		// Using string check as sql.ErrNoRows might not be returned by Sum
		if strings.Contains(err.Error(), "no rows") {
			return 0, nil // Return 0 if no matching records
		}
		return 0, gerror.Wrap(err, "dao: failed to sum transaction amount")
	}
	return sum, nil
}
