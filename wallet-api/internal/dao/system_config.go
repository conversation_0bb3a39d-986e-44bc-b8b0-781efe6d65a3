// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql" // Needed for sql.ErrNoRows check
	"errors"       // Needed for errors.Is
	"strings"      // Needed for error check
	"wallet-api/internal/dao/internal"

	"github.com/gogf/gf/v2/errors/gerror" // Added import
	"github.com/gogf/gf/v2/frame/g"       // Added import for g.Map
)

// systemConfigDao is the data access object for the table system_config.
// You can define custom methods on it to extend its functionality as needed.
type systemConfigDao struct {
	*internal.SystemConfigDao
}

var (
	// SystemConfig is a globally accessible object for table system_config operations.
	SystemConfig = systemConfigDao{internal.NewSystemConfigDao()}
)

// Add your custom methods and functionality below.

// --- ISystemConfigDao Interface Implementation ---

// GetValueByKey retrieves the value for a given key. Returns empty string and nil error if key not found.
func (d *systemConfigDao) GetValueByKey(ctx context.Context, key string) (string, error) {
	value, err := d.Ctx(ctx).Where("key", key).Value("value") // Uses Ctx().Where().Value()
	if err != nil {
		// Check if the error is "record not found"
		if errors.Is(err, sql.ErrNoRows) || strings.Contains(err.Error(), "no rows") {
			return "", nil // Key not found is not an error, return empty string
		}
		return "", gerror.Wrapf(err, "dao: failed to get value for key %s", key)
	}
	return value.String(), nil
}

// SetValueByKey inserts or updates a key-value pair.
func (d *systemConfigDao) SetValueByKey(ctx context.Context, key string, value string) error {
	// Use Save which handles Insert/Update based on primary key or unique index (`key` column)
	_, err := d.Ctx(ctx).Data(g.Map{"key": key, "value": value}).Save() // Uses Ctx().Data().Save()
	if err != nil {
		return gerror.Wrapf(err, "dao: failed to set value for key %s", key)
	}
	return nil
}

// DeleteByKey deletes a configuration entry by its key.
func (d *systemConfigDao) DeleteByKey(ctx context.Context, key string) error {
	_, err := d.Ctx(ctx).Where("key", key).Delete() // Uses Ctx().Where().Delete()
	if err != nil {
		return gerror.Wrapf(err, "dao: failed to delete key %s", key)
	}
	return nil
}
