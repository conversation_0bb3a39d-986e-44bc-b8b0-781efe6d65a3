package dao

import (
	"context"
	"wallet-api/internal/model/entity"
)

// IPasswordAttemptsDao defines the interface for password_attempts table data access operations.
type IPasswordAttemptsDao interface {
	// --- Custom Methods ---
	FindByID(ctx context.Context, id int64) (*entity.PasswordAttempts, error)
	Insert(ctx context.Context, attempt *entity.PasswordAttempts) error
	UpdateByID(ctx context.Context, id int64, data map[string]interface{}) error
	CountByID(ctx context.Context, id int64) (int, error)
	// Add other necessary methods based on repository needs...
}
