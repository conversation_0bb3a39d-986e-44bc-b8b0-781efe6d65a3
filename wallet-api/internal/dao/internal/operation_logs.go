// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// OperationLogsDao is the data access object for the table operation_logs.
type OperationLogsDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  OperationLogsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// OperationLogsColumns defines and stores column names for the table operation_logs.
type OperationLogsColumns struct {
	Id          string //
	Logtime     string // logtime
	Logtype     string // logtype
	Logcontent  string // logcontent
	Logstatus   string // 日志状态
	ErrorInfo   string // 错误信息
	CorrectInfo string // 正确信息
	CreateAt    string // 创建时间
	UpdateAt    string // 更新时间
}

// operationLogsColumns holds the columns for the table operation_logs.
var operationLogsColumns = OperationLogsColumns{
	Id:          "id",
	Logtime:     "logtime",
	Logtype:     "logtype",
	Logcontent:  "logcontent",
	Logstatus:   "logstatus",
	ErrorInfo:   "error_info",
	CorrectInfo: "correct_info",
	CreateAt:    "create_at",
	UpdateAt:    "update_at",
}

// NewOperationLogsDao creates and returns a new DAO object for table data access.
func NewOperationLogsDao(handlers ...gdb.ModelHandler) *OperationLogsDao {
	return &OperationLogsDao{
		group:    "default",
		table:    "operation_logs",
		columns:  operationLogsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *OperationLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *OperationLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *OperationLogsDao) Columns() OperationLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *OperationLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *OperationLogsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *OperationLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
