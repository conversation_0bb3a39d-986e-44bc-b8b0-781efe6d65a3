// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserWithdrawsDao is the data access object for the table user_withdraws.
type UserWithdrawsDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  UserWithdrawsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// UserWithdrawsColumns defines and stores column names for the table user_withdraws.
type UserWithdrawsColumns struct {
	UserWithdrawsId      string // 主键ID
	TokenFeeSupplementId string //
	Name                 string // 币种ID
	Chan                 string // 链
	FromAddress          string // 提币目标地址
	ToAddress            string // 提币目标地址
	Amount               string // 申请提现金额
	HandlingFee          string // 提现手续费
	ActualAmount         string // 实际到账金额
	State                string // 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)
	TxHash               string // 链上交易哈希/ID
	ErrorMessage         string // 失败或错误信息
	CreatedAt            string // 创建时间
	CheckedAt            string // 审核时间 (审核通过或拒绝的时间)
	ProcessingAt         string // 开始处理时间 (进入“处理中”状态的时间)
	CompletedAt          string // 完成时间 (变为“已完成”或“失败”状态的时间)
	UpdatedAt            string // 最后更新时间
	Retries              string //
	NergyState           string // 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	GasfeeState          string // gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	EthFeeMode           string // eth 矿工费模式 1 自动 2手动
	EthFeeMax            string // eth 最大矿工费
	EthGasPrice          string //
	EthGasLimit          string //
	TrxFeeMax            string //
	RechargesId          string // 充值订单id
	GasfeeHash           string // gas费hash
	GasfeeAmount         string // gas费金额
	EnergyMsg            string //
	CanActAt             string // 完成时间 (变为“已完成”或“失败”状态的时间)
}

// userWithdrawsColumns holds the columns for the table user_withdraws.
var userWithdrawsColumns = UserWithdrawsColumns{
	UserWithdrawsId:      "user_withdraws_id",
	TokenFeeSupplementId: "token_fee_supplement_id",
	Name:                 "name",
	Chan:                 "chan",
	FromAddress:          "from_address",
	ToAddress:            "to_address",
	Amount:               "amount",
	HandlingFee:          "handling_fee",
	ActualAmount:         "actual_amount",
	State:                "state",
	TxHash:               "tx_hash",
	ErrorMessage:         "error_message",
	CreatedAt:            "created_at",
	CheckedAt:            "checked_at",
	ProcessingAt:         "processing_at",
	CompletedAt:          "completed_at",
	UpdatedAt:            "updated_at",
	Retries:              "retries",
	NergyState:           "nergy_state",
	GasfeeState:          "gasfee_state",
	EthFeeMode:           "eth_fee_mode",
	EthFeeMax:            "eth_fee_max",
	EthGasPrice:          "eth_gas_price",
	EthGasLimit:          "eth_gas_limit",
	TrxFeeMax:            "trx_fee_max",
	RechargesId:          "recharges_id",
	GasfeeHash:           "gasfee_hash",
	GasfeeAmount:         "gasfee_amount",
	EnergyMsg:            "energy_msg",
	CanActAt:             "can_act_at",
}

// NewUserWithdrawsDao creates and returns a new DAO object for table data access.
func NewUserWithdrawsDao(handlers ...gdb.ModelHandler) *UserWithdrawsDao {
	return &UserWithdrawsDao{
		group:    "default",
		table:    "user_withdraws",
		columns:  userWithdrawsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserWithdrawsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserWithdrawsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserWithdrawsDao) Columns() UserWithdrawsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserWithdrawsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserWithdrawsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserWithdrawsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
