// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WithdrawPlanDao is the data access object for the table withdraw_plan.
type WithdrawPlanDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  WithdrawPlanColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// WithdrawPlanColumns defines and stores column names for the table withdraw_plan.
type WithdrawPlanColumns struct {
	WithdrawPlanId string // 主键ID
	Chan           string // 链
	Address        string // 地址
	State          string // 状态: 1-待确认, 2-完成
	ErrorMessage   string // 失败或错误信息
	CreatedAt      string // 创建时间
	UpdatedAt      string // 最后更新时间
	RetryCount     string //
}

// withdrawPlanColumns holds the columns for the table withdraw_plan.
var withdrawPlanColumns = WithdrawPlanColumns{
	WithdrawPlanId: "withdraw_plan_id",
	Chan:           "chan",
	Address:        "address",
	State:          "state",
	ErrorMessage:   "error_message",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	RetryCount:     "retry_count",
}

// NewWithdrawPlanDao creates and returns a new DAO object for table data access.
func NewWithdrawPlanDao(handlers ...gdb.ModelHandler) *WithdrawPlanDao {
	return &WithdrawPlanDao{
		group:    "default",
		table:    "withdraw_plan",
		columns:  withdrawPlanColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WithdrawPlanDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WithdrawPlanDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WithdrawPlanDao) Columns() WithdrawPlanColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WithdrawPlanDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WithdrawPlanDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WithdrawPlanDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
