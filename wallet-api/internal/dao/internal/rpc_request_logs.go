// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RpcRequestLogsDao is the data access object for the table rpc_request_logs.
type RpcRequestLogsDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  RpcRequestLogsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// RpcRequestLogsColumns defines and stores column names for the table rpc_request_logs.
type RpcRequestLogsColumns struct {
	Id               string //
	RequestTime      string // 请求时间
	RequestType      string // 请求类型
	RequestMethod    string // 请求方法
	Address          string // 请求address
	Coin             string // coin
	RequestParams    string // 请求参数
	RequestUrl       string // 请求完整地址
	RequestStatus    string // 请求状态
	RequestResult    string // 请求结果
	RequestErrorInfo string // 请求错误信息
	CreateAt         string // 创建时间
	UpdateAt         string // 更新时间
}

// rpcRequestLogsColumns holds the columns for the table rpc_request_logs.
var rpcRequestLogsColumns = RpcRequestLogsColumns{
	Id:               "id",
	RequestTime:      "request_time",
	RequestType:      "request_type",
	RequestMethod:    "request_method",
	Address:          "address",
	Coin:             "coin",
	RequestParams:    "request_params",
	RequestUrl:       "request_url",
	RequestStatus:    "request_status",
	RequestResult:    "request_result",
	RequestErrorInfo: "request_error_info",
	CreateAt:         "create_at",
	UpdateAt:         "update_at",
}

// NewRpcRequestLogsDao creates and returns a new DAO object for table data access.
func NewRpcRequestLogsDao(handlers ...gdb.ModelHandler) *RpcRequestLogsDao {
	return &RpcRequestLogsDao{
		group:    "default",
		table:    "rpc_request_logs",
		columns:  rpcRequestLogsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RpcRequestLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RpcRequestLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RpcRequestLogsDao) Columns() RpcRequestLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RpcRequestLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RpcRequestLogsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RpcRequestLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
