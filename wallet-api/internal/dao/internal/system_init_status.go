// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SystemInitStatusDao is the data access object for the table system_init_status.
type SystemInitStatusDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  SystemInitStatusColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// SystemInitStatusColumns defines and stores column names for the table system_init_status.
type SystemInitStatusColumns struct {
	Id            string // Primary Key
	IsInitialized string // Is the system initialized (0=false, 1=true)
	PasswordHash  string // Hashed password for startup (bcrypt)
	Salt          string // Salt for password hashing (kept for potential future use, bcrypt includes salt in hash)
	CreatedAt     string // Creation Time
	UpdatedAt     string // Update Time
}

// systemInitStatusColumns holds the columns for the table system_init_status.
var systemInitStatusColumns = SystemInitStatusColumns{
	Id:            "id",
	IsInitialized: "is_initialized",
	PasswordHash:  "password_hash",
	Salt:          "salt",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewSystemInitStatusDao creates and returns a new DAO object for table data access.
func NewSystemInitStatusDao(handlers ...gdb.ModelHandler) *SystemInitStatusDao {
	return &SystemInitStatusDao{
		group:    "default",
		table:    "system_init_status",
		columns:  systemInitStatusColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SystemInitStatusDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SystemInitStatusDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SystemInitStatusDao) Columns() SystemInitStatusColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SystemInitStatusDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SystemInitStatusDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SystemInitStatusDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
