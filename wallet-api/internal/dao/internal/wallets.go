// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WalletsDao is the data access object for the table wallets.
type WalletsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  WalletsColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// WalletsColumns defines and stores column names for the table wallets.
type WalletsColumns struct {
	Id                        string //
	Mnemonic                  string // BIP39 Mnemonic phrase (should be encrypted)
	Password                  string // Password for UI access (consider hashing)
	PasswordHash              string // Hashed password for secure storage
	CollectAddress            string // Default collection address (maybe deprecated by chain-specific ones)
	GoogleCode                string // Google Authenticator secret key (should be encrypted)
	MinerPrivateKey           string // Private key for mining/staking (handle with extreme care, encrypt)
	MinerAddress              string // Address associated with the miner private key
	StrategyCollectSwitch     string // Enable/disable strategic collection
	EthCollectThreshold       string // Threshold for ETH collection
	TrxCollectThreshold       string // Threshold for TRX collection
	UsdtCollectThreshold      string // Threshold for USDT collection
	CronCollectSwitch         string // Enable/disable scheduled collection via cron
	CronCollectTime           string // Cron schedule time (e.g., 0 2 * * *)
	CreateAt                  string //
	UpdateAt                  string //
	LastUnlockAt              string // Timestamp of the last successful unlock
	UnlockErrorCount          string // Count of consecutive unlock failures
	GoogleCodeSwitch          string // Enable/disable Google Authenticator requirement
	TrxCollectAddress         string // Specific collection address for TRX chain
	EthCollectAddress         string // Specific collection address for ETH chain
	TrxFeePrivateKey          string // Private key for paying TRX fees (handle with care, encrypt)
	EthFeePrivateKey          string // Private key for paying ETH fees (handle with care, encrypt)
	TrxFeeAddress             string // Address holding funds for TRX fees
	TrxActivateAmount         string // Address holding funds for TRX fees
	EthFeeAddress             string // Address holding funds for ETH fees
	EthFeeMode                string // eth 矿工费模式 1 自动 2手动
	EthFeeAmount              string // eth固定矿工费发送金额
	TrxFeeAmount              string // trx 固定矿工费发送金额
	Trc20TriggerFeeAmount     string // trx 固定矿工费发送金额
	EthFeeMax                 string // eth 最大矿工费
	Erc20FeeMax               string // eth 最大矿工费
	EthGasPrice               string //
	EthGasLimit               string //
	Erc20GasPrice             string //
	Erc20GasLimit             string //
	TrxFeeMode                string // eth 矿工费模式 1 自动 2手动
	TrxFeeMax                 string // trx 交易最多支付手续费
	TrxKeepAmount             string // 归集保持账户内最低trx 余额 防止交易失败
	EthKeepAmount             string // eth 归集的时候 预留金额
	Trc20MinRequiredEnergy    string // 转账usdt最低需要的能量 手动模式设置
	Trc20MaxEnergyFee         string // 最大允许消耗的 trx 能量费金额 此处用于购买trx 能量的费用设置
	Trc20MinRequiredBandwidth string // 最低带宽
	MnemonicSalt              string //
	MnemonicIterations        string //
	GoogleSecretSalt          string //
	GoogleSecretIterations    string //
	EthMinTakeAmount          string // eth最小充值金额
	TrxMinTakeAmount          string // trx最小充值金额
	UsdtMinTakeAmount         string // usdt 最小充值金额
}

// walletsColumns holds the columns for the table wallets.
var walletsColumns = WalletsColumns{
	Id:                        "id",
	Mnemonic:                  "mnemonic",
	Password:                  "password",
	PasswordHash:              "password_hash",
	CollectAddress:            "collect_address",
	GoogleCode:                "google_code",
	MinerPrivateKey:           "miner_private_key",
	MinerAddress:              "miner_address",
	StrategyCollectSwitch:     "strategy_collect_switch",
	EthCollectThreshold:       "eth_collect_threshold",
	TrxCollectThreshold:       "trx_collect_threshold",
	UsdtCollectThreshold:      "usdt_collect_threshold",
	CronCollectSwitch:         "cron_collect_switch",
	CronCollectTime:           "cron_collect_time",
	CreateAt:                  "create_at",
	UpdateAt:                  "update_at",
	LastUnlockAt:              "last_unlock_at",
	UnlockErrorCount:          "unlock_error_count",
	GoogleCodeSwitch:          "google_code_switch",
	TrxCollectAddress:         "trx_collect_address",
	EthCollectAddress:         "eth_collect_address",
	TrxFeePrivateKey:          "trx_fee_private_key",
	EthFeePrivateKey:          "eth_fee_private_key",
	TrxFeeAddress:             "trx_fee_address",
	TrxActivateAmount:         "trx_activate_amount",
	EthFeeAddress:             "eth_fee_address",
	EthFeeMode:                "eth_fee_mode",
	EthFeeAmount:              "eth_fee_amount",
	TrxFeeAmount:              "trx_fee_amount",
	Trc20TriggerFeeAmount:     "trc20_trigger_fee_amount",
	EthFeeMax:                 "eth_fee_max",
	Erc20FeeMax:               "erc20_fee_max",
	EthGasPrice:               "eth_gas_price",
	EthGasLimit:               "eth_gas_limit",
	Erc20GasPrice:             "erc20_gas_price",
	Erc20GasLimit:             "erc20_gas_limit",
	TrxFeeMode:                "trx_fee_mode",
	TrxFeeMax:                 "trx_fee_max",
	TrxKeepAmount:             "trx_keep_amount",
	EthKeepAmount:             "eth_keep_amount",
	Trc20MinRequiredEnergy:    "trc20_min_required_energy",
	Trc20MaxEnergyFee:         "trc20_max_energy_fee",
	Trc20MinRequiredBandwidth: "trc20_min_required_bandwidth",
	MnemonicSalt:              "mnemonic_salt",
	MnemonicIterations:        "mnemonic_iterations",
	GoogleSecretSalt:          "google_secret_salt",
	GoogleSecretIterations:    "google_secret_iterations",
	EthMinTakeAmount:          "eth_min_take_amount",
	TrxMinTakeAmount:          "trx_min_take_amount",
	UsdtMinTakeAmount:         "usdt_min_take_amount",
}

// NewWalletsDao creates and returns a new DAO object for table data access.
func NewWalletsDao(handlers ...gdb.ModelHandler) *WalletsDao {
	return &WalletsDao{
		group:    "default",
		table:    "wallets",
		columns:  walletsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WalletsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WalletsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WalletsDao) Columns() WalletsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WalletsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WalletsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WalletsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
