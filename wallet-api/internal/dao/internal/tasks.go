// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TasksDao is the data access object for the table tasks.
type TasksDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TasksColumns       // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TasksColumns defines and stores column names for the table tasks.
type TasksColumns struct {
	Id          string //
	NetworkType string // e.g., ETH, TRX
	TokenType   string // e.g., USDT, ETH, TRX
	TaskType    string // e.g., transfer, collect
	ExecuteType string // e.g., manual, cron
	TaskId      string // Unique identifier for the task batch
	TaskName    string // User-defined name for the task
	Amount      string // Amount per transaction (if not is_all_amount)
	IsAllAmount string // Flag indicating if the full balance should be sent
	TotalAmount string // Calculated total amount for the task
	TotalFee    string // Calculated total fee for the task
	TaskStatus  string // Overall status of the task batch
	CreateAt    string //
	UpdateAt    string //
	GasLimit    string // Gas limit for transactions
	GasPrice    string // Gas price (e.g., in Gwei)
	FromAddress string // Source address for single transfers/collections
	ToAddress   string // Destination address for single transfers/collections
}

// tasksColumns holds the columns for the table tasks.
var tasksColumns = TasksColumns{
	Id:          "id",
	NetworkType: "network_type",
	TokenType:   "token_type",
	TaskType:    "task_type",
	ExecuteType: "execute_type",
	TaskId:      "task_id",
	TaskName:    "task_name",
	Amount:      "amount",
	IsAllAmount: "is_all_amount",
	TotalAmount: "total_amount",
	TotalFee:    "total_fee",
	TaskStatus:  "task_status",
	CreateAt:    "create_at",
	UpdateAt:    "update_at",
	GasLimit:    "gas_limit",
	GasPrice:    "gas_price",
	FromAddress: "from_address",
	ToAddress:   "to_address",
}

// NewTasksDao creates and returns a new DAO object for table data access.
func NewTasksDao(handlers ...gdb.ModelHandler) *TasksDao {
	return &TasksDao{
		group:    "default",
		table:    "tasks",
		columns:  tasksColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TasksDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TasksDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TasksDao) Columns() TasksColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TasksDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TasksDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TasksDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
