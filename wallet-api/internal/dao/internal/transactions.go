// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TransactionsDao is the data access object for the table transactions.
type TransactionsDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  TransactionsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// TransactionsColumns defines and stores column names for the table transactions.
type TransactionsColumns struct {
	Id                string //
	TransactionStatus string // e.g., pending, confirmed, failed
	Chain             string // Blockchain network (e.g., ETH, TRX)
	IsToken           string // Is this a token transaction (TRUE) or native coin (FALSE)?
	ContractAddress   string // Token contract address if is_token is TRUE
	TokenName         string // Token name (e.g., Tether)
	SenderAddress     string // Sender address
	ReceiverAddress   string // Receiver address
	TransactionTime   string // Timestamp of the transaction
	Amount            string // Transaction amount as string to handle large numbers
	TransactionType   string // e.g., transfer, contract call
	TransactionFee    string // Transaction fee as string
	Status            string // Internal status flag (e.g., 0=new, 1=processed)
	BlockNumber       string // Block number containing the transaction
	BlockHash         string // Hash of the block containing the transaction
	Confirmations     string // Number of block confirmations
	Notes             string // Additional notes about the transaction
	CreateAt          string //
	UpdateAt          string //
	TransactionHash   string // Unique hash of the transaction
	NetFee            string // Network fee component (e.g., Tron)
	EnergyFee         string // Energy fee component (e.g., Tron)
	EffectiveGasPrice string // Effective gas price paid (e.g., ETH EIP-1559)
	GasUsed           string // Amount of gas consumed
	CumulativeGasUsed string // Cumulative gas used in the block
	TokenSymbol       string // Token symbol (e.g., USDT)
}

// transactionsColumns holds the columns for the table transactions.
var transactionsColumns = TransactionsColumns{
	Id:                "id",
	TransactionStatus: "transaction_status",
	Chain:             "chain",
	IsToken:           "is_token",
	ContractAddress:   "contract_address",
	TokenName:         "token_name",
	SenderAddress:     "sender_address",
	ReceiverAddress:   "receiver_address",
	TransactionTime:   "transaction_time",
	Amount:            "amount",
	TransactionType:   "transaction_type",
	TransactionFee:    "transaction_fee",
	Status:            "status",
	BlockNumber:       "block_number",
	BlockHash:         "block_hash",
	Confirmations:     "confirmations",
	Notes:             "notes",
	CreateAt:          "create_at",
	UpdateAt:          "update_at",
	TransactionHash:   "transaction_hash",
	NetFee:            "net_fee",
	EnergyFee:         "energy_fee",
	EffectiveGasPrice: "effective_gas_price",
	GasUsed:           "gas_used",
	CumulativeGasUsed: "cumulative_gas_used",
	TokenSymbol:       "token_symbol",
}

// NewTransactionsDao creates and returns a new DAO object for table data access.
func NewTransactionsDao(handlers ...gdb.ModelHandler) *TransactionsDao {
	return &TransactionsDao{
		group:    "default",
		table:    "transactions",
		columns:  transactionsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TransactionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TransactionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TransactionsDao) Columns() TransactionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TransactionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TransactionsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TransactionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
