// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TaskAddressDao is the data access object for the table task_address.
type TaskAddressDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TaskAddressColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TaskAddressColumns defines and stores column names for the table task_address.
type TaskAddressColumns struct {
	Id              string //
	TaskId          string // Associated task ID
	SenderAddress   string // Sender address
	ReceiverAddress string // Receiver address
	Amount          string // Amount for this specific address pair
	Fee             string // Fee for this transaction
	Network         string // Network identifier
	Status          string // Status of this address task (e.g., pending, success, failed)
	FailReason      string // Reason for failure, if any
	TransactionHash string // Resulting transaction hash
	CreateAt        string //
	UpdateAt        string //
}

// taskAddressColumns holds the columns for the table task_address.
var taskAddressColumns = TaskAddressColumns{
	Id:              "id",
	TaskId:          "task_id",
	SenderAddress:   "sender_address",
	ReceiverAddress: "receiver_address",
	Amount:          "amount",
	Fee:             "fee",
	Network:         "network",
	Status:          "status",
	FailReason:      "fail_reason",
	TransactionHash: "transaction_hash",
	CreateAt:        "create_at",
	UpdateAt:        "update_at",
}

// NewTaskAddressDao creates and returns a new DAO object for table data access.
func NewTaskAddressDao(handlers ...gdb.ModelHandler) *TaskAddressDao {
	return &TaskAddressDao{
		group:    "default",
		table:    "task_address",
		columns:  taskAddressColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TaskAddressDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TaskAddressDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TaskAddressDao) Columns() TaskAddressColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TaskAddressDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TaskAddressDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TaskAddressDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
