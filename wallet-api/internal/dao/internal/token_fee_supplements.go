// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TokenFeeSupplementsDao is the data access object for the table token_fee_supplements.
type TokenFeeSupplementsDao struct {
	table    string                     // table is the underlying table name of the DAO.
	group    string                     // group is the database configuration group name of the current DAO.
	columns  TokenFeeSupplementsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler         // handlers for customized model modification.
}

// TokenFeeSupplementsColumns defines and stores column names for the table token_fee_supplements.
type TokenFeeSupplementsColumns struct {
	TokenFeeSupplementId string // 主键ID
	WithdrawPlanId       string // 计划订单id
	UserWithdrawId       string // 计划订单id
	Address              string // 需要补充费用的地址
	ChainType            string // 链类型 (例如: ERC20, TRC20)
	TokenSymbol          string // 代币符号 (例如: USDT)
	FeeType              string // 费用类型 (例如: gas_fee, energy)
	RequiredAmount       string // 需要的费用数量 (原生代币单位)
	ProvidedAmount       string // 已补充的费用数量 (原生代币单位)
	EnergyAmount         string // 补充能量数量 trc20 专用
	EnergyFee            string // 补充能量数量 trc20 专用
	Status               string // 状态 (pending, processing, success, failed, partial_success)
	TransactionHash      string // 补充费用的交易哈希
	ErrorMessage         string // 错误信息
	RelatedTaskId        string // 关联的归集任务ID
	RetryCount           string // 重试次数
	CreatedAt            string // 创建时间
	UpdatedAt            string // 更新时间
	EnergyId             string //
	IsActivating         string // 是否激活 0 未处理1  激活中 2 已激活
	ActivateHash         string // 激活hash
	ActivateAmount       string // 激活消耗trx
	TrxSupplementNeeded  string // TRX补充是否需要 0-不需要 1-需要 2 补充中 3 成功 4 失败
	TrxSupplementStatus  string // TRX补充状态 pending-待处理 processing-处理中 success-成功 failed-失败
	TrxSupplementHash    string // TRX补充交易哈希
	TrxSupplementAmount  string // TRX补充数量
	TrxBalanceBefore     string // 补充前TRX余额
	TrxBalanceAfter      string // 补充后TRX余额
}

// tokenFeeSupplementsColumns holds the columns for the table token_fee_supplements.
var tokenFeeSupplementsColumns = TokenFeeSupplementsColumns{
	TokenFeeSupplementId: "token_fee_supplement_id",
	WithdrawPlanId:       "withdraw_plan_id",
	UserWithdrawId:       "user_withdraw_id",
	Address:              "address",
	ChainType:            "chain_type",
	TokenSymbol:          "token_symbol",
	FeeType:              "fee_type",
	RequiredAmount:       "required_amount",
	ProvidedAmount:       "provided_amount",
	EnergyAmount:         "energy_amount",
	EnergyFee:            "energy_fee",
	Status:               "status",
	TransactionHash:      "transaction_hash",
	ErrorMessage:         "error_message",
	RelatedTaskId:        "related_task_id",
	RetryCount:           "retry_count",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	EnergyId:             "energy_id",
	IsActivating:         "is_activating",
	ActivateHash:         "activate_hash",
	ActivateAmount:       "activate_amount",
	TrxSupplementNeeded:  "trx_supplement_needed",
	TrxSupplementStatus:  "trx_supplement_status",
	TrxSupplementHash:    "trx_supplement_hash",
	TrxSupplementAmount:  "trx_supplement_amount",
	TrxBalanceBefore:     "trx_balance_before",
	TrxBalanceAfter:      "trx_balance_after",
}

// NewTokenFeeSupplementsDao creates and returns a new DAO object for table data access.
func NewTokenFeeSupplementsDao(handlers ...gdb.ModelHandler) *TokenFeeSupplementsDao {
	return &TokenFeeSupplementsDao{
		group:    "default",
		table:    "token_fee_supplements",
		columns:  tokenFeeSupplementsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TokenFeeSupplementsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TokenFeeSupplementsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TokenFeeSupplementsDao) Columns() TokenFeeSupplementsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TokenFeeSupplementsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TokenFeeSupplementsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TokenFeeSupplementsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
