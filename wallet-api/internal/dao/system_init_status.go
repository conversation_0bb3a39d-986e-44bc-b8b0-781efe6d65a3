// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"wallet-api/internal/dao/internal"
)

// systemInitStatusDao is the data access object for the table system_init_status.
// You can define custom methods on it to extend its functionality as needed.
type systemInitStatusDao struct {
	*internal.SystemInitStatusDao
}

var (
	// SystemInitStatus is a globally accessible object for table system_init_status operations.
	SystemInitStatus = systemInitStatusDao{internal.NewSystemInitStatusDao()}
)

// Add your custom methods and functionality below.
