// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql" // Added import
	"errors"       // Added import
	"strings"      // Added import
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/entity" // Added import

	// Added import

	// Added import
	// Added import

	"github.com/gogf/gf/v2/database/gdb"  // Corrected to v2
	"github.com/gogf/gf/v2/errors/gerror" // Added import
	"github.com/gogf/gf/v2/frame/g"       // Added import
	"github.com/gogf/gf/v2/os/gtime"      // Needed for UpdateLastUnlockTime
)

// walletsDao is the data access object for the table wallets.
// You can define custom methods on it to extend its functionality as needed.
type walletsDao struct {
	*internal.WalletsDao
}

var (
	// Wallets is a globally accessible object for table wallets operations.
	Wallets = walletsDao{internal.NewWalletsDao()}
)

// Add your custom methods and functionality below.

// GetWallet retrieves the single wallet entity. Assumes only one record exists.
func (d *walletsDao) GetWallet(ctx context.Context) (*entity.Wallets, error) {
	var wallet *entity.Wallets
	// Assuming the table has only one row, or we filter by a specific ID (e.g., 1)
	// Assuming the wallet record always has ID=1
	err := d.Ctx(ctx).Where("id", 1).Scan(&wallet)
	if err != nil {
		// Use the helper function defined in repository_utils.go (assuming it's accessible or copied here)
		// For now, inline the check:
		isNotFound := false
		if errors.Is(err, sql.ErrNoRows) {
			isNotFound = true
		} else if err != nil && strings.Contains(strings.ToLower(err.Error()), "no rows in result set") {
			isNotFound = true
		}

		if isNotFound {
			return nil, nil // Not found is not an error here
		}
		return nil, gerror.Wrap(err, "failed to get wallet")
	}
	return wallet, nil
}

// UpdateWallet updates the single wallet record.
func (d *walletsDao) UpdateWallet(ctx context.Context, data g.Map) error {
	// Assuming the table has only one row, or we filter by a specific ID (e.g., 1)
	// Update without where might affect multiple rows if table structure changes.
	// It's safer to update by a specific condition, e.g., ID=1 if that's the assumption.
	// Assuming the wallet record always has ID=1
	_, err := d.Ctx(ctx).Data(data).Where("id", 1).Update()
	if err != nil {
		return gerror.Wrap(err, "failed to update wallet")
	}
	return nil
}

// --- Custom Methods ---

// GetEncryptedMnemonic retrieves the encrypted mnemonic string from the wallet.
func (d *walletsDao) GetEncryptedMnemonic(ctx context.Context) (string, error) {
	// Assuming the wallet record always has ID=1
	var mnemonicValue gdb.Value
	var queryErr error
	mnemonicValue, queryErr = d.Ctx(ctx).Where("id", 1).Fields(d.Columns().Mnemonic).Value()
	if queryErr != nil {
		if errors.Is(queryErr, sql.ErrNoRows) {
			return "", nil // Not found
		}
		return "", gerror.Wrap(queryErr, "dao: failed to get encrypted mnemonic")
	}
	// Value() itself might return nil if field doesn't exist or value is null,
	// but sql.ErrNoRows is the primary check for record existence.
	// If record exists but field is null, String() on a nil gdb.Value might panic or return empty.
	// Assuming Mnemonic is a NOT NULL field if a wallet record exists.
	return mnemonicValue.String(), nil
}

// GetSeed is DEPRECATED in DAO layer as it performs decryption.
// Logic should be moved to service/logic layer.
// func (d *walletsDao) GetSeed(ctx context.Context) ([]byte, error) {
// 	// ... old logic ...
// }

// GetEncryptedTRXFeePrivateKey retrieves the encrypted TRX fee private key and its address.
func (d *walletsDao) GetEncryptedTRXFeePrivateKey(ctx context.Context) (encryptedPrivateKey string, feeAddress string, err error) {
	var record gdb.Record
	var queryErr error
	record, queryErr = d.Ctx(ctx).Where("id", 1).Fields(d.Columns().TrxFeePrivateKey, d.Columns().TrxFeeAddress).One()
	if queryErr != nil {
		if errors.Is(queryErr, sql.ErrNoRows) {
			return "", "", nil // Not found
		}
		return "", "", gerror.Wrap(queryErr, "dao: failed to get encrypted TRX fee private key")
	}
	// If record is found, One() does not return a nil record.
	// Accessing map keys directly is fine if fields were selected.
	return record[d.Columns().TrxFeePrivateKey].String(), record[d.Columns().TrxFeeAddress].String(), nil
}

// GetTRXMinerFeePrivateKey is DEPRECATED in DAO. Use GetEncryptedTRXFeePrivateKey and decrypt in logic.
// func (d *walletsDao) GetTRXMinerFeePrivateKey(ctx context.Context) (string, string, error) {
// 	// ... old logic ...
// }

// 获取trx 归集地址
func (d *walletsDao) GetTRXCollectAddress(ctx context.Context) (string, error) {
	wallet, err := d.Ctx(ctx).One()
	if err != nil {
		return "", err
	}
	return wallet["trx_collect_address"].String(), nil
}

// GetEncryptedETHFeePrivateKey retrieves the encrypted ETH fee private key and its address.
func (d *walletsDao) GetEncryptedETHFeePrivateKey(ctx context.Context) (encryptedPrivateKey string, feeAddress string, err error) {
	var record gdb.Record
	var queryErr error
	record, queryErr = d.Ctx(ctx).Where("id", 1).Fields(d.Columns().EthFeePrivateKey, d.Columns().EthFeeAddress).One()
	if queryErr != nil {
		if errors.Is(queryErr, sql.ErrNoRows) {
			return "", "", nil // Not found
		}
		return "", "", gerror.Wrap(queryErr, "dao: failed to get encrypted ETH fee private key")
	}
	return record[d.Columns().EthFeePrivateKey].String(), record[d.Columns().EthFeeAddress].String(), nil
}

// GetETHMinerFeePrivateKey is DEPRECATED in DAO. Use GetEncryptedETHFeePrivateKey and decrypt in logic.
// func (d *walletsDao) GetETHMinerFeePrivateKey(ctx context.Context) (string, string, error) {
// 	// ... old logic ...
// }

// 获取eth 归集地址
func (d *walletsDao) GetETHCollectAddress(ctx context.Context) (string, error) {
	wallet, err := d.Ctx(ctx).One()
	if err != nil {
		return "", err
	}
	return wallet["eth_collect_address"].String(), nil
}

// InsertWallet inserts a new wallet record into the database.
func (d *walletsDao) InsertWallet(ctx context.Context, data *entity.Wallets) error {
	_, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to insert wallet")
	}
	return nil
}

// UpdatePasswordHash updates the password hash for the wallet (assuming ID=1).
func (d *walletsDao) UpdatePasswordHash(ctx context.Context, newHash string) error {
	_, err := d.Ctx(ctx).Data(g.Map{"password": newHash}).Where("id", 1).Update()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to update password hash")
	}
	return nil
}

// UpdateGoogleSecret updates the Google Authenticator secret and status (assuming ID=1).
func (d *walletsDao) UpdateGoogleSecret(ctx context.Context, newEncryptedSecret string, enabled bool) error {
	switchVal := 0
	if enabled {
		switchVal = 1
	}
	_, err := d.Ctx(ctx).Data(g.Map{
		"google_code":        newEncryptedSecret,
		"google_code_switch": switchVal,
	}).Where("id", 1).Update()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to update google secret")
	}
	return nil
}

// UpdateLastUnlockTime updates the last unlock timestamp for the wallet (assuming ID=1).
func (d *walletsDao) UpdateLastUnlockTime(ctx context.Context, t *gtime.Time) error {
	_, err := d.Ctx(ctx).Data(g.Map{"last_unlock_at": t}).Where("id", 1).Update()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to update last unlock time")
	}
	return nil
}

// IncrementUnlockErrorCount increments the unlock error count for the wallet (assuming ID=1).
func (d *walletsDao) IncrementUnlockErrorCount(ctx context.Context) error {
	_, err := d.Ctx(ctx).Where("id", 1).Increment("unlock_error_count", 1)
	if err != nil {
		return gerror.Wrap(err, "dao: failed to increment unlock error count")
	}
	return nil
}

// ResetUnlockErrorCount resets the unlock error count for the wallet (assuming ID=1).
func (d *walletsDao) ResetUnlockErrorCount(ctx context.Context) error {
	_, err := d.Ctx(ctx).Data(g.Map{"unlock_error_count": 0}).Where("id", 1).Update()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to reset unlock error count")
	}
	return nil
}
