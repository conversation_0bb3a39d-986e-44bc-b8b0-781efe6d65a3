package dao // 放在 internal/dao 包下

import (
	"context"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// IAddressDao defines the interface for address table data access operations.
type IAddressDao interface {

	// --- Data Query Methods ---
	FindByAddress(ctx context.Context, address string, addressType string) (*entity.Address, error)
	FindByAddresses(ctx context.Context, addresses []string, addressType string) ([]*entity.Address, error)
	ListAddresses(ctx context.Context, condition map[string]interface{}, page, size int, orderBy string) (list []*entity.Address, total int, err error)
	CountAddresses(ctx context.Context, condition map[string]interface{}) (int, error)
	GetEncryptedTRXAddressPrivateKey(ctx context.Context, address string) (string, error)
	GetEncryptedETHAddressPrivateKey(ctx context.Context, address string) (string, error)
	SumBalances(ctx context.Context, condition map[string]interface{}) (coinSum float64, usdtSum float64, err error)
	GetMaxPath(ctx context.Context, addressType string) (int, error)
	// Add other query methods as needed...

	// --- Data Manipulation Methods ---

	// FindOneByCondition finds and returns a single record by condition.
	// It returns nil if no record is found.
	FindOneByCondition(ctx context.Context, condition g.Map) (*entity.Address, error)

	// InsertAddress inserts a single address record into the database.
	InsertAddress(ctx context.Context, data *entity.Address) error

	BatchInsertAddresses(ctx context.Context, addresses []*entity.Address) error
	UpdateBalance(ctx context.Context, address string, addressType string, chainCoinBalance float64, chainUsdtBalance float64) error
	UpdateAddressStatus(ctx context.Context, address string, addressType string, status int) error
	UpdateAddress(ctx context.Context, address string, data map[string]interface{}) error
	UpdateAddresses(ctx context.Context, data map[string]interface{}, condition map[string]interface{}) error
	// Add other manipulation methods as needed...
	// --- Custom methods ---
	// GetMaxEthAddressId(ctx context.Context) (int, error) // Consider moving logic to Repo/Logic
}
