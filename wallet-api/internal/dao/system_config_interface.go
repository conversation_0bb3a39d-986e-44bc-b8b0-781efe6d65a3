package dao

import (
	"context"
)

// ISystemConfigDao defines the interface for system_config table data access operations.
type ISystemConfigDao interface {
	// --- Custom Methods ---
	GetValueByKey(ctx context.Context, key string) (string, error)
	SetValueByKey(ctx context.Context, key string, value string) error // <PERSON><PERSON> insert or update
	DeleteByKey(ctx context.Context, key string) error
	// Add other necessary methods based on repository needs...
}
