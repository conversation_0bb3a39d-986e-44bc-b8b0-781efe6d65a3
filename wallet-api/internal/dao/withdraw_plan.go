// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"wallet-api/internal/dao/internal"
)

// withdrawPlanDao is the data access object for the table withdraw_plan.
// You can define custom methods on it to extend its functionality as needed.
type withdrawPlanDao struct {
	*internal.WithdrawPlanDao
}

var (
	// WithdrawPlan is a globally accessible object for table withdraw_plan operations.
	WithdrawPlan = withdrawPlanDao{internal.NewWithdrawPlanDao()}
)

// Add your custom methods and functionality below.
