package dao

import (
	"context"
	// "github.com/gogf/gf/v2/frame/g"  // 移除未使用的导入
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/do"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/os/gtime" // 用于时间处理
)

// sRefreshTokensDao 实现了 RefreshTokensDAO 接口，封装了底层 internal.RefreshTokensDao
type sRefreshTokensDao struct {
	*internal.RefreshTokensDao
}

var (
	// RefreshTokens is the singleton instance of the refresh_tokens DAO.
	RefreshTokens = NewRefreshTokensDAO()
)

// NewRefreshTokensDAO 创建并返回一个 RefreshTokensDAO 的实例
func NewRefreshTokensDAO() RefreshTokensDAO {
	return &sRefreshTokensDao{
		RefreshTokensDao: internal.NewRefreshTokensDao(),
	}
}

// Save 保存一个新的 refresh token 记录
func (d *sRefreshTokensDao) Save(ctx context.Context, token *do.RefreshTokens) error {
	_, err := d.Ctx(ctx).Data(token).Insert()
	if err != nil {
		return err
	}
	return nil
}

// GetByJTI 根据 jti 获取 refresh token 记录
func (d *sRefreshTokensDao) GetByJTI(ctx context.Context, jti string) (*entity.RefreshTokens, error) {
	one, err := d.Ctx(ctx).Where(d.Columns().Jti, jti).One()
	if err != nil {
		return nil, err
	}
	if one.IsEmpty() {
		return nil, nil
	}
	var token entity.RefreshTokens
	if err := one.Struct(&token); err != nil {
		return nil, err
	}
	return &token, nil
}

// RevokeByJTI 吊销指定 jti 的 refresh token
func (d *sRefreshTokensDao) RevokeByJTI(ctx context.Context, jti string) error {
	_, err := d.Ctx(ctx).Where(d.Columns().Jti, jti).Data(do.RefreshTokens{
		IsRevoked: true,
	}).Update()
	if err != nil {
		return err
	}
	return nil
}

// RevokeAllByUserID 吊销指定用户的所有 refresh token
func (d *sRefreshTokensDao) RevokeAllByUserID(ctx context.Context, userID uint) error {
	_, err := d.Ctx(ctx).Where(d.Columns().UserId, userID).Data(do.RefreshTokens{
		IsRevoked: true,
	}).Update()
	if err != nil {
		return err
	}
	return nil
}

// DeleteExpiredAndRevoked 删除所有已过期或已吊销的刷新令牌
func (d *sRefreshTokensDao) DeleteExpiredAndRevoked(ctx context.Context) (affectedRows int64, err error) {
	// 获取当前时间
	now := gtime.Now()

	// 删除已过期或已被吊销的令牌
	model := d.Ctx(ctx)

	// 构建 WHERE 条件： 已吊销 OR 已过期
	revoked := d.Columns().IsRevoked + "=true"
	expired := d.Columns().ExpiresAt + "<'" + now.String() + "'"
	model = model.WhereOr(revoked, expired)

	result, err := model.Delete()

	if err != nil {
		return 0, err
	}

	rowsAffected, err := result.RowsAffected()
	return rowsAffected, err
}
