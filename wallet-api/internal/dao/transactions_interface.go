package dao

import (
	"context"
	"wallet-api/internal/model/entity"
)

// ITransactionsDao defines the interface for transactions table data access operations.
type ITransactionsDao interface {
	// --- Custom Methods ---
	InsertTransaction(ctx context.Context, tx *entity.Transactions) error
	FindTransactionByHash(ctx context.Context, txHash string) (*entity.Transactions, error)
	ListTransactions(ctx context.Context, condition map[string]interface{}, page, size int, orderBy string) (list []*entity.Transactions, total int, err error)
	CountTransactions(ctx context.Context, condition map[string]interface{}) (int, error)
	SumAmount(ctx context.Context, condition map[string]interface{}) (float64, error)
	// Add other necessary methods based on repository needs...
}
