// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"wallet-api/internal/dao/internal"

	"github.com/gogf/gf/v2/database/gdb"
)

// tokenFeeSupplementsDao is the data access object for the table token_fee_supplements.
// You can define custom methods on it to extend its functionality as needed.
type tokenFeeSupplementsDao struct {
	*internal.TokenFeeSupplementsDao
}

var (
	// TokenFeeSupplements is a globally accessible object for table token_fee_supplements operations.
	TokenFeeSupplements = tokenFeeSupplementsDao{internal.NewTokenFeeSupplementsDao()}
)

// TX creates and returns a Model for the current DAO with transaction.
func (d *tokenFeeSupplementsDao) TX(tx gdb.TX) *gdb.Model {
	return d.TokenFeeSupplementsDao.DB().Model(d.TokenFeeSupplementsDao.Table()).TX(tx)
}

// Add your custom methods and functionality below.
