// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/entity" // Added import

	"github.com/gogf/gf/v2/errors/gerror" // Added import
)

// passwordAttemptsDao is the data access object for the table password_attempts.
// You can define custom methods on it to extend its functionality as needed.
type passwordAttemptsDao struct {
	*internal.PasswordAttemptsDao
}

var (
	// PasswordAttempts is a globally accessible object for table password_attempts operations.
	PasswordAttempts = passwordAttemptsDao{internal.NewPasswordAttemptsDao()}
)

// Add your custom methods and functionality below.

// --- IPasswordAttemptsDao Interface Implementation ---

// FindByID finds a single password attempt record by its ID.
func (d *passwordAttemptsDao) FindByID(ctx context.Context, id int64) (*entity.PasswordAttempts, error) {
	var attempt *entity.PasswordAttempts
	err := d.Ctx(ctx).Where("id", id).Scan(&attempt) // Uses Ctx().Where().Scan()
	if err != nil {
		return nil, gerror.Wrapf(err, "dao: failed to find password attempt by ID %d", id)
	}
	if attempt == nil {
		return nil, nil // Not found
	}
	return attempt, nil
}

// Insert inserts a new password attempt record.
func (d *passwordAttemptsDao) Insert(ctx context.Context, attempt *entity.PasswordAttempts) error {
	_, err := d.Ctx(ctx).Data(attempt).Insert() // Uses Ctx().Data().Insert()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to insert password attempt")
	}
	return nil
}

// UpdateByID updates password attempt data by ID.
func (d *passwordAttemptsDao) UpdateByID(ctx context.Context, id int64, data map[string]interface{}) error {
	if len(data) == 0 {
		return gerror.New("dao: update data cannot be empty")
	}
	_, err := d.Ctx(ctx).Data(data).Where("id", id).Update() // Uses Ctx().Data().Where().Update()
	if err != nil {
		return gerror.Wrapf(err, "dao: failed to update password attempt with ID %d", id)
	}
	return nil
}

// CountByID counts password attempts by ID.
func (d *passwordAttemptsDao) CountByID(ctx context.Context, id int64) (int, error) {
	count, err := d.Ctx(ctx).Where("id", id).Count() // Uses Ctx().Where().Count()
	if err != nil {
		return 0, gerror.Wrapf(err, "dao: failed to count password attempt by ID %d", id)
	}
	return count, nil
}
