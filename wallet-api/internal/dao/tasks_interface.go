package dao

import (
	"context"
	"wallet-api/internal/model/entity"
)

// ITasksDao defines the interface for tasks table data access operations.
type ITasksDao interface {
	// --- Custom Methods ---
	InsertTaskAndGetID(ctx context.Context, task *entity.Tasks) (int64, error)
	FindTaskByID(ctx context.Context, taskID int64) (*entity.Tasks, error)
	ListTasks(ctx context.Context, condition map[string]interface{}, page, size int, orderBy string) (list []*entity.Tasks, total int, err error)
	CountTasks(ctx context.Context, condition map[string]interface{}) (int, error)
	UpdateTask(ctx context.Context, taskID int64, data map[string]interface{}) error
	// Add other necessary methods based on repository needs...
}
