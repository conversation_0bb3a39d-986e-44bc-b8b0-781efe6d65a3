// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"strings" // Needed for SumAmount error check
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/entity" // Added import

	"github.com/gogf/gf/v2/database/gdb"  // Added import
	"github.com/gogf/gf/v2/errors/gerror" // Added import

	"github.com/gogf/gf/v2/util/gconv" // Added import for gconv
)

// taskAddressDao is the data access object for the table task_address.
// You can define custom methods on it to extend its functionality as needed.
type taskAddressDao struct {
	*internal.TaskAddressDao
}

var (
	// TaskAddress is a globally accessible object for table task_address operations.
	TaskAddress = taskAddressDao{internal.NewTaskAddressDao()}
)

// Add your custom methods and functionality below.

// --- ITaskAddressDao Interface Implementation ---

// BatchInsertTaskAddresses inserts multiple task address records in batches.
func (d *taskAddressDao) BatchInsertTaskAddresses(ctx context.Context, taskAddresses []*entity.TaskAddress) error {
	dataList := make(gdb.List, len(taskAddresses))
	for i, addr := range taskAddresses {
		dataList[i] = gconv.Map(addr) // Convert struct pointer to map
	}
	_, err := d.Ctx(ctx).Data(dataList).Batch(100).Insert() // Uses Ctx().Data().Batch().Insert()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to batch insert task addresses")
	}
	return nil
}

// ListTaskAddressesByTaskID retrieves all task addresses associated with a specific task ID.
func (d *taskAddressDao) ListTaskAddressesByTaskID(ctx context.Context, taskID int64) ([]*entity.TaskAddress, error) {
	var list []*entity.TaskAddress
	err := d.Ctx(ctx).Where("task_id", taskID).Scan(&list) // Uses Ctx().Where().Scan()
	if err != nil {
		return nil, gerror.Wrapf(err, "dao: failed to list task addresses for task ID %d", taskID)
	}
	return list, nil
}

// GetTaskAddressStatsByTaskID calculates statistics for task addresses of a specific task ID.
func (d *taskAddressDao) GetTaskAddressStatsByTaskID(ctx context.Context, taskID int64) (totalCount int, successCount int, failCount int, totalAmount float64, err error) {
	model := d.Ctx(ctx).Where("task_id", taskID) // Uses Ctx().Where()

	totalCount, err = model.Count() // Uses Count()
	if err != nil {
		err = gerror.Wrapf(err, "dao: failed to count total task addresses for task ID %d", taskID)
		return
	}

	successCount, err = model.Clone().Where("status", 2).Count() // Assuming status 2 means success
	if err != nil {
		err = gerror.Wrapf(err, "dao: failed to count successful task addresses for task ID %d", taskID)
		return
	}

	failCount, err = model.Clone().Where("status", 3).Count() // Assuming status 3 means fail
	if err != nil {
		err = gerror.Wrapf(err, "dao: failed to count failed task addresses for task ID %d", taskID)
		return
	}

	// Assuming 'amount' column stores the value to be summed
	totalAmount, err = model.Clone().Where("status", 2).Sum("amount") // Sum amount only for successful ones
	if err != nil {
		// Handle potential "no rows" error for Sum if necessary
		if strings.Contains(err.Error(), "no rows") {
			totalAmount = 0
			err = nil // Clear the error
		} else {
			err = gerror.Wrapf(err, "dao: failed to sum amount for task addresses of task ID %d", taskID)
			return
		}
	}

	return
}

// UpdateTaskAddress updates task address data by ID.
func (d *taskAddressDao) UpdateTaskAddress(ctx context.Context, taskAddressID int64, data map[string]interface{}) error {
	if len(data) == 0 {
		return gerror.New("dao: update data cannot be empty")
	}
	_, err := d.Ctx(ctx).Data(data).Where("id", taskAddressID).Update() // Uses Ctx().Data().Where().Update()
	if err != nil {
		return gerror.Wrapf(err, "dao: failed to update task address with ID %d", taskAddressID)
	}
	return nil
}
