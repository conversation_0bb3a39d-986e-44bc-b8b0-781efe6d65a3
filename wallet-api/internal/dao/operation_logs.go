// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/entity" // Added import

	"github.com/gogf/gf/v2/errors/gerror" // Added import
)

// operationLogsDao is the data access object for the table operation_logs.
// You can define custom methods on it to extend its functionality as needed.
type operationLogsDao struct {
	*internal.OperationLogsDao
}

var (
	// OperationLogs is a globally accessible object for table operation_logs operations.
	OperationLogs = operationLogsDao{internal.NewOperationLogsDao()}
)

// Add your custom methods and functionality below.

// --- IOperationLogsDao Interface Implementation ---

// InsertLog inserts a new operation log record.
func (d *operationLogsDao) InsertLog(ctx context.Context, log *entity.OperationLogs) error {
	_, err := d.Ctx(ctx).Data(log).Insert() // Uses Ctx().Data().Insert()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to insert operation log")
	}
	return nil
}
