package dao // 包名修改为 dao

import (
	"context"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// IWalletsDao defines the interface for wallets table data access operations.
type IWalletsDao interface {

	// --- Custom methods from dao/wallets.go ---
	GetEncryptedMnemonic(ctx context.Context) (string, error)
	GetEncryptedTRXFeePrivateKey(ctx context.Context) (encryptedPrivateKey string, feeAddress string, err error)
	GetTRXCollectAddress(ctx context.Context) (string, error)
	GetEncryptedETHFeePrivateKey(ctx context.Context) (encryptedPrivateKey string, feeAddress string, err error)
	GetETHCollectAddress(ctx context.Context) (string, error)

	// --- Potentially useful standard methods (explicitly defined for clarity) ---
	GetWallet(ctx context.Context) (*entity.Wallets, error)       // Assumes only one wallet record exists
	UpdateWallet(ctx context.Context, data g.Map) error           // Update the single wallet record (simplified signature)
	InsertWallet(ctx context.Context, data *entity.Wallets) error // Added InsertWallet

	// Transaction wraps the transaction logic using function f.
	Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) error

	// Add other necessary methods based on logic layer requirements...
}
