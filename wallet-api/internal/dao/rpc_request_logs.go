// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/entity" // Added import

	"github.com/gogf/gf/v2/errors/gerror" // Added import
)

// rpcRequestLogsDao is the data access object for the table rpc_request_logs.
// You can define custom methods on it to extend its functionality as needed.
type rpcRequestLogsDao struct {
	*internal.RpcRequestLogsDao
}

var (
	// RpcRequestLogs is a globally accessible object for table rpc_request_logs operations.
	RpcRequestLogs = rpcRequestLogsDao{internal.NewRpcRequestLogsDao()}
)

// Add your custom methods and functionality below.

// --- IRpcRequestLogsDao Interface Implementation ---

// InsertLog inserts a new RPC request log record.
func (d *rpcRequestLogsDao) InsertLog(ctx context.Context, log *entity.RpcRequestLogs) error {
	_, err := d.Ctx(ctx).Data(log).Insert() // Uses Ctx().Data().Insert()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to insert RPC request log")
	}
	return nil
}
