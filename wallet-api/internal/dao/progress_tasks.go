// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"wallet-api/internal/dao/internal"
)

// progressTasksDao is the data access object for the table progress_tasks.
// You can define custom methods on it to extend its functionality as needed.
type progressTasksDao struct {
	*internal.ProgressTasksDao
}

var (
	// ProgressTasks is a globally accessible object for table progress_tasks operations.
	ProgressTasks = progressTasksDao{internal.NewProgressTasksDao()}
)

// Add your custom methods and functionality below.
