package dao

import (
	"context"
	"wallet-api/internal/model/do"     // <-- 修改导入
	"wallet-api/internal/model/entity" // <-- 保留 entity 供 GetByJTI 使用
)

// RefreshTokensDAO 定义了 refresh_tokens 表的基本 CRUD 操作接口
type RefreshTokensDAO interface {
	Save(ctx context.Context, token *do.RefreshTokens) error // <-- 修改参数类型
	GetByJTI(ctx context.Context, jti string) (*entity.RefreshTokens, error)
	RevokeByJTI(ctx context.Context, jti string) error
	RevokeAllByUserID(ctx context.Context, userID uint) error
	DeleteExpiredAndRevoked(ctx context.Context) (affectedRows int64, err error) // <-- 新增方法
}