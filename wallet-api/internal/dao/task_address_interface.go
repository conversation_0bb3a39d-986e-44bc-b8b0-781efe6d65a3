package dao

import (
	"context"
	"wallet-api/internal/model/entity"
)

// ITaskAddressDao defines the interface for task_address table data access operations.
type ITaskAddressDao interface {
	// --- Custom Methods ---
	BatchInsertTaskAddresses(ctx context.Context, taskAddresses []*entity.TaskAddress) error
	ListTaskAddressesByTaskID(ctx context.Context, taskID int64) ([]*entity.TaskAddress, error)
	GetTaskAddressStatsByTaskID(ctx context.Context, taskID int64) (totalCount int, successCount int, failCount int, totalAmount float64, err error)
	UpdateTaskAddress(ctx context.Context, taskAddressID int64, data map[string]interface{}) error
	// Add other necessary methods based on repository needs...
}
