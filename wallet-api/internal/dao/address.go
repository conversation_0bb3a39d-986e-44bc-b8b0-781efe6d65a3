// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"
	"errors"
	"strings" // Needed for error checking in GetMaxPath
	"wallet-api/internal/consts"
	"wallet-api/internal/dao/internal"
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv" // Needed for Map conversion
)

// addressDao is the data access object for the table address.
// You can define custom methods on it to extend its functionality as needed.
type addressDao struct {
	*internal.AddressDao
}

var (
	// Address is a globally accessible object for table address operations.
	Address = addressDao{internal.NewAddressDao()}
)

// Add your custom methods and functionality below.

// --- IAddressDao Interface Implementation ---

// FindByAddress finds a single address record by address and type.
func (d *addressDao) FindByAddress(ctx context.Context, address string, addressType string) (*entity.Address, error) {
	var addr *entity.Address
	err := d.Ctx(ctx).Where("address", address).Where("type", addressType).Scan(&addr) // Uses Ctx().Where().Scan()
	if err != nil {
		// Consider handling sql.ErrNoRows specifically if needed, otherwise wrap
		// Note: gdb Scan might return nil, nil instead of sql.ErrNoRows
		return nil, gerror.Wrapf(err, "dao: failed to find address %s (%s)", address, addressType)
	}
	// Scan might succeed but result in nil pointer if no row found
	if addr == nil {
		return nil, nil // Explicitly return nil, nil for not found
	}
	return addr, nil
}

// FindByAddresses finds multiple address records by a list of addresses and type.
func (d *addressDao) FindByAddresses(ctx context.Context, addresses []string, addressType string) ([]*entity.Address, error) {
	var addrList []*entity.Address
	err := d.Ctx(ctx).Where("address", addresses).Where("type", addressType).Scan(&addrList) // Uses Ctx().Where().Scan()
	if err != nil {
		return nil, gerror.Wrapf(err, "dao: failed to find addresses (%s)", addressType)
	}
	return addrList, nil
}

// ListAddresses retrieves a list of addresses based on conditions, pagination, and ordering.
func (d *addressDao) ListAddresses(ctx context.Context, condition map[string]interface{}, page, size int, orderBy string) (list []*entity.Address, total int, err error) {
	model := d.Ctx(ctx) // Uses Ctx()
	if condition != nil {
		model = model.Where(condition)
	}

	total, err = model.Count() // Uses Count()
	if err != nil {
		err = gerror.Wrap(err, "dao: failed to count addresses")
		return
	}

	if orderBy != "" {
		model = model.Order(orderBy)
	}

	if page > 0 && size > 0 {
		model = model.Page(page, size)
	}

	err = model.Scan(&list) // Uses Scan()
	if err != nil {
		err = gerror.Wrap(err, "dao: failed to list addresses")
	}
	return
}

// CountAddresses counts addresses based on conditions.
func (d *addressDao) CountAddresses(ctx context.Context, condition map[string]interface{}) (int, error) {
	model := d.Ctx(ctx) // Uses Ctx()
	if condition != nil {
		model = model.Where(condition)
	}
	count, err := model.Count() // Uses Count()
	if err != nil {
		return 0, gerror.Wrap(err, "dao: failed to count addresses")
	}
	return count, nil
}

// GetEncryptedTRXAddressPrivateKey retrieves the encrypted TRX private key for a given address.
func (d *addressDao) GetEncryptedTRXAddressPrivateKey(ctx context.Context, address string) (string, error) {
	record, err := d.Ctx(ctx).Where("type", consts.NetworkTypeTRX).Where("address", address).Fields("private_key").One() // Use string literal for field
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return "", nil // Not found, or return a specific "not found" error
		}
		return "", gerror.Wrapf(err, "dao: failed to get address info for TRX address %s", address)
	}
	// One() returns a non-nil record if err is nil.
	encryptedPrivateKey := record["private_key"].String() // Use string literal
	if encryptedPrivateKey == "" {
		// This case might mean the private key was never stored or is legitimately empty (unlikely for a PK).
		// Or it could be an issue if the field is nullable and actually NULL in DB.
		g.Log().Warningf(ctx, "Encrypted TRX private key is empty in DB for address %s", address)
		return "", errors.New("encrypted private key is empty in database")
	}
	return encryptedPrivateKey, nil
}

// GetEncryptedETHAddressPrivateKey retrieves the encrypted ETH private key for a given address.
func (d *addressDao) GetEncryptedETHAddressPrivateKey(ctx context.Context, address string) (string, error) {
	record, err := d.Ctx(ctx).Where("type", consts.NetworkTypeETH).Where("address", address).Fields("private_key").One() // Use string literal for field
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return "", nil // Not found
		}
		return "", gerror.Wrapf(err, "dao: failed to get address info for ETH address %s", address)
	}
	encryptedPrivateKey := record["private_key"].String() // Use string literal
	if encryptedPrivateKey == "" {
		g.Log().Warningf(ctx, "Encrypted ETH private key is empty in DB for address %s", address)
		return "", errors.New("encrypted private key is empty in database")
	}
	return encryptedPrivateKey, nil
}

// BatchInsertAddresses inserts multiple address records in batches.
func (d *addressDao) BatchInsertAddresses(ctx context.Context, addresses []*entity.Address) error {
	// Convert []*entity.Address to []interface{} or gdb.List for batch insert
	dataList := make(gdb.List, len(addresses))
	for i, addr := range addresses {
		// Convert struct pointer to map for batch insertion
		dataList[i] = gconv.Map(addr)
	}

	_, err := d.Ctx(ctx).Data(dataList).Batch(100).Insert() // Uses Ctx().Data().Batch().Insert()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to batch insert addresses")
	}
	return nil
}

// UpdateBalance updates the coin and USDT balance for a specific address.
func (d *addressDao) UpdateBalance(ctx context.Context, address string, addressType string, chainCoinBalance float64, chainUsdtBalance float64) error {
	_, err := d.Ctx(ctx).Data(g.Map{
		"chain_coin_balance": chainCoinBalance,
		"chain_usdt_balance": chainUsdtBalance,
	}).Where("address", address).Where("type", addressType).Update() // Uses Ctx().Data().Where().Update()
	if err != nil {
		return gerror.Wrapf(err, "dao: failed to update balance for address %s (%s)", address, addressType)
	}
	return nil
}

// UpdateAddressStatus updates the status for a specific address.
func (d *addressDao) UpdateAddressStatus(ctx context.Context, address string, addressType string, status int) error {
	_, err := d.Ctx(ctx).Data(g.Map{"status": status}).Where("address", address).Where("type", addressType).Update() // Uses Ctx().Data().Where().Update()
	if err != nil {
		return gerror.Wrapf(err, "dao: failed to update status for address %s (%s)", address, addressType)
	}
	return nil
}

// UpdateAddress updates generic fields for a specific address.
func (d *addressDao) UpdateAddress(ctx context.Context, address string, data map[string]interface{}) error {
	if len(data) == 0 {
		return gerror.New("dao: update data cannot be empty")
	}
	// Since addressType is no longer provided, we assume it can be found in the data map
	// or we're updating without type filtering
	addressType, hasType := data["type"].(string)
	model := d.Ctx(ctx).Data(data).Where("address", address)

	// Only add type condition if we have a type
	if hasType {
		// Remove type from data to avoid updating the type itself
		delete(data, "type")
		model = model.Where("type", addressType)
	}

	_, err := model.Update() // Uses Ctx().Data().Where().Update()
	if err != nil {
		return gerror.Wrapf(err, "dao: failed to update address %s", address)
	}
	return nil
}

// UpdateAddresses updates generic fields for multiple addresses based on a condition.
func (d *addressDao) UpdateAddresses(ctx context.Context, data map[string]interface{}, condition map[string]interface{}) error {
	if len(data) == 0 {
		return gerror.New("dao: update data cannot be empty")
	}
	if len(condition) == 0 {
		return gerror.New("dao: update condition cannot be empty for safety") // Prevent accidental full table update
	}
	model := d.Ctx(ctx) // Uses Ctx()
	model = model.Where(condition)
	_, err := model.Data(data).Update() // Uses Data().Update()
	if err != nil {
		return gerror.Wrap(err, "dao: failed to update addresses")
	}
	return nil
}

// GetMaxPath retrieves the maximum path value for a given address type.
func (d *addressDao) GetMaxPath(ctx context.Context, addressType string) (int, error) {
	maxPathVal, err := d.Ctx(ctx).Where("type", addressType).Max("path") // Uses Ctx().Where().Max()
	if err != nil {
		// Check if the error is because no records exist for the type
		// Need to handle potential nil result or specific DB errors for "no rows" with Max
		// This check might need adjustment based on actual DB/driver behavior
		// Using string check as a fallback, sql.ErrNoRows might not be returned by Max
		if strings.Contains(err.Error(), "converting NULL") || strings.Contains(err.Error(), "no rows") {
			return 0, nil // No addresses yet, next path is 0
		}
		return 0, gerror.Wrapf(err, "dao: failed to get max path for type %s", addressType)
	}
	// Convert float64 result from Max to int
	return int(maxPathVal), nil
}

// SumBalances calculates the sum of coin and USDT balances based on a condition.
func (d *addressDao) SumBalances(ctx context.Context, condition map[string]interface{}) (coinSum float64, usdtSum float64, err error) {
	model := d.Ctx(ctx) // Uses Ctx()
	if condition != nil {
		model = model.Where(condition)
	}
	// Need to perform two separate Sum operations
	coinSum, err = model.Sum("chain_coin_balance") // Uses Sum()
	if err != nil {
		// If sum returns error on no rows, treat sum as 0
		if strings.Contains(err.Error(), "no rows") {
			coinSum = 0
			err = nil // Clear the error
		} else {
			err = gerror.Wrap(err, "dao: failed to sum coin balance")
			return
		}
	}
	// Clone the model to avoid modifying the original condition for the second sum
	modelForUsdt := model.Clone()
	usdtSum, err = modelForUsdt.Sum("chain_usdt_balance") // Uses Sum()
	if err != nil {
		// If sum returns error on no rows, treat sum as 0
		if strings.Contains(err.Error(), "no rows") {
			usdtSum = 0
			err = nil // Clear the error
		} else {
			err = gerror.Wrap(err, "dao: failed to sum usdt balance")
		}
	}
	return
}

// FindOneByCondition finds and returns a single record by condition.
func (d *addressDao) FindOneByCondition(ctx context.Context, condition g.Map) (*entity.Address, error) {
	var addr *entity.Address
	err := d.Ctx(ctx).Where(condition).Scan(&addr)
	if err != nil {
		// Note: gdb Scan might return nil, nil instead of sql.ErrNoRows
		return nil, gerror.Wrapf(err, "dao: failed to find address by condition: %+v", condition)
	}
	// Scan might succeed but result in nil pointer if no row found
	if addr == nil {
		return nil, nil // Explicitly return nil, nil for not found
	}
	return addr, nil
}

// InsertAddress inserts a single address record into the database.
func (d *addressDao) InsertAddress(ctx context.Context, data *entity.Address) error {
	_, err := d.Ctx(ctx).Data(data).Insert()
	if err != nil {
		// Consider checking for specific DB errors like duplicate keys if needed
		return gerror.Wrapf(err, "dao: failed to insert address: %+v", data)
	}
	return nil
}
