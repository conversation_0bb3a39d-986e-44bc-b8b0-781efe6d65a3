package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Merchant Module Error Codes
// Starting from 8401 for uniqueness.
var (
	// Placeholder for merchant specific codes
	CodeMerchantGetDerivedKeyFailed     = gcode.New(8401, "Failed to get derived private key", nil)
	CodeMerchantConvertPrivateKeyFailed = gcode.New(8402, "Failed to convert private key", nil)
	CodeMerchantEncryptPrivateKeyFailed = gcode.New(8403, "Failed to encrypt private key", nil)
)
