package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Utility Module Error Codes
// Starting from 8001 for uniqueness.
var (
	// internal/utility/init.go
	CodeUtilityTokenSecretNotInitialized = gcode.New(8001, "Token secret not initialized", nil)

	// internal/utility/quicknode.go
	CodeUtilityQuickNodeEndpointMissing         = gcode.New(8002, "QuickNode endpoint config missing", nil)
	CodeUtilityQuickNodeCloseRespBodyFailed     = gcode.New(8003, "Failed to close QuickNode response body", nil)
	CodeUtilityQuickNodeParseBlockHeaderFailed  = gcode.New(8004, "Failed to parse block header info", nil)
	CodeUtilityQuickNodeParseBlockRawFailed     = gcode.New(8005, "Failed to parse block raw data", nil)
	CodeUtilityQuickNodeParseBlockNumberFailed  = gcode.New(8006, "Failed to parse block number", nil)
	CodeUtilityQuickNodeBroadcastFailed         = gcode.New(8007, "Transaction broadcast failed", nil)
	CodeUtilityQuickNodeGetTokenBalanceFailed   = gcode.New(8008, "Failed to get token balance", nil)
	CodeUtilityQuickNodeParseTokenBalanceFailed = gcode.New(8009, "Failed to parse token balance", nil)

	// internal/utility/util/util.go
	CodeUtilityEncryptFailed      = gcode.New(8010, "Encryption failed", nil)
	CodeUtilityDecryptFormatError = gcode.New(8011, "Decryption string format error", nil)
	CodeUtilityDecryptFailed      = gcode.New(8012, "Decryption failed", nil)

	// internal/utility/crypto/tron/tron.go
	CodeUtilityInvalidBase58Character    = gcode.New(8013, "Invalid base58 character", nil)
	CodeUtilityHexParseFailed            = gcode.New(8014, "Failed to parse hex string", nil)
	CodeUtilityEmptyTxHash               = gcode.New(8015, "Transaction hash cannot be empty", nil)
	CodeUtilityCloseTxInfoRespBodyFailed = gcode.New(8016, "Failed to close transaction info response body", nil)

	// internal/utility/crypto/tron/sign.go
	CodeUtilityDecodeSignDataFailed = gcode.New(8017, "Failed to decode data to sign", nil)

	// internal/utility/util/userdata.go
	CodeUtilityDbOpenFailed           = gcode.New(8018, "Failed to open database", nil)
	CodeUtilityDbCloseConnFailed      = gcode.New(8019, "Failed to close database connection", nil)
	CodeUtilityDbPingFailed           = gcode.New(8020, "Database connection test failed", nil)
	CodeUtilityDbInitSchemaFailed     = gcode.New(8021, "Failed to initialize database schema", nil)
	CodeUtilityDbCreateTableFailed    = gcode.New(8022, "Failed to create table", nil)
	CodeUtilityDbCloseConnCheckFailed = gcode.New(8023, "Failed to close database connection during check", nil) // For IsDatabaseValid specific close error

	// internal/utility/crypto/eth/events.go & tron/events.go (Common patterns)
	CodeUtilityApiRequestFailed               = gcode.New(8030, "API request failed", nil)
	CodeUtilityApiResponseCloseFailed         = gcode.New(8031, "Failed to close API response body", nil)
	CodeUtilityApiResponseReadFailed          = gcode.New(8032, "Failed to read API response body", nil)
	CodeUtilityApiResponseParseFailed         = gcode.New(8033, "Failed to parse API response", nil)
	CodeUtilityApiReturnedError               = gcode.New(8034, "API returned an error status", nil)
	CodeUtilityApiParseDataFailed             = gcode.New(8035, "Failed to parse data from API response", nil)
	CodeUtilityApiEmptyResponse               = gcode.New(8036, "Received empty response body from API", nil)
	CodeUtilityConvertWeiFailed               = gcode.New(8037, "Failed to convert Wei value", nil)
	CodeUtilityConvertGasUsedFailed           = gcode.New(8038, "Failed to convert gasUsed value", nil)
	CodeUtilityConvertTxDataFailed            = gcode.New(8039, "Failed to convert transaction data", nil)
	CodeUtilityConvertSunFailed               = gcode.New(8040, "Failed to convert SUN value", nil)
	CodeUtilityConvertTrxFailed               = gcode.New(8041, "Failed to convert TRX value", nil)
	CodeUtilityConvertTokenValueFailed        = gcode.New(8042, "Failed to convert token value", nil)
	CodeUtilityInvalidSunValue                = gcode.New(8043, "Invalid SUN value", nil)
	CodeUtilityInvalidTrxValue                = gcode.New(8044, "Invalid TRX value", nil)
	CodeUtilityInvalidTokenValue              = gcode.New(8045, "Invalid token value", nil)
	CodeUtilityGetTxReceiptFailed             = gcode.New(8046, "Failed to get transaction receipt", nil)
	CodeUtilityParseTxReceiptFailed           = gcode.New(8047, "Failed to parse transaction receipt", nil)
	CodeUtilityJsonRpcSerializeFailed         = gcode.New(8048, "Failed to serialize JSON-RPC request", nil)
	CodeUtilityJsonRpcCreateRequestFailed     = gcode.New(8049, "Failed to create JSON-RPC request", nil)
	CodeUtilityJsonRpcSendRequestFailed       = gcode.New(8050, "Failed to send JSON-RPC request", nil)
	CodeUtilityJsonRpcCloseResponseFailed     = gcode.New(8051, "Failed to close JSON-RPC response body", nil)
	CodeUtilityJsonRpcReadResponseFailed      = gcode.New(8052, "Failed to read JSON-RPC response", nil)
	CodeUtilityJsonRpcParseResponseFailed     = gcode.New(8053, "Failed to parse JSON-RPC response", nil)
	CodeUtilityJsonRpcReturnedError           = gcode.New(8054, "JSON-RPC returned an error", nil)
	CodeUtilityJsonRpcResultNotFound          = gcode.New(8055, "Result field not found or null in JSON-RPC response", nil)
	CodeUtilityParseBlockNumberFailed         = gcode.New(8056, "Failed to parse block number", nil)
	CodeUtilityParseGasPriceFailed            = gcode.New(8057, "Failed to parse gas price value", nil)
	CodeUtilityParseCumulativeGasFailed       = gcode.New(8058, "Failed to parse cumulative gas used", nil)
	CodeUtilityParseGasUsedFailed             = gcode.New(8059, "Failed to parse gas used", nil)
	CodeUtilityBroadcastTxSerializeFailed     = gcode.New(8060, "Failed to serialize transaction data for broadcast", nil)
	CodeUtilityBroadcastTxCreateRequestFailed = gcode.New(8061, "Failed to create broadcast request", nil)
	CodeUtilityBroadcastTxSendRequestFailed   = gcode.New(8062, "Failed to send broadcast transaction request", nil)
	CodeUtilityBroadcastTxCloseResponseFailed = gcode.New(8063, "Failed to close broadcast response body", nil)
	CodeUtilityBroadcastTxReadResponseFailed  = gcode.New(8064, "Failed to read broadcast response", nil)
	CodeUtilityBroadcastTxParseResponseFailed = gcode.New(8065, "Failed to parse broadcast response", nil)
	CodeUtilityBroadcastTxApiError            = gcode.New(8066, "Broadcast transaction API returned an error", nil)
	CodeUtilityGetBlockNumberFailed           = gcode.New(8067, "Failed to get current block number", nil)
	CodeUtilityParseCurrentBlockFailed        = gcode.New(8068, "Failed to parse current block number", nil)
	CodeUtilityParseTxBlockFailed             = gcode.New(8069, "Failed to parse transaction block number", nil)
	CodeUtilityUnknownTxReceiptStatus         = gcode.New(8070, "Unknown transaction receipt status", nil)
	CodeUtilityConvertBalanceFailed           = gcode.New(8071, "Failed to convert balance", nil)
	CodeUtilityUsingMemoryTokenSecret         = gcode.New(8072, "Using in-memory tokenSecret, skipping database operation", nil) // For init.go log

	// internal/utility/crypto/tron/tron.go specific errors
	CodeUtilityTronTxNoCostData    = gcode.New(8073, "Transaction has no cost data", nil)
	CodeUtilityJsonMarshalFailed   = gcode.New(8074, "Failed to marshal JSON data", nil)
	CodeUtilityJsonUnmarshalFailed = gcode.New(8075, "Failed to unmarshal JSON data", nil)
	CodeUtilityAmountParseFailed   = gcode.New(8076, "Failed to parse amount string", nil)

	// internal/utility/crypto/eth/events.go specific errors
	CodeUtilityTxExecutionFailed        = gcode.New(8077, "Transaction execution failed", nil)
	CodeUtilityTxNotFoundOrPending      = gcode.New(8078, "Transaction not found or pending confirmation", nil)
	CodeUtilityParseConfirmationsFailed = gcode.New(8079, "Failed to parse confirmations", nil)
	CodeUtilityTxNotFound               = gcode.New(8080, "Transaction not found", nil)
	CodeUtilityTokenSecretNotConfigured = gcode.New(8081, "JWT token secret is not configured", nil) // Added for init.go
)
