package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Wallet Logic Module Error Codes
// Starting from 8501 for uniqueness.
var (
	// Placeholder for wallet logic specific codes

	// internal/logic/wallet/wallet.go
	CodeWalletQueryFailed                       = gcode.New(8501, "Failed to query wallet record", nil)
	CodeWalletAlreadyExists                     = gcode.New(8502, "Wallet already exists", nil)
	CodeWalletGenerateMnemonicFailed            = gcode.New(8503, "Failed to generate mnemonic", nil)
	CodeWalletInvalidGoogleCode                 = gcode.New(8504, "Invalid Google Authenticator code", nil)
	CodeWalletInvalidMnemonic                   = gcode.New(8505, "Invalid mnemonic phrase format", nil)
	CodeWalletEncryptPasswordFailed             = gcode.New(8506, "Failed to encrypt password", nil)
	CodeWalletEncryptMnemonicFailed             = gcode.New(8507, "Failed to encrypt mnemonic", nil)
	CodeWalletEncryptGoogleSecretFailed         = gcode.New(8508, "Failed to encrypt Google Authenticator secret", nil)
	CodeWalletSaveFailed                        = gcode.New(8509, "Failed to save wallet information", nil)
	CodeWalletGetRequestInfoFailed              = gcode.New(8510, "Failed to get request info", nil) // For GetStatus context error
	CodeWalletNotInitialized                    = gcode.New(8511, "Wallet not initialized", nil)
	CodeWalletNotUnlocked                       = gcode.New(8512, "Wallet not unlocked", nil)
	CodeWalletExpired                           = gcode.New(8513, "Wallet session expired, please login again", nil)
	CodeWalletAuthTokenNotProvided              = gcode.New(8514, "Authentication token not provided", nil)
	CodeWalletAuthTokenFormatError              = gcode.New(8515, "Authentication token format error", nil)
	CodeWalletGetTokenSecretFailed              = gcode.New(8516, "Failed to get token secret", nil)
	CodeWalletInvalidSignAlgorithm              = gcode.New(8517, "Invalid signing algorithm", nil)
	CodeWalletInvalidAuthToken                  = gcode.New(8518, "Invalid authentication token", nil)
	CodeWalletStatusOk                          = gcode.New(8519, "Wallet status is normal", nil) // For success message
	CodeWalletNotFound                          = gcode.New(8520, "Wallet record not found", nil)
	CodeWalletGetTrc20UsdtFailed                = gcode.New(8521, "Failed to get TRC20 USDT balance", nil)
	CodeWalletGetTrxFailed                      = gcode.New(8522, "Failed to get TRX balance", nil)
	CodeWalletGetErc20UsdtFailed                = gcode.New(8523, "Failed to get ERC20 USDT balance", nil)
	CodeWalletGetEthFailed                      = gcode.New(8524, "Failed to get ETH balance", nil)
	CodeWalletGenerateSaltFailed                = gcode.New(8525, "Failed to generate salt for encryption", nil)                         // New
	CodeConfigError                             = gcode.New(8526, "Configuration error", nil)                                            // New - General config error
	CodeWalletAuthPasswordRequiredForDecryption = gcode.New(8527, "Password is required for decryption operation", nil)                  // New
	CodeUtilityFunctionRemoved                  = gcode.New(8528, "A required utility function has been removed or is unavailable", nil) // New

	// internal/logic/wallet/node.go
	CodeWalletNodeApiKeyNotConfigured       = gcode.New(8551, "ethscan.api_key not configured", nil)
	CodeWalletNodeHttpRequestFailed         = gcode.New(8552, "HTTP request failed", nil)
	CodeWalletNodeApiRequestFailed          = gcode.New(8553, "ethscan API request failed", nil)
	CodeWalletNodeReadResponseFailed        = gcode.New(8554, "Failed to read response body", nil)
	CodeWalletNodeParseResponseFailed       = gcode.New(8555, "Failed to parse response body", nil)
	CodeWalletNodeParseSafeGasFailed        = gcode.New(8556, "Failed to parse SafeGasPrice", nil)
	CodeWalletNodeParseProposeGasFailed     = gcode.New(8557, "Failed to parse ProposeGasPrice", nil)
	CodeWalletNodeParseFastGasFailed        = gcode.New(8558, "Failed to parse FastGasPrice", nil)
	CodeWalletNodeParseSuggestBaseFeeFailed = gcode.New(8559, "Failed to parse SuggestBaseFee", nil)
	CodeWalletNodeGetErc20UsdtFailedLog     = gcode.New(8560, "Failed to get ERC20 USDT balance", nil)   // For logging only
	CodeWalletNodeGetEthFailedLog           = gcode.New(8561, "Failed to get ETH balance", nil)          // For logging only
	CodeWalletNodeGetTronBalanceFailedLog   = gcode.New(8562, "Failed to get TRON account balance", nil) // For logging only
	CodeWalletNodeUnsupportedType           = gcode.New(8563, "Unsupported type", nil)

	// internal/logic/wallet/setting.go
	CodeWalletSettingInvalidTrxAddressFormat     = gcode.New(8601, "Invalid TRX collection address format", nil)
	CodeWalletSettingInvalidEthAddressFormat     = gcode.New(8602, "Invalid ETH collection address format", nil)
	CodeWalletSettingInvalidTrxKeyFormat         = gcode.New(8603, "Invalid TRX fee private key format", nil)
	CodeWalletSettingTrxAddressConvertFailed     = gcode.New(8604, "TRX fee address conversion failed", nil)
	CodeWalletSettingInvalidEthKeyFormat         = gcode.New(8605, "Invalid ETH fee private key format", nil)
	CodeWalletSettingEthAddressConvertFailed     = gcode.New(8606, "ETH fee address conversion failed", nil)
	CodeWalletSettingUpdateInfoFailed            = gcode.New(8607, "Failed to update wallet information", nil)
	CodeWalletSettingGetInfoFailed               = gcode.New(8608, "Failed to get wallet information", nil)
	CodeWalletSettingUpdateCronCollectFailed     = gcode.New(8609, "Failed to update wallet cron collection settings", nil)
	CodeWalletSettingUpdateStrategyCollectFailed = gcode.New(8610, "Failed to update wallet strategy collection settings", nil)

	// internal/logic/wallet/auth.go
	CodeWalletAuthGenerateJwtFailed          = gcode.New(8651, "Failed to generate JWT token", nil)
	CodeWalletAuthUpdateLastUnlockFailed     = gcode.New(8652, "Failed to update wallet last unlock time", nil)
	CodeWalletAuthOldPasswordIncorrect       = gcode.New(8653, "Incorrect old password", nil)
	CodeWalletAuthNewPasswordSameAsOld       = gcode.New(8654, "New password cannot be the same as the old password", nil)
	CodeWalletAuthUpdatePasswordFailed       = gcode.New(8655, "Failed to update password", nil)
	CodeWalletAuthUpdateTimestampFailedLog   = gcode.New(8656, "Failed to update timestamp (password updated)", nil) // For logging only
	CodeWalletAuthGenerateGoogleKeyFailed    = gcode.New(8657, "Failed to generate Google Authenticator key", nil)
	CodeWalletAuthEncryptGoogleKeyFailed     = gcode.New(8658, "Failed to encrypt Google Authenticator key", nil)
	CodeWalletAuthUpdateGoogleKeyFailed      = gcode.New(8659, "Failed to update Google Authenticator key", nil)
	CodeWalletAuthDecryptGoogleKeyFailed     = gcode.New(8660, "Failed to decrypt Google Authenticator key", nil)
	CodeWalletAuthPasswordVerifyFailed       = gcode.New(8661, "Password verification failed", nil)
	CodeWalletAuthHashMigrationFailedLog     = gcode.New(8662, "Failed to re-hash password during migration", nil)           // For logging only
	CodeWalletAuthDbUpdateMigrationFailedLog = gcode.New(8663, "Failed to update password hash in DB during migration", nil) // For logging only
	CodeWalletAuthHashMigrationSuccessLog    = gcode.New(8664, "Wallet password hash migrated successfully", nil)            // For logging only

	// internal/logic/wallet/collect.go
	CodeWalletCollectCreateTaskFailed        = gcode.New(8701, "Failed to create collection task", nil)
	CodeWalletCollectCreateTaskAddressFailed = gcode.New(8702, "Failed to create task address data", nil)
	CodeWalletCollectCreateTaskSuccessLog    = gcode.New(8703, "Collection task created successfully", nil) // For logging only
	CodeWalletCollectInvalidTransferType     = gcode.New(8704, "Invalid transfer type", nil)
	CodeWalletCollectInvalidNetworkType      = gcode.New(8705, "Invalid network type", nil)
	CodeWalletCollectOneToManyNoAllAmount    = gcode.New(8706, "One-to-many transfer does not support 'all amount'", nil)

	// internal/logic/wallet/recharge.go
	CodeWalletGetRechargeRecordFailed = gcode.New(8721, "Failed to get recharge records", nil)
	CodeWalletGetRechargeStatsFailed  = gcode.New(8722, "Failed to get recharge statistics", nil)

	// internal/logic/wallet/withdraw.go
	CodeWalletGetWithdrawsFailed     = gcode.New(8731, "Failed to get withdraw records", nil)
	CodeWalletGetWithdrawStatsFailed = gcode.New(8732, "Failed to get withdraw statistics", nil)
	CodeWalletCreateWithdrawFailed   = gcode.New(8733, "Failed to create withdraw record", nil)

	CodeWalletCollectInvalidAmount            = gcode.New(8707, "Please specify a valid transfer amount", nil)
	CodeWalletCollectMissingToAddress         = gcode.New(8708, "Please specify at least one recipient address", nil)
	CodeWalletCollectMissingFromAddress       = gcode.New(8709, "Please specify at least one sender address", nil)
	CodeWalletCollectInvalidGasLimit          = gcode.New(8710, "Please specify a valid gas limit", nil)
	CodeWalletCollectInvalidGasPrice          = gcode.New(8711, "Please specify a valid gas price", nil)
	CodeWalletCollectAddressNotSet            = gcode.New(8712, "Collection address not set for the network", nil)
	CodeWalletCollectFeeAddressNotSet         = gcode.New(8713, "Fee address not set for the network", nil)
	CodeWalletCollectTimeFormatFailed         = gcode.New(8714, "Time formatting failed", nil)
	CodeWalletCollectGetTaskListFailed        = gcode.New(8715, "Failed to get collection task list", nil)
	CodeWalletCollectGetTaskStatsFailedLog    = gcode.New(8716, "Failed to get address statistics for task", nil) // For logging only
	CodeWalletCollectGetTaskFailed            = gcode.New(8717, "Failed to get collection task", nil)
	CodeWalletCollectTaskNotFound             = gcode.New(8718, "Task not found", nil)
	CodeWalletCollectGetTaskAddressListFailed = gcode.New(8719, "Failed to get task address list", nil)
	CodeWalletCollectCsvHeaderFailed          = gcode.New(8720, "Failed to write CSV header", nil)
	CodeWalletCollectCsvRowFailedLog          = gcode.New(8721, "Failed to write CSV row", nil) // For logging only
	CodeWalletCollectCsvProcessError          = gcode.New(8722, "Error during CSV writing process", nil)
	CodeWalletCollectCsvFlushFailed           = gcode.New(8723, "Failed to flush CSV writer", nil)
	CodeWalletCollectGetTxRecordFailed        = gcode.New(8724, "Failed to get transaction records", nil)
	CodeWalletCollectExportTxRecordFailed     = gcode.New(8725, "Failed to export transaction records", nil)
	CodeWalletCollectExportTxRecordSuccessLog = gcode.New(8726, "Exported transaction records", nil) // For logging only
	CodeWalletCollectGetTxStatsFailed         = gcode.New(8727, "Failed to get transaction statistics", nil)

	// internal/logic/wallet/address.go
	CodeWalletAddressCreateTooMany           = gcode.New(8751, "Cannot create more than 10000 addresses at once", nil)
	CodeWalletAddressGetDerivedKeyFailedLog  = gcode.New(8752, "Failed to get derived private key", nil) // For logging only
	CodeWalletAddressConvertKeyFailedLog     = gcode.New(8753, "Failed to convert private key", nil)     // For logging only
	CodeWalletAddressEncryptKeyFailedLog     = gcode.New(8754, "Failed to encrypt private key", nil)     // For logging only
	CodeWalletAddressBatchCreateFailedLog    = gcode.New(8755, "Failed to batch create addresses", nil)  // For logging only
	CodeWalletAddressBatchCreateSuccessLog   = gcode.New(8756, "Successfully created addresses", nil)    // For logging only
	CodeWalletAddressCheckWalletExistsFailed = gcode.New(8757, "Failed to check if wallet exists", nil)
	CodeWalletAddressGetListFailed           = gcode.New(8758, "Failed to get address list", nil)
	CodeWalletAddressGetStatsFailed          = gcode.New(8759, "Failed to get address statistics", nil)
	CodeWalletAddressInvalidExportType       = gcode.New(8760, "Invalid export type", nil)
	CodeWalletAddressFindFailed              = gcode.New(8761, "Failed to find addresses", nil)
	CodeWalletAddressUpdateBindStatusFailed  = gcode.New(8762, "Failed to update address bind status", nil)
	CodeWalletAddressNoAddressToExport       = gcode.New(8763, "No addresses available for export", nil)
	CodeWalletAddressNotFound                = gcode.New(8764, "Address not found", nil)
	CodeWalletAddressGetEthBalanceFailedLog  = gcode.New(8765, "Failed to get ETH balance", nil)        // For logging only
	CodeWalletAddressGetErc20UsdtFailedLog   = gcode.New(8766, "Failed to get ERC20 USDT balance", nil) // For logging only
	CodeWalletAddressGetTrxBalanceFailedLog  = gcode.New(8767, "Failed to get TRX balance", nil)        // For logging only
	CodeWalletAddressUpdateBalanceFailedLog  = gcode.New(8768, "Failed to update address balance", nil) // For logging only
	// Codes for key derivation utilities, logically grouped with address generation
	CodeWalletAddressGetSeedFailed                    = gcode.New(8769, "Failed to get wallet seed for derivation", nil)
	CodeWalletAddressGetMaxPathFailed                 = gcode.New(8770, "Failed to get max derivation path for chain", nil)
	CodeWalletAddressNewMasterKeyFailed               = gcode.New(8771, "Failed to create master key from seed", nil)
	CodeWalletAddressNewChildKeyFailed                = gcode.New(8772, "Failed to derive child key", nil)
	CodeWalletAddressGetTaskProgressFailed            = gcode.New(8773, "Failed to get address creation task progress", nil)
	CodeWalletAddressTaskNotFound                     = gcode.New(8774, "Address creation task not found", nil)
	CodeWalletSettingInvalidEthFeeMode                = gcode.New(8611, "Invalid eth fee mode", nil)
	CodeWalletSettingInvalidEthFeeMax                 = gcode.New(8612, "Invalid eth fee max", nil)
	CodeWalletSettingInvalidEthGasPrice               = gcode.New(8613, "Invalid eth gas price", nil)
	CodeWalletSettingInvalidEthGasLimit               = gcode.New(8614, "Invalid eth gas limit", nil)
	CodeWalletSettingInvalidTrxFeeMax                 = gcode.New(8615, "Invalid trx fee max", nil)
	CodeWalletSettingInvalidTrxFeeMode                = gcode.New(8616, "Invalid trx fee mode", nil)
	CodeWalletSettingInvalidTrxKeepAmount             = gcode.New(8617, "Invalid trx keep amount", nil)
	CodeWalletSettingInvalidEthKeepAmount             = gcode.New(8618, "Invalid eth keep amount", nil)
	CodeWalletSettingInvalidTrc20MinRequiredEnergy    = gcode.New(8619, "Invalid trc20 min required energy", nil)
	CodeWalletSettingInvalidTrc20MaxEnergyFee         = gcode.New(8620, "Invalid trc20 max energy fee", nil)
	CodeWalletSettingInvalidTrc20MinRequiredBandwidth = gcode.New(8621, "Invalid trc20 min required bandwidth", nil)
	CodeWalletSettingInvalidTrc20TriggerFeeAmount     = gcode.New(8622, "Invalid trc20 trigger fee amount", nil)
)
