package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Kafka Module Error Codes
// Starting from 8101 for uniqueness.
var (
	// internal/pkg/kafka/producer.go
	CodeKafkaCreateProducerFailed   = gcode.New(8101, "Failed to create Kafka producer", nil)
	CodeKafkaSerializeMessageFailed = gcode.New(8102, "Failed to serialize message", nil)
	CodeKafkaSendMessageFailed      = gcode.New(8103, "Failed to send message to Kafka", nil)

	// internal/pkg/kafka/consumer.go
	CodeKafkaCreateConsumerGroupFailed = gcode.New(8104, "Failed to create Kafka consumer group", nil)
	CodeKafkaNoTopicSubscribed         = gcode.New(8105, "No topic subscribed", nil)
	CodeKafkaConsumeError              = gcode.New(8106, "Kafka consume error", nil)      // For logging
	CodeKafkaHandleMessageFailed       = gcode.New(8107, "Failed to handle message", nil) // For logging
)
