package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Task Module Error Codes
// Starting from 8201 for uniqueness.
var (
	// internal/task/task_eth.go
	CodeTaskInitEthBalanceJobFailed   = gcode.New(8201, "Failed to init ETH balance query job", nil)    // Log
	CodeTaskQueryEthAddressListFailed = gcode.New(8202, "Failed to query ETH address list", nil)        // Log
	CodeTaskQueryEthTxRecordFailed    = gcode.New(8203, "Failed to query ETH transaction records", nil) // Log (Retry loop)
	CodeTaskCheckTxExistsFailed       = gcode.New(8204, "Failed to check if transaction exists", nil)   // Log
	CodeTaskQueryCollectionTaskFailed = gcode.New(8205, "Failed to query collection task", nil)         // Log
	CodeTaskGetTxStatusFailed         = gcode.New(8206, "Failed to get transaction status", nil)        // Log
	CodeTaskConvertGasUsedToEthFailed = gcode.New(8207, "Failed to convert GasUsed to ETH", nil)        // Log (Commented out in original)
	CodeTaskSaveTxRecordFailed        = gcode.New(8208, "Failed to save transaction record", nil)       // Log
	CodeTaskQueryAddressInfoFailed    = gcode.New(8209, "Failed to query address info", nil)            // Log
	CodeTaskAddressInfoNotFound       = gcode.New(8210, "Address info not found", nil)                  // Log
	CodeTaskQueryEthBalanceFailed     = gcode.New(8211, "Failed to query ETH balance", nil)             // Log
	CodeTaskQueryUsdtBalanceFailed    = gcode.New(8212, "Failed to query USDT balance", nil)            // Log
	CodeTaskUpdateBalanceFailed       = gcode.New(8213, "Failed to update address balance", nil)        // Log

	// internal/task/task_tron.go
	CodeTaskInitTronBalanceJobFailed   = gcode.New(8251, "Failed to init TRON balance query job", nil)    // Log
	CodeTaskQueryTronAddressListFailed = gcode.New(8252, "Failed to query TRON address list", nil)        // Log
	CodeTaskQueryTronTxRecordFailed    = gcode.New(8253, "Failed to query TRON transaction records", nil) // Log (Retry loop)
	// CodeTaskCheckTxExistsFailed (Use 8204)
	// CodeTaskQueryCollectionTaskFailed (Use 8205)
	// CodeTaskSaveTxRecordFailed (Use 8208)
	// CodeTaskQueryAddressInfoFailed (Use 8209)
	// CodeTaskAddressInfoNotFound (Use 8210)
	CodeTaskQueryTronBalanceInfoFailed = gcode.New(8254, "Failed to query TRON balance info", nil) // Log
	// CodeTaskUpdateBalanceFailed (Use 8213)

	// internal/task/trans_eth.go
	CodeTaskInitEthTxJobFailed = gcode.New(8301, "Failed to init ETH transaction query job", nil) // Log

	// internal/task/trans_tron.go
	CodeTaskInitTronTxJobFailed        = gcode.New(8351, "Failed to init TRON transaction query job", nil) // Log
	CodeTaskQueryTronTaskListFailed    = gcode.New(8352, "Failed to query TRON task list", nil)            // Log
	CodeTaskTronTaskNotFound           = gcode.New(8353, "TRON task not found", nil)                       // Log
	CodeTaskQueryTronWalletFailed      = gcode.New(8354, "Failed to query TRON wallet", nil)               // Log (Commented out)
	CodeTaskQueryTronPrivKeyFailed     = gcode.New(8355, "Failed to query TRON address private key", nil)  // Log (Commented out)
	CodeTaskTronInvalidTaskType        = gcode.New(8356, "Invalid TRON task type", nil)                    // Log (Commented out)
	CodeTaskTronAmountConvertFailed    = gcode.New(8357, "Failed to convert TRON amount", nil)             // Log (Commented out)
	CodeTaskTronAmountToInt64Failed    = gcode.New(8358, "Failed to convert TRON amount to int64", nil)    // Log (Commented out)
	CodeTaskTronPrivKeyDecodeFailed    = gcode.New(8359, "Failed to decode TRON private key", nil)         // Log (Commented out)
	CodeTaskTronBroadcastTxFailed      = gcode.New(8360, "Failed to broadcast TRON transaction", nil)      // Log (Commented out)
	CodeTaskTronSaveTxRecordFailed     = gcode.New(8361, "Failed to save TRON transaction record", nil)    // Log (Commented out)
	CodeTaskTronUpdateTaskStatusFailed = gcode.New(8362, "Failed to update TRON task status", nil)         // Log (Commented out)

)
