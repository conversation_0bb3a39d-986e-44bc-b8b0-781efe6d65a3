package codes

import "github.com/gogf/gf/v2/errors/gcode"

// Withdraw Plan Module Error Codes
// Starting from 8801 for uniqueness.
var (
	// Withdraw Plan specific codes
	CodeWalletWithdrawPlanQueryFailed  = gcode.New(8801, "Failed to query withdraw plan", nil)
	CodeWalletWithdrawPlanCreateFailed = gcode.New(8802, "Failed to create withdraw plan", nil)
	CodeWalletWithdrawPlanUpdateFailed = gcode.New(8803, "Failed to update withdraw plan", nil)
	CodeWalletWithdrawPlanNotFound     = gcode.New(8804, "Withdraw plan not found", nil)
	CodeWalletCheckWalletExistsFailed  = gcode.New(8805, "Failed to check if wallet exists", nil)
)
