package codes

// Boot process messages
const (
	// Errors
	ErrorCreateConfigDir         = "Failed to create config directory"
	ErrorReadPasswordFail        = "Failed to read password: %v"
	ErrorPasswordEmpty           = "Password cannot be empty"
	ErrorInitialHashFail         = "Failed to hash password on initial setup: %v"
	ErrorPasswordHashFail        = "Password hashing failed"
	ErrorSaveSecretFail          = "Failed to save tokenSecret to DB via DAO"
	ErrorReadAttemptFail         = "Failed to read attempt record"
	ErrorReadAttemptFailDAO      = "Failed to read attempt record via DAO: %v"
	ErrorAttemptNotFound         = "Attempt record not found after creation attempt"
	ErrorGetAttemptFail          = "Cannot retrieve attempt record"
	ErrorResetDeleteSecretFail   = "Failed to delete tokenSecret via DAO on reset: %v"
	ErrorPasswordCompareFail     = "Unexpected error during password comparison: %v"
	ErrorUserInputReadFailed     = "Failed to read user input" // Corresponds to boot.ErrUserInputReadFailed

	// Warnings (Logs)
	LogWarnQuerySecretFail         = "Failed to query tokenSecret via DAO: %v"
	LogWarnInitialResetAttemptFail = "Failed to reset attempt record via DAO on initial setup: %v"
	LogWarnResetAttemptFail        = "Failed to reset attempt record via DAO on reset: %v"
	LogWarnUpdateAttemptFail       = "Failed to update attempt record via DAO: %v"
	LogWarnResetAttemptSuccessFail = "Failed to reset attempts via DAO after success: %v"
	LogWarnCheckAttemptFail        = "Failed to check attempt record via DAO: %v"
	LogWarnCreateAttemptFail       = "Failed to create initial attempt record via DAO: %v"
	LogWarnStartupCheckFailed    = "Startup security check failed, aborting: %v"

	// Fatal (Logs)
	LogFatalStartupInternalError = "Internal error during startup security check: %v"
	LogFatalLoggerInitFailed     = "Logger system initialization failed: %v"
	LogFatalDatabaseInitFailed   = "Database initialization failed: %v"
	LogFatalCacheInitFailed      = "Cache service initialization failed: %v"

	// Info (Logs)
	LogInfoCurrentAttempts      = "Current attempt count: %d"
	LogInfoIncorrectPassword    = "Incorrect password entered. Attempts left: %d"
	LogInfoPasswordSuccess      = "Password verified, attempts reset"
	LogInfoCreateAttemptSuccess = "Initial attempt record created via DAO"
	LogInfoSystemInitComplete   = "System resources initialized successfully"
	LogInfoDatabasePlaceholder  = "Database initialization placeholder (old password logic removed)"

	// Debug (Logs)
	LogDebugSecretNotFound = "Startup password hash not found in DB (via DAO)"

	// Prompts (fmt.Println/Printf)
	PromptInitialPassword         = "First system start, please set startup password:"
	PromptErrorPasswordEmpty      = "Error: Password cannot be empty"
	PromptInitialSetupComplete    = "System initialized, password set. Please restart."
	PromptMaxAttemptsReachedReset = "Max attempts reached, system will reset..."
	PromptSystemResetComplete     = "System reset. Please restart for initialization."
	PromptWarningMaxAttempts      = "Warning: Exceeding max attempts will reset the system and clear all data!"
	PromptEnterPassword           = "Enter startup password (Attempts left: %d):\n"
	PromptPasswordIncorrect       = "Incorrect password. Attempts left: %d\n"
	PromptPasswordSuccess         = "Password verified, system starting..."
)