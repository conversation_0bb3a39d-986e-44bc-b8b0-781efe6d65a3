// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// OperationLogs is the golang structure for table operation_logs.
type OperationLogs struct {
	Id          int         `json:"id"          orm:"id"           description:""`           //
	Logtime     *gtime.Time `json:"logtime"     orm:"logtime"      description:"logtime"`    // logtime
	Logtype     string      `json:"logtype"     orm:"logtype"      description:"logtype"`    // logtype
	Logcontent  string      `json:"logcontent"  orm:"logcontent"   description:"logcontent"` // logcontent
	Logstatus   string      `json:"logstatus"   orm:"logstatus"    description:"日志状态"`       // 日志状态
	ErrorInfo   string      `json:"errorInfo"   orm:"error_info"   description:"错误信息"`       // 错误信息
	CorrectInfo string      `json:"correctInfo" orm:"correct_info" description:"正确信息"`       // 正确信息
	CreateAt    *gtime.Time `json:"createAt"    orm:"create_at"    description:"创建时间"`       // 创建时间
	UpdateAt    *gtime.Time `json:"updateAt"    orm:"update_at"    description:"更新时间"`       // 更新时间
}
