// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TokenFeeSupplements is the golang structure for table token_fee_supplements.
type TokenFeeSupplements struct {
	TokenFeeSupplementId uint64      `json:"tokenFeeSupplementId" orm:"token_fee_supplement_id" description:"主键ID"`                                                       // 主键ID
	WithdrawPlanId       int64       `json:"withdrawPlanId"       orm:"withdraw_plan_id"        description:"计划订单id"`                                                     // 计划订单id
	UserWithdrawId       int64       `json:"userWithdrawId"       orm:"user_withdraw_id"        description:"计划订单id"`                                                     // 计划订单id
	Address              string      `json:"address"              orm:"address"                 description:"需要补充费用的地址"`                                                  // 需要补充费用的地址
	ChainType            string      `json:"chainType"            orm:"chain_type"              description:"链类型 (例如: ERC20, TRC20)"`                                     // 链类型 (例如: ERC20, TRC20)
	TokenSymbol          string      `json:"tokenSymbol"          orm:"token_symbol"            description:"代币符号 (例如: USDT)"`                                            // 代币符号 (例如: USDT)
	FeeType              string      `json:"feeType"              orm:"fee_type"                description:"费用类型 (例如: gas_fee, energy)"`                                 // 费用类型 (例如: gas_fee, energy)
	RequiredAmount       float64     `json:"requiredAmount"       orm:"required_amount"         description:"需要的费用数量 (原生代币单位)"`                                           // 需要的费用数量 (原生代币单位)
	ProvidedAmount       float64     `json:"providedAmount"       orm:"provided_amount"         description:"已补充的费用数量 (原生代币单位)"`                                          // 已补充的费用数量 (原生代币单位)
	EnergyAmount         float64     `json:"energyAmount"         orm:"energy_amount"           description:"补充能量数量 trc20 专用"`                                            // 补充能量数量 trc20 专用
	EnergyFee            float64     `json:"energyFee"            orm:"energy_fee"              description:"补充能量数量 trc20 专用"`                                            // 补充能量数量 trc20 专用
	Status               string      `json:"status"               orm:"status"                  description:"状态 (pending, processing, success, failed, partial_success)"` // 状态 (pending, processing, success, failed, partial_success)
	TransactionHash      string      `json:"transactionHash"      orm:"transaction_hash"        description:"补充费用的交易哈希"`                                                  // 补充费用的交易哈希
	ErrorMessage         string      `json:"errorMessage"         orm:"error_message"           description:"错误信息"`                                                       // 错误信息
	RelatedTaskId        string      `json:"relatedTaskId"        orm:"related_task_id"         description:"关联的归集任务ID"`                                                  // 关联的归集任务ID
	RetryCount           uint        `json:"retryCount"           orm:"retry_count"             description:"重试次数"`                                                       // 重试次数
	CreatedAt            *gtime.Time `json:"createdAt"            orm:"created_at"              description:"创建时间"`                                                       // 创建时间
	UpdatedAt            *gtime.Time `json:"updatedAt"            orm:"updated_at"              description:"更新时间"`                                                       // 更新时间
	EnergyId             string      `json:"energyId"             orm:"energy_id"               description:""`                                                           //
	IsActivating         int         `json:"isActivating"         orm:"is_activating"           description:"是否激活 0 未处理1  激活中 2 已激活"`                                     // 是否激活 0 未处理1  激活中 2 已激活
	ActivateHash         string      `json:"activateHash"         orm:"activate_hash"           description:"激活hash"`                                                     // 激活hash
	ActivateAmount       float64     `json:"activateAmount"       orm:"activate_amount"         description:"激活消耗trx"`                                                    // 激活消耗trx
	TrxSupplementNeeded  int         `json:"trxSupplementNeeded"  orm:"trx_supplement_needed"   description:"TRX补充是否需要 0-不需要 1-需要 2 补充中 3 成功 4 失败"`                       // TRX补充是否需要 0-不需要 1-需要 2 补充中 3 成功 4 失败
	TrxSupplementStatus  string      `json:"trxSupplementStatus"  orm:"trx_supplement_status"   description:"TRX补充状态 pending-待处理 processing-处理中 success-成功 failed-失败"`    // TRX补充状态 pending-待处理 processing-处理中 success-成功 failed-失败
	TrxSupplementHash    string      `json:"trxSupplementHash"    orm:"trx_supplement_hash"     description:"TRX补充交易哈希"`                                                  // TRX补充交易哈希
	TrxSupplementAmount  float64     `json:"trxSupplementAmount"  orm:"trx_supplement_amount"   description:"TRX补充数量"`                                                    // TRX补充数量
	TrxBalanceBefore     float64     `json:"trxBalanceBefore"     orm:"trx_balance_before"      description:"补充前TRX余额"`                                                   // 补充前TRX余额
	TrxBalanceAfter      float64     `json:"trxBalanceAfter"      orm:"trx_balance_after"       description:"补充后TRX余额"`                                                   // 补充后TRX余额
}
