// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Transactions is the golang structure for table transactions.
type Transactions struct {
	Id                int         `json:"id"                orm:"id"                  description:""`                                                           //
	TransactionStatus string      `json:"transactionStatus" orm:"transaction_status"  description:"e.g., pending, confirmed, failed"`                           // e.g., pending, confirmed, failed
	Chain             string      `json:"chain"             orm:"chain"               description:"Blockchain network (e.g., ETH, TRX)"`                        // Blockchain network (e.g., ETH, TRX)
	IsToken           int         `json:"isToken"           orm:"is_token"            description:"Is this a token transaction (TRUE) or native coin (FALSE)?"` // Is this a token transaction (TRUE) or native coin (FALSE)?
	ContractAddress   string      `json:"contractAddress"   orm:"contract_address"    description:"Token contract address if is_token is TRUE"`                 // Token contract address if is_token is TRUE
	TokenName         string      `json:"tokenName"         orm:"token_name"          description:"Token name (e.g., Tether)"`                                  // Token name (e.g., Tether)
	SenderAddress     string      `json:"senderAddress"     orm:"sender_address"      description:"Sender address"`                                             // Sender address
	ReceiverAddress   string      `json:"receiverAddress"   orm:"receiver_address"    description:"Receiver address"`                                           // Receiver address
	TransactionTime   *gtime.Time `json:"transactionTime"   orm:"transaction_time"    description:"Timestamp of the transaction"`                               // Timestamp of the transaction
	Amount            string      `json:"amount"            orm:"amount"              description:"Transaction amount as string to handle large numbers"`       // Transaction amount as string to handle large numbers
	TransactionType   string      `json:"transactionType"   orm:"transaction_type"    description:"e.g., transfer, contract call"`                              // e.g., transfer, contract call
	TransactionFee    string      `json:"transactionFee"    orm:"transaction_fee"     description:"Transaction fee as string"`                                  // Transaction fee as string
	Status            int         `json:"status"            orm:"status"              description:"Internal status flag (e.g., 0=new, 1=processed)"`            // Internal status flag (e.g., 0=new, 1=processed)
	BlockNumber       int64       `json:"blockNumber"       orm:"block_number"        description:"Block number containing the transaction"`                    // Block number containing the transaction
	BlockHash         string      `json:"blockHash"         orm:"block_hash"          description:"Hash of the block containing the transaction"`               // Hash of the block containing the transaction
	Confirmations     int         `json:"confirmations"     orm:"confirmations"       description:"Number of block confirmations"`                              // Number of block confirmations
	Notes             string      `json:"notes"             orm:"notes"               description:"Additional notes about the transaction"`                     // Additional notes about the transaction
	CreateAt          *gtime.Time `json:"createAt"          orm:"create_at"           description:""`                                                           //
	UpdateAt          *gtime.Time `json:"updateAt"          orm:"update_at"           description:""`                                                           //
	TransactionHash   string      `json:"transactionHash"   orm:"transaction_hash"    description:"Unique hash of the transaction"`                             // Unique hash of the transaction
	NetFee            string      `json:"netFee"            orm:"net_fee"             description:"Network fee component (e.g., Tron)"`                         // Network fee component (e.g., Tron)
	EnergyFee         string      `json:"energyFee"         orm:"energy_fee"          description:"Energy fee component (e.g., Tron)"`                          // Energy fee component (e.g., Tron)
	EffectiveGasPrice string      `json:"effectiveGasPrice" orm:"effective_gas_price" description:"Effective gas price paid (e.g., ETH EIP-1559)"`              // Effective gas price paid (e.g., ETH EIP-1559)
	GasUsed           string      `json:"gasUsed"           orm:"gas_used"            description:"Amount of gas consumed"`                                     // Amount of gas consumed
	CumulativeGasUsed string      `json:"cumulativeGasUsed" orm:"cumulative_gas_used" description:"Cumulative gas used in the block"`                           // Cumulative gas used in the block
	TokenSymbol       string      `json:"tokenSymbol"       orm:"token_symbol"        description:"Token symbol (e.g., USDT)"`                                  // Token symbol (e.g., USDT)
}
