// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Tasks is the golang structure for table tasks.
type Tasks struct {
	Id          int         `json:"id"          orm:"id"            description:""`                                                     //
	NetworkType string      `json:"networkType" orm:"network_type"  description:"e.g., ETH, TRX"`                                       // e.g., ETH, TRX
	TokenType   string      `json:"tokenType"   orm:"token_type"    description:"e.g., USDT, ETH, TRX"`                                 // e.g., USDT, ETH, TRX
	TaskType    string      `json:"taskType"    orm:"task_type"     description:"e.g., transfer, collect"`                              // e.g., transfer, collect
	ExecuteType string      `json:"executeType" orm:"execute_type"  description:"e.g., manual, cron"`                                   // e.g., manual, cron
	TaskId      string      `json:"taskId"      orm:"task_id"       description:"Unique identifier for the task batch"`                 // Unique identifier for the task batch
	TaskName    string      `json:"taskName"    orm:"task_name"     description:"User-defined name for the task"`                       // User-defined name for the task
	Amount      float64     `json:"amount"      orm:"amount"        description:"Amount per transaction (if not is_all_amount)"`        // Amount per transaction (if not is_all_amount)
	IsAllAmount int         `json:"isAllAmount" orm:"is_all_amount" description:"Flag indicating if the full balance should be sent"`   // Flag indicating if the full balance should be sent
	TotalAmount float64     `json:"totalAmount" orm:"total_amount"  description:"Calculated total amount for the task"`                 // Calculated total amount for the task
	TotalFee    float64     `json:"totalFee"    orm:"total_fee"     description:"Calculated total fee for the task"`                    // Calculated total fee for the task
	TaskStatus  string      `json:"taskStatus"  orm:"task_status"   description:"Overall status of the task batch"`                     // Overall status of the task batch
	CreateAt    *gtime.Time `json:"createAt"    orm:"create_at"     description:""`                                                     //
	UpdateAt    *gtime.Time `json:"updateAt"    orm:"update_at"     description:""`                                                     //
	GasLimit    int         `json:"gasLimit"    orm:"gas_limit"     description:"Gas limit for transactions"`                           // Gas limit for transactions
	GasPrice    float64     `json:"gasPrice"    orm:"gas_price"     description:"Gas price (e.g., in Gwei)"`                            // Gas price (e.g., in Gwei)
	FromAddress string      `json:"fromAddress" orm:"from_address"  description:"Source address for single transfers/collections"`      // Source address for single transfers/collections
	ToAddress   string      `json:"toAddress"   orm:"to_address"    description:"Destination address for single transfers/collections"` // Destination address for single transfers/collections
}
