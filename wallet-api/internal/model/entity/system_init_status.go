// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SystemInitStatus is the golang structure for table system_init_status.
type SystemInitStatus struct {
	Id            uint        `json:"id"            orm:"id"             description:"Primary Key"`                                                                             // Primary Key
	IsInitialized uint        `json:"isInitialized" orm:"is_initialized" description:"Is the system initialized (0=false, 1=true)"`                                             // Is the system initialized (0=false, 1=true)
	PasswordHash  string      `json:"passwordHash"  orm:"password_hash"  description:"Hashed password for startup (bcrypt)"`                                                    // Hashed password for startup (bcrypt)
	Salt          string      `json:"salt"          orm:"salt"           description:"Salt for password hashing (kept for potential future use, bcrypt includes salt in hash)"` // Salt for password hashing (kept for potential future use, bcrypt includes salt in hash)
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:"Creation Time"`                                                                           // Creation Time
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:"Update Time"`                                                                             // Update Time
}
