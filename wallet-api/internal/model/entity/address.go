// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Address is the golang structure for table address.
type Address struct {
	Id               int         `json:"id"               orm:"id"                 description:""`                   //
	WalletId         int         `json:"walletId"         orm:"wallet_id"          description:"钱包id"`               // 钱包id
	Address          string      `json:"address"          orm:"address"            description:"地址"`                 // 地址
	Type             string      `json:"type"             orm:"type"               description:"类型"`                 // 类型
	Label            string      `json:"label"            orm:"label"              description:"标签"`                 // 标签
	BindStatus       int         `json:"bindStatus"       orm:"bind_status"        description:"绑定状态 (0 = default)"` // 绑定状态 (0 = default)
	BindAt           *gtime.Time `json:"bindAt"           orm:"bind_at"            description:"绑定时间"`               // 绑定时间
	LastQueryAt      *gtime.Time `json:"lastQueryAt"      orm:"last_query_at"      description:"最近查询时间"`             // 最近查询时间
	Status           int         `json:"status"           orm:"status"             description:"状态 (0 = default)"`   // 状态 (0 = default)
	ChainCoinBalance float64     `json:"chainCoinBalance" orm:"chain_coin_balance" description:"chain coin 余额"`      // chain coin 余额
	ChainUsdtBalance float64     `json:"chainUsdtBalance" orm:"chain_usdt_balance" description:"chain usdt 余额"`      // chain usdt 余额
	CreateAt         *gtime.Time `json:"createAt"         orm:"create_at"          description:"创建时间"`               // 创建时间
	UpdateAt         *gtime.Time `json:"updateAt"         orm:"update_at"          description:"更新时间"`               // 更新时间
	Path             int         `json:"path"             orm:"path"               description:"路径"`                 // 路径
	Alias            string      `json:"alias"            orm:"alias"              description:""`                   //
	MerchantId       int         `json:"merchantId"       orm:"merchantId"         description:""`                   //
	PrivateKey       string      `json:"privateKey"       orm:"private_key"        description:""`                   //
}
