// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PasswordAttempts is the golang structure for table password_attempts.
type PasswordAttempts struct {
	Id           int         `json:"id"           orm:"id"            description:""` //
	AttemptCount int         `json:"attemptCount" orm:"attempt_count" description:""` //
	LastAttempt  *gtime.Time `json:"lastAttempt"  orm:"last_attempt"  description:""` //
}
