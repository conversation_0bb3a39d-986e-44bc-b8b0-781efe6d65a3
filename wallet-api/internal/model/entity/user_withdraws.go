// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserWithdraws is the golang structure for table user_withdraws.
type UserWithdraws struct {
	UserWithdrawsId      uint        `json:"userWithdrawsId"      orm:"user_withdraws_id"       description:"主键ID"`                                                                                   // 主键ID
	TokenFeeSupplementId int64       `json:"tokenFeeSupplementId" orm:"token_fee_supplement_id" description:""`                                                                                       //
	Name                 string      `json:"name"                 orm:"name"                    description:"币种ID"`                                                                                   // 币种ID
	Chan                 string      `json:"chan"                 orm:"chan"                    description:"链"`                                                                                      // 链
	FromAddress          string      `json:"fromAddress"          orm:"from_address"            description:"提币目标地址"`                                                                                 // 提币目标地址
	ToAddress            string      `json:"toAddress"            orm:"to_address"              description:"提币目标地址"`                                                                                 // 提币目标地址
	Amount               float64     `json:"amount"               orm:"amount"                  description:"申请提现金额"`                                                                                 // 申请提现金额
	HandlingFee          float64     `json:"handlingFee"          orm:"handling_fee"            description:"提现手续费"`                                                                                  // 提现手续费
	ActualAmount         float64     `json:"actualAmount"         orm:"actual_amount"           description:"实际到账金额"`                                                                                 // 实际到账金额
	State                uint        `json:"state"                orm:"state"                   description:"状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)"` // 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)
	TxHash               string      `json:"txHash"               orm:"tx_hash"                 description:"链上交易哈希/ID"`                                                                              // 链上交易哈希/ID
	ErrorMessage         string      `json:"errorMessage"         orm:"error_message"           description:"失败或错误信息"`                                                                                // 失败或错误信息
	CreatedAt            *gtime.Time `json:"createdAt"            orm:"created_at"              description:"创建时间"`                                                                                   // 创建时间
	CheckedAt            *gtime.Time `json:"checkedAt"            orm:"checked_at"              description:"审核时间 (审核通过或拒绝的时间)"`                                                                      // 审核时间 (审核通过或拒绝的时间)
	ProcessingAt         *gtime.Time `json:"processingAt"         orm:"processing_at"           description:"开始处理时间 (进入“处理中”状态的时间)"`                                                                  // 开始处理时间 (进入“处理中”状态的时间)
	CompletedAt          *gtime.Time `json:"completedAt"          orm:"completed_at"            description:"完成时间 (变为“已完成”或“失败”状态的时间)"`                                                               // 完成时间 (变为“已完成”或“失败”状态的时间)
	UpdatedAt            *gtime.Time `json:"updatedAt"            orm:"updated_at"              description:"最后更新时间"`                                                                                 // 最后更新时间
	Retries              int         `json:"retries"              orm:"retries"                 description:""`                                                                                       //
	NergyState           int         `json:"nergyState"           orm:"nergy_state"             description:"0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了"`                                                         // 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	GasfeeState          int         `json:"gasfeeState"          orm:"gasfee_state"            description:"gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了"`                                                // gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	EthFeeMode           int         `json:"ethFeeMode"           orm:"eth_fee_mode"            description:"eth 矿工费模式 1 自动 2手动"`                                                                     // eth 矿工费模式 1 自动 2手动
	EthFeeMax            float64     `json:"ethFeeMax"            orm:"eth_fee_max"             description:"eth 最大矿工费"`                                                                              // eth 最大矿工费
	EthGasPrice          int64       `json:"ethGasPrice"          orm:"eth_gas_price"           description:""`                                                                                       //
	EthGasLimit          int64       `json:"ethGasLimit"          orm:"eth_gas_limit"           description:""`                                                                                       //
	TrxFeeMax            int64       `json:"trxFeeMax"            orm:"trx_fee_max"             description:""`                                                                                       //
	RechargesId          int         `json:"rechargesId"          orm:"recharges_id"            description:"充值订单id"`                                                                                 // 充值订单id
	GasfeeHash           string      `json:"gasfeeHash"           orm:"gasfee_hash"             description:"gas费hash"`                                                                               // gas费hash
	GasfeeAmount         float64     `json:"gasfeeAmount"         orm:"gasfee_amount"           description:"gas费金额"`                                                                                 // gas费金额
	EnergyMsg            string      `json:"energyMsg"            orm:"energy_msg"              description:""`                                                                                       //
	CanActAt             *gtime.Time `json:"canActAt"             orm:"can_act_at"              description:"完成时间 (变为“已完成”或“失败”状态的时间)"`                                                               // 完成时间 (变为“已完成”或“失败”状态的时间)
}
