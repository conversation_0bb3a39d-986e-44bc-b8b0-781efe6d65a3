// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SystemConfig is the golang structure for table system_config.
type SystemConfig struct {
	Id        int         `json:"id"        orm:"id"         description:""`                    //
	Key       string      `json:"key"       orm:"key"        description:"Configuration key"`   // Configuration key
	Value     string      `json:"value"     orm:"value"      description:"Configuration value"` // Configuration value
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""`                    //
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""`                    //
}
