// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Wallets is the golang structure for table wallets.
type Wallets struct {
	Id                        int         `json:"id"                        orm:"id"                           description:""`                                                                     //
	Mnemonic                  string      `json:"mnemonic"                  orm:"mnemonic"                     description:"BIP39 Mnemonic phrase (should be encrypted)"`                          // BIP39 Mnemonic phrase (should be encrypted)
	Password                  string      `json:"password"                  orm:"password"                     description:"Password for UI access (consider hashing)"`                            // Password for UI access (consider hashing)
	PasswordHash              string      `json:"passwordHash"              orm:"password_hash"                description:"Hashed password for secure storage"`                                   // Hashed password for secure storage
	CollectAddress            string      `json:"collectAddress"            orm:"collect_address"              description:"Default collection address (maybe deprecated by chain-specific ones)"` // Default collection address (maybe deprecated by chain-specific ones)
	GoogleCode                string      `json:"googleCode"                orm:"google_code"                  description:"Google Authenticator secret key (should be encrypted)"`                // Google Authenticator secret key (should be encrypted)
	MinerPrivateKey           string      `json:"minerPrivateKey"           orm:"miner_private_key"            description:"Private key for mining/staking (handle with extreme care, encrypt)"`   // Private key for mining/staking (handle with extreme care, encrypt)
	MinerAddress              string      `json:"minerAddress"              orm:"miner_address"                description:"Address associated with the miner private key"`                        // Address associated with the miner private key
	StrategyCollectSwitch     int         `json:"strategyCollectSwitch"     orm:"strategy_collect_switch"      description:"Enable/disable strategic collection"`                                  // Enable/disable strategic collection
	EthCollectThreshold       string      `json:"ethCollectThreshold"       orm:"eth_collect_threshold"        description:"Threshold for ETH collection"`                                         // Threshold for ETH collection
	TrxCollectThreshold       string      `json:"trxCollectThreshold"       orm:"trx_collect_threshold"        description:"Threshold for TRX collection"`                                         // Threshold for TRX collection
	UsdtCollectThreshold      string      `json:"usdtCollectThreshold"      orm:"usdt_collect_threshold"       description:"Threshold for USDT collection"`                                        // Threshold for USDT collection
	CronCollectSwitch         int         `json:"cronCollectSwitch"         orm:"cron_collect_switch"          description:"Enable/disable scheduled collection via cron"`                         // Enable/disable scheduled collection via cron
	CronCollectTime           string      `json:"cronCollectTime"           orm:"cron_collect_time"            description:"Cron schedule time (e.g., 0 2 * * *)"`                                 // Cron schedule time (e.g., 0 2 * * *)
	CreateAt                  *gtime.Time `json:"createAt"                  orm:"create_at"                    description:""`                                                                     //
	UpdateAt                  *gtime.Time `json:"updateAt"                  orm:"update_at"                    description:""`                                                                     //
	LastUnlockAt              *gtime.Time `json:"lastUnlockAt"              orm:"last_unlock_at"               description:"Timestamp of the last successful unlock"`                              // Timestamp of the last successful unlock
	UnlockErrorCount          int         `json:"unlockErrorCount"          orm:"unlock_error_count"           description:"Count of consecutive unlock failures"`                                 // Count of consecutive unlock failures
	GoogleCodeSwitch          int         `json:"googleCodeSwitch"          orm:"google_code_switch"           description:"Enable/disable Google Authenticator requirement"`                      // Enable/disable Google Authenticator requirement
	TrxCollectAddress         string      `json:"trxCollectAddress"         orm:"trx_collect_address"          description:"Specific collection address for TRX chain"`                            // Specific collection address for TRX chain
	EthCollectAddress         string      `json:"ethCollectAddress"         orm:"eth_collect_address"          description:"Specific collection address for ETH chain"`                            // Specific collection address for ETH chain
	TrxFeePrivateKey          string      `json:"trxFeePrivateKey"          orm:"trx_fee_private_key"          description:"Private key for paying TRX fees (handle with care, encrypt)"`          // Private key for paying TRX fees (handle with care, encrypt)
	EthFeePrivateKey          string      `json:"ethFeePrivateKey"          orm:"eth_fee_private_key"          description:"Private key for paying ETH fees (handle with care, encrypt)"`          // Private key for paying ETH fees (handle with care, encrypt)
	TrxFeeAddress             string      `json:"trxFeeAddress"             orm:"trx_fee_address"              description:"Address holding funds for TRX fees"`                                   // Address holding funds for TRX fees
	TrxActivateAmount         float64     `json:"trxActivateAmount"         orm:"trx_activate_amount"          description:"Address holding funds for TRX fees"`                                   // Address holding funds for TRX fees
	EthFeeAddress             string      `json:"ethFeeAddress"             orm:"eth_fee_address"              description:"Address holding funds for ETH fees"`                                   // Address holding funds for ETH fees
	EthFeeMode                int         `json:"ethFeeMode"                orm:"eth_fee_mode"                 description:"eth 矿工费模式 1 自动 2手动"`                                                   // eth 矿工费模式 1 自动 2手动
	EthFeeAmount              float64     `json:"ethFeeAmount"              orm:"eth_fee_amount"               description:"eth固定矿工费发送金额"`                                                         // eth固定矿工费发送金额
	TrxFeeAmount              float64     `json:"trxFeeAmount"              orm:"trx_fee_amount"               description:"trx 固定矿工费发送金额"`                                                        // trx 固定矿工费发送金额
	Trc20TriggerFeeAmount     float64     `json:"trc20TriggerFeeAmount"     orm:"trc20_trigger_fee_amount"     description:"trx 固定矿工费发送金额"`                                                        // trx 固定矿工费发送金额
	EthFeeMax                 float64     `json:"ethFeeMax"                 orm:"eth_fee_max"                  description:"eth 最大矿工费"`                                                            // eth 最大矿工费
	Erc20FeeMax               float64     `json:"erc20FeeMax"               orm:"erc20_fee_max"                description:"eth 最大矿工费"`                                                            // eth 最大矿工费
	EthGasPrice               int64       `json:"ethGasPrice"               orm:"eth_gas_price"                description:""`                                                                     //
	EthGasLimit               int64       `json:"ethGasLimit"               orm:"eth_gas_limit"                description:""`                                                                     //
	Erc20GasPrice             int64       `json:"erc20GasPrice"             orm:"erc20_gas_price"              description:""`                                                                     //
	Erc20GasLimit             int64       `json:"erc20GasLimit"             orm:"erc20_gas_limit"              description:""`                                                                     //
	TrxFeeMode                int         `json:"trxFeeMode"                orm:"trx_fee_mode"                 description:"eth 矿工费模式 1 自动 2手动"`                                                   // eth 矿工费模式 1 自动 2手动
	TrxFeeMax                 int64       `json:"trxFeeMax"                 orm:"trx_fee_max"                  description:"trx 交易最多支付手续费"`                                                        // trx 交易最多支付手续费
	TrxKeepAmount             int64       `json:"trxKeepAmount"             orm:"trx_keep_amount"              description:"归集保持账户内最低trx 余额 防止交易失败"`                                               // 归集保持账户内最低trx 余额 防止交易失败
	EthKeepAmount             int64       `json:"ethKeepAmount"             orm:"eth_keep_amount"              description:"eth 归集的时候 预留金额"`                                                       // eth 归集的时候 预留金额
	Trc20MinRequiredEnergy    int64       `json:"trc20MinRequiredEnergy"    orm:"trc20_min_required_energy"    description:"转账usdt最低需要的能量 手动模式设置"`                                                 // 转账usdt最低需要的能量 手动模式设置
	Trc20MaxEnergyFee         float64     `json:"trc20MaxEnergyFee"         orm:"trc20_max_energy_fee"         description:"最大允许消耗的 trx 能量费金额 此处用于购买trx 能量的费用设置"`                                  // 最大允许消耗的 trx 能量费金额 此处用于购买trx 能量的费用设置
	Trc20MinRequiredBandwidth int64       `json:"trc20MinRequiredBandwidth" orm:"trc20_min_required_bandwidth" description:"最低带宽"`                                                                 // 最低带宽
	MnemonicSalt              []byte      `json:"mnemonicSalt"              orm:"mnemonic_salt"                description:""`                                                                     //
	MnemonicIterations        int         `json:"mnemonicIterations"        orm:"mnemonic_iterations"          description:""`                                                                     //
	GoogleSecretSalt          []byte      `json:"googleSecretSalt"          orm:"google_secret_salt"           description:""`                                                                     //
	GoogleSecretIterations    int         `json:"googleSecretIterations"    orm:"google_secret_iterations"     description:""`                                                                     //
	EthMinTakeAmount          float64     `json:"ethMinTakeAmount"          orm:"eth_min_take_amount"          description:"eth最小充值金额"`                                                            // eth最小充值金额
	TrxMinTakeAmount          float64     `json:"trxMinTakeAmount"          orm:"trx_min_take_amount"          description:"trx最小充值金额"`                                                            // trx最小充值金额
	UsdtMinTakeAmount         float64     `json:"usdtMinTakeAmount"         orm:"usdt_min_take_amount"         description:"usdt 最小充值金额"`                                                          // usdt 最小充值金额
}
