// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// RefreshTokens is the golang structure for table refresh_tokens.
type RefreshTokens struct {
	Id        uint        `json:"id"        orm:"id"         description:""`                                                //
	UserId    uint        `json:"userId"    orm:"user_id"    description:"Associated user/wallet ID"`                       // Associated user/wallet ID
	Jti       string      `json:"jti"       orm:"jti"        description:"JWT ID, unique identifier for the refresh token"` // JWT ID, unique identifier for the refresh token
	ExpiresAt *gtime.Time `json:"expiresAt" orm:"expires_at" description:"Expiry timestamp of the refresh token"`           // Expiry timestamp of the refresh token
	IsRevoked int         `json:"isRevoked" orm:"is_revoked" description:"Flag indicating if the token has been revoked"`   // Flag indicating if the token has been revoked
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"Timestamp of creation"`                           // Timestamp of creation
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"Timestamp of last update"`                        // Timestamp of last update
}
