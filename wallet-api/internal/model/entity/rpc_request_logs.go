// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// RpcRequestLogs is the golang structure for table rpc_request_logs.
type RpcRequestLogs struct {
	Id               int         `json:"id"               orm:"id"                 description:""`          //
	RequestTime      *gtime.Time `json:"requestTime"      orm:"request_time"       description:"请求时间"`      // 请求时间
	RequestType      string      `json:"requestType"      orm:"request_type"       description:"请求类型"`      // 请求类型
	RequestMethod    string      `json:"requestMethod"    orm:"request_method"     description:"请求方法"`      // 请求方法
	Address          string      `json:"address"          orm:"address"            description:"请求address"` // 请求address
	Coin             string      `json:"coin"             orm:"coin"               description:"coin"`      // coin
	RequestParams    string      `json:"requestParams"    orm:"request_params"     description:"请求参数"`      // 请求参数
	RequestUrl       string      `json:"requestUrl"       orm:"request_url"        description:"请求完整地址"`    // 请求完整地址
	RequestStatus    string      `json:"requestStatus"    orm:"request_status"     description:"请求状态"`      // 请求状态
	RequestResult    string      `json:"requestResult"    orm:"request_result"     description:"请求结果"`      // 请求结果
	RequestErrorInfo string      `json:"requestErrorInfo" orm:"request_error_info" description:"请求错误信息"`    // 请求错误信息
	CreateAt         *gtime.Time `json:"createAt"         orm:"create_at"          description:"创建时间"`      // 创建时间
	UpdateAt         *gtime.Time `json:"updateAt"         orm:"update_at"          description:"更新时间"`      // 更新时间
}
