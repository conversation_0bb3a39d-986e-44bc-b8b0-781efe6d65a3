// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskAddress is the golang structure for table task_address.
type TaskAddress struct {
	Id              int         `json:"id"              orm:"id"               description:""`                                                             //
	TaskId          int         `json:"taskId"          orm:"task_id"          description:"Associated task ID"`                                           // Associated task ID
	SenderAddress   string      `json:"senderAddress"   orm:"sender_address"   description:"Sender address"`                                               // Sender address
	ReceiverAddress string      `json:"receiverAddress" orm:"receiver_address" description:"Receiver address"`                                             // Receiver address
	Amount          float64     `json:"amount"          orm:"amount"           description:"Amount for this specific address pair"`                        // Amount for this specific address pair
	Fee             float64     `json:"fee"             orm:"fee"              description:"Fee for this transaction"`                                     // Fee for this transaction
	Network         string      `json:"network"         orm:"network"          description:"Network identifier"`                                           // Network identifier
	Status          string      `json:"status"          orm:"status"           description:"Status of this address task (e.g., pending, success, failed)"` // Status of this address task (e.g., pending, success, failed)
	FailReason      string      `json:"failReason"      orm:"fail_reason"      description:"Reason for failure, if any"`                                   // Reason for failure, if any
	TransactionHash string      `json:"transactionHash" orm:"transaction_hash" description:"Resulting transaction hash"`                                   // Resulting transaction hash
	CreateAt        *gtime.Time `json:"createAt"        orm:"create_at"        description:""`                                                             //
	UpdateAt        *gtime.Time `json:"updateAt"        orm:"update_at"        description:""`                                                             //
}
