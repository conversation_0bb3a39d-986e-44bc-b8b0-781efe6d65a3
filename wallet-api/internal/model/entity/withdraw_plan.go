// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// WithdrawPlan is the golang structure for table withdraw_plan.
type WithdrawPlan struct {
	WithdrawPlanId uint        `json:"withdrawPlanId" orm:"withdraw_plan_id" description:"主键ID"`            // 主键ID
	Chan           string      `json:"chan"           orm:"chan"             description:"链"`               // 链
	Address        string      `json:"address"        orm:"address"          description:"地址"`              // 地址
	State          uint        `json:"state"          orm:"state"            description:"状态: 1-待确认, 2-完成"` // 状态: 1-待确认, 2-完成
	ErrorMessage   string      `json:"errorMessage"   orm:"error_message"    description:"失败或错误信息"`         // 失败或错误信息
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"       description:"创建时间"`            // 创建时间
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"       description:"最后更新时间"`          // 最后更新时间
	RetryCount     int         `json:"retryCount"     orm:"retry_count"      description:""`                //
}
