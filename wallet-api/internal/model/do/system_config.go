// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SystemConfig is the golang structure of table system_config for DAO operations like Where/Data.
type SystemConfig struct {
	g.Meta    `orm:"table:system_config, do:true"`
	Id        interface{} //
	Key       interface{} // Configuration key
	Value     interface{} // Configuration value
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
}
