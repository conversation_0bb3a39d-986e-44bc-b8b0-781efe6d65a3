// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Tasks is the golang structure of table tasks for DAO operations like Where/Data.
type Tasks struct {
	g.Meta      `orm:"table:tasks, do:true"`
	Id          interface{} //
	NetworkType interface{} // e.g., ETH, TRX
	TokenType   interface{} // e.g., USDT, ETH, TRX
	TaskType    interface{} // e.g., transfer, collect
	ExecuteType interface{} // e.g., manual, cron
	TaskId      interface{} // Unique identifier for the task batch
	TaskName    interface{} // User-defined name for the task
	Amount      interface{} // Amount per transaction (if not is_all_amount)
	IsAllAmount interface{} // Flag indicating if the full balance should be sent
	TotalAmount interface{} // Calculated total amount for the task
	TotalFee    interface{} // Calculated total fee for the task
	TaskStatus  interface{} // Overall status of the task batch
	CreateAt    *gtime.Time //
	UpdateAt    *gtime.Time //
	GasLimit    interface{} // Gas limit for transactions
	GasPrice    interface{} // Gas price (e.g., in Gwei)
	FromAddress interface{} // Source address for single transfers/collections
	ToAddress   interface{} // Destination address for single transfers/collections
}
