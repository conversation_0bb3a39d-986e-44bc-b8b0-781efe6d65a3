// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SystemInitStatus is the golang structure of table system_init_status for DAO operations like Where/Data.
type SystemInitStatus struct {
	g.Meta        `orm:"table:system_init_status, do:true"`
	Id            interface{} // Primary Key
	IsInitialized interface{} // Is the system initialized (0=false, 1=true)
	PasswordHash  interface{} // Hashed password for startup (bcrypt)
	Salt          interface{} // Salt for password hashing (kept for potential future use, bcrypt includes salt in hash)
	CreatedAt     *gtime.Time // Creation Time
	UpdatedAt     *gtime.Time // Update Time
}
