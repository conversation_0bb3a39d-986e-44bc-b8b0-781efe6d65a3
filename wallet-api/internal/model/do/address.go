// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Address is the golang structure of table address for DAO operations like Where/Data.
type Address struct {
	g.Meta           `orm:"table:address, do:true"`
	Id               interface{} //
	WalletId         interface{} // 钱包id
	Address          interface{} // 地址
	Type             interface{} // 类型
	Label            interface{} // 标签
	BindStatus       interface{} // 绑定状态 (0 = default)
	BindAt           *gtime.Time // 绑定时间
	LastQueryAt      *gtime.Time // 最近查询时间
	Status           interface{} // 状态 (0 = default)
	ChainCoinBalance interface{} // chain coin 余额
	ChainUsdtBalance interface{} // chain usdt 余额
	CreateAt         *gtime.Time // 创建时间
	UpdateAt         *gtime.Time // 更新时间
	Path             interface{} // 路径
	Alias            interface{} //
	MerchantId       interface{} //
	PrivateKey       interface{} //
}
