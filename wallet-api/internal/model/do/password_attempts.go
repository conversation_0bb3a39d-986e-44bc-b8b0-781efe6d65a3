// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PasswordAttempts is the golang structure of table password_attempts for DAO operations like Where/Data.
type PasswordAttempts struct {
	g.Meta       `orm:"table:password_attempts, do:true"`
	Id           interface{} //
	AttemptCount interface{} //
	LastAttempt  *gtime.Time //
}
