// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Transactions is the golang structure of table transactions for DAO operations like Where/Data.
type Transactions struct {
	g.Meta            `orm:"table:transactions, do:true"`
	Id                interface{} //
	TransactionStatus interface{} // e.g., pending, confirmed, failed
	Chain             interface{} // Blockchain network (e.g., ETH, TRX)
	IsToken           interface{} // Is this a token transaction (TRUE) or native coin (FALSE)?
	ContractAddress   interface{} // Token contract address if is_token is TRUE
	TokenName         interface{} // Token name (e.g., Tether)
	SenderAddress     interface{} // Sender address
	ReceiverAddress   interface{} // Receiver address
	TransactionTime   *gtime.Time // Timestamp of the transaction
	Amount            interface{} // Transaction amount as string to handle large numbers
	TransactionType   interface{} // e.g., transfer, contract call
	TransactionFee    interface{} // Transaction fee as string
	Status            interface{} // Internal status flag (e.g., 0=new, 1=processed)
	BlockNumber       interface{} // Block number containing the transaction
	BlockHash         interface{} // Hash of the block containing the transaction
	Confirmations     interface{} // Number of block confirmations
	Notes             interface{} // Additional notes about the transaction
	CreateAt          *gtime.Time //
	UpdateAt          *gtime.Time //
	TransactionHash   interface{} // Unique hash of the transaction
	NetFee            interface{} // Network fee component (e.g., Tron)
	EnergyFee         interface{} // Energy fee component (e.g., Tron)
	EffectiveGasPrice interface{} // Effective gas price paid (e.g., ETH EIP-1559)
	GasUsed           interface{} // Amount of gas consumed
	CumulativeGasUsed interface{} // Cumulative gas used in the block
	TokenSymbol       interface{} // Token symbol (e.g., USDT)
}
