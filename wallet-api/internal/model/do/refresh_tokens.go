// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RefreshTokens is the golang structure of table refresh_tokens for DAO operations like Where/Data.
type RefreshTokens struct {
	g.Meta    `orm:"table:refresh_tokens, do:true"`
	Id        interface{} //
	UserId    interface{} // Associated user/wallet ID
	Jti       interface{} // JWT ID, unique identifier for the refresh token
	ExpiresAt *gtime.Time // Expiry timestamp of the refresh token
	IsRevoked interface{} // Flag indicating if the token has been revoked
	CreatedAt *gtime.Time // Timestamp of creation
	UpdatedAt *gtime.Time // Timestamp of last update
}
