// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Wallets is the golang structure of table wallets for DAO operations like Where/Data.
type Wallets struct {
	g.Meta                    `orm:"table:wallets, do:true"`
	Id                        interface{} //
	Mnemonic                  interface{} // BIP39 Mnemonic phrase (should be encrypted)
	Password                  interface{} // Password for UI access (consider hashing)
	PasswordHash              interface{} // Hashed password for secure storage
	CollectAddress            interface{} // Default collection address (maybe deprecated by chain-specific ones)
	GoogleCode                interface{} // Google Authenticator secret key (should be encrypted)
	MinerPrivateKey           interface{} // Private key for mining/staking (handle with extreme care, encrypt)
	MinerAddress              interface{} // Address associated with the miner private key
	StrategyCollectSwitch     interface{} // Enable/disable strategic collection
	EthCollectThreshold       interface{} // Threshold for ETH collection
	TrxCollectThreshold       interface{} // Threshold for TRX collection
	UsdtCollectThreshold      interface{} // Threshold for USDT collection
	CronCollectSwitch         interface{} // Enable/disable scheduled collection via cron
	CronCollectTime           interface{} // Cron schedule time (e.g., 0 2 * * *)
	CreateAt                  *gtime.Time //
	UpdateAt                  *gtime.Time //
	LastUnlockAt              *gtime.Time // Timestamp of the last successful unlock
	UnlockErrorCount          interface{} // Count of consecutive unlock failures
	GoogleCodeSwitch          interface{} // Enable/disable Google Authenticator requirement
	TrxCollectAddress         interface{} // Specific collection address for TRX chain
	EthCollectAddress         interface{} // Specific collection address for ETH chain
	TrxFeePrivateKey          interface{} // Private key for paying TRX fees (handle with care, encrypt)
	EthFeePrivateKey          interface{} // Private key for paying ETH fees (handle with care, encrypt)
	TrxFeeAddress             interface{} // Address holding funds for TRX fees
	TrxActivateAmount         interface{} // Address holding funds for TRX fees
	EthFeeAddress             interface{} // Address holding funds for ETH fees
	EthFeeMode                interface{} // eth 矿工费模式 1 自动 2手动
	EthFeeAmount              interface{} // eth固定矿工费发送金额
	TrxFeeAmount              interface{} // trx 固定矿工费发送金额
	Trc20TriggerFeeAmount     interface{} // trx 固定矿工费发送金额
	EthFeeMax                 interface{} // eth 最大矿工费
	Erc20FeeMax               interface{} // eth 最大矿工费
	EthGasPrice               interface{} //
	EthGasLimit               interface{} //
	Erc20GasPrice             interface{} //
	Erc20GasLimit             interface{} //
	TrxFeeMode                interface{} // eth 矿工费模式 1 自动 2手动
	TrxFeeMax                 interface{} // trx 交易最多支付手续费
	TrxKeepAmount             interface{} // 归集保持账户内最低trx 余额 防止交易失败
	EthKeepAmount             interface{} // eth 归集的时候 预留金额
	Trc20MinRequiredEnergy    interface{} // 转账usdt最低需要的能量 手动模式设置
	Trc20MaxEnergyFee         interface{} // 最大允许消耗的 trx 能量费金额 此处用于购买trx 能量的费用设置
	Trc20MinRequiredBandwidth interface{} // 最低带宽
	MnemonicSalt              []byte      //
	MnemonicIterations        interface{} //
	GoogleSecretSalt          []byte      //
	GoogleSecretIterations    interface{} //
	EthMinTakeAmount          interface{} // eth最小充值金额
	TrxMinTakeAmount          interface{} // trx最小充值金额
	UsdtMinTakeAmount         interface{} // usdt 最小充值金额
}
