// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WithdrawPlan is the golang structure of table withdraw_plan for DAO operations like Where/Data.
type WithdrawPlan struct {
	g.Meta         `orm:"table:withdraw_plan, do:true"`
	WithdrawPlanId interface{} // 主键ID
	Chan           interface{} // 链
	Address        interface{} // 地址
	State          interface{} // 状态: 1-待确认, 2-完成
	ErrorMessage   interface{} // 失败或错误信息
	CreatedAt      *gtime.Time // 创建时间
	UpdatedAt      *gtime.Time // 最后更新时间
	RetryCount     interface{} //
}
