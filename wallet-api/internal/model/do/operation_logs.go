// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// OperationLogs is the golang structure of table operation_logs for DAO operations like Where/Data.
type OperationLogs struct {
	g.Meta      `orm:"table:operation_logs, do:true"`
	Id          interface{} //
	Logtime     *gtime.Time // logtime
	Logtype     interface{} // logtype
	Logcontent  interface{} // logcontent
	Logstatus   interface{} // 日志状态
	ErrorInfo   interface{} // 错误信息
	CorrectInfo interface{} // 正确信息
	CreateAt    *gtime.Time // 创建时间
	UpdateAt    *gtime.Time // 更新时间
}
