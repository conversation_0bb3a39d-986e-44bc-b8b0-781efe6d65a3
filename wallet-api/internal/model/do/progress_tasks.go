// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ProgressTasks is the golang structure of table progress_tasks for DAO operations like Where/Data.
type ProgressTasks struct {
	g.Meta        `orm:"table:progress_tasks, do:true"`
	TaskId        interface{} //
	Status        interface{} //
	Progress      interface{} //
	ProcessedRows interface{} //
	TotalRows     interface{} //
	ErrorMessage  interface{} //
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
}
