// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserWithdraws is the golang structure of table user_withdraws for DAO operations like Where/Data.
type UserWithdraws struct {
	g.Meta               `orm:"table:user_withdraws, do:true"`
	UserWithdrawsId      interface{} // 主键ID
	TokenFeeSupplementId interface{} //
	Name                 interface{} // 币种ID
	Chan                 interface{} // 链
	FromAddress          interface{} // 提币目标地址
	ToAddress            interface{} // 提币目标地址
	Amount               interface{} // 申请提现金额
	HandlingFee          interface{} // 提现手续费
	ActualAmount         interface{} // 实际到账金额
	State                interface{} // 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)
	TxHash               interface{} // 链上交易哈希/ID
	ErrorMessage         interface{} // 失败或错误信息
	CreatedAt            *gtime.Time // 创建时间
	CheckedAt            *gtime.Time // 审核时间 (审核通过或拒绝的时间)
	ProcessingAt         *gtime.Time // 开始处理时间 (进入“处理中”状态的时间)
	CompletedAt          *gtime.Time // 完成时间 (变为“已完成”或“失败”状态的时间)
	UpdatedAt            *gtime.Time // 最后更新时间
	Retries              interface{} //
	NergyState           interface{} // 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	GasfeeState          interface{} // gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	EthFeeMode           interface{} // eth 矿工费模式 1 自动 2手动
	EthFeeMax            interface{} // eth 最大矿工费
	EthGasPrice          interface{} //
	EthGasLimit          interface{} //
	TrxFeeMax            interface{} //
	RechargesId          interface{} // 充值订单id
	GasfeeHash           interface{} // gas费hash
	GasfeeAmount         interface{} // gas费金额
	EnergyMsg            interface{} //
	CanActAt             *gtime.Time // 完成时间 (变为“已完成”或“失败”状态的时间)
}
