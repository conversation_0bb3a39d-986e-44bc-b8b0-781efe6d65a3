// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskAddress is the golang structure of table task_address for DAO operations like Where/Data.
type TaskAddress struct {
	g.Meta          `orm:"table:task_address, do:true"`
	Id              interface{} //
	TaskId          interface{} // Associated task ID
	SenderAddress   interface{} // Sender address
	ReceiverAddress interface{} // Receiver address
	Amount          interface{} // Amount for this specific address pair
	Fee             interface{} // Fee for this transaction
	Network         interface{} // Network identifier
	Status          interface{} // Status of this address task (e.g., pending, success, failed)
	FailReason      interface{} // Reason for failure, if any
	TransactionHash interface{} // Resulting transaction hash
	CreateAt        *gtime.Time //
	UpdateAt        *gtime.Time //
}
