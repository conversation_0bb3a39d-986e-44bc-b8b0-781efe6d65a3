// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RpcRequestLogs is the golang structure of table rpc_request_logs for DAO operations like Where/Data.
type RpcRequestLogs struct {
	g.Meta           `orm:"table:rpc_request_logs, do:true"`
	Id               interface{} //
	RequestTime      *gtime.Time // 请求时间
	RequestType      interface{} // 请求类型
	RequestMethod    interface{} // 请求方法
	Address          interface{} // 请求address
	Coin             interface{} // coin
	RequestParams    interface{} // 请求参数
	RequestUrl       interface{} // 请求完整地址
	RequestStatus    interface{} // 请求状态
	RequestResult    interface{} // 请求结果
	RequestErrorInfo interface{} // 请求错误信息
	CreateAt         *gtime.Time // 创建时间
	UpdateAt         *gtime.Time // 更新时间
}
