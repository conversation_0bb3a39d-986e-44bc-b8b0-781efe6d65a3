// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TokenFeeSupplements is the golang structure of table token_fee_supplements for DAO operations like Where/Data.
type TokenFeeSupplements struct {
	g.Meta               `orm:"table:token_fee_supplements, do:true"`
	TokenFeeSupplementId interface{} // 主键ID
	WithdrawPlanId       interface{} // 计划订单id
	UserWithdrawId       interface{} // 计划订单id
	Address              interface{} // 需要补充费用的地址
	ChainType            interface{} // 链类型 (例如: ERC20, TRC20)
	TokenSymbol          interface{} // 代币符号 (例如: USDT)
	FeeType              interface{} // 费用类型 (例如: gas_fee, energy)
	RequiredAmount       interface{} // 需要的费用数量 (原生代币单位)
	ProvidedAmount       interface{} // 已补充的费用数量 (原生代币单位)
	EnergyAmount         interface{} // 补充能量数量 trc20 专用
	EnergyFee            interface{} // 补充能量数量 trc20 专用
	Status               interface{} // 状态 (pending, processing, success, failed, partial_success)
	TransactionHash      interface{} // 补充费用的交易哈希
	ErrorMessage         interface{} // 错误信息
	RelatedTaskId        interface{} // 关联的归集任务ID
	RetryCount           interface{} // 重试次数
	CreatedAt            *gtime.Time // 创建时间
	UpdatedAt            *gtime.Time // 更新时间
	EnergyId             interface{} //
	IsActivating         interface{} // 是否激活 0 未处理1  激活中 2 已激活
	ActivateHash         interface{} // 激活hash
	ActivateAmount       interface{} // 激活消耗trx
	TrxSupplementNeeded  interface{} // TRX补充是否需要 0-不需要 1-需要 2 补充中 3 成功 4 失败
	TrxSupplementStatus  interface{} // TRX补充状态 pending-待处理 processing-处理中 success-成功 failed-失败
	TrxSupplementHash    interface{} // TRX补充交易哈希
	TrxSupplementAmount  interface{} // TRX补充数量
	TrxBalanceBefore     interface{} // 补充前TRX余额
	TrxBalanceAfter      interface{} // 补充后TRX余额
}
