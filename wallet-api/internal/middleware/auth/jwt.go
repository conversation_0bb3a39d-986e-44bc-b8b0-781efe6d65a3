package auth

import (
	"strings"
	"wallet-api/internal/service" // 确保 service 包已正确实现 ParseUserIDFromAccessToken

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// UserIDCtxKey 是用于在请求上下文中存储用户ID的键
const UserIDCtxKey = "UserID"

// JWTAuthMiddleware JWT认证中间件
func JWTAuthMiddleware(r *ghttp.Request) {
	g.Log().Debugf(r.Context(), "JWTAuthMiddleware: Entering. Path = %s, Headers = %v", r.URL.Path, r.Header)
	// 不需要验证的路径
	excludePaths := []string{
		"/wallet/init-status",          // 检测钱包是否初始化
		"/wallet/status",               // 检测钱包解锁状态是否过期 (原为 /wallet/status)
		"/wallet/generate",             // 生成钱包 助记词
		"/wallet/create",               // 创建钱包
		"/wallet/generate-google-code", // 生成谷歌验证码密钥以及二维码
		"/wallet/auth",                 // 钱包认证
		"/node/get-gastracker",         // 获取gastracker
	}

	// r.Response.WriteJsonExit(g.Map{
	// 	"code":    gcode.CodeNotAuthorized.Code(),
	// 	"message": "无效或已过期的认证令牌: ",
	// })
	// return
	//todo 上线放开 - 已放开
	// r.Middleware.Next() // 注释掉这个过早的调用
	// // return
	// 检查是否是排除的路径
	for _, path := range excludePaths {
		if strings.HasPrefix(r.URL.Path, path) {
			r.Middleware.Next()
			return
		}
	}

	// 获取Authorization头
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		r.Response.WriteJsonExit(g.Map{
			"code":    gcode.CodeNotAuthorized.Code(),
			"message": "未提供认证令牌",
		})
		return
	}

	// 提取token
	parts := strings.SplitN(authHeader, " ", 2)
	if !(len(parts) == 2 && parts[0] == "Bearer") {
		r.Response.WriteJsonExit(g.Map{
			"code":    gcode.CodeNotAuthorized.Code(),
			"message": "认证令牌格式错误",
		})
		return
	}
	tokenString := parts[1]

	// --- 新的 Access Token 验证逻辑 ---
	// 调用认证服务解析 Access Token 获取用户ID
	userID, err := service.Auth().ParseUserIDFromAccessToken(r.Context(), tokenString)
	if err != nil {
		// 根据任务指示，可以暴露 err.Error()
		// service.Auth().ParseUserIDFromAccessToken 应确保返回的错误信息适合暴露或提供足够上下文
		r.Response.WriteJsonExit(g.Map{
			"code":    gcode.CodeNotAuthorized.Code(),
			"message": "无效或已过期的认证令牌: " + err.Error(),
		})
		return
	}

	// 确保 userID 有效。如果 service.Auth().ParseUserIDFromAccessToken 逻辑正确，
	// 成功的err为nil时，userID不应为空。
	if userID == 0 { // userID 是 uint 类型，所以与 0 比较
		// 这是一个异常情况，可能表示 service.Auth().ParseUserIDFromAccessToken 的实现有潜在问题
		// 或者 token 解析成功但未包含有效的 userID (尽管这在正常逻辑中不太可能发生，除非 ParseUserIDFromAccessToken 允许)
		g.Log().Errorf(r.Context(), "JWTAuthMiddleware: UserID is 0 after successful token parsing for token string (first 10 chars): %.10s...", tokenString)
		r.Response.WriteJsonExit(g.Map{
			"code":    gcode.CodeNotAuthorized.Code(),
			"message": "认证令牌无效，无法解析用户信息",
		})
		return
	}

	// 将 userID 存储到请求上下文中
	r.SetCtxVar(UserIDCtxKey, userID)
	// 可选：记录日志，确认用户信息已存储
	g.Log().Debugf(r.Context(), "JWTAuthMiddleware: UserID '%d' stored in context.", userID) // userID 是 uint，使用 %d

	// 继续处理请求
	r.Middleware.Next()
}
