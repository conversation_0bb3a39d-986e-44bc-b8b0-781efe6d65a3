package auth

import (
	"strings"

	"wallet-api/internal/dao"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// WalletValidationMiddleware 钱包验证中间件
// 验证钱包是否存在，如果不存在则返回错误
func WalletValidationMiddleware(r *ghttp.Request) {
	// 不需要验证的路径
	excludePaths := []string{
		"/wallet/init-status",          // 检测钱包是否初始化
		"/wallet/status",               // 检测钱包解锁状态是否过期
		"/wallet/generate",             // 生成钱包 助记词
		"/wallet/create",               // 创建钱包
		"/wallet/generate-google-code", // 生成谷歌验证码密钥以及二维码
		"/wallet/auth",                 // 钱包认证
		"/node/get-gastracker",         // 获取gastracker
		"/wallet/withdraw-plan-list",   // 获取归集计划列表 (临时添加，用于测试)
	}

	// 检查是否是排除的路径
	for _, path := range excludePaths {
		if strings.HasPrefix(r.URL.Path, path) {
			r.Middleware.Next()
			return
		}
	}

	// 获取上下文
	ctx := r.Context()

	// 查找钱包
	wallet, err := dao.Wallets.Ctx(ctx).One()
	g.Log().Debug(ctx, wallet, err)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code":    gcode.CodeInternalError.Code(),
			"message": "Error finding wallet: " + err.Error(),
		})
		return
	}

	// 检查钱包是否存在
	if wallet == nil {
		r.Response.WriteJsonExit(g.Map{
			"code":    gcode.CodeInvalidParameter.Code(),
			"message": "Wallet not found",
		})
		return
	}

	// 钱包存在，继续处理请求
	r.Middleware.Next()
}
