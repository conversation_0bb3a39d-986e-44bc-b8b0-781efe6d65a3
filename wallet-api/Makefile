ROOT_DIR    = $(shell pwd)
NAMESPACE   = "default"
DEPLOY_NAME = "template-single"
DOCKER_NAME = "template-single"

include ./hack/hack-cli.mk
include ./hack/hack.mk

# --- Go Application Build ---

# 输出目录 (如果未定义，则默认为 bin)
OUTPUT_DIR ?= bin
# Go 编译器 (如果未定义，则默认为 go)
GO ?= go
# Go 构建标志 (如果未定义，则默认为 -s -w)
LDFLAGS ?= "-s -w"

# 目标可执行文件名称 (可以被外部覆盖)
HTTP_APP_NAME ?= app-http
GRPC_APP_NAME ?= app-grpc
TASK_APP_NAME ?= app-task

# 目标可执行文件完整路径
HTTP_BINARY = $(OUTPUT_DIR)/$(HTTP_APP_NAME)
GRPC_BINARY = $(OUTPUT_DIR)/$(GRPC_APP_NAME)
TASK_BINARY = $(OUTPUT_DIR)/$(TASK_APP_NAME)

# 查找所有 Go 源文件 (排除 vendor 目录)
# 使用 find 命令以支持大量文件
GO_FILES := $(shell find . -type f -name '*.go' -not -path "./vendor/*")

# --- Targets ---

# 默认目标：构建所有程序 (仅当 'all' 目标未在 hack 文件中定义时)
ifndef all
all: build
endif

# 构建所有程序 (仅当 'build' 目标未在 hack 文件中定义时)
ifndef build
build: $(HTTP_BINARY) $(GRPC_BINARY) $(TASK_BINARY)
	@echo "All binaries built successfully in $(OUTPUT_DIR)/"
endif

# 构建 http 程序
# 依赖所有 Go 文件, go.mod, go.sum，以及输出目录的创建
$(HTTP_BINARY): $(GO_FILES) go.mod go.sum | $(OUTPUT_DIR)_create
	@echo "Building $(HTTP_APP_NAME)..."
	$(GO) build -ldflags=$(LDFLAGS) -o $(HTTP_BINARY) main.go

# 构建 grpc 程序
$(GRPC_BINARY): $(GO_FILES) go.mod go.sum | $(OUTPUT_DIR)_create
	@echo "Building $(GRPC_APP_NAME)..."
	$(GO) build -ldflags=$(LDFLAGS) -o $(GRPC_BINARY) main.go

# 构建 task 程序
$(TASK_BINARY): $(GO_FILES) go.mod go.sum | $(OUTPUT_DIR)_create
	@echo "Building $(TASK_APP_NAME)..."
	$(GO) build -ldflags=$(LDFLAGS) -o $(TASK_BINARY) main.go

# Order-only prerequisite: 创建输出目录，确保只执行一次
$(OUTPUT_DIR)_create:
	@mkdir -p $(OUTPUT_DIR)

# 清理构建产物 (仅当 'clean' 目标未在 hack 文件中定义时)
ifndef clean
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(OUTPUT_DIR)
endif

# --- Phony Targets for Individual Builds ---

# 单独构建 http 程序
http: $(HTTP_BINARY)
	@echo "$(HTTP_APP_NAME) built successfully."

# 单独构建 grpc 程序
grpc: $(GRPC_BINARY)
	@echo "$(GRPC_APP_NAME) built successfully."

# 单独构建 task 程序
task: $(TASK_BINARY)
	@echo "$(TASK_APP_NAME) built successfully."

# --- Docker Targets ---

# Docker image name
DOCKER_IMAGE_NAME ?= wallet-api
DOCKER_TAG ?= latest

# Build Docker image
docker-build:
	@echo "Building Docker image $(DOCKER_IMAGE_NAME):$(DOCKER_TAG)..."
	docker build -t $(DOCKER_IMAGE_NAME):$(DOCKER_TAG) .

# Run HTTP service with docker-compose
docker-up:
	@echo "Starting services with docker-compose..."
	docker-compose up -d

# Stop docker-compose services
docker-down:
	@echo "Stopping services with docker-compose..."
	docker-compose down

# View logs from all services
docker-logs:
	docker-compose logs -f

# View logs from HTTP service only
docker-logs-http:
	docker-compose logs -f wallet-api-http

# View logs from Task service only
docker-logs-task:
	docker-compose logs -f wallet-api-task

# Restart services
docker-restart:
	docker-compose restart

# Clean up Docker resources
docker-clean:
	@echo "Cleaning up Docker resources..."
	docker-compose down -v
	docker system prune -f

# Build and run services
docker-run: docker-build docker-up

# Show service status
docker-status:
	docker-compose ps

# Execute shell in HTTP container
docker-shell-http:
	docker-compose exec wallet-api-http /bin/bash

# Execute shell in Task container
docker-shell-task:
	docker-compose exec wallet-api-task /bin/bash

# 确保所有自定义目标和可能冲突的目标都被声明为 .PHONY
# 这可以防止与同名文件/目录产生冲突
.PHONY: all build clean http grpc task $(OUTPUT_DIR)_create \
        docker-build docker-up docker-down docker-logs docker-logs-http docker-logs-task \
        docker-restart docker-clean docker-run docker-status docker-shell-http docker-shell-task
