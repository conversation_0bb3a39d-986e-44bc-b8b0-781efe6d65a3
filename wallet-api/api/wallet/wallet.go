// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package wallet

import (
	"context"

	"wallet-api/api/wallet/v1"
)

type IWalletV1 interface {
	BatchCreateAddress(ctx context.Context, req *v1.BatchCreateAddressReq) (res *v1.BatchCreateAddressRes, err error)
	GetAddressTaskProgress(ctx context.Context, req *v1.GetAddressTaskProgressReq) (res *v1.GetAddressTaskProgressRes, err error)
	GetAddressList(ctx context.Context, req *v1.GetAddressListReq) (res *v1.GetAddressListRes, err error)
	GetAddressStatistic(ctx context.Context, req *v1.GetAddressStatisticReq) (res *v1.GetAddressStatisticRes, err error)
	ExportAddress(ctx context.Context, req *v1.ExportAddressReq) (res *v1.ExportAddressRes, err error)
	RefreshAddress(ctx context.Context, req *v1.RefreshAddressReq) (res *v1.RefreshAddressRes, err error)
	BindAddress(ctx context.Context, req *v1.BindAddressReq) (res *v1.BindAddressRes, err error)
	GetTransactionRecord(ctx context.Context, req *v1.GetTransactionRecordReq) (res *v1.GetTransactionRecordRes, err error)
	GetTransactionRecordStatistic(ctx context.Context, req *v1.GetTransactionRecordStatisticReq) (res *v1.GetTransactionRecordStatisticRes, err error)
	ExportTransactionRecord(ctx context.Context, req *v1.ExportTransactionRecordReq) (res *v1.ExportTransactionRecordRes, err error)
	Auth(ctx context.Context, req *v1.AuthReq) (res *v1.AuthRes, err error)
	ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error)
	GenerateGoogleCode(ctx context.Context, req *v1.GenerateGoogleCodeReq) (res *v1.GenerateGoogleCodeRes, err error)
	ReBindGoogleCode(ctx context.Context, req *v1.ReBindGoogleCodeReq) (res *v1.ReBindGoogleCodeRes, err error)
	RefreshToken(ctx context.Context, req *v1.RefreshTokenReq) (res *v1.RefreshTokenRes, err error)
	Logout(ctx context.Context, req *v1.LogoutReq) (res *v1.LogoutRes, err error)
	GetFeeAddress(ctx context.Context, req *v1.GetFeeAddressReq) (res *v1.GetFeeAddressRes, err error)
	GetCollectAddress(ctx context.Context, req *v1.GetCollectAddressReq) (res *v1.GetCollectAddressRes, err error)
	SubmitCollectTask(ctx context.Context, req *v1.SubmitCollectTaskReq) (res *v1.SubmitCollectTaskRes, err error)
	CollectTaskList(ctx context.Context, req *v1.CollectTaskListReq) (res *v1.CollectTaskListRes, err error)
	CollectRecordStatistic(ctx context.Context, req *v1.CollectRecordStatisticReq) (res *v1.CollectRecordStatisticRes, err error)
	ExportTaskRecord(ctx context.Context, req *v1.ExportTaskRecordReq) (res *v1.ExportTaskRecordRes, err error)
	TaskAddressRecord(ctx context.Context, req *v1.TaskAddressRecordReq) (res *v1.TaskAddressRecordRes, err error)
	ExportTaskAddressRecord(ctx context.Context, req *v1.ExportTaskAddressRecordReq) (res *v1.ExportTaskAddressRecordRes, err error)
	TaskAddressStatistic(ctx context.Context, req *v1.TaskAddressStatisticReq) (res *v1.TaskAddressStatisticRes, err error)
	GetTokenFeeSupplements(ctx context.Context, req *v1.GetTokenFeeSupplementsReq) (res *v1.GetTokenFeeSupplementsRes, err error)
	UpdateTokenFeeSupplementStatus(ctx context.Context, req *v1.UpdateTokenFeeSupplementStatusReq) (res *v1.UpdateTokenFeeSupplementStatusRes, err error)
	GetFeeStatistics(ctx context.Context, req *v1.GetFeeStatisticsReq) (res *v1.GetFeeStatisticsRes, err error)
	GetGastracker(ctx context.Context, req *v1.GetGastrackerReq) (res *v1.GetGastrackerRes, err error)
	GetTokenBalance(ctx context.Context, req *v1.GetTokenBalanceReq) (res *v1.GetTokenBalanceRes, err error)
	GetPublicWalletInitStatus(ctx context.Context, req *v1.GetPublicWalletInitStatusReq) (res *v1.GetPublicWalletInitStatusRes, err error)
	GetRechargeRecord(ctx context.Context, req *v1.GetRechargeRecordReq) (res *v1.GetRechargeRecordRes, err error)
	ExportRechargeRecord(ctx context.Context, req *v1.ExportRechargeRecordReq) (res *v1.ExportRechargeRecordRes, err error)
	GetRechargeRecordStatistic(ctx context.Context, req *v1.GetRechargeRecordStatisticReq) (res *v1.GetRechargeRecordStatisticRes, err error)
	WalletSetting(ctx context.Context, req *v1.WalletSettingReq) (res *v1.WalletSettingRes, err error)
	BaseSetting(ctx context.Context, req *v1.BaseSettingReq) (res *v1.BaseSettingRes, err error)
	CheckStatus(ctx context.Context, req *v1.CheckStatusReq) (res *v1.CheckStatusRes, err error)
	GenerateWallet(ctx context.Context, req *v1.GenerateWalletReq) (res *v1.GenerateWalletRes, err error)
	ResetWallet(ctx context.Context, req *v1.ResetWalletReq) (res *v1.ResetWalletRes, err error)
	GetWalletInfo(ctx context.Context, req *v1.GetWalletInfoReq) (res *v1.GetWalletInfoRes, err error)
	CreateWallet(ctx context.Context, req *v1.CreateWalletReq) (res *v1.CreateWalletRes, err error)
	DashboardStatistic(ctx context.Context, req *v1.DashboardStatisticReq) (res *v1.DashboardStatisticRes, err error)
	GetWithdrawRecord(ctx context.Context, req *v1.GetWithdrawRecordReq) (res *v1.GetWithdrawRecordRes, err error)
	ExportWithdrawRecord(ctx context.Context, req *v1.ExportWithdrawRecordReq) (res *v1.ExportWithdrawRecordRes, err error)
	GetWithdrawRecordStatistic(ctx context.Context, req *v1.GetWithdrawRecordStatisticReq) (res *v1.GetWithdrawRecordStatisticRes, err error)
	CreateWithdraw(ctx context.Context, req *v1.CreateWithdrawReq) (res *v1.CreateWithdrawRes, err error)
	AddAddressToWithdrawPlan(ctx context.Context, req *v1.AddAddressToWithdrawPlanReq) (res *v1.AddAddressToWithdrawPlanRes, err error)
	GetWithdrawPlanList(ctx context.Context, req *v1.GetWithdrawPlanListReq) (res *v1.GetWithdrawPlanListRes, err error)
}
