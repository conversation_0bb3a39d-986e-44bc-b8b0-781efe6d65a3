package v1

import "github.com/gogf/gf/v2/frame/g"

// AuthReq 认证请求
type AuthReq struct {
	g.Meta     `path:"/wallet/auth" method:"post" tags:"auth" summary:"钱包认证"`
	Password   string `json:"password" v:"required#请输入密码" dc:"密码"`
	GoogleCode string `json:"google_code" v:"required#请输入谷歌验证码,length:6#格式错误,numeric#谷歌验证码必须是6位数字" dc:"谷歌验证码"`
}

// AuthRes 认证响应
type AuthRes struct {
	AccessToken  string `json:"accessToken" dc:"Access Token"`
	RefreshToken string `json:"refreshToken" dc:"Refresh Token"`
}

// 修改密码 此接口需要先认证
type ChangePasswordReq struct {
	g.Meta      `path:"/wallet/change-password" method:"post" tags:"auth" summary:"修改密码" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	OldPassword string `json:"old_password" v:"required#请输入旧密码,password#密码格式错误" dc:"旧密码"`
	NewPassword string `json:"new_password" v:"required#请输入新密码,password#密码格式错误" dc:"新密码"`
	//重复密码
	RepeatPassword string `json:"repeat_password" v:"required#请输入重复密码,password#密码格式错误" dc:"重复密码"`
	GoogleCode     string `json:"google_code" v:"required#请输入谷歌验证码,length:6#格式错误,numeric#谷歌验证码必须是6位数字" dc:"谷歌验证码"`
}

// ChangePasswordRes 修改密码响应
type ChangePasswordRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 生成google验证码密钥以及二维码
type GenerateGoogleCodeReq struct {
	g.Meta `path:"/wallet/generate-google-code" method:"get"  tags:"auth" summary:"生成google验证码密钥以及二维码" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// GenerateGoogleCodeRes 生成google验证码密钥以及二维码响应
type GenerateGoogleCodeRes struct {
	Secret string `json:"secret" dc:"密钥"`
	QrCode string `json:"qr_code" dc:"二维码图片（Base64）"`
}

// 绑定google验证码
type ReBindGoogleCodeReq struct {
	g.Meta    `path:"/wallet/rebind-google-code" method:"post" tags:"auth" summary:"绑定google验证码" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Code      string `json:"code" v:"required#请输入谷歌验证码" dc:"谷歌验证码"`
	NewSecret string `json:"new_secret" v:"required#请输入新谷歌验证码密钥" dc:"新谷歌验证码密钥"`
	NewCode   string `json:"new_code" v:"required#请输入新谷歌验证码" dc:"新谷歌验证码"`
	Password  string `json:"password" v:"required#请输入密码" dc:"密码"`
}

// ReBindGoogleCodeRes 绑定google验证码响应
type ReBindGoogleCodeRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// RefreshTokenReq defines the request body structure for refreshing a token.
type RefreshTokenReq struct {
	g.Meta       `path:"/wallet/refresh" method:"post" tags:"auth" summary:"Refresh token"`
	RefreshToken string `json:"refreshToken" v:"required#refreshToken不能为空" dc:"Refresh Token"`
}

// RefreshTokenRes defines the response body structure for refreshing a token.
type RefreshTokenRes struct {
	AccessToken  string `json:"accessToken" dc:"New Access Token"`
	RefreshToken string `json:"refreshToken" dc:"New Refresh Token"`
}

// LogoutReq defines the request body structure for logging out.
type LogoutReq struct {
	g.Meta       `path:"/wallet/logout" method:"post" tags:"auth" summary:"Logout"`
	RefreshToken string `json:"refreshToken" v:"required#refreshToken不能为空" dc:"Refresh Token to be invalidated"`
}

type LogoutRes struct {
	// No specific data fields needed for logout success.
}
