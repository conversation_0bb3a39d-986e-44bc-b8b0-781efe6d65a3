package v1

import "github.com/gogf/gf/v2/frame/g"

// 获取矿工费地址
type GetFeeAddressReq struct {
	g.Meta `path:"/wallet/get-fee-address" method:"get" tags:"collect" summary:"获取矿工费地址" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// GetFeeAddressRes 获取矿工费地址响应
type GetFeeAddressRes struct {
	EthFeeAddress string `json:"eth_fee_address" dc:"eth矿工费地址"`
	TrxFeeAddress string `json:"trx_fee_address" dc:"trx矿工费地址"`
}

// 获取归集地址
type GetCollectAddressReq struct {
	g.Meta `path:"/wallet/get-collect-address" method:"get" tags:"collect" summary:"获取归集地址" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// GetCollectAddressRes 获取归集地址响应
type GetCollectAddressRes struct {
	EthCollectAddress string `json:"eth_collect_address" dc:"eth归集地址"`
	TrxCollectAddress string `json:"trx_collect_address" dc:"trx归集地址"`
}

type CollectTask struct {
	Type        string   `json:"type" v:"required#请输入类型|in:ETH,TRON#类型必须是ETH或TRON" dc:"链类型，支持ETH或TRON"`
	Coin        string   `json:"coin" v:"required#请输入币种|in:ETH,TRX,USDT#币种必须是ETH、TRX或USDT" dc:"币种，支持ETH、TRX或USDT"`
	TransType   string   `json:"trans_type" v:"required#请输入交易类型|in:one2many,many2one#交易类型必须是one2many或many2one" dc:"交易类型，one2many(一对多转账)或many2one(多对一归集)"`
	FromAddress []string `json:"from_address" v:"required#请输入转出地址" dc:"转出地址，归集使用 多对一 归集资金时候"`
	ToAddress   []string `json:"to_address" v:"required#请输入转入地址" dc:"转入地址，收矿工费使用 一对多发送矿工费时"`
	IsAllAmount bool     `json:"is_all_amount" v:"required#请选择是否全部金额" dc:"是否转出全部金额 如果为true，则不填写amount"`
	Amount      string   `json:"amount" v:"required-if:is_all_amount,false#当非全部金额时请输入转账金额" dc:"转账金额，仅在is_all_amount为false时必填"`
	GasLimit    string   `json:"gas_limit" dc:"矿工费限制，可选参数，默认使用系统估算值 eth 网络专用"`
	GasPrice    string   `json:"gas_price" dc:"矿工费价格，可选参数，默认使用系统估算值 eth 网络专用"`
	FeeLimit    string   `json:"fee_limit" dc:"手续费限制，可选参数，默认使用系统估算值 tron 网络专用"`
}

// 提交归集任务
type SubmitCollectTaskReq struct {
	g.Meta `path:"/wallet/submit-collect-task" method:"post" tags:"collect" summary:"提交归集任务" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	CollectTask
}

// SubmitCollectTaskRes 提交归集任务响应
type SubmitCollectTaskRes struct {
	Success bool  `json:"success" dc:"是否成功"`
	TaskID  int64 `json:"task_id,omitempty" dc:"归集任务ID，成功时返回"`
}

// 归集任务列表
type CollectTaskListReq struct {
	g.Meta `path:"/wallet/collect-task-list" method:"get" tags:"collect" summary:"归集任务列表" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TaskRecordReq
}

// TaskList 归集任务列表
type TaskList struct {
	Id                   int64  `json:"id" dc:"ID"`
	NetworkType          string `json:"network_type" dc:"网络类型，支持ETH或TRON"`
	TokenType            string `json:"token_type" dc:"币种类型，支持ETH、TRX或USDT"`
	TaskType             string `json:"task_type" dc:"任务类型，支持many2one(多对一归集)或one2many(一对多转账)"`
	ExecuteType          string `json:"execute_type" dc:"执行类型，支持manual(手动)或auto(自动)"`
	TaskName             string `json:"task_name" dc:"任务名称"`
	TaskID               int64  `json:"task_id" dc:"归集任务ID"`
	Amount               string `json:"amount" dc:"转账金额"`
	IsAllAmount          bool   `json:"is_all_amount" dc:"是否转出全部金额"`
	TotalAmount          string `json:"total_amount" dc:"总金额"`
	TotalFee             string `json:"total_fee" dc:"总手续费"`
	TaskStatus           string `json:"task_status" dc:"任务状态"`
	CreateAt             string `json:"create_at" dc:"创建时间"`
	UpdateAt             string `json:"update_at" dc:"更新时间"`
	GasLimit             string `json:"gas_limit" dc:"矿工费限制"`
	GasPrice             string `json:"gas_price" dc:"矿工费价格"`
	FromAddress          string `json:"from_address" dc:"转出地址"`
	ToAddress            string `json:"to_address" dc:"转入地址"`
	SuccessAddressCount  int    `json:"success_address_count" dc:"成功的任务地址数量"`
	FailedAddressCount   int    `json:"failed_address_count" dc:"失败的任务地址数量"`
	PendingAddressCount  int    `json:"pending_address_count" dc:"待执行的任务地址数量"`
	CanceledAddressCount int    `json:"canceled_address_count" dc:"取消的任务地址数量"`
}

// CollectTaskListRes 归集任务列表响应
type CollectTaskListRes struct {
	Page Page       `json:"page" dc:"分页信息"`
	List []TaskList `json:"list" dc:"归集任务列表"`
}

// 归集统计信息
type CollectRecordStatisticReq struct {
	g.Meta `path:"/wallet/collect-record-statistic" method:"get" tags:"collect" summary:"归集任务统计信息" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TaskRecordReq
}

// CollectRecordStatisticRes 归集记录统计信息响应
type CollectRecordStatisticRes struct {
	CompletedTaskCount  int `json:"completed_task_count" dc:"完成的任务数量"`
	ProcessingTaskCount int `json:"processing_task_count" dc:"处理中的任务数量"`
	FailedTaskCount     int `json:"failed_task_count" dc:"失败的任务数量"`
	PendingTaskCount    int `json:"pending_task_count" dc:"待执行的任务数量"`
	CanceledTaskCount   int `json:"canceled_task_count" dc:"取消的任务数量"`
}

type TaskRecordReq struct {
	Page        int    `json:"page" v:"required#请输入页码,numeric#页码必须是数字,min:1#页码不能小于1" dc:"页码"`
	Limit       int    `json:"limit" v:"required#请输入每页数量,numeric#每页数量必须是数字,min:1#每页数量不能小于1" dc:"每页数量"`
	Address     string `json:"address" dc:"地址"`
	Type        string `json:"type" dc:"类型"`
	SortField   string `json:"sort_field"  dc:"排序字段"`
	SortOrder   string `json:"sort_order" v:"in:asc,desc#排序方向必须是asc或desc" dc:"排序方向"`
	Status      string `json:"status" v:"in:completed,processing,failed,pending,canceled#状态必须是completed、processing、failed、pending或canceled" dc:"状态"`
	Coin        string `json:"coin" v:"in:ETH,TRX,USDT#币种必须是ETH、TRX或USDT" dc:"币种"`
	DateRange   string `json:"date_range" dc:"日期范围"`
	TaskType    string `json:"task_type" v:"in:many2one,one2many#任务类型必须是many2one或one2many" dc:"任务类型"`
	ExecuteType string `json:"execute_type" v:"in:manual,auto#执行类型必须是manual或auto" dc:"执行类型"`
}

// 导出任务记录
type ExportTaskRecordReq struct {
	g.Meta `path:"/wallet/export-task-record" method:"get" tags:"collect" summary:"导出任务记录" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TaskRecordReq
}

// ExportTaskRecordRes 导出任务记录响应
type ExportTaskRecordRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

type TaskAddress struct {
	TaskID    int64  `json:"task_id" v:"required#请输入归集任务ID" dc:"归集任务ID"`
	Page      int    `json:"page" v:"required#请输入页码,numeric#页码必须是数字,min:1#页码不能小于1" dc:"页码"`
	Limit     int    `json:"limit" v:"required#请输入每页数量,numeric#每页数量必须是数字,min:1#每页数量不能小于1" dc:"每页数量"`
	Address   string `json:"address" dc:"地址"`
	SortField string `json:"sort_field"  dc:"排序字段"`
	SortOrder string `json:"sort_order" v:"in:asc,desc#排序方向必须是asc或desc" dc:"排序方向"`
	Status    string `json:"status" v:"in:completed,processing,failed,pending,canceled#状态必须是completed、processing、failed、pending或canceled" dc:"状态"`
}

// 任务地址记录
type TaskAddressRecordReq struct {
	g.Meta `path:"/wallet/task-address-record" method:"get" tags:"collect" summary:"任务地址记录" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TaskAddress
}

// TaskAddressRecord 任务地址记录
type TaskAddressRecord struct {
	Id              int64    `json:"id" dc:"ID"`
	TaskID          int64    `json:"task_id" dc:"归集任务ID"`
	SenderAddress   string   `json:"sender_address" dc:"转出地址"`
	ReceiverAddress string   `json:"receiver_address" dc:"转入地址"`
	Amount          string   `json:"amount" dc:"转账金额"`
	Fee             string   `json:"fee" dc:"手续费"`
	Network         string   `json:"network" dc:"网络类型"`
	Status          string   `json:"status" dc:"状态 completed,processing,failed,pending,canceled"`
	FailReason      string   `json:"fail_reason" dc:"失败原因"`
	TransactionHash string   `json:"transaction_hash" dc:"交易哈希"`
	CreateAt        string   `json:"create_at" dc:"创建时间"`
	UpdateAt        string   `json:"update_at" dc:"更新时间"`
	TaskInfo        TaskList `json:"task_info" dc:"任务信息"`
}

// TaskAddressRecordRes 任务地址记录响应
type TaskAddressRecordRes struct {
	Page Page                `json:"page" dc:"分页信息"`
	List []TaskAddressRecord `json:"list" dc:"归集任务列表"`
}

// 导出任务地址记录
type ExportTaskAddressRecordReq struct {
	g.Meta `path:"/wallet/export-task-address-record" method:"get" tags:"collect" summary:"导出任务地址记录" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TaskAddress
}

// ExportTaskAddressRecordRes 导出任务地址记录响应
type ExportTaskAddressRecordRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 任务地址统计信息
type TaskAddressStatisticReq struct {
	g.Meta `path:"/wallet/task-address-statistic" method:"get" tags:"collect" summary:"任务地址统计信息" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TaskID int64 `json:"task_id" dc:"归集任务ID"`
}

// TaskAddressStatisticRes 任务地址统计信息响应
type TaskAddressStatisticRes struct {
	TotalAmount     string `json:"total_amount" dc:"任务总金额"`
	TotalFee        string `json:"total_fee" dc:"手续费"`
	NetworkType     string `json:"network_type" dc:"网络类型"`
	CoinType        string `json:"coin_type" dc:"代币类型"`
	TransferCount   int    `json:"transfer_count" dc:"转账总数"`
	CompletedCount  int    `json:"completed_count" dc:"已完成数量"`
	ProcessingCount int    `json:"processing_count" dc:"处理中数量"`
	FailedCount     int    `json:"failed_count" dc:"失败数量"`
	PendingCount    int    `json:"pending_count" dc:"待执行数量"`
	CanceledCount   int    `json:"canceled_count" dc:"取消数量"`
}
