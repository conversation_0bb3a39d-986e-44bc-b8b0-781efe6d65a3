package v1

import "github.com/gogf/gf/v2/frame/g"

// 批量创建地址接口
type BatchCreateAddressReq struct {
	g.Meta     `path:"/wallet/batch-create-address" method:"post" tags:"address" summary:"批量创建地址" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Count      int    `json:"count" v:"required#请输入创建地址数量,min:1#创建地址数量不能小于1,max:1000#创建地址数量不能大于1000" dc:"创建地址数量"`
	Password   string `json:"password" v:"required#请输入密码" dc:"密码"`
	GoogleCode string `json:"google_code" v:"required#请输入谷歌验证码,length:6#格式错误,numeric#谷歌验证码必须是6位数字" dc:"谷歌验证码"`
}

// BatchCreateAddressRes 批量创建地址响应
type BatchCreateAddressRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	TaskId  string `json:"task_id,omitempty" dc:"任务ID，用于查询任务进度"`
}

// 查询地址创建任务进度
type GetAddressTaskProgressReq struct {
	g.Meta `path:"/wallet/address-task-progress" method:"get" tags:"address" summary:"查询地址创建任务进度" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TaskId string `json:"task_id" v:"required#请输入任务ID" dc:"任务ID"`
}

// GetAddressTaskProgressRes 查询地址创建任务进度响应
type GetAddressTaskProgressRes struct {
	TaskId        string  `json:"task_id" dc:"任务ID"`
	Status        string  `json:"status" dc:"任务状态：pending(待处理), processing(处理中), completed(已完成), failed(失败)"`
	Progress      float64 `json:"progress" dc:"进度百分比，0-100"`
	ProcessedRows int     `json:"processed_rows" dc:"已处理数量"`
	TotalRows     int     `json:"total_rows" dc:"总数量"`
	ErrorMessage  string  `json:"error_message,omitempty" dc:"错误信息，仅在status为failed时有值"`
}

// 获取地址列表
type GetAddressListReq struct {
	g.Meta     `path:"/wallet/address-list" method:"get" tags:"address" summary:"获取地址列表" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Page       int    `json:"page" v:"required#请输入页码,numeric#页码必须是数字,min:1#页码不能小于1" dc:"页码"`
	Limit      int    `json:"limit" v:"required#请输入每页数量,numeric#每页数量必须是数字,min:1#每页数量不能小于1" dc:"每页数量"`
	Address    string `json:"address" dc:"地址"`
	Type       string `json:"type" dc:"类型"`
	SortField  string `json:"sort_field"  dc:"排序字段"`
	SortOrder  string `json:"sort_order" v:"in:asc,desc#排序方向必须是asc或desc" dc:"排序方向"`
	HasBalance bool   `json:"has_balance" dc:"是否查询有余额的地址"`
}

// 获取地址列表返回
type GetAddressListRes struct {
	Page Page          `json:"page" dc:"分页信息"`
	List []AddressInfo `json:"list" dc:"地址列表"`
}

// AddressInfo 地址信息
type AddressInfo struct {
	Id               int    `json:"id" dc:"id"`
	WalletId         int    `json:"wallet_id" dc:"钱包id"`
	Address          string `json:"address" dc:"地址"`
	Type             string `json:"type" dc:"类型"`
	Label            string `json:"label" dc:"标签"`
	BindStatus       int    `json:"bind_status" dc:"绑定状态"`
	BindAt           string `json:"bind_at" dc:"绑定时间"`
	LastQueryAt      string `json:"last_query_at" dc:"最近查询时间"`
	Status           int    `json:"status" dc:"状态"`
	ChainCoinBalance string `json:"chain_coin_balance" dc:"链币余额"`
	ChainUsdtBalance string `json:"chain_usdt_balance" dc:"链usdt余额"`
	CreateAt         string `json:"create_at" dc:"创建时间"`
	UpdateAt         string `json:"update_at" dc:"更新时间"`
}

// 获取地址页面的统计信息
type GetAddressStatisticReq struct {
	g.Meta `path:"/wallet/address-statistic" method:"get" tags:"address" summary:"获取地址页面的统计信息" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// GetAddressStatisticRes 获取地址页面的统计信息响应
type GetAddressStatisticRes struct {
	//总数量
	TotalSize int `json:"total_size" dc:"总数量"`
	//活跃地址
	ActiveAddress int `json:"active_address" dc:"活跃地址"`
	//eth总余额
	EthTotalBalance string `json:"eth_total_balance" dc:"eth总余额"`
	//erc20usdt总余额
	Erc20UsdtTotalBalance string `json:"erc20_usdt_total_balance" dc:"erc20usdt总余额"`
	//trx总余额
	TrxTotalBalance string `json:"trx_total_balance" dc:"trx总余额"`
	//trc20usdt总余额
	Trc20UsdtTotalBalance string `json:"trc20_usdt_total_balance" dc:"trc20usdt总余额"`
	//eth地址数量
	EthAddressCount int `json:"eth_address_count" dc:"eth地址数量"`
	//tron地址数量
	TrxAddressCount int `json:"trx_address_count" dc:"tron地址数量"`
}

// 导出地址
type ExportAddressReq struct {
	g.Meta     `path:"/wallet/export-address" method:"post" tags:"address" summary:"导出地址" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Type       string `json:"type" v:"required#请输入类型,in:all,unbound,bind#类型必须是all或unbind或bind" dc:"类型"`
	Password   string `json:"password" v:"required#请输入密码" dc:"密码"`
	GoogleCode string `json:"google_code" v:"required#请输入谷歌验证码,length:6#格式错误,numeric#谷歌验证码必须是6位数字" dc:"谷歌验证码"`
}

// ExportAddressRes 导出地址响应
type ExportAddressRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 刷新地址余额
type RefreshAddressReq struct {
	g.Meta  `path:"/wallet/refresh-address" method:"post" tags:"address" summary:"刷新地址余额" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Address string `json:"address" v:"required#请输入地址" dc:"地址"`
}

// RefreshAddressRes 刷新地址余额响应
type RefreshAddressRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 绑定地址
type BindAddressReq struct {
	g.Meta  `path:"/wallet/bind-address" method:"post" tags:"address" summary:"绑定地址" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Address string `json:"address" v:"required#请输入地址" dc:"地址"`
	Type    string `json:"type" v:"required#请输入地址类型" dc:"地址类型"`
}

// BindAddressRes 绑定地址响应
type BindAddressRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

type TransactionType struct {
	Page      int    `json:"page" v:"required#请输入页码,numeric#页码必须是数字,min:1#页码不能小于1" dc:"页码"`
	Limit     int    `json:"limit" v:"required#请输入每页数量,numeric#每页数量必须是数字,min:1#每页数量不能小于1" dc:"每页数量"`
	Address   string `json:"address" dc:"地址"`
	Type      string `json:"type" v:"in:all,deposit,withdraw,transfer,miner_fee#类型必须是all、deposit、withdraw、transfer或miner_fee" dc:"类型"`
	Chain     string `json:"chain" v:"in:ETH,TRON#链必须是ETH、TRON" dc:"链"`
	SortField string `json:"sort_field"  dc:"排序字段"`
	SortOrder string `json:"sort_order" v:"in:asc,desc#排序方向必须是asc或desc" dc:"排序方向"`
	Status    string `json:"status" v:"in:completed,processing,failed,pending,canceled#状态必须是completed、processing、failed、pending或canceled" dc:"状态"`
	Coin      string `json:"coin" v:"in:ETH,TRX,USDT#币种必须是ETH、TRX或USDT" dc:"币种"`
	DateRange string `json:"date_range" dc:"日期范围"`
	// TransactionType string `json:"transaction_type" v:"in:deposit,withdraw,transfer,miner_fee#交易类型必须是deposit、withdraw、transfer或miner_fee" dc:"交易类型"`
}

// 交易记录列表
type GetTransactionRecordReq struct {
	g.Meta `path:"/wallet/transaction-record" method:"get" tags:"address" summary:"交易记录" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TransactionType
}

// GetTransactionRecordRes 交易记录列表响应
type GetTransactionRecordRes struct {
	Page Page          `json:"page" dc:"分页信息"`
	List []Transaction `json:"list" dc:"交易记录列表"`
}

// Transaction 交易记录
type Transaction struct {
	Id                int    `json:"id" dc:"id"`
	TransactionStatus string `json:"transaction_status" dc:"交易状态,completed,failed"`
	Chain             string `json:"chain" dc:"链"`
	IsToken           bool   `json:"is_token" dc:"是否代币"`
	ContractAddress   string `json:"contract_address" dc:"合约地址"`
	TokenName         string `json:"token_name" dc:"代币名字"`
	SenderAddress     string `json:"sender_address" dc:"发送方地址"`
	ReceiverAddress   string `json:"receiver_address" dc:"接收方地址"`
	TransactionTime   string `json:"transaction_time" dc:"交易时间"`
	Amount            string `json:"amount" dc:"交易金额"`
	TransactionType   string `json:"transaction_type" dc:"交易类型,deposit 充值,withdraw 提现,transfer 转账,miner_fee 矿工费"`
	TransactionFee    string `json:"transaction_fee" dc:"交易手续费"`
	BlockNumber       int    `json:"block_number" dc:"区块号"`
	BlockHash         string `json:"block_hash" dc:"区块哈希"`
	Confirmations     int    `json:"confirmations" dc:"确认数"`
	Notes             string `json:"notes" dc:"备注"`
	CreateAt          string `json:"create_at" dc:"创建时间"`
	UpdateAt          string `json:"update_at" dc:"更新时间"`
	NetFee            string `json:"net_fee" dc:"net_fee"`
	EnergyFee         string `json:"energy_fee" dc:"energy_fee"`
	EffectiveGasPrice string `json:"effective_gas_price" dc:"effective_gas_price"`
	GasUsed           string `json:"gas_used" dc:"gas_used"`
	CumulativeGasUsed string `json:"cumulative_gas_used" dc:"cumulative_gas_used"`
	TransactionHash   string `json:"transaction_hash" dc:"transaction_hash"`
}

// 交易记录统计
type GetTransactionRecordStatisticReq struct {
	g.Meta `path:"/wallet/transaction-record-statistic" method:"get" tags:"address" summary:"交易记录统计" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// 导出交易记录
type ExportTransactionRecordReq struct {
	g.Meta `path:"/wallet/export-transaction-record" method:"post" tags:"address" summary:"导出交易记录" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TransactionType
}

// ExportTransactionRecordRes 导出交易记录响应
type ExportTransactionRecordRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// GetTransactionRecordStatisticRes 交易记录统计响应
type GetTransactionRecordStatisticRes struct {
	DepositOrderCount        int `json:"deposit_order_count" dc:"充值订单数量"`
	WithdrawOrderCount       int `json:"withdraw_order_count" dc:"归集订单数量"`
	MinerFeeOrderCount       int `json:"miner_fee_order_count" dc:"矿工费订单数量"`
	PendingConfirmationCount int `json:"pending_confirmation_count" dc:"待确认交易数量"`

	TotalUsdtDepositAmount  string `json:"total_usdt_deposit_amount" dc:"累计usdt充值数量"`
	TotalUsdtWithdrawAmount string `json:"total_usdt_withdraw_amount" dc:"累计usdt归集数量"`

	TotalTrxWithdrawAmount string `json:"total_trx_withdraw_amount" dc:"累计trx归集数量"`
	// TotalTrxMinerFeeAmount string `json:"total_trx_miner_fee_amount" dc:"累计trx矿工费数量"`
	TotalTrxDepositAmount string `json:"total_trx_deposit_amount" dc:"累计trx充值数量"`

	TotalEthDepositAmount  string `json:"total_eth_deposit_amount" dc:"累计eth充值数量"`
	TotalEthWithdrawAmount string `json:"total_eth_withdraw_amount" dc:"累计eth归集数量"`
	// TotalEthMinerFeeAmount string `json:"total_eth_miner_fee_amount" dc:"累计eth矿工费数量"`
}
