package v1

import "github.com/gogf/gf/v2/frame/g"

// RechargeType defines common parameters for recharge-related requests
type RechargeType struct {
	Page      int    `json:"page" v:"required#请输入页码,numeric#页码必须是数字,min:1#页码不能小于1" dc:"页码"`
	Limit     int    `json:"limit" v:"required#请输入每页数量,numeric#每页数量必须是数字,min:1#每页数量不能小于1" dc:"每页数量"`
	Address   string `json:"address" dc:"地址"`
	Chain     string `json:"chain" v:"in:ETH,TRON#链必须是ETH、TRON" dc:"链"`
	SortField string `json:"sort_field" dc:"排序字段"`
	SortOrder string `json:"sort_order" v:"in:asc,desc#排序方向必须是asc或desc" dc:"排序方向"`
	Status    string `json:"status" v:"in:completed,pending#状态必须是completed、pending" dc:"状态"`
	Coin      string `json:"coin" v:"in:ETH,TRX,USDT#币种必须是ETH、TRX或USDT" dc:"币种"`
	DateRange string `json:"date_range" dc:"日期范围"`
}

// 充值记录列表
type GetRechargeRecordReq struct {
	g.Meta `path:"/wallet/recharge-record" method:"get" tags:"recharge" summary:"充值记录" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	RechargeType
}

// GetRechargeRecordRes 充值记录列表响应
type GetRechargeRecordRes struct {
	Page Page             `json:"page" dc:"分页信息"`
	List []RechargeRecord `json:"list" dc:"充值记录列表"`
}

// RechargeRecord 充值记录
type RechargeRecord struct {
	RechargesId          int    `json:"recharges_id" dc:"充值ID"`
	TokenId              int    `json:"token_id" dc:"币种ID"`
	Name                 string `json:"name" dc:"币种名称"`
	Chain                string `json:"chain" dc:"链"`
	TokenContractAddress string `json:"token_contract_address" dc:"代币合约地址"`
	FromAddress          string `json:"from_address" dc:"来源地址"`
	ToAddress            string `json:"to_address" dc:"目标地址"`
	TxHash               string `json:"tx_hash" dc:"交易哈希"`
	Error                string `json:"error" dc:"失败原因"`
	Amount               string `json:"amount" dc:"充值数量"`
	State                int    `json:"state" dc:"状态: 1-待确认/处理中(Pending), 2-已完成/已入账(Completed)"`
	Confirmations        int    `json:"confirmations" dc:"确认数"`
	CreatedAt            string `json:"created_at" dc:"创建时间"`
	CompletedAt          string `json:"completed_at" dc:"完成时间"`
	UpdatedAt            string `json:"updated_at" dc:"更新时间"`
	IsWithdraw           int    `json:"is_withdraw" dc:"是否提现"`
	WithdrawId           int    `json:"withdraw_id" dc:"提现ID"`
}

// 导出充值记录
type ExportRechargeRecordReq struct {
	g.Meta `path:"/wallet/export-recharge-record" method:"post" tags:"recharge" summary:"导出充值记录" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	RechargeType
}

// ExportRechargeRecordRes 导出充值记录响应
type ExportRechargeRecordRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 充值记录统计
type GetRechargeRecordStatisticReq struct {
	g.Meta `path:"/wallet/recharge-record-statistic" method:"get" tags:"recharge" summary:"充值记录统计" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// GetRechargeRecordStatisticRes 充值记录统计响应
type GetRechargeRecordStatisticRes struct {
	TotalRechargeCount int `json:"total_recharge_count" dc:"充值订单总数量"`
	PendingCount       int `json:"pending_count" dc:"待确认充值数量"`
	CompletedCount     int `json:"completed_count" dc:"已完成充值数量"`

	TotalUsdtAmount string `json:"total_usdt_amount" dc:"累计USDT充值数量"`
	TotalTrxAmount  string `json:"total_trx_amount" dc:"累计TRX充值数量"`
	TotalEthAmount  string `json:"total_eth_amount" dc:"累计ETH充值数量"`
}
