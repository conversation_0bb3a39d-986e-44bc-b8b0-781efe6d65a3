package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 获取代币费用补充记录列表
type GetTokenFeeSupplementsReq struct {
	g.Meta              `path:"/wallet/token-fee-supplements" method:"get" tags:"fee" summary:"获取代币费用补充记录列表" dc:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Page                int    `json:"page" v:"required#请输入页码,numeric#页码必须是数字,min:1#页码不能小于1" dc:"页码"`
	Limit               int    `json:"limit" v:"required#请输入每页数量,numeric#每页数量必须是数字,min:1#每页数量不能小于1" dc:"每页数量"`
	Address             string `json:"address" dc:"地址"`
	ChainType           string `json:"chain_type" dc:"链类型 (例如: ETH, TRON)"`
	TokenSymbol         string `json:"token_symbol" dc:"代币符号 (例如: ETH, TRX, USDT)"`
	FeeType             string `json:"fee_type" dc:"费用类型 (例如: gas_fee, energy)"`
	Status              string `json:"status" dc:"状态 (pending, processing, success, failed, partial_success)"`
	TrxSupplementStatus string `json:"trx_supplement_status" dc:"TRX补充状态 (pending, processing, success, failed)"`
	SortField           string `json:"sort_field" dc:"排序字段"`
	SortOrder           string `json:"sort_order" v:"in:asc,desc#排序方向必须是asc或desc" dc:"排序方向"`
}

// GetTokenFeeSupplementsRes 获取代币费用补充记录列表响应
type GetTokenFeeSupplementsRes struct {
	Page  Page                  `json:"page" dc:"分页信息"`
	List  []*TokenFeeSupplement `json:"list" dc:"代币费用补充记录列表"`
	Stats FeeStatistics         `json:"stats" dc:"费用统计信息"`
}

// TokenFeeSupplement 代币费用补充记录
type TokenFeeSupplement struct {
	TokenFeeSupplementId uint64      `json:"token_fee_supplement_id" dc:"主键ID"`                                                       // 主键ID
	WithdrawPlanId       int64       `json:"withdraw_plan_id"        dc:"计划订单id"`                                                     // 计划订单id
	UserWithdrawId       int64       `json:"user_withdraw_id"        dc:"计划订单id"`                                                     // 计划订单id
	Address              string      `json:"address"                 dc:"需要补充费用的地址"`                                                  // 需要补充费用的地址
	ChainType            string      `json:"chain_type"              dc:"链类型 (例如: ERC20, TRC20)"`                                     // 链类型 (例如: ERC20, TRC20)
	TokenSymbol          string      `json:"token_symbol"            dc:"代币符号 (例如: USDT)"`                                            // 代币符号 (例如: USDT)
	FeeType              string      `json:"fee_type"                dc:"费用类型 (例如: gas_fee, energy)"`                                 // 费用类型 (例如: gas_fee, energy)
	RequiredAmount       float64     `json:"required_amount"         dc:"需要的费用数量 (原生代币单位)"`                                           // 需要的费用数量 (原生代币单位)
	ProvidedAmount       float64     `json:"provided_amount"         dc:"已补充的费用数量 (原生代币单位)"`                                          // 已补充的费用数量 (原生代币单位)
	EnergyAmount         float64     `json:"energy_amount"           dc:"补充能量数量 trc20 专用"`                                            // 补充能量数量 trc20 专用
	EnergyFee            float64     `json:"energy_fee"              dc:"补充能量数量 trc20 专用"`                                            // 补充能量数量 trc20 专用
	Status               string      `json:"status"                  dc:"状态 (pending, processing, success, failed, partial_success)"` // 状态 (pending, processing, success, failed, partial_success)
	TransactionHash      string      `json:"transaction_hash"        dc:"补充费用的交易哈希"`                                                  // 补充费用的交易哈希
	ErrorMessage         string      `json:"error_message"           dc:"错误信息"`                                                       // 错误信息
	RelatedTaskId        string      `json:"related_task_id"         dc:"关联的归集任务ID"`                                                  // 关联的归集任务ID
	RetryCount           uint        `json:"retry_count"             dc:"重试次数"`                                                       // 重试次数
	CreatedAt            *gtime.Time `json:"created_at"              dc:"创建时间"`                                                       // 创建时间
	UpdatedAt            *gtime.Time `json:"updated_at"              dc:"更新时间"`                                                       // 更新时间
	EnergyId             string      `json:"energy_id"               dc:""`                                                           //
	IsActivating         int         `json:"is_activating"           dc:"是否激活 0 未激活 1 激活中 2 已激活"`                                     // 是否激活 0 未激活 1 激活中 2 已激活
	ActivateHash         string      `json:"activate_hash"           dc:"激活hash"`                                                     // 激活hash
	ActivateAmount       float64     `json:"activate_amount"         dc:"激活消耗trx"`                                                    // 激活消耗trx
	TrxSupplementNeeded  int         `json:"trx_supplement_needed"   dc:"TRX补充是否需要 0-不需要 1-需要"`                                       // TRX补充是否需要 0-不需要 1-需要
	TrxSupplementStatus  string      `json:"trx_supplement_status"   dc:"TRX补充状态 pending-待处理 processing-处理中 success-成功 failed-失败"`    // TRX补充状态 pending-待处理 processing-处理中 success-成功 failed-失败
	TrxSupplementHash    string      `json:"trx_supplement_hash"     dc:"TRX补充交易哈希"`                                                  // TRX补充交易哈希
	TrxSupplementAmount  float64     `json:"trx_supplement_amount"   dc:"TRX补充数量"`                                                    // TRX补充数量
	TrxBalanceBefore     float64     `json:"trx_balance_before"      dc:"补充前TRX余额"`                                                   // 补充前TRX余额
	TrxBalanceAfter      float64     `json:"trx_balance_after"       dc:"补充后TRX余额"`                                                   // 补充后TRX余额
}

// FeeStatistics 费用统计信息
type FeeStatistics struct {
	TotalCount          int     `json:"total_count" dc:"总记录数"`
	PendingCount        int     `json:"pending_count" dc:"待处理记录数"`
	ProcessingCount     int     `json:"processing_count" dc:"处理中记录数"`
	SuccessCount        int     `json:"success_count" dc:"成功记录数"`
	FailedCount         int     `json:"failed_count" dc:"失败记录数"`
	PartialSuccessCount int     `json:"partial_success_count" dc:"部分成功记录数"`
	TotalRequiredAmount float64 `json:"total_required_amount" dc:"总需要费用数量"`
	TotalProvidedAmount float64 `json:"total_provided_amount" dc:"总已补充费用数量"`
}

// 更新代币费用补充记录状态
type UpdateTokenFeeSupplementStatusReq struct {
	g.Meta               `path:"/wallet/token-fee-supplement-status" method:"post" tags:"fee" summary:"更新代币费用补充记录状态" dc:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	TokenFeeSupplementId uint64 `json:"token_fee_supplement_id" v:"required#请输入代币费用补充记录ID" dc:"代币费用补充记录ID"`
	Status               string `json:"status" v:"required#请输入状态,in:pending,processing,success,failed,partial_success#状态必须是pending、processing、success、failed或partial_success" dc:"状态 (pending, processing, success, failed, partial_success)"`
	TransactionHash      string `json:"transaction_hash" dc:"补充费用的交易哈希"`
	ErrorMessage         string `json:"error_message" dc:"错误信息"`
	GoogleCode           string `json:"google_code" v:"required#请输入谷歌验证码" dc:"谷歌验证码"`
	Password             string `json:"password" v:"required#请输入密码" dc:"密码"`
}

// UpdateTokenFeeSupplementStatusRes 更新代币费用补充记录状态响应
type UpdateTokenFeeSupplementStatusRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 获取费用统计信息
type GetFeeStatisticsReq struct {
	g.Meta `path:"/wallet/fee-statistics" method:"get" tags:"fee" summary:"获取费用统计信息" dc:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// GetFeeStatisticsRes 获取费用统计信息响应
type GetFeeStatisticsRes struct {
	Statistics FeeStatistics `json:"statistics" dc:"费用统计信息"`
}
