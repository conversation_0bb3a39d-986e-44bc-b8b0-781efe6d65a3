package v1

import "github.com/gogf/gf/v2/frame/g"

// 添加地址到归集计划
type AddAddressToWithdrawPlanReq struct {
	g.Meta           `path:"/wallet/add-address-to-withdraw-plan" method:"post" tags:"withdraw_plan" summary:"添加地址到归集计划" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Address          string `json:"address" v:"required#请输入地址" dc:"地址"`
	Chan             string `json:"chan" v:"required#请输入链类型,in:ETH,TRON#链类型必须是ETH或TRON" dc:"链类型，如ETH、TRON等"`
	CheckBalance     bool   `json:"check_balance" dc:"是否检查余额，默认为true"`
	MinBalanceAmount string `json:"min_balance_amount" dc:"最小余额要求，仅在check_balance为true时生效"`
}

// AddAddressToWithdrawPlanRes 添加地址到归集计划响应
type AddAddressToWithdrawPlanRes struct {
	Success          bool   `json:"success" dc:"是否成功"`
	WithdrawPlanId   uint   `json:"withdraw_plan_id,omitempty" dc:"归集计划ID"`
	ChainBalance     string `json:"chain_balance,omitempty" dc:"链币余额"`
	UsdtBalance      string `json:"usdt_balance,omitempty" dc:"USDT余额"`
	BalanceCheckInfo string `json:"balance_check_info,omitempty" dc:"余额检查信息"`
}

// 获取归集计划列表
type GetWithdrawPlanListReq struct {
	g.Meta `path:"/wallet/withdraw-plan-list" method:"get" tags:"withdraw_plan" summary:"获取归集计划列表" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Page   int    `json:"page" v:"required#请输入页码,min:1#页码不能小于1" dc:"页码，默认为1"`
	Limit  int    `json:"limit" v:"required#请输入每页数量,min:1,max:100#每页数量必须在1-100之间" dc:"每页数量，默认为10"`
	Chan   string `json:"chan" dc:"链类型，如ETH、TRON等，不传则查询所有"`
	State  int    `json:"state" dc:"状态: 1-待确认, 2-完成，不传则查询所有"`
}

// GetWithdrawPlanListRes 获取归集计划列表响应
type GetWithdrawPlanListRes struct {
	Page  Page                `json:"page" dc:"分页信息"`
	List  []*WithdrawPlanInfo `json:"list" dc:"归集计划列表"`
	Total int                 `json:"total" dc:"总数"`
}

// WithdrawPlanInfo 归集计划信息
type WithdrawPlanInfo struct {
	WithdrawPlanId uint   `json:"withdrawPlanId" dc:"归集计划ID"`
	Chan           string `json:"chan" dc:"链"`
	Address        string `json:"address" dc:"地址"`
	State          uint   `json:"state" dc:"状态: 1-待确认, 2-完成"`
	ErrorMessage   string `json:"errorMessage" dc:"失败或错误信息"`
	CreatedAt      string `json:"createdAt" dc:"创建时间"`
	UpdatedAt      string `json:"updatedAt" dc:"最后更新时间"`
}
