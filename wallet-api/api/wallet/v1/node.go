package v1

import (
	// "wallet-api/internal/utility/crypto/tron"

	"github.com/gogf/gf/v2/frame/g"
)

// 获取gastracker
type GetGastrackerReq struct {
	g.Meta `path:"/node/get-gastracker" method:"get" tags:"node" summary:"获取gastracker" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

type GetGastrackerRes struct {
	LastBlock       string  `json:"LastBlock" dc:"最新区块高度"`
	SafeGasPrice    float64 `json:"SafeGasPrice" dc:"安全gas价格"`
	ProposeGasPrice float64 `json:"ProposeGasPrice" dc:"提议gas价格"`
	FastGasPrice    float64 `json:"FastGasPrice" dc:"快速gas价格"`
	SuggestBaseFee  float64 `json:"SuggestBaseFee" dc:"建议基础费用"`
	GasUsedRatio    string  `json:"GasUsedRatio" dc:"gas使用率"`
}

// 查询代币和 usdt余额
type GetTokenBalanceReq struct {
	g.Meta  `path:"/node/get-token-balance" method:"get" tags:"node" summary:"查询代币和 usdt余额" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Address string `json:"address" dc:"钱包地址"`
	Type    string `json:"type" dc:"类型"`
}

type GetTokenBalanceRes struct {
	TokenBalance string `json:"tokenBalance" dc:"代币余额"`
	UsdtBalance  string `json:"usdtBalance" dc:"usdt余额"`
}

// 查询tron hash 交易手续费详细信息
// type GetTronTransactionFeeReq struct {
// 	g.Meta `path:"/node/get-tron-transaction-fee" method:"get"  tags:"node" summary:"查询tron hash 交易手续费详细信息" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
// 	Hash   string `json:"hash" dc:"交易hash" v:"required#交易hash不能为空"`
// }

// type GetTronTransactionFeeRes struct {
// 	Cost *tron.Cost `json:"cost" dc:"交易手续费"`
// }
