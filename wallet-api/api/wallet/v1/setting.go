package v1

import (
	"wallet-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// 钱包设置 - 合并所有设置到一个请求
type WalletSettingReq struct {
	g.Meta `path:"/wallet/setting" method:"post" tags:"setting" summary:"钱包设置" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	// 基本验证
	GoogleCode string `json:"google_code" v:"required#请输入谷歌验证码,length:6#格式错误,numeric#谷歌验证码必须是6位数字" dc:"谷歌验证码"`
	Password   string `json:"password" v:"required#请输入密码" dc:"密码"`

	// 策略归集设置
	StrategyCollectSwitch string `json:"strategy_collect_switch" dc:"策略归集开关"`
	EthCollectThreshold   string `json:"eth_collect_threshold" dc:"ETH归集阈值"`
	TrxCollectThreshold   string `json:"trx_collect_threshold" dc:"TRX归集阈值"`
	UsdtCollectThreshold  string `json:"usdt_collect_threshold" dc:"USDT归集阈值"`

	// 定时归集设置
	CronCollectSwitch string `json:"cron_collect_switch" dc:"定时归集开关"`
	CronCollectTime   string `json:"cron_collect_time" dc:"定时归集时间"`

	// 归集地址设置
	TrxCollectAddress string `json:"trx_collect_address" dc:"TRX链特定归集地址"`
	EthCollectAddress string `json:"eth_collect_address" dc:"ETH链特定归集地址"`

	// 费用私钥设置
	TrxFeePrivateKey string `json:"trx_fee_private_key" dc:"TRX费用支付私钥(请谨慎处理,加密)"`
	EthFeePrivateKey string `json:"eth_fee_private_key" dc:"ETH费用支付私钥(请谨慎处理,加密)"`

	// 费用地址设置
	TrxFeeAddress string `json:"trx_fee_address" dc:"TRX费用资金持有地址"`
	EthFeeAddress string `json:"eth_fee_address" dc:"ETH费用资金持有地址"`

	// TRX激活金额
	TrxActivateAmount string `json:"trx_activate_amount" dc:"TRX激活账户金额" v:"min:0#激活金额不能为负数"`

	// 费用模式设置
	EthFeeMode string `json:"eth_fee_mode" dc:"ETH矿工费模式 1自动 2手动" v:"in:1,2#ETH矿工费模式必须是1或2"`
	TrxFeeMode string `json:"trx_fee_mode" dc:"TRX矿工费模式 1自动 2手动" v:"in:1,2#TRX矿工费模式必须是1或2"`

	// 固定费用金额
	EthFeeAmount string `json:"eth_fee_amount" dc:"ETH固定矿工费发送金额" v:"min:0#费用金额不能为负数"`
	TrxFeeAmount string `json:"trx_fee_amount" dc:"TRX固定矿工费发送金额" v:"min:0#费用金额不能为负数"`

	// 最大费用限制
	EthFeeMax   string `json:"eth_fee_max" dc:"ETH最大矿工费" v:"min:0#最大费用不能为负数"`
	Erc20FeeMax string `json:"erc20_fee_max" dc:"ERC20最大矿工费" v:"min:0#最大费用不能为负数"`
	TrxFeeMax   string `json:"trx_fee_max" dc:"TRX交易最多支付手续费" v:"min:0#最大费用不能为负数"`

	// Gas设置
	EthGasPrice   string `json:"eth_gas_price" dc:"ETH Gas价格"`
	EthGasLimit   string `json:"eth_gas_limit" dc:"ETH Gas限制"`
	Erc20GasPrice string `json:"erc20_gas_price" dc:"ERC20 Gas价格"`
	Erc20GasLimit string `json:"erc20_gas_limit" dc:"ERC20 Gas限制"`

	// 保留金额设置
	TrxKeepAmount string `json:"trx_keep_amount" dc:"归集保持账户内最低TRX余额,防止交易失败" v:"min:0#保留金额不能为负数"`
	EthKeepAmount string `json:"eth_keep_amount" dc:"ETH归集时预留金额" v:"min:0#保留金额不能为负数"`

	// TRC20能量和带宽设置
	Trc20MinRequiredEnergy    string `json:"trc20_min_required_energy" dc:"转账USDT最低需要的能量,手动模式设置" v:"min:0#能量值不能为负数"`
	Trc20MaxEnergyFee         string `json:"trc20_max_energy_fee" dc:"最大允许消耗的TRX能量费金额,用于购买TRX能量的费用设置" v:"min:0#能量费不能为负数"`
	Trc20MinRequiredBandwidth string `json:"trc20_min_required_bandwidth" dc:"最低带宽" v:"min:0#带宽值不能为负数"`

	//最小取款金额设置
	EthMinTakeAmount  string `json:"eth_min_take_amount" dc:"eth最小取款金额" v:"min:0#最小取款金额不能为负数"`
	TrxMinTakeAmount  string `json:"trx_min_take_amount" dc:"trx最小取款金额" v:"min:0#最小取款金额不能为负数"`
	UsdtMinTakeAmount string `json:"usdt_min_take_amount" dc:"usdt最小取款金额" v:"min:0#最小取款金额不能为负数"`

	// TRC20触发手续费
	Trc20TriggerFeeAmount string `json:"trc20_trigger_fee_amount" dc:"TRC20触发手续费" v:"min:0#触发手续费不能为负数"`
}

// WalletSettingRes 钱包设置响应
type WalletSettingRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 以下为兼容旧版API的结构,新代码应使用WalletSettingReq

// 基本设置
type BaseSettingReq struct {
	g.Meta `path:"/wallet/base-setting" method:"post" tags:"setting" summary:"基本设置(已废弃,请使用/wallet/setting)" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	*entity.Wallets
	GoogleCode string `json:"google_code" v:"required#请输入谷歌验证码,length:6#格式错误,numeric#谷歌验证码必须是6位数字" dc:"谷歌验证码"`
	Password   string `json:"password" v:"required#请输入密码" dc:"密码"`
}

// BaseSettingRes 基本设置响应
type BaseSettingRes struct {
	Success bool `json:"success" dc:"是否成功"`
}
