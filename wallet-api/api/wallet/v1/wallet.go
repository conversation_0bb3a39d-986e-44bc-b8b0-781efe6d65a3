package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CheckStatusReq 检测钱包状态请求
type CheckStatusReq struct {
	g.Meta `path:"/wallet/status" method:"get" tags:"wallet" summary:"检测钱包状态"`
}

// CheckStatusRes 检测钱包状态响应
type CheckStatusRes struct {
	Message       string `json:"message" dc:"状态描述"`
	IsUnlocked    bool   `json:"is_unlocked" dc:"钱包是否解锁"`
	IsInitialized bool   `json:"is_initialized" dc:"钱包是否初始化"`
}

// GenerateWalletReq 生成钱包请求
type GenerateWalletReq struct {
	g.Meta `path:"/wallet/generate" method:"get" tags:"wallet" summary:"生成钱包助记词"`
}

// GenerateWalletRes 生成钱包响应
type GenerateWalletRes struct {
	Mnemonic string `json:"mnemonic" dc:"助记词"`
}

// ResetWalletReq 重置钱包请求
type ResetWalletReq struct {
	g.Meta   `path:"/wallet/reset" method:"post" tags:"wallet" summary:"重置钱包"`
	Password string `json:"password" v:"required#请输入密码" dc:"密码"`
}

// ResetWalletRes 重置钱包响应
type ResetWalletRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 获取钱包信息
type GetWalletInfoReq struct {
	g.Meta `path:"/wallet/info" method:"get" tags:"wallet" summary:"获取钱包信息" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// GetWalletInfoRes 获取钱包信息响应
type GetWalletInfoRes struct {
	WalletInfo WalletInfo `json:"wallet_info" dc:"钱包信息"`
}

// WalletInfo 钱包信息
type WalletInfo struct {
	TrxCollectAddress string `json:"trx_collect_address" dc:"归集地址"`
	EthCollectAddress string `json:"eth_collect_address" dc:"归集地址"`
	//矿工费私钥
	TrxFeePrivateKeyIsSet bool `json:"trx_fee_private_key_is_set" dc:"矿工费私钥是否设置"`
	EthFeePrivateKeyIsSet bool `json:"eth_fee_private_key_is_set" dc:"矿工费私钥是否设置"`

	// 矿工费地址
	TrxFeeAddress string `json:"trx_fee_address" dc:"TRX费用地址"`
	EthFeeAddress string `json:"eth_fee_address" dc:"ETH费用地址"`

	//策略归集开关 1 开启 0 关闭
	StrategyCollectSwitch int `json:"strategy_collect_switch" dc:"策略归集开关"`
	//eth 归集阈值
	EthCollectThreshold string `json:"eth_collect_threshold" dc:"eth 归集阈值"`
	//trx 归集阈值
	TrxCollectThreshold string `json:"trx_collect_threshold" dc:"trx 归集阈值"`
	//usdt 归集阈值
	UsdtCollectThreshold string `json:"usdt_collect_threshold" dc:"usdt 归集阈值"`
	//定时归集开关
	CronCollectSwitch int `json:"cron_collect_switch" dc:"定时归集开关"`
	//定时归集时间
	CronCollectTime string `json:"cron_collect_time" dc:"定时归集时间"`
	//google验证码开关
	GoogleCodeSwitch bool `json:"google_code_switch" dc:"google验证码开关"`

	// eth矿工费 模式 自动 ，手动
	EthFeeMode int `json:"eth_fee_mode" dc:"eth矿工费模式"`
	// trx矿工费 模式 自动 ，手动
	TrxFeeMode int `json:"trx_fee_mode" dc:"trx矿工费模式"`

	//eth 矿工费最大值
	EthFeeMax float64 `json:"eth_fee_max" dc:"eth 矿工费最大值"`
	//erc20 矿工费最大值
	Erc20FeeMax float64 `json:"erc20_fee_max" dc:"erc20 矿工费最大值"`
	//eth固定矿工费发送金额
	EthFeeAmount float64 `json:"eth_fee_amount" dc:"eth固定矿工费金额"`
	//trx固定矿工费发送金额
	TrxFeeAmount float64 `json:"trx_fee_amount" dc:"trx固定矿工费金额"`
	//gas price
	EthGasPrice int64 `json:"eth_gas_price" dc:"eth gas price"`
	//gas limit
	EthGasLimit int64 `json:"eth_gas_limit" dc:"eth gas limit"`
	//erc20 gas price
	Erc20GasPrice int64 `json:"erc20_gas_price" dc:"erc20 gas price"`
	//erc20 gas limit
	Erc20GasLimit int64 `json:"erc20_gas_limit" dc:"erc20 gas limit"`

	//trx 矿工费最大值
	TrxFeeMax int64 `json:"trx_fee_max" dc:"trx 矿工费最大值"`
	//trx激活账户金额
	TrxActivateAmount float64 `json:"trx_activate_amount" dc:"trx激活账户金额"`
	//归集保持账户内最低trx余额
	TrxKeepAmount int64 `json:"trx_keep_amount" dc:"归集保持账户内最低trx余额"`
	//eth归集预留金额
	EthKeepAmount int64 `json:"eth_keep_amount" dc:"eth归集预留金额"`
	//转账usdt最低需要的能量
	Trc20MinRequiredEnergy int64 `json:"trc20_min_required_energy" dc:"转账usdt最低需要的能量"`
	//最大允许消耗的trx能量费金额
	Trc20MaxEnergyFee float64 `json:"trc20_max_energy_fee" dc:"最大允许消耗的trx能量费金额"`
	//最低带宽
	Trc20MinRequiredBandwidth int64 `json:"trc20_min_required_bandwidth" dc:"最低带宽"`

	//eth最小取款金额
	EthMinTakeAmount float64 `json:"eth_min_take_amount" dc:"eth最小取款金额"`
	//trx最小取款金额
	TrxMinTakeAmount float64 `json:"trx_min_take_amount" dc:"trx最小取款金额"`
	//usdt最小取款金额
	UsdtMinTakeAmount float64 `json:"usdt_min_take_amount" dc:"usdt最小取款金额"`
	//TRC20触发手续费
	Trc20TriggerFeeAmount float64 `json:"trc20_trigger_fee_amount" dc:"TRC20触发手续费"`
}

// 创建钱包
type CreateWalletReq struct {
	g.Meta     `path:"/wallet/create" method:"post" tags:"wallet" summary:"创建钱包" description:"使用助记词创建钱包 需要输入密码"`
	Mnemonic   string `json:"mnemonic" v:"required#请输入助记词" dc:"助记词"`
	Password   string `json:"password" v:"required#请输入密码" dc:"密码"`
	GoogleCode string `json:"google_code" v:"required#请输入谷歌验证码,length:6#格式错误,numeric#谷歌验证码必须是6位数字" dc:"谷歌验证码"`
	Secret     string `json:"secret" v:"required#请输入谷歌验证码密钥" dc:"谷歌验证码密钥"`
}

// CreateWalletRes 创建钱包响应
type CreateWalletRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 仪表盘统计
type DashboardStatisticReq struct {
	g.Meta `path:"/wallet/dashboard-statistic" method:"get" tags:"wallet" summary:"仪表盘统计" description:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// DashboardStatisticRes 仪表盘统计响应
type DashboardStatisticRes struct {
	//总余额
	TotalBalance string `json:"total_balance" dc:"总余额"`
	//eth总余额
	EthTotalBalance string `json:"eth_total_balance" dc:"eth总余额"`
	//trx总余额
	TrxTotalBalance string `json:"trx_total_balance" dc:"trx总余额"`

	//TRC20余额
	Trc20UsdtTotalBalance string `json:"trc20_usdt_total_balance" dc:"TRC20 USDT余额"`
	//TRC20余额
	Erc20UsdtTotalBalance string `json:"erc20_usdt_total_balance" dc:"ERC20 USDT余额"`
}
