package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WithdrawType defines common parameters for withdraw-related requests
type WithdrawType struct {
	Page        int    `json:"page" v:"required#请输入页码,numeric#页码必须是数字,min:1#页码不能小于1" dc:"页码"`
	Limit       int    `json:"limit" v:"required#请输入每页数量,numeric#每页数量必须是数字,min:1#每页数量不能小于1" dc:"每页数量"`
	ToAddress   string `json:"address" dc:"地址"`
	FromAddress string `json:"from_address" dc:"提币源地址"`
	Chain       string `json:"chain" v:"in:ETH,TRON#链必须是ETH、TRON" dc:"链"`
	SortField   string `json:"sort_field" dc:"排序字段"`
	SortOrder   string `json:"sort_order" v:"in:asc,desc#排序方向必须是asc或desc" dc:"排序方向"`
	Status      string `json:"status" v:"in:pending,processing,rejected,completed,failed#状态必须是pending、processing、rejected、completed、failed" dc:"状态"`
	Coin        string `json:"coin" v:"in:ETH,TRX,USDT#币种必须是ETH、TRX或USDT" dc:"币种"`
	DateRange   string `json:"date_range" dc:"日期范围"`
}

// 提现记录列表
type GetWithdrawRecordReq struct {
	g.Meta `path:"/wallet/withdraw-record" method:"get" tags:"withdraw" summary:"提现记录"  dc:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	WithdrawType
}

// GetWithdrawRecordRes 提现记录列表响应
type GetWithdrawRecordRes struct {
	Page Page            `json:"page" dc:"分页信息"`
	List []UserWithdraws `json:"list" dc:"提现记录列表"`
}

type UserWithdraws struct {
	UserWithdrawsId      uint        `json:"user_withdraws_id"        dc:"主键ID"`                                                                                   // 主键ID
	TokenFeeSupplementId int64       `json:"token_fee_supplement_id"  dc:""`                                                                                       //
	Name                 string      `json:"name"                     dc:"币种ID"`                                                                                   // 币种ID
	Chan                 string      `json:"chan"                     dc:"链"`                                                                                      // 链
	FromAddress          string      `json:"from_address"             dc:"提币目标地址"`                                                                                 // 提币目标地址
	ToAddress            string      `json:"to_address"               dc:"提币目标地址"`                                                                                 // 提币目标地址
	Amount               float64     `json:"amount"                   dc:"申请提现金额"`                                                                                 // 申请提现金额
	HandlingFee          float64     `json:"handling_fee"             dc:"提现手续费"`                                                                                  // 提现手续费
	ActualAmount         float64     `json:"actual_amount"            dc:"实际到账金额"`                                                                                 // 实际到账金额
	State                uint        `json:"state"                    dc:"状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)"` // 状态: 1-待审核(Pending), 2-处理中(Processing), 3-已拒绝(Rejected), 4-已完成(Completed), 5-失败(Failed)
	TxHash               string      `json:"tx_hash"                  dc:"链上交易哈希/ID"`                                                                              // 链上交易哈希/ID
	ErrorMessage         string      `json:"error_message"            dc:"失败或错误信息"`                                                                                // 失败或错误信息
	CreatedAt            *gtime.Time `json:"created_at"               dc:"创建时间"`                                                                                   // 创建时间
	CheckedAt            *gtime.Time `json:"checked_at"               dc:"审核时间 (审核通过或拒绝的时间)"`                                                                      // 审核时间 (审核通过或拒绝的时间)
	ProcessingAt         *gtime.Time `json:"processing_at"            dc:"开始处理时间 (进入“处理中”状态的时间)"`                                                                  // 开始处理时间 (进入“处理中”状态的时间)
	CompletedAt          *gtime.Time `json:"completed_at"             dc:"完成时间 (变为“已完成”或“失败”状态的时间)"`                                                               // 完成时间 (变为“已完成”或“失败”状态的时间)
	UpdatedAt            *gtime.Time `json:"updated_at"               dc:"最后更新时间"`                                                                                 // 最后更新时间
	Retries              int         `json:"retries"                  dc:""`                                                                                       //
	NergyState           int         `json:"nergy_state"              dc:"0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了"`                                                         // 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	GasfeeState          int         `json:"gasfee_state"             dc:"gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了"`                                                // gas费 状态  0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	EthFeeMode           int         `json:"eth_fee_mode"             dc:"eth 矿工费模式 1 自动 2手动"`                                                                     // eth 矿工费模式 1 自动 2手动
	EthFeeMax            float64     `json:"eth_fee_max"              dc:"eth 最大矿工费"`                                                                              // eth 最大矿工费
	EthGasPrice          int64       `json:"eth_gas_price"            dc:""`                                                                                       //
	EthGasLimit          int64       `json:"eth_gas_limit"            dc:""`                                                                                       //
	TrxFeeMax            int64       `json:"trx_fee_max"              dc:""`                                                                                       //
	RechargesId          int         `json:"recharges_id"             dc:"充值订单id"`                                                                                 // 充值订单id
	GasfeeHash           string      `json:"gasfee_hash"              dc:"gas费hash"`                                                                               // gas费hash
	GasfeeAmount         float64     `json:"gasfee_amount"            dc:"gas费金额"`                                                                                 // gas费金额
	EnergyMsg            string      `json:"energy_msg"               dc:""`                                                                                       //

}

// 导出提现记录
type ExportWithdrawRecordReq struct {
	g.Meta `path:"/wallet/export-withdraw-record" method:"post" tags:"withdraw" summary:"导出提现记录"  dc:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	WithdrawType
}

// ExportWithdrawRecordRes 导出提现记录响应
type ExportWithdrawRecordRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// 提现记录统计
type GetWithdrawRecordStatisticReq struct {
	g.Meta `path:"/wallet/withdraw-record-statistic" method:"get" tags:"withdraw" summary:"提现记录统计"  dc:"此接口需要在Authorization头部提供Bearer Token进行认证"`
}

// GetWithdrawRecordStatisticRes 提现记录统计响应
type GetWithdrawRecordStatisticRes struct {
	TotalWithdrawCount int `json:"total_withdraw_count" dc:"提现订单总数量"`
	PendingCount       int `json:"pending_count" dc:"待审核提现数量"`
	ProcessingCount    int `json:"processing_count" dc:"处理中提现数量"`
	RejectedCount      int `json:"rejected_count" dc:"已拒绝提现数量"`
	CompletedCount     int `json:"completed_count" dc:"已完成提现数量"`
	FailedCount        int `json:"failed_count" dc:"失败提现数量"`

	TotalUsdtAmount string `json:"total_usdt_amount" dc:"累计USDT提现数量"`
	TotalTrxAmount  string `json:"total_trx_amount" dc:"累计TRX提现数量"`
	TotalEthAmount  string `json:"total_eth_amount" dc:"累计ETH提现数量"`
}

// 创建提现记录
type CreateWithdrawReq struct {
	g.Meta  `path:"/wallet/create-withdraw" method:"post" tags:"withdraw" summary:"创建提现记录"  dc:"此接口需要在Authorization头部提供Bearer Token进行认证"`
	Address string `json:"address" v:"required#请输入提币地址" dc:"提币目标地址"`
}

// CreateWithdrawRes 创建提现记录响应
type CreateWithdrawRes struct {
	UserWithdrawsId int `json:"user_withdraws_id" dc:"提现ID"`
}
