package main

import (
	"context"
	"wallet-api/internal/cmd"
	_ "wallet-api/internal/logic"
	_ "wallet-api/internal/packed"
	_ "wallet-api/internal/task" // Ensure task init is run

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
)

func main() {
	// 添加 Http 命令作为子命令
	cmd.Main.AddCommand(
		&cmd.Http, // 添加 HTTP 服务器命令
		&cmd.RunTask,
		&cmd.RunAllTask,
		&cmd.Task, // 添加新的任务调度命令
	)
	// 运行主命令，它会自动处理子命令的解析和执行
	cmd.Main.Run(context.Background())
}
