services:
  # HTTP API Service
  wallet-api-http:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wallet-api-http
    command: ["./wallet-api-http"]
    environment:
      - PATH_TO_SECRET_FILE=/app/config_variables.json
    ports:
      - "9999:9999"
    volumes:
      - ./config_variables.json:/app/config_variables.json:ro
      - ./logs:/app/logs:rw
    restart: unless-stopped
    networks:
      - xpay_app-network

  # Task Processing Service
  # wallet-api-task:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: wallet-api-task
  #   command: ["./wallet-api-task"]
  #   environment:
  #     - PATH_TO_SECRET_FILE=/app/config_variables.json
  #   volumes:
  #     - ./config_variables.json:/app/config_variables.json:ro
  #     - ./logs:/app/logs:rw
  #   restart: unless-stopped
  #   networks:
      # - xpay_app-network

networks:
  xpay_app-network:
    external: true
    name: xpay_app-network