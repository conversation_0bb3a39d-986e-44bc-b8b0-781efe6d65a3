#!/bin/bash
# Script to build multi-architecture Docker images

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Setup buildx builder if not exists
if ! docker buildx ls | grep -q "multiarch-builder"; then
    echo -e "${YELLOW}Creating multiarch builder...${NC}"
    docker buildx create --name multiarch-builder --driver docker-container --use
    docker buildx inspect --bootstrap
else
    echo -e "${GREEN}Using existing multiarch builder${NC}"
    docker buildx use multiarch-builder
fi

# Enable QEMU for ARM emulation
echo -e "${YELLOW}Enabling QEMU for cross-platform builds...${NC}"
docker run --rm --privileged multiarch/qemu-user-static --reset -p yes

# Function to build for specific platform
build_platform() {
    local platform=$1
    local tag=$2
    echo -e "${GREEN}Building for platform: ${platform}${NC}"
    docker buildx build --platform ${platform} -t ${tag} --load .
}

# Main menu
echo -e "${YELLOW}Select build option:${NC}"
echo "1. Build for current platform only (auto-detect)"
echo "2. Build for AMD64 only"
echo "3. Build for ARM64 only"
echo "4. Build for both platforms (requires registry push)"
echo "5. Build and push to registry (both platforms)"
read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        # Auto-detect current platform
        CURRENT_ARCH=$(uname -m)
        if [ "$CURRENT_ARCH" = "x86_64" ]; then
            build_platform "linux/amd64" "wallet-api:latest"
        elif [ "$CURRENT_ARCH" = "aarch64" ]; then
            build_platform "linux/arm64" "wallet-api:latest"
        else
            echo -e "${RED}Unknown architecture: $CURRENT_ARCH${NC}"
            exit 1
        fi
        ;;
    2)
        build_platform "linux/amd64" "wallet-api:amd64"
        ;;
    3)
        build_platform "linux/arm64" "wallet-api:arm64"
        ;;
    4)
        echo -e "${YELLOW}Note: Multi-platform build requires pushing to a registry${NC}"
        echo -e "${YELLOW}Building both platforms without push (will create cache only)...${NC}"
        docker buildx build --platform linux/amd64,linux/arm64 -t wallet-api:multi-arch .
        echo -e "${GREEN}Build complete! Use option 5 to push to registry.${NC}"
        ;;
    5)
        read -p "Enter your registry/image name (e.g., myregistry/wallet-api): " registry_image
        read -p "Enter tag (default: latest): " tag
        tag=${tag:-latest}
        echo -e "${YELLOW}Building and pushing to ${registry_image}:${tag}...${NC}"
        docker buildx build --platform linux/amd64,linux/arm64 -t ${registry_image}:${tag} --push .
        ;;
    *)
        echo -e "${RED}Invalid choice!${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}Build process complete!${NC}"