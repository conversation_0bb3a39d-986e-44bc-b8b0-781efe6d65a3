package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"wallet-api/internal/model/entity"
	"wallet-api/internal/utility/utils"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
)

// RecoverMnemonic 恢复助记词工具
type RecoverMnemonic struct {
	db gdb.DB
}

// NewRecoverMnemonic 创建恢复工具实例
func NewRecoverMnemonic() *RecoverMnemonic {
	return &RecoverMnemonic{
		db: g.DB(),
	}
}

// Recover 执行助记词恢复
func (r *RecoverMnemonic) Recover(ctx context.Context, mnemonic, password string) error {
	// 1. 获取钱包记录
	wallet, err := r.getWallet(ctx)
	if err != nil {
		return fmt.Errorf("获取钱包记录失败: %v", err)
	}

	if wallet == nil {
		return fmt.Errorf("钱包记录不存在")
	}

	// 2. 检查必要的加密参数
	if wallet.MnemonicSalt == nil || len(wallet.MnemonicSalt) == 0 {
		return fmt.Errorf("助记词盐值缺失，无法恢复")
	}

	if wallet.MnemonicIterations <= 0 {
		return fmt.Errorf("助记词迭代次数无效: %d", wallet.MnemonicIterations)
	}

	// 3. 使用现有的盐值和迭代次数重新加密助记词
	encryptedMnemonic, err := utils.EncryptStringWithPBKDF2(
		ctx,
		mnemonic,
		password,
		wallet.MnemonicSalt,
		wallet.MnemonicIterations,
	)
	if err != nil {
		return fmt.Errorf("加密助记词失败: %v", err)
	}

	// 4. 更新数据库中的助记词字段
	err = r.updateMnemonic(ctx, wallet.Id, encryptedMnemonic)
	if err != nil {
		return fmt.Errorf("更新数据库失败: %v", err)
	}

	// 5. 验证恢复是否成功
	err = r.verifyRecovery(ctx, wallet.Id, mnemonic, password)
	if err != nil {
		return fmt.Errorf("验证恢复失败: %v", err)
	}

	fmt.Printf("✅ 助记词恢复成功！钱包ID: %d\n", wallet.Id)
	return nil
}

// getWallet 获取钱包记录
func (r *RecoverMnemonic) getWallet(ctx context.Context) (*entity.Wallets, error) {
	var wallet entity.Wallets
	err := r.db.Model("wallets").Scan(&wallet)
	if err != nil {
		return nil, err
	}

	// 检查是否找到记录
	if wallet.Id == 0 {
		return nil, nil
	}

	return &wallet, nil
}

// updateMnemonic 更新数据库中的助记词字段
func (r *RecoverMnemonic) updateMnemonic(ctx context.Context, walletId int, encryptedMnemonic string) error {
	_, err := r.db.Model("wallets").
		Where("id", walletId).
		Update(g.Map{
			"mnemonic": encryptedMnemonic,
		})
	return err
}

// verifyRecovery 验证恢复是否成功
func (r *RecoverMnemonic) verifyRecovery(ctx context.Context, walletId int, originalMnemonic, password string) error {
	// 重新获取钱包记录
	wallet, err := r.getWalletById(ctx, walletId)
	if err != nil {
		return fmt.Errorf("重新获取钱包记录失败: %v", err)
	}

	// 尝试解密验证
	decryptedMnemonic, err := utils.DecryptStringWithPBKDF2(
		ctx,
		wallet.Mnemonic,
		password,
		wallet.MnemonicSalt,
		wallet.MnemonicIterations,
	)
	if err != nil {
		return fmt.Errorf("解密验证失败: %v", err)
	}

	if decryptedMnemonic != originalMnemonic {
		return fmt.Errorf("验证失败：解密后的助记词与原始助记词不匹配")
	}

	return nil
}

// getWalletById 根据ID获取钱包记录
func (r *RecoverMnemonic) getWalletById(ctx context.Context, id int) (*entity.Wallets, error) {
	var wallet entity.Wallets
	err := r.db.Model("wallets").Where("id", id).Scan(&wallet)
	if err != nil {
		return nil, err
	}
	return &wallet, nil
}

func main() {
	ctx := gctx.New()

	// 检查命令行参数
	if len(os.Args) != 3 {
		fmt.Println("使用方法: go run recover_mnemonic.go \"助记词\" \"密码\"")
		fmt.Println("示例: go run recover_mnemonic.go \"word1 word2 word3 ... word24\" \"your_password\"")
		os.Exit(1)
	}

	mnemonic := os.Args[1]
	password := os.Args[2]

	// 验证助记词格式（简单检查）
	if len(mnemonic) < 10 {
		log.Fatal("❌ 助记词格式不正确，请提供完整的助记词")
	}

	if len(password) < 1 {
		log.Fatal("❌ 密码不能为空")
	}

	fmt.Println("🔧 开始恢复助记词...")
	fmt.Printf("📝 助记词长度: %d 字符\n", len(mnemonic))
	fmt.Printf("🔑 密码长度: %d 字符\n", len(password))

	// 创建恢复工具并执行恢复
	recoverer := NewRecoverMnemonic()
	err := recoverer.Recover(ctx, mnemonic, password)
	if err != nil {
		log.Fatalf("❌ 恢复失败: %v", err)
	}

	fmt.Println("🎉 助记词恢复完成！")
}
