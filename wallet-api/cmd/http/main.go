package main

import (
	"wallet-api/internal/cmd" // 确保这是正确的模块路径

	// "github.com/gogf/gf/v2/os/gcmd" // 移除未使用的导入

	"github.com/gogf/gf/v2/os/gctx"

	// Import the task logic package
	// Import the task registry

	// 恢复导入 cmd 包以访问 Main
	_ "wallet-api/internal/logic/redis"
	_ "wallet-api/internal/task" // Import task logic to ensure registration

	// _ "wallet-api/internal/logic/transaction" // Import transaction logic to ensure registration
	_ "wallet-api/internal/logic/user_recharges"
	_ "wallet-api/internal/logic/wallet"
	_ "wallet-api/internal/packed"

	_ "wallet-api/internal/logic"
	_ "wallet-api/internal/packed"
	_ "wallet-api/internal/task" // Ensure task init is run

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2" // Add Redis adapter import

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	
)

func main() {
	cmd.Http.Run(gctx.New())
}
