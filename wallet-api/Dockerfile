# Multi-stage Dockerfile for wallet-api project
# Stage 1: Builder
FROM golang:1.24-alpine AS builder

# Build ARGs for multi-platform support
ARG TARGETARCH
ARG TARGETOS

# Set environment variables for Go build
ENV GO111MODULE=on \
    CGO_ENABLED=1 \
    GOOS=${TARGETOS:-linux} \
    GOARCH=${TARGETARCH:-arm64}

# Install build dependencies
RUN apk update && apk add --no-cache \
    gcc \
    musl-dev \
    git \
    make \
    binutils-gold \
    ca-certificates \
    tzdata
# Set timezone
ENV TZ=Asia/Shanghai
RUN apk --no-cache add tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

# Set working directory
WORKDIR /build

# Copy go mod files first for better caching
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download
RUN go mod verify

# Copy source code
COPY . .

# Build the applications
# RUN go build -tags musl -ldflags '-extldflags "-static" -s -w' -o ./wallet-api-http cmd/http/main.go
# RUN go build -tags musl -ldflags '-extldflags "-static" -s -w' -o ./wallet-api-task cmd/task/main.go
# RUN go build -tags musl -ldflags '-extldflags "-static" -s -w' -o ./wallet-api-main main.go
RUN CC=gcc go build -tags musl -ldflags '-extldflags "-static" -s -w' -o ./wallet-api-http cmd/http/main.go
RUN CC=gcc go build -tags musl -ldflags '-extldflags "-static" -s -w' -o ./wallet-api-task cmd/task/main.go
RUN CC=gcc go build -tags musl -ldflags '-extldflags "-static" -s -w' -o ./wallet-api-main main.go

# Verify binaries
RUN ls -la wallet-api-*

# Stage 2: Runtime
FROM alpine:latest

# Install runtime dependencies
RUN apk update && apk add --no-cache \
    bash \
    jq \
    gettext \
    util-linux \
    curl \
    tzdata \
    ca-certificates && \
    rm -rf /var/cache/apk/*

# Create app user
RUN addgroup -g 1001 -S app && \
    adduser -S -D -H -u 1001 -h /app -s /sbin/nologin -G app -g app app

# Set working directory
WORKDIR /app

# Copy binaries from builder
COPY --from=builder /build/wallet-api-http ./
COPY --from=builder /build/wallet-api-task ./
COPY --from=builder /build/wallet-api-main ./

# Copy configuration and resources
COPY --from=builder /build/manifest ./manifest
COPY --from=builder /build/resource ./resource
COPY --from=builder /build/entrypoint.sh ./

# Make binaries and scripts executable
RUN chmod +x wallet-api-* entrypoint.sh

# Create necessary directories for logs with proper permissions
RUN mkdir -p logs/database && \
    chmod -R 777 logs

# Change ownership to app user
RUN chown -R app:app /app

# Switch to app user
USER app

# Set entrypoint
ENTRYPOINT ["./entrypoint.sh"]

# Default command (can be overridden)
CMD ["./wallet-api-main"]
