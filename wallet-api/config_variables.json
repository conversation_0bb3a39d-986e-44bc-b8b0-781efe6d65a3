{"WALLET_API_ETH_RPC_URL": "http://************:8545", "WALLET_API_TRON_HTTP_RPC_URL": "************:50051", "WALLET_API_TRON_GRPC_RPC_URL": "************:50051", "WALLET_API_TRON_API_KEY_PLACEHOLDER": "171a479b-2062-4e80-a91a-e38f9da6d803", "WALLET_API_USDT_ERC20_CONTRACT": "******************************************", "WALLET_API_USDT_TRC20_CONTRACT": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "WALLET_API_ETH_CONFIRMATIONS": 5, "WALLET_API_TRON_CONFIRMATIONS": 5, "WALLET_API_ETH_DECIMALS": 18, "WALLET_API_ERCUSDT_DECIMALS": 6, "WALLET_API_TRON_DECIMALS": 6, "WALLET_API_TRC20USDT_DECIMALS": 6, "WALLET_API_BLOCKCHAIN_DECIMALS_ETH": 18, "WALLET_API_BLOCKCHAIN_DECIMALS_TRON": 6, "WALLET_API_BLOCKCHAIN_DECIMALS_ERC20USDT": 6, "WALLET_API_BLOCKCHAIN_DECIMALS_TRC20USDT": 6, "WALLET_API_BLOCKCHAIN_ETH_RPCURL": "http://************:8545", "WALLET_API_BLOCKCHAIN_ETH_ERC20CONTRACTADDRESS": "******************************************", "WALLET_API_BLOCKCHAIN_TRON_RPCURL": "************:50051", "WALLET_API_BLOCKCHAIN_TRON_TRC20CONTRACTADDRESS": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "WALLET_API_BLOCKCHAIN_TRON_APIKEY": "171a479b-2062-4e80-a91a-e38f9da6d803", "WALLET_API_APP_TOKEN": "86e63351-8427-49d7-80ce-1ceeea8dd2d7", "WALLET_API_SERVER_ADDRESS": ":9999", "WALLET_API_SERVER_OPENAPIPATH": "/api.json", "WALLET_API_SERVER_SWAGGERPATH": "/swagger", "WALLET_API_SERVER_DUMPROUTERMAP": false, "WALLET_API_LOGGER_LEVEL": "all", "WALLET_API_LOGGER_STDOUT": true, "WALLET_API_DATABASE_LOGGER_PATH": "logs/database", "WALLET_API_DATABASE_LOGGER_LEVEL": "all", "WALLET_API_DATABASE_LOGGER_STDOUT": true, "WALLET_API_DATABASE_DEFAULT_LINK": "mysql:root:root@tcp(mysql:3306)/wallet?loc=Local&parseTime=true&charset=utf8mb4&timeout=30s&readTimeout=30s&writeTimeout=30s", "WALLET_API_DATABASE_DEFAULT_DEBUG": false, "WALLET_API_AUTH_EXPIRESECONDS": 3600, "WALLET_API_AUTH_REFRESHTOKENEXPIRESECONDS": 604800, "WALLET_API_AUTH_REFRESH": 600, "WALLET_API_AUTH_ATTEMPT": 5, "WALLET_API_JWT_SECRET": "YOUR_VERY_SECURE_AND_UNIQUE_JWT_SECRET_KEY_PLEASE_REPLACE", "WALLET_API_REDIS_DEFAULT_ADDRESS": "valkey:6379", "WALLET_API_REDIS_DEFAULT_PASS": "valkey_password", "WALLET_API_REDIS_DEFAULT_DB": 5, "WALLET_API_DEPOSITCHECK_ENABLED": true, "WALLET_API_DEPOSITCHECK_SPEC": "# * * * * *", "WALLET_API_DEPOSITCHECK_CHAINS_ETH_ENABLED": false, "WALLET_API_DEPOSITCHECK_CHAINS_ETH_CONFIRMATIONS": 5, "WALLET_API_DEPOSITCHECK_CHAINS_ETH_NATIVESYMBOL": "ETH", "WALLET_API_DEPOSITCHECK_CHAINS_ETH_RPC": "http://************:8545", "WALLET_API_DEPOSITCHECK_CHAINS_ETH_TOKENS_USDT_ENABLED": true, "WALLET_API_DEPOSITCHECK_CHAINS_ETH_TOKENS_USDT_CONFIRMATIONS": 5, "WALLET_API_DEPOSITCHECK_CHAINS_ETH_TOKENS_USDT_CONTRACTADDRESS": "******************************************", "WALLET_API_DEPOSITCHECK_CHAINS_ETH_TOKENS_USDT_DECIMALS": 6, "WALLET_API_DEPOSITCHECK_CHAINS_TRON_ENABLED": true, "WALLET_API_DEPOSITCHECK_CHAINS_TRON_NATIVESYMBOL": "TRX", "WALLET_API_DEPOSITCHECK_CHAINS_TRON_CONFIRMATIONS": 5, "WALLET_API_DEPOSITCHECK_CHAINS_TRON_RPC": "************:50051", "WALLET_API_DEPOSITCHECK_CHAINS_TRON_HTTPRPC": "************:50051", "WALLET_API_DEPOSITCHECK_CHAINS_TRON_APIKEY": "171a479b-2062-4e80-a91a-e38f9da6d803", "WALLET_API_DEPOSITCHECK_CHAINS_TRON_TOKENS_USDT_ENABLED": true, "WALLET_API_DEPOSITCHECK_CHAINS_TRON_TOKENS_USDT_CONTRACTADDRESS": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "WALLET_API_DEPOSITCHECK_CHAINS_TRON_TOKENS_USDT_DECIMALS": 6, "WALLET_API_DEPOSITCHECK_CHAINS_TRON_TOKENS_USDT_CONFIRMATIONS": 5, "WALLET_API_DEPOSITCONFIRM_ENABLED": true, "WALLET_API_DEPOSITCONFIRM_SPEC": "# * * * * *", "WALLET_API_FEEHANDLER_ENABLED": true, "WALLET_API_FEEHANDLER_SPEC": "# * * * * *", "WALLET_API_FEEHANDLER_VERIFYSPEC": "# * * * * *", "WALLET_API_WITHDRAWPLANPROCESSOR_ENABLED": true, "WALLET_API_WITHDRAWPLANPROCESSOR_SPEC": "*/10 * * * * *", "WALLET_API_BALANCECOLLECTOR_ENABLED": true, "WALLET_API_BALANCECOLLECTOR_SPEC": "* 0 * * * *", "WALLET_API_WITHDRAWALPROCESSOR_ENABLED": true, "WALLET_API_WITHDRAWALPROCESSOR_SPEC": "*/10 * * * * *", "WALLET_API_WITHDRAWALPROCESSOR_BATCHSIZE": 100, "WALLET_API_WITHDRAWALPROCESSOR_RETRY_MAXRETRIES": 3, "WALLET_API_WITHDRAWALPROCESSOR_RETRY_RETRYINTERVALSEC": 5, "WALLET_API_WITHDRAWFEEHANDLER_ENABLED": true, "WALLET_API_WITHDRAWFEEHANDLER_SPEC": "*/10 * * * * *", "WALLET_API_WITHDRAWFEEHANDLER_BATCHSIZE": 100, "WALLET_API_WITHDRAWFEEHANDLER_GASFEE_ETH_AMOUNT": "0.01", "WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_APIKEY": "A44137431E0D402AB441DB0E06B5D257", "WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_APISECRET": "997EBBDC83C0161B8902F2CC1685DCA4F7F7B562118CC186599EC6834FFF4AAD", "WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_APIBASEURL": "https://itrx.io", "WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_PERIOD": "1H", "WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_ENERGY": 65000, "WALLET_API_WITHDRAWFEEHANDLER_GASFEE_TRON_MODEL": "order", "WALLET_API_WITHDRAWSTEP3PROCESSING_ENABLED": true, "WALLET_API_WITHDRAWSTEP3PROCESSING_SPEC": "*/10 * * * * *", "WALLET_API_WITHDRAWSTEP3PROCESSING_BATCHSIZE": 1000, "WALLET_API_WITHDRAWSTEP4PROCESSING_ENABLED": true, "WALLET_API_WITHDRAWSTEP4PROCESSING_SPEC": "*/10 * * * * *", "WALLET_API_WITHDRAWSTEP4PROCESSING_BATCHSIZE": 1000, "WALLET_API_WITHDRAWSTEP4PROCESSING_MAXRETRIES": 30, "WALLET_API_WITHDRAWSTEP4PROCESSING_DEFAULTCONFIRMATIONS": 1, "WALLET_API_WITHDRAWSTEP4PROCESSING_MINWAITMINUTES": 1, "WALLET_API_WITHDRAWSTEP4PROCESSING_CHAINS_ETH_CONFIRMATIONS": 1, "WALLET_API_WITHDRAWSTEP4PROCESSING_CHAINS_TRON_CONFIRMATIONS": 1, "WALLET_API_SECURITY_PBKDF2ITERATIONS": 310000, "WALLET_API_WALLETSAPI_BASEURL": "http://wallets:8080"}