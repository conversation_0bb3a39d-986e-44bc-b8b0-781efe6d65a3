# Wallet API 项目结构文档

## 项目概述

这是一个基于 GoFrame 框架开发的多链钱包 API 系统，支持以太坊(ETH)和波场(TRON)网络，提供钱包管理、地址生成、充值检测、提现处理等功能。项目采用微服务架构，支持定时任务调度和 HTTP API 服务。

## 项目根目录结构

```
wallet-api/
├── README.MD                    # 项目说明文档，包含任务运行命令
├── go.mod                       # Go 模块依赖管理文件
├── go.sum                       # Go 模块依赖校验文件
├── main.go                      # 主程序入口，集成 HTTP 和任务调度命令
├── Dockerfile                   # Docker 容器构建文件
├── docker-compose.yml           # Docker Compose 编排文件
├── entrypoint.sh               # Docker 容器启动脚本
├── Makefile                    # 构建和部署脚本
├── config_variables.json       # 配置变量定义文件
├── wallet-api                  # 编译后的可执行文件
├── api/                        # API 接口定义层
├── cmd/                        # 命令行程序入口
├── internal/                   # 内部业务逻辑实现
├── manifest/                   # 配置文件和部署清单
├── resource/                   # 静态资源文件
├── utility/                    # 工具类库
├── hack/                       # 开发工具和脚本
├── git-tools/                  # Git 相关工具脚本
├── doc/                        # 项目文档
├── docs/                       # 技术文档
└── logs/                       # 日志文件目录
```

## API 接口层 (api/)

### api/wallet/
- **wallet.go** - 自动生成的钱包接口定义，包含所有钱包相关的接口方法签名
- **v1/** - API v1 版本接口定义
  - **wallet.go** - 钱包基础功能接口（状态检查、生成、创建等）
  - **auth.go** - 认证相关接口（登录、Token 刷新等）
  - **address.go** - 地址管理接口（批量创建、查询、导出等）
  - **setting.go** - 钱包设置接口（归集策略、费用配置等）
  - **public.go** - 公共接口（无需认证的接口）
  - **withdraw.go** - 提现相关接口
  - **recharge.go** - 充值相关接口
  - **transaction.go** - 交易记录接口

## 命令行入口 (cmd/)

### cmd/http/
- **main.go** - HTTP 服务器启动入口，提供 REST API 服务

### cmd/task/
- **main.go** - 任务调度器启动入口，运行定时任务

## 内部实现层 (internal/)

### internal/boot/
- **system.go** - 系统初始化模块，负责启动时的资源初始化

### internal/cmd/
- **cmd.go** - 主命令定义，集成系统初始化
- **http.go** - HTTP 服务器命令实现
- **task.go** - 任务调度器命令实现
- **run_task.go** - 单个任务运行命令
- **run_all_task.go** - 所有任务运行命令

### internal/codes/
错误码和消息定义模块
- **codes.go** - 通用错误码定义
- **wallet.go** - 钱包相关错误码
- **task.go** - 任务相关错误码
- **repository.go** - 数据访问层错误码
- **merchant.go** - 商户相关错误码
- **withdraw_plan.go** - 提现计划错误码
- **utility.go** - 工具类错误码
- **kafka.go** - Kafka 相关错误码
- **boot.go** - 启动相关错误码

### internal/consts/
常量定义模块
- **consts.go** - 通用常量定义
- **network.go** - 网络相关常量（ETH、TRON 等）
- **task.go** - 任务相关常量
- **transactions.go** - 交易相关常量

### internal/controller/wallet/
HTTP 控制器层，处理 API 请求
- **wallet_new.go** - 控制器工厂方法
- **wallet_v1_*.go** - 各个 API 接口的具体实现
  - 钱包管理：create_wallet, generate_wallet, reset_wallet, check_status
  - 认证授权：auth, refresh_token, logout, change_password
  - 地址管理：batch_create_address, get_address_list, export_address
  - 设置配置：wallet_setting, base_setting
  - 数据查询：dashboard_statistic, get_wallet_info
  - 交易记录：get_transaction_record, export_transaction_record
  - 充值提现：get_recharge_record, get_withdraw_record, create_withdraw

### internal/dao/
数据访问对象层，封装数据库操作
- **address.go** / **address_interface.go** - 地址数据访问
- **wallets.go** / **wallets_interface.go** - 钱包数据访问
- **transactions.go** / **transactions_interface.go** - 交易数据访问
- **user_recharges.go** - 用户充值数据访问
- **user_withdraws.go** - 用户提现数据访问
- **tasks.go** / **tasks_interface.go** - 任务数据访问
- **task_address.go** / **task_address_interface.go** - 任务地址关联数据访问
- **withdraw_plan.go** - 提现计划数据访问
- **token_fee_supplements.go** - 代币手续费补充数据访问
- **system_config.go** / **system_config_interface.go** - 系统配置数据访问
- **operation_logs.go** / **operation_logs_interface.go** - 操作日志数据访问
- **rpc_request_logs.go** / **rpc_request_logs_interface.go** - RPC 请求日志数据访问
- **refresh_tokens.go** / **refresh_tokens_interface.go** - 刷新令牌数据访问
- **password_attempts.go** / **password_attempts_interface.go** - 密码尝试记录数据访问
- **progress_tasks.go** - 进度任务数据访问
- **system_init_status.go** - 系统初始化状态数据访问
- **internal/** - DAO 内部实现，包含具体的数据库操作逻辑

### internal/logic/
业务逻辑层，核心业务处理
- **logic.go** - 业务逻辑层初始化和注册

#### internal/logic/wallet/
钱包业务逻辑模块
- **wallet.go** - 钱包核心业务逻辑（创建、认证、设置等）
- **address.go** - 地址管理业务逻辑
- **auth.go** - 认证授权业务逻辑
- **setting.go** - 钱包设置业务逻辑
- **wallet_setting.go** - 钱包配置业务逻辑
- **recharge.go** - 充值业务逻辑
- **withdraw.go** - 提现业务逻辑
- **withdraw_plan.go** - 提现计划业务逻辑
- **collect.go** - 归集业务逻辑
- **bind_address.go** - 地址绑定业务逻辑
- **node.go** - 节点管理业务逻辑
- **address_task_progress.go** - 地址任务进度业务逻辑
- **wallet_token_fee_supplements.go** - 代币手续费补充业务逻辑

#### internal/logic/repository/
仓储模式实现，业务逻辑与数据访问的桥梁
- **wallet.go** / **wallet_impl.go** - 钱包仓储接口和实现
- **address.go** / **address_impl.go** - 地址仓储接口和实现
- **transaction.go** / **transaction_impl.go** - 交易仓储接口和实现
- **recharge.go** / **recharge_impl.go** - 充值仓储接口和实现
- **withdraw.go** / **withdraw_impl.go** - 提现仓储接口和实现
- **task.go** / **task_impl.go** - 任务仓储接口和实现
- **task_address.go** / **task_address_impl.go** - 任务地址仓储接口和实现
- **system_config.go** / **system_config_impl.go** - 系统配置仓储接口和实现
- **operation_log.go** / **operation_log_impl.go** - 操作日志仓储接口和实现
- **rpc_request_log.go** / **rpc_request_log_impl.go** - RPC 请求日志仓储接口和实现
- **password_attempt.go** / **password_attempt_impl.go** - 密码尝试仓储接口和实现
- **progress_tasks.go** / **progress_tasks_impl.go** - 进度任务仓储接口和实现
- **repository_utils.go** - 仓储工具类

#### internal/logic/redis/
Redis 缓存业务逻辑
- **redis.go** - Redis 服务初始化
- **client.go** - Redis 客户端管理
- **get.go**, **set.go** - 基础 GET/SET 操作
- **hget.go**, **hset.go**, **hgetall.go** - Hash 操作
- **incr.go**, **decr.go** - 计数器操作
- **lpush.go**, **rpop.go** - 列表操作
- **exists.go**, **remove.go** - 键存在性和删除操作
- **clear_cache.go** - 缓存清理
- **get_string.go** - 字符串获取
- **get_user_language.go**, **set_user_language.go** - 用户语言设置
- **get_payment_password_attempts.go** - 支付密码尝试次数获取
- **increment_payment_password_attempts.go** - 支付密码尝试次数递增
- **is_payment_password_locked.go** - 支付密码锁定状态检查
- **lock_payment_password.go** - 支付密码锁定
- **reset_payment_password_attempts.go** - 重置支付密码尝试次数

#### internal/logic/authservice/
认证服务业务逻辑
- **authservice.go** - 认证服务实现，处理 JWT Token 生成和验证

#### internal/logic/user_recharges/
用户充值业务逻辑
- **user_recharges.go** - 用户充值业务处理

#### internal/logic/ports/
端口适配器模式实现
- **config.go** - 配置端口适配器
- **deposit.go** - 充值端口适配器
- **scanner.go** - 扫描器端口适配器

### internal/model/
数据模型层，定义数据结构
- **wallet.go** - 钱包相关数据模型
- **user_recharges.go** - 用户充值数据模型

#### internal/model/entity/
数据库实体模型，对应数据库表结构
- **wallets.go** - 钱包表实体
- **address.go** - 地址表实体
- **transactions.go** - 交易表实体
- **user_recharges.go** - 用户充值表实体
- **user_withdraws.go** - 用户提现表实体
- **tasks.go** - 任务表实体
- **task_address.go** - 任务地址关联表实体
- **withdraw_plan.go** - 提现计划表实体
- **token_fee_supplements.go** - 代币手续费补充表实体
- **system_config.go** - 系统配置表实体
- **operation_logs.go** - 操作日志表实体
- **rpc_request_logs.go** - RPC 请求日志表实体
- **refresh_tokens.go** - 刷新令牌表实体
- **password_attempts.go** - 密码尝试记录表实体
- **progress_tasks.go** - 进度任务表实体
- **system_init_status.go** - 系统初始化状态表实体

#### internal/model/do/
数据操作对象，用于数据库操作的数据传输
- 与 entity 目录对应，包含相同名称的文件
- 用于数据库查询、插入、更新操作的数据结构定义

### internal/middleware/
中间件层，处理横切关注点

#### internal/middleware/auth/
认证中间件
- **jwt.go** - JWT Token 认证中间件
- **wallet.go** - 钱包验证中间件

### internal/router/
路由配置层
- **wallet.go** - 钱包相关路由配置，集成中间件和控制器

### internal/security/
安全模块

#### internal/security/credentialmanager/
凭证管理器
- **credentialmanager.go** - 密码和私钥的安全管理，包括加密存储和内存缓存

### internal/service/
服务层，提供可复用的业务服务
- **auth.go** - 认证服务
- **crypto.go** - 加密服务
- **redis.go** - Redis 服务
- **task.go** - 任务服务
- **user_recharges.go** - 用户充值服务
- **wallet.go** - 钱包服务

### internal/task/
定时任务模块，处理后台业务逻辑
- **init.go** - 任务模块初始化
- **task.go** - 任务基础定义

#### internal/task/balance_collector/
余额收集任务
- **balance_collector.go** - 余额收集任务实现
- **config.go** - 余额收集任务配置
- **init.go** - 余额收集任务初始化

#### internal/task/deposit_step1_check/
充值检查任务（第一步）
- **deposit_check_impl.go** - 充值检查任务实现
- **deposit_handler.go** - 充值处理器
- **data_preparation.go** - 数据准备模块
- **config.go** - 充值检查任务配置
- **init.go** - 充值检查任务初始化
- **scanner_factory.go** - 扫描器工厂
- **scanner/** - 区块链扫描器
  - **interface.go** - 扫描器接口定义
  - **eth_scanner.go** - 以太坊扫描器
  - **trx/** - 波场扫描器模块

#### internal/task/deposit_step2_confirm/
充值确认任务（第二步）
- **deposit_confirm_impl.go** - 充值确认任务实现
- **confirmation_fetcher.go** - 确认信息获取器
- **recharge_processor.go** - 充值处理器
- **config_helper.go** - 配置助手
- **init.go** - 充值确认任务初始化

#### internal/task/withdraw_step1_plan/
提现计划任务（第一步）
- **processor.go** - 提现计划处理器
- **init.go** - 提现计划任务初始化

#### internal/task/withdraw_step2_fee/
提现手续费处理任务（第二步）
- **fee_handler.go** - 手续费处理器
- **eth_handler.go** - 以太坊手续费处理器
- **tron_handler.go** - 波场手续费处理器
- **pending_processor.go** - 待处理订单处理器
- **processing_verifier.go** - 处理中订单验证器
- **utils.go** - 工具函数
- **config.go** - 手续费任务配置
- **init.go** - 手续费任务初始化

#### internal/task/withdraw_step3_processing/
提现处理任务（第三步）
- **processor.go** - 提现处理器
- **eth_handler.go** - 以太坊提现处理器
- **tron_handler.go** - 波场提现处理器
- **config.go** - 提现处理任务配置
- **init.go** - 提现处理任务初始化

#### internal/task/withdraw_step4_confirm/
提现确认任务（第四步）
- **processor.go** - 提现确认处理器
- **confirmation.go** - 确认逻辑
- **eth_transaction.go** - 以太坊交易确认
- **tron_transaction.go** - 波场交易确认
- **transaction_utils.go** - 交易工具函数
- **token_info.go** - 代币信息处理
- **constants.go** - 常量定义
- **config.go** - 提现确认任务配置
- **init.go** - 提现确认任务初始化

### internal/task_registry/
任务注册中心
- **registry.go** - 任务注册和调度管理

### internal/utility/
工具模块

#### internal/utility/config/
配置工具
- **contract.go** - 智能合约配置

#### internal/utility/crypto/
加密工具
- **client_manager.go** - 加密客户端管理器
- **eth/** - 以太坊加密工具
- **tron/** - 波场加密工具

#### internal/utility/jwtutil/
JWT 工具
- **jwt.go** - JWT Token 生成和验证
- **jwt_test.go** - JWT 工具测试

#### internal/utility/utils/
通用工具
- **util.go** - 通用工具函数
- **hash_utils.go** - 哈希工具函数
- **userdata.go** - 用户数据处理工具
- **error_logger.go** - 错误日志工具

### internal/其他模块
- **init.go** - 工具模块初始化
- **quicknode.go** - QuickNode 节点集成
- **quicknode_example.go** - QuickNode 使用示例
- **swagger.go** - Swagger 文档生成
- **packed/packed.go** - 打包资源管理
- **logutils/asynq_logger.go** - 异步队列日志工具
- **ports/** - 端口定义（配置、充值、扫描器）

## 配置和部署 (manifest/)

### manifest/config/
- **config.yaml** - 主配置文件，包含数据库、Redis、区块链节点等配置
- **config.yaml.template** - 配置文件模板，用于环境变量替换

### manifest/deploy/
- **kustomize/** - Kubernetes 部署配置
  - **base/** - 基础部署配置
  - **overlays/** - 环境特定配置覆盖

### manifest/docker/
- **Dockerfile** - Docker 镜像构建文件
- **docker.sh** - Docker 构建和部署脚本

### manifest/sql/
- **wallet.sql** - 数据库表结构定义和初始化脚本

### manifest/其他
- **i18n/** - 国际化配置目录
- **protobuf/** - Protocol Buffers 定义目录

## 静态资源 (resource/)

### resource/abi/
智能合约 ABI 定义
- **erc20usdtabi.json** - ERC20 USDT 合约 ABI
- **trc20usdtabi.json** - TRC20 USDT 合约 ABI

### resource/public/
公共静态资源
- **html/** - HTML 模板文件
- **plugin/** - 插件文件
- **resource/** - 静态资源文件
  - **css/** - 样式文件
  - **js/** - JavaScript 文件
  - **image/** - 图片文件

### resource/template/
模板文件目录

## 工具和脚本

### hack/
开发工具和脚本
- **config.yaml** - 开发配置
- **hack-cli.mk** - CLI 工具 Makefile
- **hack.mk** - 开发工具 Makefile

### git-tools/
Git 相关工具
- **daily_push_summary_gitlab.sh** - GitLab 每日推送摘要脚本

### utility/
外部工具库目录

## 文档目录

### doc/
项目文档
- **docker-compose.yml** - Docker Compose 配置示例
- **pbkdf2_wallet_encryption_plan.md** - PBKDF2 钱包加密方案
- **plan_completion_status.md** - 计划完成状态文档
- **secure_wallet_unlock_plan.md** - 安全钱包解锁方案
- **withdraw_gas_estimation_plan.md** - 提现 Gas 估算方案
- **Okay, based on your clarifications and a.md** - 其他说明文档

### docs/
技术文档
- **TRON_USDT_FEE_LIMIT_FIX.md** - TRON USDT 手续费限制修复文档

## 日志目录 (logs/)

### logs/database/
数据库日志
- **2025-05-22.log** - 按日期分割的数据库操作日志
- **2025-05-23.log** - 数据库日志文件

## 核心业务流程

### 1. 钱包管理流程
1. **钱包初始化** - 生成助记词 → 创建钱包 → 设置密码和 Google 验证
2. **钱包认证** - 密码验证 → Google 验证码验证 → JWT Token 生成
3. **地址管理** - 批量生成地址 → 地址绑定 → 地址导出

### 2. 充值处理流程
1. **BalanceCollectorTask** - 定时收集地址余额变化
2. **DepositCheckTask** - 检查充值交易，生成充值记录
3. **DepositConfirmTask** - 确认充值交易，生成提现计划

### 3. 提现处理流程
1. **WithdrawPlanProcessorTask** - 根据计划生成手续费记录
2. **WithdrawFeeHandlerTask** - 发放矿工费，检查完成状态
3. **WithdrawStep3ProcessingTask** - 执行提币操作
4. **WithdrawStep4ConfirmTask** - 确认提现交易完成

### 4. 支持的区块链网络
- **以太坊 (ETH)** - 支持 ETH 和 ERC20 USDT
- **波场 (TRON)** - 支持 TRX 和 TRC20 USDT

## 技术栈

- **框架**: GoFrame v2.9.0
- **数据库**: MySQL (通过 GoFrame MySQL 驱动)
- **缓存**: Redis (通过 GoFrame Redis 驱动)
- **区块链**: Ethereum (go-ethereum), TRON (gotron-sdk)
- **认证**: JWT (golang-jwt/jwt)
- **加密**: PBKDF2, AES, BIP32/BIP39
- **任务调度**: GoFrame Cron
- **容器化**: Docker, Docker Compose
- **部署**: Kubernetes (Kustomize)

## 启动方式

### HTTP 服务器
```bash
go run main.go  # 默认启动 HTTP 服务器
# 或
go run cmd/http/main.go
```

### 任务调度器
```bash
go run main.go task  # 启动任务调度器
# 或
go run cmd/task/main.go
```

### 单个任务执行
```bash
go run main.go run-task --name BalanceCollectorTask
go run main.go run-task --name DepositCheckTask
go run main.go run-task --name DepositConfirmTask
go run main.go run-task --name WithdrawPlanProcessorTask
go run main.go run-task --name WithdrawFeeHandlerTask
go run main.go run-task --name WithdrawStep3ProcessingTask
go run main.go run-task --name WithdrawStep4ConfirmTask
```

## 安全特性

1. **密码加密** - 使用 PBKDF2 进行密码哈希
2. **私钥保护** - 助记词和私钥使用 AES 加密存储
3. **双因子认证** - 集成 Google Authenticator TOTP
4. **JWT 认证** - API 访问需要有效的 JWT Token
5. **密码尝试限制** - Redis 缓存密码错误尝试次数
6. **操作日志** - 记录所有关键操作的审计日志

这个钱包 API 系统提供了完整的多链钱包管理解决方案，支持自动化的充值检测和提现处理，具备高安全性和可扩展性。
