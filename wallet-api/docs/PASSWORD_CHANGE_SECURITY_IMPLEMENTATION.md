# 密码修改安全实现方案

## 问题描述

在原有系统中，修改密码时只更新了密码哈希，但没有重新加密钱包中的敏感数据（助记词、手续费私钥、谷歌验证码密钥）。这导致了严重的安全漏洞：

- **旧密码仍然可以解密敏感数据**：即使用户修改了密码，攻击者如果获得了旧密码和加密的敏感数据，仍然可以解密这些信息
- **密码修改失去意义**：从安全角度来看，修改密码应该使旧密码完全失效

## 解决方案

### 核心思路

修改密码时，需要：
1. 使用旧密码解密所有敏感数据
2. 生成新的盐值和迭代次数
3. 使用新密码重新加密所有敏感数据
4. 在数据库事务中原子性更新所有字段
5. 更新内存中的密码缓存

### 涉及的敏感数据

- **助记词** (mnemonic) - 使用 mnemonic_salt + mnemonic_iterations
- **谷歌验证码密钥** (google_code) - 使用 google_secret_salt + google_secret_iterations  
- **ETH手续费私钥** (eth_fee_private_key) - 使用 mnemonic_salt + mnemonic_iterations
- **TRX手续费私钥** (trx_fee_private_key) - 使用 mnemonic_salt + mnemonic_iterations

## 实现细节

### 1. Repository 层新增方法

在 `IWalletRepository` 接口中新增：

```go
// UpdatePasswordAndReencryptSensitiveData updates password and re-encrypts all sensitive data in a transaction.
UpdatePasswordAndReencryptSensitiveData(ctx context.Context, newPasswordHash string, 
    newEncryptedMnemonic string, newMnemonicSalt []byte, newMnemonicIterations int,
    newEncryptedGoogleCode string, newGoogleSecretSalt []byte, newGoogleSecretIterations int,
    newEncryptedEthFeePrivateKey string, newEncryptedTrxFeePrivateKey string) error
```

### 2. 数据库事务实现

在 `wallet_impl.go` 中实现事务处理：

```go
func (r *walletRepository) UpdatePasswordAndReencryptSensitiveData(...) error {
    // 使用数据库事务确保原子性
    err := r.walletDao.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
        // 准备更新数据
        data := g.Map{
            "password_hash":              newPasswordHash,
            "mnemonic":                   newEncryptedMnemonic,
            "mnemonic_salt":              newMnemonicSalt,
            "mnemonic_iterations":        newMnemonicIterations,
            "google_code":                newEncryptedGoogleCode,
            "google_secret_salt":         newGoogleSecretSalt,
            "google_secret_iterations":   newGoogleSecretIterations,
            "updated_at":                 gtime.Now(),
        }
        
        // 只有在私钥不为空时才更新
        if newEncryptedEthFeePrivateKey != "" {
            data["eth_fee_private_key"] = newEncryptedEthFeePrivateKey
        }
        if newEncryptedTrxFeePrivateKey != "" {
            data["trx_fee_private_key"] = newEncryptedTrxFeePrivateKey
        }
        
        // 在事务中执行更新
        _, err := tx.Model("wallets").Data(data).Update()
        return err
    })
    
    return err
}
```

### 3. 业务逻辑层重新加密

在 `auth.go` 中新增 `_reencryptSensitiveDataWithNewPassword` 方法：

1. **解密阶段**：使用旧密码解密所有敏感数据
2. **重新加密阶段**：生成新的盐值，使用新密码重新加密
3. **数据库更新**：在事务中更新所有字段
4. **缓存更新**：更新内存中的密码缓存

### 4. 修改 ChangePassword 方法

```go
func (s *sWallet) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error) {
    // 验证旧密码和谷歌验证码
    // ...
    
    // 生成新的密码哈希
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
    if err != nil {
        return nil, codes.WrapError(err, codes.CodeWalletEncryptPasswordFailed)
    }

    // 重新加密所有敏感数据
    err = s._reencryptSensitiveDataWithNewPassword(ctx, wallet, req.OldPassword, req.NewPassword, string(hashedPassword))
    if err != nil {
        return nil, err
    }

    // 更新内存中的密码缓存
    credentialmanager.SetPassword(ctx, req.NewPassword, wallet)

    return &v1.ChangePasswordRes{Success: true}, nil
}
```

## 安全保证

### 1. 数据库事务保证原子性

所有敏感数据的更新在同一个数据库事务中执行，确保要么全部成功，要么全部回滚，避免数据不一致。

### 2. 新盐值增强安全性

每次修改密码时都会生成新的盐值，即使使用相同的新密码，加密结果也会不同，增强了安全性。

### 3. 旧密码完全失效

修改密码后，旧密码无法解密任何敏感数据，确保了密码修改的安全意义。

### 4. 内存缓存同步更新

修改密码后立即更新内存中的密码缓存，确保后续操作使用新密码。

## 测试验证

创建了完整的测试用例验证：

1. ✅ 原始数据可以用旧密码解密
2. ✅ 重新加密过程正常执行
3. ✅ 新数据可以用新密码解密
4. ✅ 旧密码无法解密新数据
5. ✅ 密码哈希正确更新

## 影响范围

### 修改的文件

- `internal/logic/repository/wallet.go` - 新增接口方法
- `internal/logic/repository/wallet_impl.go` - 实现事务更新方法
- `internal/dao/wallets_interface.go` - 新增 Transaction 方法
- `internal/logic/wallet/auth.go` - 重写 ChangePassword 和新增重新加密方法

### 向后兼容性

- 不影响现有的钱包创建和认证流程
- 不改变数据库表结构
- 不影响其他业务功能

## 部署建议

1. **测试环境验证**：在测试环境充分验证修改密码功能
2. **备份数据**：生产环境部署前备份钱包数据
3. **监控日志**：部署后监控密码修改相关的日志
4. **用户通知**：建议用户在更新后重新修改一次密码以确保安全

## 总结

此实现完全解决了密码修改的安全漏洞，确保修改密码后旧密码完全失效，无法解密任何敏感数据。通过数据库事务保证了操作的原子性，通过新盐值增强了安全性，是一个完整且安全的解决方案。
