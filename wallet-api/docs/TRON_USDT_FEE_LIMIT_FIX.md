# TRON USDT Transaction Fee Limit Fix

## Problem Description

TRON USDT transactions were failing with "Out of Energy" errors due to extremely low Energy Fee Limits (e.g., 0.0001 TRX). The transaction hash `bf13c7652e69aa89c9196c1775c39d775d74527d2facaf66e260cf5fa43d3678` failed because the Energy Fee Limit was set to only 0.0001 TRX (100 SUN), which is insufficient for TRC20 USDT transfers.

## Root Cause Analysis

1. **Database Configuration Issue**: The `trx_fee_max` field in the `wallets` table had a default value of only 100 SUN (0.0001 TRX), which is far too low for TRC20 transactions.

2. **Unit Confusion**: The code was treating `TrxFeeMax` as if it was stored in SUN units, but it's actually stored in TRX units in the database.

3. **Insufficient Minimum Limits**: There was no enforcement of minimum fee limits for TRC20 transactions, allowing dangerously low values to be used.

## Solution Implemented

### 1. Fixed Unit Conversion Logic

Updated `internal/task/withdraw_step3_processing/tron_handler.go` to properly handle TRX to SUN conversion:

```go
// TrxFeeMax is stored in TRX units in the database, but feeLimit needs SUN units
if cfg.WalletConfig.TrxFeeMax > 0 {
    configuredFeeLimitTRX := cfg.WalletConfig.TrxFeeMax
    
    // Ensure the configured fee limit is at least the minimum required for TRC20
    if configuredFeeLimitTRX >= minTrc20FeeLimitTRX {
        // Convert TRX to SUN (1 TRX = 1,000,000 SUN)
        feeLimit = configuredFeeLimitTRX * 1_000_000
    } else {
        // Use minimum instead of dangerously low configured value
        feeLimit = minTrc20FeeLimitTRX * 1_000_000
    }
}
```

### 2. Enforced Minimum Fee Limits

- Set minimum fee limit for TRC20 USDT transactions to 5 TRX (5,000,000 SUN)
- Default fee limit remains 30 TRX (30,000,000 SUN) when no configuration is provided
- Added warning logs when configured values are too low

### 3. Database Migration

Created migration script `manifest/sql/migrations/001_update_trx_fee_max.sql`:

```sql
-- Update existing wallets with TrxFeeMax < 5 to use 30 TRX as default
UPDATE wallets 
SET trx_fee_max = 30 
WHERE trx_fee_max < 5;

-- Update default value for new wallets
ALTER TABLE wallets 
MODIFY COLUMN trx_fee_max bigint NOT NULL DEFAULT 30 
COMMENT 'TRX交易最多支付手续费 (TRX units, minimum 5 TRX for TRC20 transactions)';
```

### 4. Added Comprehensive Testing

Created test suite `internal/task/withdraw_step3_processing/tron_handler_test.go` to verify:
- Correct fee limit calculation for various TrxFeeMax values
- Proper enforcement of minimum limits
- Accurate TRX to SUN conversion

## Key Changes Made

1. **File**: `internal/task/withdraw_step3_processing/tron_handler.go`
   - Fixed TRX to SUN conversion logic
   - Added minimum fee limit enforcement (5 TRX)
   - Improved logging for fee limit decisions

2. **File**: `manifest/sql/migrations/001_update_trx_fee_max.sql`
   - Database migration to update existing low values
   - Updated default value for new wallets

3. **File**: `internal/task/withdraw_step3_processing/tron_handler_test.go`
   - Comprehensive test suite for fee limit logic

## Expected Results

After applying this fix:

1. **Successful Transactions**: TRC20 USDT transactions should complete successfully with adequate fee limits
2. **No More "Out of Energy" Errors**: Minimum 5 TRX fee limit ensures sufficient resources
3. **Backward Compatibility**: Existing high fee limit configurations remain unchanged
4. **Safety**: Low fee limit configurations are automatically upgraded to safe minimums

## Verification Steps

1. Run the test suite: `go test ./internal/task/withdraw_step3_processing -v`
2. Apply the database migration
3. Monitor TRON USDT transactions for successful completion
4. Check logs for proper fee limit usage

## Notes

- TRC20 USDT transactions typically require around 65,000 energy units
- Without sufficient energy, transactions must pay TRX fees which can be substantial
- The energy rental system should work in conjunction with proper fee limits
- 5 TRX minimum provides a safety buffer for most TRC20 transactions
- 30 TRX default should handle even high-cost scenarios without energy rental

## Related Memory

This fix addresses the issue documented in the system memory:
> "TRON USDT transactions can fail with 'Out of Energy' error when Energy Fee Limit is set too low (e.g., 0.0001 TRX), requiring higher energy fee limits for successful transfers."
