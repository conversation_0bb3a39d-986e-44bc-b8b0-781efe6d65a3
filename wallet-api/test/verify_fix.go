package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"wallet-api/internal/ports"
	"wallet-api/internal/service"
	trxscanner "wallet-api/internal/task/deposit_step1_check/scanner/trx"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
)

// 测试地址
const testAddress = "TN69oECxenBUBtE8Z5uc36gUCt2PzrFRpd"

// 模拟的存款处理函数
func mockHandleDeposit(ctx context.Context, rechargeService service.IUserRecharges, deposit ports.DepositInfo) error {
	fmt.Printf("=== 发现充值记录 ===\n")
	fmt.Printf("链: %s\n", deposit.Chain)
	fmt.Printf("代币: %s\n", deposit.TokenSymbol)
	fmt.Printf("交易哈希: %s\n", deposit.TxHash)
	fmt.Printf("发送地址: %s\n", deposit.FromAddress)
	fmt.Printf("接收地址: %s\n", deposit.ToAddress)
	fmt.Printf("金额: %s\n", deposit.Amount)
	fmt.Printf("区块号: %d\n", deposit.BlockNumber)
	fmt.Printf("用户ID: %d\n", deposit.UserID)
	fmt.Printf("代币ID: %d\n", deposit.TokenID)
	fmt.Printf("==================\n\n")
	return nil
}

func main() {
	ctx := gctx.New()
	
	fmt.Printf("验证 TRX 扫描器修复效果\n")
	fmt.Printf("目标地址: %s\n\n", testAddress)

	// 获取 Redis 客户端
	redisClient := g.Redis()
	if redisClient == nil {
		log.Fatal("Failed to get Redis client")
	}

	// 准备链配置
	chainCfg := ports.ChainConfig{
		Enabled:       true,
		NativeSymbol:  "TRX",
		Confirmations: 5,
		Rpc:          "************:50051",
		HttpRpc:      "************:50051",
		ApiKey:       "171a479b-2062-4e80-a91a-e38f9da6d803",
		Tokens: map[string]ports.TokenConfig{
			"usdt": {
				Enabled:         true,
				ContractAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
				Decimals:        6,
			},
		},
	}

	// 准备地址映射
	nativeAddrToUser := map[string]uint{
		testAddress: 999,
	}
	
	usdtAddrToUser := map[string]uint{
		testAddress: 999,
	}
	
	nativeAddressesSet := map[string]bool{
		testAddress: true,
	}
	
	usdtAddressesSet := map[string]bool{
		testAddress: true,
	}
	
	originalNativeAddrs := map[string]string{
		testAddress: testAddress,
	}
	
	originalTokenAddrs := map[string]string{
		testAddress: testAddress,
	}

	// 设置测试起始区块
	testStartBlock := int64(74201150)
	redisKey := "deposit_check:last_block:trx"
	
	fmt.Printf("设置测试起始区块: %d\n", testStartBlock-1)
	_, err := redisClient.Set(ctx, redisKey, testStartBlock-1)
	if err != nil {
		log.Fatalf("Failed to set test start block: %v", err)
	}

	// 创建扫描器
	scanner, err := trxscanner.NewTrxScanner(
		redisClient,
		chainCfg,
		nativeAddrToUser,
		usdtAddrToUser,
		nativeAddressesSet,
		usdtAddressesSet,
		"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
		6,
		1,
		2,
		nil,
		mockHandleDeposit,
		originalNativeAddrs,
		originalTokenAddrs,
	)
	
	if err != nil {
		log.Fatalf("Failed to create TRX scanner: %v", err)
	}

	fmt.Printf("TRX 扫描器创建成功\n")
	fmt.Printf("监控地址: %s\n", testAddress)
	fmt.Printf("开始扫描...\n\n")

	// 执行扫描
	start := time.Now()
	
	// 监控 Redis 进度变化
	go func() {
		for i := 0; i < 30; i++ { // 监控 30 秒
			time.Sleep(1 * time.Second)
			currentBlock, err := redisClient.Get(ctx, redisKey)
			if err == nil && currentBlock != nil {
				fmt.Printf("[进度监控] 当前 Redis 中的区块号: %s\n", currentBlock.String())
			}
		}
	}()
	
	err = scanner.Scan(ctx)
	if err != nil {
		log.Printf("扫描过程中出现错误: %v", err)
	} else {
		fmt.Printf("扫描完成，耗时: %v\n", time.Since(start))
	}

	// 检查最终的 Redis 状态
	finalBlock, err := redisClient.Get(ctx, redisKey)
	if err != nil {
		log.Printf("Failed to get final block from Redis: %v", err)
	} else if finalBlock != nil {
		fmt.Printf("最终 Redis 中的区块号: %s\n", finalBlock.String())
	}

	fmt.Println("\n=== 修复验证完成 ===")
	fmt.Println("如果看到进度监控显示区块号在逐步增加，说明修复成功")
	fmt.Println("如果只在最后看到区块号更新，说明还是批量更新模式")
}
