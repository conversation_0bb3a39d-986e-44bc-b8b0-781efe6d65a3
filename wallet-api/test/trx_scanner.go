package main

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"math/big"
	"time"

	"wallet-api/internal/utility/crypto"

	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"github.com/fbsobreira/gotron-sdk/pkg/common"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"
)

// 测试地址
const testAddress = "TN69oECxenBUBtE8Z5uc36gUCt2PzrFRpd"

// USDT 合约地址
const usdtContractAddress = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"

// Transfer 事件签名
const transferEventSig = "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"

// 创建 TRON gRPC 客户端
func createTronClient(ctx context.Context) (*client.GrpcClient, error) {
	clientManager := crypto.GetInstance()
	return clientManager.GetDefaultTronClient(ctx)
}

// 扫描指定区块范围
func scanBlocks(ctx context.Context, tronClient *client.GrpcClient, startBlock, endBlock int64) error {
	fmt.Printf("开始扫描区块范围: %d - %d\n", startBlock, endBlock)

	var trxCount, usdtCount int

	for blockNum := startBlock; blockNum <= endBlock; blockNum++ {
		if blockNum%10 == 0 {
			fmt.Printf("正在扫描区块: %d\n", blockNum)
		}

		block, err := tronClient.GetBlockByNum(blockNum)
		if err != nil {
			log.Printf("获取区块 %d 失败: %v", blockNum, err)
			continue
		}

		if block == nil {
			log.Printf("区块 %d 为空", blockNum)
			continue
		}

		// 处理区块中的所有交易
		for _, txWrapper := range block.GetTransactions() {
			tx := txWrapper.GetTransaction()
			if tx == nil || tx.GetRawData() == nil {
				continue
			}

			// 计算交易哈希
			rawData, err := proto.Marshal(tx.GetRawData())
			if err != nil {
				continue
			}
			hash := sha256.Sum256(rawData)
			txHash := hex.EncodeToString(hash[:])

			// 检查交易类型
			txData := tx.GetRawData()
			for _, contract := range txData.GetContract() {
				if contract.GetType() == core.Transaction_Contract_TransferContract {
					// TRX 转账
					if processTrxTransaction(ctx, tx, contract, blockNum, txHash) {
						trxCount++
					}
				} else if contract.GetType() == core.Transaction_Contract_TriggerSmartContract {
					// TRC20 转账
					if processTrc20Transaction(ctx, tronClient, tx, contract, blockNum, txHash) {
						usdtCount++
					}
				}
			}
		}
	}

	fmt.Printf("扫描完成！找到 %d 笔 TRX 转账，%d 笔 TRC20 USDT 转账\n", trxCount, usdtCount)
	return nil
}

// 处理 TRX 转账交易
func processTrxTransaction(ctx context.Context, tx *core.Transaction, contract *core.Transaction_Contract, blockNum int64, txHash string) bool {
	// 解析 TransferContract
	transferContract := &core.TransferContract{}
	if err := contract.GetParameter().UnmarshalTo(transferContract); err != nil {
		return false
	}

	// 获取发送者和接收者地址
	fromAddr := common.EncodeCheck(transferContract.GetOwnerAddress())
	toAddr := common.EncodeCheck(transferContract.GetToAddress())
	amountSun := transferContract.GetAmount()

	// 检查是否是目标地址
	if toAddr != testAddress {
		return false
	}

	// 转换金额从 SUN 到 TRX
	amountTRX := decimal.NewFromInt(amountSun).Div(decimal.New(1, 6))

	fmt.Printf("=== 发现 TRX 充值 ===\n")
	fmt.Printf("区块号: %d\n", blockNum)
	fmt.Printf("交易哈希: %s\n", txHash)
	fmt.Printf("发送地址: %s\n", fromAddr)
	fmt.Printf("接收地址: %s\n", toAddr)
	fmt.Printf("金额: %s TRX (%d SUN)\n", amountTRX.String(), amountSun)
	fmt.Printf("==================\n\n")

	return true
}

// 处理 TRC20 转账交易
func processTrc20Transaction(ctx context.Context, tronClient *client.GrpcClient, tx *core.Transaction, contract *core.Transaction_Contract, blockNum int64, txHash string) bool {
	// 获取交易信息以访问日志
	txInfo, err := tronClient.GetTransactionInfoByID(txHash)
	if err != nil {
		return false
	}

	found := false

	// 解析交易日志中的 Transfer 事件
	for _, logEntry := range txInfo.GetLog() {
		// 检查是否是 Transfer 事件 (3 个 topics: 签名, from, to)
		if len(logEntry.GetTopics()) != 3 {
			continue
		}

		// 检查事件签名
		topic0 := hex.EncodeToString(logEntry.GetTopics()[0])
		if topic0 != transferEventSig {
			continue
		}

		// 解析合约地址
		contractAddrBytes := append([]byte{0x41}, logEntry.GetAddress()...)
		contractAddr := common.EncodeCheck(contractAddrBytes)

		// 检查是否是 USDT 合约
		if contractAddr != usdtContractAddress {
			continue
		}

		// 解析 'to' 地址 (Topic[2])
		var toAddrBytes [21]byte
		toAddrBytes[0] = 0x41
		copy(toAddrBytes[1:], logEntry.GetTopics()[2][12:])
		toAddr := common.EncodeCheck(toAddrBytes[:])

		// 检查是否是目标地址
		if toAddr != testAddress {
			continue
		}

		// 解析 'from' 地址 (Topic[1])
		var fromAddrBytes [21]byte
		fromAddrBytes[0] = 0x41
		copy(fromAddrBytes[1:], logEntry.GetTopics()[1][12:])
		fromAddr := common.EncodeCheck(fromAddrBytes[:])

		// 解析转账金额
		amountRaw := new(big.Int).SetBytes(logEntry.GetData())
		if amountRaw.Cmp(big.NewInt(0)) <= 0 {
			continue
		}

		// 转换金额考虑精度 (USDT 6 位小数)
		amountDecimal := decimal.NewFromBigInt(amountRaw, 0).Div(decimal.New(1, 6))

		fmt.Printf("=== 发现 TRC20 USDT 充值 ===\n")
		fmt.Printf("区块号: %d\n", blockNum)
		fmt.Printf("交易哈希: %s\n", txHash)
		fmt.Printf("合约地址: %s\n", contractAddr)
		fmt.Printf("发送地址: %s\n", fromAddr)
		fmt.Printf("接收地址: %s\n", toAddr)
		fmt.Printf("金额: %s USDT (Raw: %s)\n", amountDecimal.String(), amountRaw.String())
		fmt.Printf("========================\n\n")

		found = true
	}

	return found
}

func main() {
	ctx := gctx.New()

	fmt.Printf("开始扫描 TRON 区块，目标地址: %s\n\n", testAddress)

	// 创建 TRON 客户端
	tronClient, err := createTronClient(ctx)
	if err != nil {
		log.Fatalf("创建 TRON 客户端失败: %v", err)
	}

	// 获取最新区块号
	latestBlock, err := tronClient.GetNowBlock()
	if err != nil {
		log.Fatalf("获取最新区块失败: %v", err)
	}

	latestBlockNum := latestBlock.GetBlockHeader().GetRawData().GetNumber()
	fmt.Printf("当前最新区块号: %d\n", latestBlockNum)

	// 从指定区块开始扫描
	startBlock := int64(74201150)
	endBlock := startBlock + 10 // 扫描 1000 个区块

	// 确保不超过最新区块
	if endBlock > latestBlockNum {
		endBlock = latestBlockNum
	}

	fmt.Printf("将扫描区块范围: %d - %d\n\n", startBlock, endBlock)

	start := time.Now()
	err = scanBlocks(ctx, tronClient, startBlock, endBlock)
	if err != nil {
		log.Printf("扫描过程中出现错误: %v", err)
	}

	fmt.Printf("扫描耗时: %v\n", time.Since(start))
}
