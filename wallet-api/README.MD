# GoFrame Template For SingleRepo




#定时生成充值记录
 go run main.go run-task --name BalanceCollectorTask

#充值检查 生成充值记录
 go run main.go run-task --name DepositCheckTask 

#充值确认 确认充值记录和 生成提现计划
 go run main.go run-task --name DepositConfirmTask

#根据计划生成手续费记录
 go run main.go run-task --name WithdrawPlanProcessorTask


#发放矿工费 ，检查矿工费订单是否完成 完成后修改计划状态 以及 生成提现记录 
 go run main.go run-task --name WithdrawFeeHandlerTask 

#根据提现记录逐个完成提币操作 优先提usdt
 go run main.go run-task --name WithdrawStep3ProcessingTask

#确认提现订单完成 
 go run main.go run-task --name WithdrawStep4ConfirmTask




