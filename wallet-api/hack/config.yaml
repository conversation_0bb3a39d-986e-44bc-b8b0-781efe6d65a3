# CLI tool, only in development environment.
# https://goframe.org/docs/cli
gfcli:
  gen:
    dao:
      - link: "mysql:root:root@tcp(127.0.0.1:3306)/wallet?loc=Local&parseTime=true&charset=utf8mb4"
        descriptionTag: true
        debug: true
  docker:
    build: "-a amd64 -s linux -p temp -ew"
    tagPrefixes:
      - my.image.pub/my-app

# Kafka configuration
kafka:
  brokers:
    - "localhost:9092"
  topics:
    task_creation: "wallet-task-creation"
    task_execution: "wallet-task-execution"
    task_status: "wallet-task-status"
  consumer:
    group_id: "wallet-service"
    auto_offset_reset: "earliest"
  producer:
    acks: "all"
    retries: 3
    batch_size: 16384
    linger_ms: 1

redis:
  addr: "localhost:6379"
  password: ""
  db: 0

asynq:
  concurrency: 10
  queues:
    default: 5
    critical: 10
  tasks:
    chain_sync: "task:chain:sync"
    address_sync: "task:address:sync"
  schedules:
    eth_sync: "*/30 * * * *"  # 每30分钟同步一次ETH
    tron_sync: "*/30 * * * *" # 每30分钟同步一次TRON
