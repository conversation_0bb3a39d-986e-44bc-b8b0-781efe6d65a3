{"permissions": {"allow": ["Bash(rg:*)", "Bash(ls:*)", "Bash(cd:*)", "Bash(pwd:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(echo:*)", "Bash(grep:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "Bash(less:*)", "<PERSON><PERSON>(man:*)", "<PERSON><PERSON>(date:*)", "<PERSON><PERSON>(whoami:*)", "Bash(df:*)", "<PERSON><PERSON>(du:*)", "Bash(tar:*)", "Bash(gzip:*)", "Bash(gunzip:*)", "Ba<PERSON>(unzip:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "Bash(sort:*)", "<PERSON><PERSON>(uniq:*)", "Bash(wc:*)", "<PERSON><PERSON>(diff:*)", "Bash(patch:*)", "<PERSON><PERSON>(clear:*)", "Bash(git:*)", "Bash(vi:*)", "Bash(nano:*)", "<PERSON><PERSON>(chmod:*)", "Bash(kill:*)", "Bash(ps:*)", "Bash(env:*)", "<PERSON><PERSON>(sh:*)", "Bash(bash:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(wget:*)", "Bash(ssh:*)", "<PERSON><PERSON>(scp:*)", "Bash(grep:*)", "Bash(go build:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/migrate_services.sh:*)", "<PERSON><PERSON>(go run:*)", "Bash(ls:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(sed:*)", "Ba<PERSON>(go vet:*)", "Bash(go mod:*)", "Bash(go get:*)", "Bash(./basic)", "Bash(./formance)", "<PERSON><PERSON>(go test:*)", "Bash(go list:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(go:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(mysql:*)", "WebFetch(domain:github.com)", "WebFetch(domain:pkg.go.dev)", "WebFetch(domain:developers.tron.network)", "WebFetch(domain:tronprotocol.github.io)", "<PERSON><PERSON>(pkill:*)"], "deny": ["<PERSON><PERSON>(sudo)", "<PERSON><PERSON>(su)", "<PERSON><PERSON>(dd)", "Bash(mkfs)", "Bash(fdisk)", "<PERSON><PERSON>(parted)", "<PERSON><PERSON>(reboot)", "<PERSON><PERSON>(shutdown)", "Bash(halt)", "<PERSON><PERSON>(poweroff)", "<PERSON><PERSON>(useradd)", "<PERSON><PERSON>(userdel)", "<PERSON><PERSON>(usermod)", "<PERSON><PERSON>(groupadd)", "<PERSON><PERSON>(groupdel)", "Bash(chown)", "<PERSON><PERSON>(passwd)", "Bash(eval)", "Bash(mount)", "<PERSON><PERSON>(umount)", "Bash(iptables)", "<PERSON><PERSON>(ufw)", "Bash(systemctl)", "Bash(service)", "Bash(dpkg)", "<PERSON><PERSON>(apt)", "<PERSON><PERSON>(apt-get)", "<PERSON>sh(yum)", "Bash(rpm)", "<PERSON><PERSON>(insmod)", "<PERSON><PERSON>(rmmod)", "Bash(nc)", "<PERSON><PERSON>(socat)", "<PERSON><PERSON>(exec)", "<PERSON>sh(killall)", "<PERSON><PERSON>(pkill)", "<PERSON><PERSON>(crontab)", "Bash(at)", "<PERSON><PERSON>(chattr)"]}, "enableAllProjectMcpServers": false}